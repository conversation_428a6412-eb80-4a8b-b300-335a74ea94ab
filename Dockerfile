FROM node:14.16.0-alpine3.13 AS build
RUN apk add --update gettext libintl \
  && cp /usr/bin/envsubst /usr/local/bin/envsubst \
  && apk del gettext

WORKDIR /app
ENV HOME /app
ENV PUBLIC_URL /self-service/
ARG DEFAULT_ENV
ADD entrypoint.sh /
RUN chmod 755 /entrypoint.sh
COPY app /app
RUN sed -i "/app_environment/s/dev/$DEFAULT_ENV/" public/config.js
# this double checks that yarn install actually worked.
RUN yarn install --network-timeout=30000 \
  && if [ ! -d node_modules ] ; then \
  echo "===> yarn install failed, check network and your vpn"; \
  exit 1; \
  fi
ENTRYPOINT ["/entrypoint.sh"]
RUN GENERATE_SOURCEMAP=false yarn run build

FROM ubuntu:20.04
RUN apt-get update \
  && apt-get install -y nginx \
  && apt-get clean \
  && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/* \
  && echo "daemon off;" >> /etc/nginx/nginx.conf
RUN ln -sf /dev/stdout /var/log/nginx/access.log && ln -sf /dev/stderr /var/log/nginx/error.log
COPY --from=build /app/build /var/www/html/self-service
ADD default /etc/nginx/sites-available/default
EXPOSE 80
CMD ["nginx"]
