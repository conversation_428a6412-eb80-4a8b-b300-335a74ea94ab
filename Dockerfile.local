ARG NODE_IMG=node:14.16.0-alpine3.13
ARG NGINX_IMG=https://fourkitesdevelopment.azurecr.io/dockerimage/nginx:1.22.1-4b294400-090123-3

# development container
FROM $NODE_IMG as development
LABEL maintainer="<PERSON>raque Viana <<EMAIL>>"

RUN apk add --update gettext libintl \
  && cp /usr/bin/envsubst /usr/local/bin/envsubst \
  && apk del gettext

WORKDIR /app
ENV HOME /app

ADD entrypoint.sh /
RUN chmod 755 /entrypoint.sh

COPY app/package.json /app/
COPY app/tsconfig.json /app/
COPY app/.yarnrc /app/
COPY app/yarn.lock /app/

# this double checks that yarn install actually worked.
RUN yarn install --network-timeout=30000 \
  && if [ ! -d node_modules ] ; then \
  echo "===> yarn install failed, check network and your vpn"; \
  exit 1; \
  fi
COPY app/public/ /app/public/
COPY app/src/ /app/src/

EXPOSE 3000
# the entrypoint runs all commands.
# this gets called as /entrypoint.sh $CMD
# this entrypoint script does our config.js templating
ENTRYPOINT ["/entrypoint.sh"]
CMD ["yarn", "start"]

# temp build container
FROM $NODE_IMG as build
COPY --from=development /app /app
COPY --from=development /usr/local/bin/envsubst /usr/local/bin
WORKDIR /app
RUN GENERATE_SOURCEMAP=false yarn run build

# production nginx container
FROM $NGINX_IMG as production

COPY --from=build /app/build /app/build
COPY --from=build /app/public /app/public

COPY nginx.conf /etc/nginx/nginx.conf

ADD entrypoint.sh /
RUN chmod 755 /entrypoint.sh

WORKDIR /app
EXPOSE 3000

ENTRYPOINT ["/entrypoint.sh"]
CMD ["nginx"]
