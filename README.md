# Connectivity (Beta) Portal UI

Repository for the Connectivity (Beta) Portal UI.

## Local Setup

You'll need to be connected to the 4k VPN in order to run this app.

### Docker

To run it locally with <PERSON><PERSON>, run the following commands:

```
docker-compose -f docker-compose.local.yml build

docker-compose -f docker-compose.local.yml up
```

### Running natively (with yarn)

In order for it to build properly, you first need to add the fourkites npm server to
the yarn registry.

`yarn config set registry http://npm.fourkites.com`

As this app is created with `create-react-app`, it supports the following scripts:

`yarn install`
`yarn test`
`yarn build`

To run the app in development mode, just do:

`yarn start`

Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

The page will reload if you make edits.\
You will also see any lint errors in the console.
