module.exports = {
  roots: ["./src"],
  setupFilesAfterEnv: ["./jest.setup.ts"],
  moduleFileExtensions: ["ts", "tsx", "js"],
  testPathIgnorePatterns: ["node_modules"],
  transform: {
    "^.+\\.(ts|tsx)$": "ts-jest",
  },
  testMatch: ["**/*.test.(ts|tsx)"],
  moduleNameMapper: {
    "\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga|pdf)$":
      "<rootDir>/assetTransformer.js",
    "^state/(.*)$": ["<rootDir>/src/state/$1"],
    "^assets/(.*)$": ["<rootDir>/src/assets/$1"],
    "^api/(.*)$": ["<rootDir>/src/api/$1"],
    "^view/(.*)$": ["<rootDir>/src/view/$1"],
    "^router/(.*)$": ["<rootDir>/src/router/$1"],
    "\\.(css|less|scss|sass)$": "identity-obj-proxy",
    "^yourPath/(.*)": "<rootDir>\\yourPath\\$1",
  },
  moduleDirectories: ["node_modules", "src"],
};
