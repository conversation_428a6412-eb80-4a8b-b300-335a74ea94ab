{"name": "self-service-ui", "version": "0.1.0", "private": true, "dependencies": {"@fourkites/elemental-accordion": "^0.0.1", "@fourkites/elemental-atoms": "^0.2.6", "@fourkites/elemental-avatar": "^0.0.6", "@fourkites/elemental-button-group": "^0.0.13", "@fourkites/elemental-checkbox": "^0.0.15", "@fourkites/elemental-chip": "^0.0.12", "@fourkites/elemental-datepicker": "^0.3.0", "@fourkites/elemental-global-header": "0.5.11", "@fourkites/elemental-hierarchical-sidebar": "^0.0.22", "@fourkites/elemental-input": "^0.0.14", "@fourkites/elemental-loading-indicator": "^0.1.6", "@fourkites/elemental-menu": "^0.2.0", "@fourkites/elemental-modal": "^0.2.9", "@fourkites/elemental-phone-input": "^0.1.2", "@fourkites/elemental-radio-button": "^0.0.15", "@fourkites/elemental-select": "^0.1.21", "@fourkites/elemental-switch": "^0.0.1", "@fourkites/elemental-table": "^0.2.18", "@fourkites/elemental-tabs": "^0.0.13", "@fourkites/elemental-tooltip": "^0.0.19", "@reduxjs/toolkit": "^1.5.0", "@types/keycloak-js": "^3.4.1", "@types/luxon": "3.0.2", "@types/node-jose": "^1.1.8", "@types/react-leaflet": "^2.8.2", "@types/react-modal": "^3.12.0", "@types/react-pdf": "^5.0.5", "@types/react-signature-canvas": "^1.0.2", "@types/react-table": "^7.0.29", "@types/react-tabs": "^2.3.2", "@types/react-toastify": "^4.1.0", "axios": "^0.21.1", "classnames": "^2.3.1", "copy-to-clipboard": "^3.3.1", "i18next": "^20.2.1", "i18next-browser-languagedetector": "^6.1.0", "i18next-http-backend": "^1.2.1", "keycloak-js": "^15.0.2", "leaflet": "^1.7.1", "lodash": "^4.17.21", "luxon": "^2.5.0", "node-jose": "^2.0.0", "react": "^17.0.1", "react-app-polyfill": "^2.0.0", "react-cookie": "^4.0.3", "react-dom": "^17.0.1", "react-helmet": "6.1.0", "react-i18next": "^11.8.13", "react-json-pretty": "^2.2.0", "react-leaflet": "^3.2.2", "react-pdf": "^5.3.2", "react-redux": "^7.2.2", "react-responsive": "^9.0.0-beta.6", "react-router-dom": "^5.2.0", "react-scripts": "4.0.3", "react-signature-canvas": "^1.0.3", "react-slideshow-image": "^4.2.1", "react-tabs": "^3.2.1", "react-toastify": "^8.1.0", "sass": "^1.32.8", "universal-cookie": "^4.0.4", "web-vitals": "^1.0.1"}, "devDependencies": {"@babel/core": "^7.12.13", "@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "@types/jest": "^26.0.15", "@types/lodash": "^4.17.13", "@types/node": "^12.0.0", "@types/react": "^18.0.25", "@types/react-dom": "^17.0.2", "@types/react-helmet": "6.1.11", "@types/react-redux": "^7.1.16", "@types/react-router-dom": "^5.1.7", "@typescript-eslint/eslint-plugin": "^4.0.0", "@typescript-eslint/parser": "^4.0.0", "babel-eslint": "^10.0.0", "babel-loader": "8.1.0", "babel-preset-react-app": "^10.0.0", "eslint": "^7.5.0", "eslint-config-react-app": "^6.0.0", "eslint-plugin-flowtype": "^5.2.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-react": "^7.20.3", "eslint-plugin-react-hooks": "^4.0.8", "husky": "^5.1.3", "identity-obj-proxy": "^3.0.0", "lint-staged": "^10.5.4", "prettier": "^2.2.1", "stylelint": "^13.11.0", "stylelint-config-standard": "^20.0.0", "stylelint-scss": "^3.19.0", "ts-jest": "26.5.0", "typescript": "^4.1.2"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}": ["prettier --write"], "*.{js,jsx,ts,tsx}": "eslint --max-warnings=0", "*.scss": "stylelint --max-warnings=0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "eject": "react-scripts eject", "test": "jest", "test:watch": "jest --watch", "test:coverage": "CI=true jest --coverage"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": ["ie 11", ">0.2%", "not dead", "not op_mini all"], "development": ["ie 11", "last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}}