import { KITE_BOT_URL } from "api/http/apiUtils";
import React, { useEffect } from "react";

export const KitebotExternalResource = () => {
  useEffect(() => {
    try {
      const loadCSS = () => {
        const link = document.createElement("link");
        link.rel = "stylesheet";
        link.id = "kitebot-css";
        link.href = `${KITE_BOT_URL}/kitebot.css`;
        link.onload = () => {
          loadJS();
        };
        if (!document.getElementById("kitebot-css"))
          document.head.appendChild(link);
      };

      loadCSS();

      const loadJS = () => {
        const script = document.createElement("script");
        script.src = `${KITE_BOT_URL}/kitebot.js`;
        script.id = "kitebot-js";
        script.async = true;

        if (!document.getElementById("kitebot-js"))
          document.body.appendChild(script);

        document.body.style.overflow = "hidden";
      };
    } catch (error) {
      console.log(error);
    }
  }, []);

  return <></>;
};
