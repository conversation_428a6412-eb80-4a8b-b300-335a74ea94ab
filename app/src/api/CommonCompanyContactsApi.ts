import { get, post, put, remove } from "./http/service";
import { serviceUrls } from "./http/apiUtils";
import { AddContactPayload } from "view/components/self-service/company-management/carrier-details/modals/AddYourContactModal.types";

class CommonCompanyContactsApi {
  /*****************************************************************************
   * COMPANY CONTACTS
   ****************************************************************************/

  // retrieve the company contacts
  retrieveCompanyContacts = async (companyId: string, scac: string) => {
    const url = `${serviceUrls.companyContacts}/contacts/get-contacts?shipper_id=${companyId}&scac=${scac}`;
    const response = await get(url);
    return response;
  };

  // Add a new contact
  createCompanyContact = async (
    companyId: string,
    scac: string,
    payload: AddContactPayload
  ) => {
    const url = `${serviceUrls.companyContacts}/contacts/add-contact?shipper_id=${companyId}&scac=${scac}`;
    const response = await post(url, payload);

    return response;
  };

  // Update a contact
  updateCompanyContact = async (
    contact_id: number,
    companyId: string,
    payload: AddContactPayload
  ) => {
    const url = `${serviceUrls.companyContacts}/contacts/edit-contact?contact_id=${contact_id}&shipper_id=${companyId}`;
    const response = await put(url, payload);

    return response;
  };

  // Delete a contact
  deleteCompanyContact = async (contact_id: number, companyId: string) => {
    const url = `${serviceUrls.companyContacts}/contacts/delete-contact?contact_id=${contact_id}&shipper_id=${companyId}`;
    const response = await remove(url);
    return response;
  };
}

const client = new CommonCompanyContactsApi();
Object.freeze(client);
export default client;
