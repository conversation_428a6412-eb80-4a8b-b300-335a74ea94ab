import { serviceUrls } from "api/http/apiUtils";
import { get } from "./http/service";
import { LoadsTrackingMode } from "state/BaseTypes";
import { storageKeys } from "./constants";

class CompaniesApi {
  /*****************************************************************************
   * CARRIER DETAILS
   ****************************************************************************/

  /*
   * Fetches the list of carriers: used by authenticated callers
   */
  searchCompanies = async (
    companyId: string,
    companyType: string,
    mode: LoadsTrackingMode,
    query: string
  ) => {
    const encodedQuery = encodeURIComponent(query);
    const url = `${serviceUrls.selfOnboarding}/companies/suggestions?search=${encodedQuery}&permalink=${companyId}&type=${companyType}&mode=${mode}`;

    // TODO: should this be on API?
    let response = await get(url);
    return response;
  };

  // TODO: this will be used on onboardin in the future
  /*
   * Fetches the list of carriers: used by unauthenticated callers
   */
  searchCompaniesExternally = async (
    query: string,
    invitationToken: string
  ) => {
    const encodedQuery = encodeURIComponent(query);
    const url = `${serviceUrls.companies}/selfservice/suggestions?search=${encodedQuery}&invitation-token=${invitationToken}`;

    // let response = await get(url);
    //return response;

    if (!query.includes("comp")) {
      return { data: [] };
    }
  };

  /*****************************************************************************
   * COMPANY DETAILS
   ****************************************************************************/

  /*
   * Calls this function to get token using username and password
   */
  getBasicTokenHeaders = (basicToken: string | undefined) => {
    return basicToken
      ? {
          Authorization: `Basic ${basicToken}`,
        }
      : {};
  };

  /*
   * Calls API to get company details using user and password
   */
  getCompanyDetailsWithPassword = async (
    companyId: string,
    username: string,
    password: string
  ) => {
    const companyServiceUrl = `${serviceUrls.companies}/companies/${companyId}`;
    const basicToken = btoa(`${username}:${password}`);
  
    try {
      const companyResponse = await get(
        companyServiceUrl,
        {},
        this.getBasicTokenHeaders(basicToken)
      );
  
      if (companyResponse) {
        localStorage.setItem(storageKeys.companyConfig, JSON.stringify(companyResponse.data.company));
      }
      return companyResponse;
    } catch (error) {
      console.error("Error fetching company details:", error);
      throw error;
    }
  };

  /*
   * Calls API to get company details using token
   */
  getCompanyDetailsWithToken = async (companyId: string) => {
    const companyServiceUrl = `${serviceUrls.companies}/companies/${companyId}`;
    try {
      const companyResponse = await get(companyServiceUrl, {});
      if (companyResponse) {
        localStorage.setItem("tfCompanyConfig", JSON.stringify(companyResponse.data.company));
      }
      return companyResponse;
    } catch (error) {
      console.error("Error fetching company details:", error);
      throw error;
    }
  };
}

const client = new CompaniesApi();
Object.freeze(client);
export default client;
