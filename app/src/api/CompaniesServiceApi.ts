  interface Company {
    id: string; 
    name: string;
  }
  
  const companySelectorKey = "companyContextId";
  
  const getHeaders = (authToken:string, deviceId:string, userId:string, isJson = true) => ({
    "Content-Type":  "application/json" ,
    Authorization: `Bearer ${authToken}`,
    "X-FourKitesDeviceId": deviceId,
    "X-FourKitesUserId": userId,
  });
  
  /* 
      fetches all child Company details for 3pl companyID 
  */
  export const fetchCompanies = async (
    domain: string,
    defaultCompanyID: string,
    currentUser: any,
    query: any
  ) => {
    try {
      const url = `https://${domain}/api/v1/companies/${defaultCompanyID}/associated_companies_autocomplete?q=${query}&show_managed_shippers=true`;
      const response = await fetch(url, {
        method: "GET",
        headers: getHeaders(
          currentUser.authToken,
          currentUser.deviceId,
          currentUser.userId,
          false
        ),
      });
      const data = await response.json();
      if (data.statusCode === 200 && data.companies.length != 0) {
        return data.companies.map((company: Company) => ({
          id: company.id,
          label: company.name,
        }));
      }
      return [];
    } catch (error) {
      return error;
    }
  };
  
  /* 
      fetches all auto Company written in search text field
  */
  export const fetchCompanyAutocomplete = async (
    authToken: string,
    deviceId: string,
    userId: string,
    domain: string,
    query: string
  ) => {
    const url = `https://${domain}/api/v1/companies/autocomplete?q=${query}&locale=en`;
    try {
      const response = await fetch(url, {
        method: "GET",
        headers: getHeaders(authToken, deviceId, userId, false),
      });
      if (!response.ok) {
        const errorMessage = await response.json();
        const message = `An error has occurred: ${response.status}: ${errorMessage.error}`;
        throw new Error(message);
      }
      const responseJson = await response.json();
      let options: any
      if (responseJson.map.length!=0){
          options = responseJson.map((item:any) => ({
          id: item.id,
          label: item.description,
        }));
      }
      return options;
    } catch (err) {
      return err;
    }
  };

  /* 
      return the intial selected company
  */
  
  export const retrieveInitialCompany = async (
    authToken: string,
    deviceId: string,
    userId: string,
    domain: string,
    performInitialQuery: boolean
  ) => {
    const savedCompanySelection = sessionStorage.getItem(companySelectorKey);
    if (isValidCompany(savedCompanySelection)) {
      const savedCompany = JSON.parse(savedCompanySelection || '{}');
      const initialCompany = {
        id: savedCompany.id,
        label: savedCompany.description,
      };
      return initialCompany;
    }
  
    if (performInitialQuery) {
      const options = await fetchCompanyAutocomplete(
        authToken,
        deviceId,
        userId,
        domain,
        "a"
      );
      return options[0];
    }
  };

  /* 
      save the selected company to Memory
  */
  
  export const saveCompanySelectionToMemory = (newSelectedCompany: any) => {
    // revert format to format returned by endpoint
    const reformattedCompany = {
      id: newSelectedCompany.id,
      description: newSelectedCompany.label,
    };
    sessionStorage.setItem(
      companySelectorKey,
      JSON.stringify(reformattedCompany)
    );
  };

  /* 
      checks the valid comapany
  */
  
  const isValidCompany = (potentialCompany: any) => {
    if (typeof potentialCompany !== "string") {
      return false;
    }
    try {
      const parsedCompany = JSON.parse(potentialCompany);
      return !!parsedCompany.id && !!parsedCompany.description;
    } catch (err) {
      return err;
    }
  };
  