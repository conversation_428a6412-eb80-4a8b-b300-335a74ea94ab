import { serviceUrls } from "api/http/apiUtils";
import { get, patch } from "api/http/service";

class CarrierCompanyDetailsApi {
  /*****************************************************************************
   * CARRIER COMPANY DETAILS
   ****************************************************************************/

  /*
   * Gets the company details for this carrier
   */
  retrieveCarrierCompanyDetails = async (carrierId: string) => {
    const url = `${serviceUrls.selfOnboarding}/carriers/${carrierId}`;

    let response = await get(url);
    return response;
  };

  /*
   * Gets the company details for this carrier
   */
  updateCarrierCompanyDetails = async (
    carrierId: string,
    carrierCompanyDetails: any
  ) => {
    const url = `${serviceUrls.selfOnboarding}/carriers/${carrierId}`;

    let data = new FormData();

    // Adds all fields present as form data
    const patchableFields = [
      { id: "logo", shouldStringify: false },
      { id: "name", shouldStringify: false },
      { id: "types", shouldStringify: true },
      { id: "address", shouldStringify: true },
      { id: "settings", shouldStringify: true },
      { id: "special_onboarding_instructions", shouldStringify: true },
    ];
    for (const p of patchableFields) {
      const fieldId = p.id;

      if (carrierCompanyDetails[fieldId]) {
        const field = p.shouldStringify
          ? JSON.stringify(carrierCompanyDetails[fieldId])
          : carrierCompanyDetails[fieldId];

        data.append(fieldId, field);
      }
    }

    let response = await patch(url, data, {
      // @ts-ignore
      "Content-Type": `multipart/form-data; boundary=${data?._boundary}`,
    });

    return response;
  };
}

const client = new CarrierCompanyDetailsApi();
Object.freeze(client);
export default client;
