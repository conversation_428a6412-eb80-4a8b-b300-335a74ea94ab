import { serviceUrls } from "api/http/apiUtils";
import { get, patch } from "api/http/service";

class CarrierConfigurationsApi {
  /*****************************************************************************
   * CARRIER COMPANY CONFIGURATIONS
   ****************************************************************************/

  /*
   * Gets the FKC configurations for this carrier
   */
  retrieveLtlConfigurationsForCarrier = async (
    carrierId: string,
    mode: string
  ) => {
    const url = `${serviceUrls.selfOnboarding}/carrier-details/${mode}/${carrierId}`;

    let response = await get(url);
    return response;
  };

  updateLtlConfigurations = async (
    carrierId: string,
    ltlConfigurations: any
  ) => {
    const url = `${serviceUrls.selfOnboarding}/carrier-details/${ltlConfigurations.mode}/${carrierId}`;
    let response = await patch(url, ltlConfigurations);
    return response;
  };
}

const client = new CarrierConfigurationsApi();
Object.freeze(client);
export default client;
