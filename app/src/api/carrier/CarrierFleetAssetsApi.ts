import { serviceUrls } from "api/http/apiUtils";
import { get, post, remove, qs } from "api/http/service";
import { FleetMode } from "state/BaseTypes";

import { sleep } from "../http/mockServiceCall";

class CarrierFleetAssetsApi {
  /*****************************************************************************
   * CARRIER ASSETS
   ****************************************************************************/

  createCarrier = async (carrierId: string) => {
    const url = `${serviceUrls.assets}/carriers/${carrierId}/`;
    let response = await post(url, {});
    return response;
  };

  /****************************** FLEETS ******************************/

  createFleet = async (
    carrierId: string,
    fleetMode: FleetMode,
    fleetName: string
  ) => {
    const url = `${serviceUrls.assets}/carriers/${carrierId}/fleets/`;
    let response = await post(url, { mode: fleetMode, name: fleetName });
    return response;
  };

  retrieveFleets = async (
    carrierId: string,
    fleetMode: FleetMode,
    isDefault: boolean
  ) => {
    const url = `${
      serviceUrls.assets
    }/carriers/${carrierId}/fleets/?mode=${fleetMode}&is_default=${
      isDefault ? 1 : 0
    }`;
    let response = await get(url);
    return response;
  };

  updateFleet = async (
    carrierId: string,
    fleetId: number | string,
    fleetMode: FleetMode
  ) => {
    //TODO
  };

  deleteFleet = async (
    carrierId: string,
    fleetId: number | string,
    fleetMode: FleetMode
  ) => {
    const url = `${serviceUrls.assets}/carriers/${carrierId}/fleets/${fleetId}/?mode=${fleetMode}`;
    let response = await remove(url);
    return response;
  };

  /****************************** ASSETS ******************************/

  /*
   * Adds a new asset to this carrier fleet
   */
  createFleetAssets = async (
    carrierId: string,
    fleetId: number | string,
    assets: any[]
  ) => {
    const url = `${serviceUrls.assets}/carriers/${carrierId}/fleets/${fleetId}/assets/batch`;
    let response = await post(url, assets);
    return response;
  };

  /*
   * Fetches the list of assets that belongs to this fleet and carrier
   */
  retrieveFleetAssets = async (
    carrierId: string,
    fleetId: number | string,
    number: string | null = null,
    assetType: string | null = null,
    loadStatus: "loads_assigned" | "available" | null = null,
    trackingStatus: "tracking" | "not_tracking" | null = null,
    pageIndex: number,
    pageSize: number
  ) => {
    let qsParameters = qs({
      number: number,
      type: assetType,
      load_status: loadStatus,
      tracking_status: trackingStatus,
      size: pageSize,
      page: pageIndex,
    });
    const url = `${serviceUrls.assets}/carriers/${carrierId}/fleets/${fleetId}/assets/${qsParameters}`;

    let response = await get(url);
    return response;
  };

  /*
   * Updates fleet asset
   */
  updateFleetAssets = async (
    carrierId: string,
    fleetId: number | string,
    assets: any[]
  ) => {
    const url = `${serviceUrls.assets}/carriers/${carrierId}/fleets/${fleetId}/assets/batch`;
    //let response = await patch(url, { mode: mode, asset: asset });
    //return response;

    await sleep(1000);
    return { data: [] };
  };

  /*
   * Delete fleet asset
   */
  deleteFleetAsset = async (
    carrierId: string,
    fleetId: number | string,
    assetId: string
  ) => {
    const url = `${serviceUrls.assets}/carriers/${carrierId}/fleets/${fleetId}/assets/${assetId}`;
    let response = await remove(url);
    return response;
  };

  /****************************** ASSET ACTIONS ******************************/

  // TODO: add ping test for other modes, where applicable
  /*
   * Get location of fleet asset
   */
  retrieveFleetAssetPingLocation = async (
    carrierId: string,
    assetId: string,
    assetType: string
  ) => {
    const assetTypeQs =
      assetType == "truck" ? "truck_number" : "trailer_number";
    const qsParameters = qs({
      locale: "en",
      carrier_id: carrierId,
      [assetTypeQs]: assetId,
    });

    const url = `${serviceUrls.carrierData}/location_providers/get_location${qsParameters}`;

    let response = await get(url);
    return response;
  };

  // TODO: implement
  /*
   * Get location of fleet asset from assets service
   */
  retrieveFleetAssetLocation = async (
    carrierId: string,
    fleetId: number | string,
    assetId: string
  ) => {
    const url = `${serviceUrls.assets}/carriers/${carrierId}/fleets/${fleetId}/assets/${assetId}/`;
    //let response = await get(url);
    //return response;

    await sleep(1000);
    return {
      data: {
        address: {
          address: "1 Street",
          zip: "123456789",
          city: "Barueri",
          state: "Sao Paolo",
          country: "Brazil",
          country_code: "BR",
        },
        location: {
          provider: {
            id: "samsara",
            name: "Samsara",
            alias: "My Keep Trucking Alias",
          },
          lat: -7.45645465465,
          lon: 45.**********,
        },
      },
    };
  };

  /*
   * Move asset between fleets
   */
  moveFleetAsset = async (
    carrierId: string,
    fleetId: number | string,
    assetId: number | string,
    fleetMode: FleetMode
  ) => {
    const url = `${serviceUrls.assets}/carriers/${carrierId}/fleets/${fleetId}/assets/?mode=${fleetMode}`;

    //TODO
  };
}

const client = new CarrierFleetAssetsApi();
Object.freeze(client);
export default client;
