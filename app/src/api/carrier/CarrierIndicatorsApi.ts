import { serviceUrls } from "api/http/apiUtils";
import { get } from "api/http/service";
import { LoadsTrackingMode } from "state/BaseTypes";

class CarrierIndicatorsApi {
  /*
   * Fetches the carrier indicators
   */
  getCarrierIndicators = async (carrierId: string, mode: LoadsTrackingMode) => {
    const url = `${serviceUrls.selfOnboarding}/carriers/${carrierId}/indicators?mode=${mode}`;

    let response = await get(url);
    return response;
  };
}

const client = new CarrierIndicatorsApi();
Object.freeze(client);
export default client;
