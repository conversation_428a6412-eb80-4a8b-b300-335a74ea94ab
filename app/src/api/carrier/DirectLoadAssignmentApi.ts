import { serviceUrls } from "api/http/apiUtils";
import { post } from "api/http/service";

class DirectLoadAssignmentApi {
  /*****************************************************************************
   * CARRIER SHIPPERS NETWORK
   ****************************************************************************/

  /*
   * Fetches the information about direct assignment loads
   */
  retrieveDirectAssignmentLoads = async (apiToken: string) => {
    const url = `${serviceUrls.tracking}/tracking/direct_assignment_loads`;

    // NOT TO SELF: I HATE THIS!!!
    let response = await post(
      url,
      {
        token: apiToken,
        // NOTE: API requires some time frame, so we are passing 10 days
        filterHours: 240,
        // NOTE: we get unassigned because if no loads assigned, bug in API
        // returns 0
        tab: "unassigned",
      },
      {
        Authorization: null,
        "X-FourKitesDeviceId": null,
        "X-FourKitesUserId": null,
      }
    );
    return response;
  };
}

const client = new DirectLoadAssignmentApi();
Object.freeze(client);
export default client;
