import { serviceUrls } from "api/http/apiUtils";
import { qs, get } from "api/http/service";

interface QsParameters {
  fromTimestamp: string;
  toTimestamp: string;
  carrierPermalink: string;
  current_page: any;
  page_size: any;
  identifiers?: string;
  shipper?: string;
}

class DispatcherLogsApi {
  /*
   * Fetches the list of dispatcher API logs
   */
  retrieveDispatcherLogs = async (
    fromTimestamp: string,
    toTimestamp: string,
    carrierId: string,
    loadIdentifier: string,
    shipper: string,
    pagination: any
  ): Promise<any> => {
    let qsParameters: QsParameters = {
      fromTimestamp: fromTimestamp,
      toTimestamp: toTimestamp,
      carrierPermalink: carrierId,
      current_page: pagination.current_page,
      page_size: pagination.page_size,
    };
    if (loadIdentifier) {
      qsParameters.identifiers = encodeURIComponent(loadIdentifier);
    }
    if (shipper) {
      qsParameters.shipper = encodeURIComponent(shipper);
    }
    let queryString = qs(qsParameters);
    var url = `${serviceUrls.dispatcherlogs}/search${queryString}`;
    const response = await get(url,{},{},true);
    const data = response.data;
    return data;
  };
}

const client = new DispatcherLogsApi();
Object.freeze(client);
export default client;