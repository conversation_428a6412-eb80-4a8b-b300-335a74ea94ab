import { serviceUrls } from "api/http/apiUtils";
import { get, post, patch, remove, qs } from "api/http/service";
import { LoadsTrackingMode, LocationProvider } from "state/BaseTypes";
import { getExternalAuthenticationHeaders } from "../external/utils";

class EldGpsIntegrationsApi {
  /*****************************************************************************
   * ELD GPS INTEGRATIONS
   ****************************************************************************/

  /*
   * Retrieves the ELD GPS integrations for this carrier
   */
  retrieveIntegrations = async (
    carrierId: string,
    modes: LoadsTrackingMode[],
    externalAccessToken?: string
  ) => {
    let qsParameters = qs({ mode: modes });
    const url = `${serviceUrls.selfOnboarding}/carriers/${carrierId}/integrations/eld-gps${qsParameters}`;

    let response = await get(
      url,
      {},
      // Headers for external access
      getExternalAuthenticationHeaders(externalAccessToken)
    );
    return response;
  };

  /*
   * Retrieves the ELD GPS integration details
   */
  retrieveIntegrationDetails = async (
    carrierId: string,
    integrationId: string | number,
    externalAccessToken?: string
  ) => {
    const url = `${serviceUrls.selfOnboarding}/carriers/${carrierId}/integrations/eld-gps/${integrationId}`;

    let response = await get(
      url,
      {},
      // Headers for external access
      getExternalAuthenticationHeaders(externalAccessToken)
    );
    return response;
  };

  /*
   * Creates a new ELD GPS integrations for this carrier
   */
  createIntegration = async (
    carrierId: string,
    modes: LoadsTrackingMode[],
    userId: string,
    provider: LocationProvider,
    assetNumber?: string,
    externalAccessToken?: string
  ) => {
    const url = `${serviceUrls.selfOnboarding}/carriers/${carrierId}/integrations/eld-gps`;

    let response = await post(
      url,
      {
        modes,
        user_id: userId,
        asset_number: assetNumber || null,
        provider: {
          id: provider.id,
          type: provider.type,
          alias: provider.alias || null,
          credentials: provider.credentials,
        },
      },
      // Headers for external access
      getExternalAuthenticationHeaders(externalAccessToken)
    );

    return response;
  };

  /*
   * Updates an existing ELD integration for this carrier
   */
  updateIntegration = async (
    carrierId: string,
    integrationId: string | number,
    modes: LoadsTrackingMode[],
    userId: string,
    provider: LocationProvider,
    assetNumber?: string,
    completeIntegrationManually?: boolean,
    externalAccessToken?: string
  ) => {
    const url = `${serviceUrls.selfOnboarding}/carriers/${carrierId}/integrations/eld-gps/${integrationId}`;

    // Param completeIntegrationManually would be passed as true for manual intervnetion providers
    const externalHeaders =
      getExternalAuthenticationHeaders(externalAccessToken);

    const headers = completeIntegrationManually
      ? {
          // Headers for external access
          ...externalHeaders,
          // @ts-ignore
          "GPS-CompleteIntegration": completeIntegrationManually,
        }
      : externalHeaders;

    let response = await patch(
      url,
      {
        modes,
        user_id: userId,
        asset_number: assetNumber || null,
        provider: {
          id: provider.id,
          type: provider.type,
          alias: provider.alias || null,
          credentials: provider.credentials,
        },
      },
      headers
    );
    return response;
  };

  /*
   * Deletes an ELD GPS integration associated with this carrier
   */
  deleteIntegration = async (
    carrierId: string,
    integrationId: string | number,
    modes: LoadsTrackingMode[],
    externalAccessToken?: string
  ) => {
    let qsParameters = qs({ mode: modes });
    const url = `${serviceUrls.selfOnboarding}/carriers/${carrierId}/integrations/eld-gps/${integrationId}${qsParameters}`;

    let response = await remove(
      url,
      {},
      // Headers for external access
      getExternalAuthenticationHeaders(externalAccessToken)
    );
    return response;
  };

  /*****************************************************************************
   * OTHER ACTIONS
   ****************************************************************************/

  /*
   * Creates a help request for this integration
   */
  createIntegrationHelpRequest = async (
    carrierId: string,
    modes: LoadsTrackingMode[],
    provider: LocationProvider,
    userMessage: string,
    externalAccessToken?: string
  ) => {
    const url = `${serviceUrls.selfOnboarding}/carriers/${carrierId}/integrations/eld-gps/help-requests`;

    let response = await post(
      url,
      {
        modes,
        provider: {
          id: provider.id,
          type: provider.type,
          name: provider.name,
        },
        userMessage,
      },
      // Headers for external access
      getExternalAuthenticationHeaders(externalAccessToken)
    );
    return response;
  };

  /*
   * NOTE: this is exclusevely used for peoplente
   * Sends information so we can create the data share agreement
   */
  createDataShareAgreement = async (
    carrierId: string,
    dsaFields: {
      cid: string;
      first_name: string;
      last_name: string;
      position: string;
    },
    signature: File,
    externalAccessToken?: string
  ) => {
    const url = `${serviceUrls.selfOnboarding}/carriers/${carrierId}/integrations/eld-gps/data-sharing-agreements`;

    // NOTE: convert file to blob, since it's an image
    //@ts-ignore
    let signatureFile = await fetch(signature);
    const signatureFileBlob = await signatureFile.blob();

    let data = new FormData();
    data.append("signature", signatureFileBlob);
    data.append("cid", dsaFields.cid);
    data.append("first_name", dsaFields.first_name);
    data.append("last_name", dsaFields.last_name);
    data.append("position", dsaFields.position);

    let response = await post(url, data, {
      ...getExternalAuthenticationHeaders(externalAccessToken),
      // @ts-ignore
      "Content-Type": `multipart/form-data; boundary=${data?._boundary}`,
    });

    return response;
  };
}

const client = new EldGpsIntegrationsApi();
Object.freeze(client);
export default client;
