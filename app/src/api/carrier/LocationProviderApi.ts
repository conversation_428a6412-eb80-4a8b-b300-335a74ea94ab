import { serviceUrls } from "api/http/apiUtils";
import { get, post } from "api/http/service";
import { base64EncodeUnicode } from "view/components/base/StringUtils";
import { getExternalAuthenticationHeaders } from "../external/utils";

class LocationProviderApi {
  /*****************************************************************************
   * CARRIER COMPANY DETAILS
   ****************************************************************************/

  /*
   * Gets the list of location providers that match parameters
   */
  getLocationProviders = async (
    showPopular: boolean,
    query: string,
    externalAccessToken?: string
  ) => {
    const parsedQuery =
      query != null && query !== ""
        ? `&q=${encodeURIComponent(query)}`
        : "&q=''";
    const url = `${serviceUrls.selfOnboarding}/location-providers?popular=${showPopular}${parsedQuery}`;

    let response = await get(
      url,
      {},
      // Optional token for external access
      getExternalAuthenticationHeaders(externalAccessToken)
    );

    return response;
  };

  /*
   * Adds a new location provider
   */
  addLocationProvider = async (
    carrierId: string,
    locationProvider: any,
    externalAccessToken?: string
  ) => {
    const url = `${serviceUrls.selfOnboarding}/location-providers`;

    let response = await post(
      url,
      {
        carrier_permalink: carrierId,
        ...locationProvider,
      },
      // Optional token for external access
      getExternalAuthenticationHeaders(externalAccessToken)
    );
    return response;
  };
}

const client = new LocationProviderApi();
Object.freeze(client);
export default client;
