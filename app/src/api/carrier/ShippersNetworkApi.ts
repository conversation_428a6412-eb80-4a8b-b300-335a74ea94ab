import { serviceUrls } from "api/http/apiUtils";
import { get, remove, post, qs } from "api/http/service";
import { LoadsTrackingMode } from "state/BaseTypes";

class ShippersNetworkApi {
  /*****************************************************************************
   * CARRIER SHIPPERS NETWORK
   ****************************************************************************/

  /*
   * Fetches the list of shippers added to this carrier's network
   */
  getShippers = async (
    carrierId: string,
    mode: LoadsTrackingMode,
    networkStatus: string = "all",
    filterBy: string,
    sortBy: string,
    pagination: string
  ): Promise<any> => {
    let qsParameters = qs({
      mode: mode,
      network_status: networkStatus,
      filter_by: filterBy,
      sort_by: sortBy,
      pagination: pagination,
    });

    const url = `${serviceUrls.selfOnboarding}/networks/${carrierId}/shippers${qsParameters}`;

    const response = await get(url);
    return response?.data;
  };

  /*
   * Disconnects a shipper from a carrier network
   */
  disconnectShipper = async (
    carrierId: string,
    shiperId: string,
    mode: LoadsTrackingMode
  ): Promise<any> => {
    const url = `${serviceUrls.selfOnboarding}/networks/${carrierId}/shippers/${shiperId}?mode=${mode}`;

    const response = await remove(url);
    return response.data;
  };
}

const client = new ShippersNetworkApi();
Object.freeze(client);
export default client;
