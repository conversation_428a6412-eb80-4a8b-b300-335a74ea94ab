import { serviceUrls } from "api/http/apiUtils";
import { get, patch, post, remove } from "api/http/service";
import { LoadsTrackingMode } from "state/BaseTypes";
import { base64EncodeUnicode } from "view/components/base/StringUtils";

class ShippersDetailsApi {
  /*****************************************************************************
   * CARRIER SHIPPERS NETWORK
   ****************************************************************************/

  /*
   * Fetches the list of shippers in the network of this carrier
   */
  retrieveShipperDetails = async (
    carrierId: string,
    shipperId: string,
    mode: LoadsTrackingMode
  ) => {
    const url = `${serviceUrls.selfOnboarding}/networks/${carrierId}/shippers/${shipperId}?mode=${mode}`;

    let response = await get(url);
    return response;
  };

  /*****************************************************************************
   * CUSTOMER CODES
   ****************************************************************************/

  /*
   * Creates a customer code
   */
  createCustomerCode = async (
    shiperId: string,
    carrierId: string,
    mode: LoadsTrackingMode,
    code: string
  ) => {
    const url = `${serviceUrls.selfOnboarding}/networks/${carrierId}/shippers/${shiperId}/customer-codes`;

    let response = await post(url, { mode, code });
    return response;
  };

   /*****************************************************************************
     * EXPORT
     ****************************************************************************/
  
    /*
     * Get the network details as an exportable list
     */
    createExportableList = async (carrierId: string) => {
      const url = `${serviceUrls.selfOnboarding}/networks/${carrierId}/shippers/exportable-lists`;
      let response = await post(url, {});
      return response;
    };

  /*
   * Deletes a customer code
   */
  deleteCustomerCode = async (
    shiperId: string,
    carrierId: string,
    mode: LoadsTrackingMode,
    code: string
  ) => {
    const encodedCode = base64EncodeUnicode(code);
    const url = `${serviceUrls.selfOnboarding}/networks/${carrierId}/shippers/${shiperId}/customer-codes/${encodedCode}?mode=${mode}`;

    let response = await remove(url);
    return response;
  };

  /*****************************************************************************
   * CARRIER SHIPPERS INVITATIONS
   ****************************************************************************/

  /*
   * Accepts invitation made by shipper via normal network invitaiton flow
   */
  acceptShipperInvitation = async (
    mode: LoadsTrackingMode,
    carrierId: string,
    networkId: string,
    accepted: boolean
  ) => {
    const url = `${serviceUrls.selfOnboarding}/networks/${carrierId}/shippers/${networkId}/invitations`;

    let response = await patch(url, { mode, connection_accepted: accepted });
    return response;
  };

  /*
   * Connects to a shipper using it's network token
   */
  connectToShipperViaToken = async (
    mode: LoadsTrackingMode,
    carrierId: string,
    inviterToken: string
  ) => {
    const url = `${serviceUrls.selfOnboarding}/networks/${carrierId}/shippers-inviter-tokens/${inviterToken}/invitations`;

    let response = await patch(url, { mode, connection_accepted: true });
    return response;
  };

  /*
   * Get general details about the export with regards to the shipper's carrier
   */
  getExportInfo = async (carrierId: string) => {
    const url = `${serviceUrls.selfOnboarding}/networks/${carrierId}/carriers/exportable-lists/info`;
    let response = await get(url);
    return response;
  };
}

const client = new ShippersDetailsApi();
Object.freeze(client);
export default client;
