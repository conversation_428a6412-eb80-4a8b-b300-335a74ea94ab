import { serviceUrls } from "api/http/apiUtils";
import { post, get } from "../http/service";

class VoiceOnboardingApi {
  /*
   * Initiates a voice call for carrier onboarding
   */
  initiateVoiceCall = async (phoneNumber: string, onboardingToken: string, language?: string) => {
    const url = `${serviceUrls.selfOnboarding}/voice/carrier-onboarding`;

    const requestBody: any = {
      phone_number: phoneNumber,
      onboarding_token: onboardingToken
    };

    // Add language if provided
    if (language) {
      requestBody.language = language;
    }

    const response = await post(url, requestBody);

    return response;
  };

  /*
   * Initiates a voice call for ELD onboarding
   */
  initiateEldOnboardingCall = async (phoneNumber: string, carrierPermalink: string, language?: string) => {
    const url = `${serviceUrls.selfOnboarding}/voice/eld-onboarding`;

    const requestBody: any = {
      phone_number: phoneNumber,
      carrier_permalink: carrierPermalink
    };

    // Add language if provided
    if (language) {
      requestBody.language = language;
    }

    const response = await post(url, requestBody);

    return response;
  };

  /*
   * Gets the status of a voice call
   */
  getCallStatus = async (callSid: string) => {
    const url = `${serviceUrls.selfOnboarding}/voice/call-status/${callSid}`;

    const response = await get(url);
    return response;
  };
}

const client = new VoiceOnboardingApi();
Object.freeze(client);
export default client;