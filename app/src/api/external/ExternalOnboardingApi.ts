import { serviceUrls } from "api/http/apiUtils";
import { get, post, qs } from "../http/service";
import { CarrierContact } from "state/BaseTypes";
import { getExternalAuthenticationHeaders } from "./utils";

class ExternalOnboardingApi {
  /*****************************************************************************
   * EXTERNAL ONBOARDING
   ****************************************************************************/

  /*
   * Gets carrier details by looking it up by USDOT
   */
  getCarrierDetails = async (
    externalAccessToken: string,
    usdot?: string,
    mcNumber?: string
  ) => {
    const url = `${serviceUrls.selfOnboarding}/external/carriers${qs({
      usdot: usdot,
      mcnumber: mcNumber,
    })}`;

    const response = await get(
      url,
      {},
      // Headers for external access
      getExternalAuthenticationHeaders(externalAccessToken)
    );

    return response;
  };

  /*
   * Gets the user details by looking up it by email
   */
  getUserDetails = async (
    externalAccessToken: string,
    carrierId: string,
    userEmail: string
  ) => {
    const url = `${serviceUrls.selfOnboarding}/external/carriers/${carrierId}/users/${userEmail}`;

    const response = await get(
      url,
      {},
      // Headers for external access
      getExternalAuthenticationHeaders(externalAccessToken)
    );

    return response;
  };

  /*
   * Creates a new carrier company account, but using m2m token as form of authentication
   */
  createCarrier = async (externalAccessToken: string, carrier: any) => {
    const url = `${serviceUrls.selfOnboarding}/external/carriers`;

    const response = await post(
      url,
      {
        ...carrier,
      },
      // Headers for external access
      getExternalAuthenticationHeaders(externalAccessToken)
    );

    return response;
  };

  /*
   * Creates a new user related to this carrier, but using m2m token as form of authentication
   */
  createCarrierUser = async (
    externalAccessToken: string,
    carrierId: string,
    user: CarrierContact,
    role: string,
    password: string,
    passwordConfirmation: string
  ) => {
    const url = `${serviceUrls.selfOnboarding}/external/carriers/${carrierId}/users`;

    const response = await post(
      url,
      {
        first_name: user.firstName,
        last_name: user.lastName,
        email: user.email,
        role,
        password,
        password_confirmation: passwordConfirmation,
      },
      // Headers for external access
      getExternalAuthenticationHeaders(externalAccessToken)
    );

    return response;
  };
}

const client = new ExternalOnboardingApi();
Object.freeze(client);
export default client;
