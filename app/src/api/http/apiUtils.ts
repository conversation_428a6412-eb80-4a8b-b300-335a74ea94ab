declare global {
  interface Window {
    appConfig: any;
  }
}

let appEnvironment = window?.appConfig?.app_environment
  ? window?.appConfig?.app_environment
  : "dev";
appEnvironment = appEnvironment !== "prod" ? `-${appEnvironment}` : "";

// NOTE: we have inconsistencies with environments, so we are adding this line
// to facilitate running locally
const isRunninLocally = false;
if (isRunninLocally) {
  appEnvironment = "-dev";
}
/*******************************************************************************
 * SERVICE  URLS
 ******************************************************************************/

  appEnvironment = "-dev";


export const COMPANY_API_DOMAIN = `company-api${appEnvironment}.fourkites.com`;
export const KITE_BOT_URL = window?.appConfig?.app_environment === "prod" ? `https://front-end-clients.fourkites.com/components/kitebot`: `https://front-end-clients.fourkites.com/components/kitebot/${appEnvironment.substring(1)}`;

export const serviceUrls = {
  assets: `https://assets-service-api${appEnvironment}.fourkites.com/api/v1`,
  companies: `https://${COMPANY_API_DOMAIN}/api/v1`,
  selfOnboarding: `http://localhost:8080/api/v1`,
  users: `https://user-api${appEnvironment || "-2"}.fourkites.com/api/v1`,
  tracking: `https://tracking-api${appEnvironment}.fourkites.com/api/v1`,
  loadData: `https://load-data-api${appEnvironment}.fourkites.com`,
  dispatcherlogs: `https://dispatcher-api-logs-service${appEnvironment}.fourkites.com/api/v1`,
  // TODO: add variable on Azure
  carrierData: `https://carrier-data-api${
    appEnvironment || "-prod"
  }.fourkites.com/api/v1`,
  companyContacts: `https://service${appEnvironment ? "-dev": ""}.ng${appEnvironment ? "-np": ""}.fourkites.com`
};

/*******************************************************************************
 * APPS URLS
 ******************************************************************************/

// Direct assignment
export const DIRECT_ASSIGNMENT_APP_URL = `https://direct-assignment-app${
  appEnvironment ? appEnvironment : "-prod"
}.fourkites.com/load-assignment`;

/*******************************************************************************
 * FOURKITES URLS
 ******************************************************************************/

export const FOURKITES_URL = `https://www.fourkites.com/`;
export const FOURKITES_APP_URL = `${window.location.origin}/`;
export const FOURKITES_SUPPORT_URL = "https://support-fourkites.force.com/";
const DYP_APP_URL = getDypDomainURL();

const NIC_PRIVATE_FLEET_APP_URL = getNICDomainURL(),
  NIC_RAIL_FLEET_APP_URL = getNICDomainURL();

export const fourkitesUrls = {
  // APP LINKS
  login: `${FOURKITES_APP_URL}#/signin/`,
  appointmentsManager: `${FOURKITES_APP_URL}oas/home/<USER>
  developerPortal: `${FOURKITES_APP_URL}developerportal/introduction/`,
  developerPortalDoc: `https://docs.fourkites.com/api-reference`,
  dynamicYard: `${FOURKITES_APP_URL}dynamic-yard/`,
  dynamicYardPlus: `${DYP_APP_URL}`,
  privateFleetTracking: `${NIC_PRIVATE_FLEET_APP_URL}`,
  railFleet: `${NIC_RAIL_FLEET_APP_URL}`,
  visibility: `${FOURKITES_APP_URL}beta/loads/`,
  connectivity: `${FOURKITES_APP_URL}self-service/`,
  myWorkspace:`${FOURKITES_APP_URL}myworkspace/home`,
  dashboard: `${FOURKITES_APP_URL}dashboard/`,
  digitalWorkforce: `${FOURKITES_APP_URL}core/agents/`,
  //home: `${FOURKITES_APP_URL}dashboard/`,
  //insights: `${FOURKITES_APP_URL}insights/foundations/`,
  //laneConnect: `${FOURKITES_APP_URL}lane-connect/`,
  //networkVisibility: `${FOURKITES_APP_URL}network-visibility/`,
  //tendering: `${FOURKITES_APP_URL}tendering/tenders`,
  // CARRIER SOLUTIONS
  carrierOfferings: `${FOURKITES_URL}network/carriers/`,
  // EXTERNAL LINKS
  addressManager: `${FOURKITES_APP_URL}tools/address-manager/`,

  // Support
  companySettings: `${FOURKITES_APP_URL}#/settings`,
  notifications: `${FOURKITES_APP_URL}dashboard/`,
  community: `${FOURKITES_SUPPORT_URL}community/login?so=00D37000000KTMQ`,
  help: `${FOURKITES_SUPPORT_URL}publicKB/s/`,
  privacyPolicy: `${FOURKITES_URL}legal/privacy-policy/`,
  helpCenter: `${FOURKITES_SUPPORT_URL}hc/s/article/FourKites-Connectivity-Beta`,
  carrierHelpCenter: `${FOURKITES_SUPPORT_URL}hc/s/`,
  eldHelpCenter: `${FOURKITES_SUPPORT_URL}hc/s/article/FourKites-Connect-Tips-for-GPS-Troubleshooting`,
  contactSupport: `${FOURKITES_SUPPORT_URL}hc/s/contactsupport`,
  termsAndConditions: `${FOURKITES_URL}legal/terms-of-use-carrier-access/`,
  webhooksKnowledgeBase: `${FOURKITES_SUPPORT_URL}hc/s/article/FourKites-Connect-Callbacks-Webhooks`,
  standardCallbacksKnowledgeBase: `${FOURKITES_SUPPORT_URL}hc/s/article/FourKites-APIs-Standard-Callbacks`,
  businessKnowledgeBase: `${FOURKITES_URL}network/carriers/`,
  sharedPartnersOverview: `${FOURKITES_SUPPORT_URL}hc/s/article/Network-Visibility-Overview`,
  webhooksLogsKnowledgeBase: `${FOURKITES_SUPPORT_URL}hc/s/article/FourKites-Connect-Logs-Errors`,
  shareTrackingDataKnowledgeBase: `${FOURKITES_SUPPORT_URL}hc/s/article/Self-service-How-to-Share-Tracking-Data-with-Customers`,
};

export const externalUrls = {
  calendlyUrl:
    "https://calendly.com/network-growth-tier-2/gps-connection-support",
};

function getDypDomainURL() {
  const origin = window.location.origin
  let subdomain: any = origin.match(
    /[a-zA-Z.-\w_]+(?=\.fourkites)|[a-zA-Z.-]+(?=:\d+)/
  )
  subdomain = subdomain && subdomain.length ? subdomain[0] : "" // kcc-na-staging || kcc-na || localhost || app || app-staging || ''
  let ENV = subdomain.match(/-([^-]dev|staging|qat)$/) // -dev || -qat || -staging || null(production or localhost)
  let host = !ENV ? subdomain.match(/[a-zA-Z.-\w_]+/) : subdomain.split(ENV[0])
  host = host && host.length ? host[0] : "" // localhost || app || kcc-na || kcc_na || || kcc.na || fk_qa_sso-testing-env
  return origin.replace(host, "dy")
}

function getNICDomainURL() {
  /**
   * FourKites to Nic-Place Mapping
   * app-dev.fourkites.com --> fourkites.int.nic-place.com     : FK Dev to Nic INT
   * app-qat.fourkites.com --> fourkites-qat.int.nic-place.com : FK Qat to Nic INT
   * app.fourkites.com     --> fourkites.nic-place.com         : FK Prod to Nic Prod
   */
  const origin = window.location.origin.replace("fourkites", "nic-place")
  let subdomain: any = origin.match(
    /[a-zA-Z.-\w_]+(?=\.nic-place)|[a-zA-Z.-]+(?=:\d+)/
  )
  subdomain = subdomain && subdomain.length ? subdomain[0] : "" //localhost || app || app-staging || app-qat
  let env = subdomain.match(/-(dev|staging|qat)$/) // -dev || -qat || -staging || null(production or localhost)
  var newSubdomain =
    env && env.length && env[0] === "-qat" ? "fourkites-qat" : "fourkites"
  newSubdomain = env && env.length ? newSubdomain + ".int" : newSubdomain
  return origin.replace(subdomain, newSubdomain)
}
