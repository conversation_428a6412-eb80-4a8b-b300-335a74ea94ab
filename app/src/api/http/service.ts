import axios from "axios";
import Cookies from "universal-cookie";

const validStatuses = [200, 201, 202, 203, 204, 300, 301, 302, 303, 304];

const cookies = new Cookies();

const DEFAULT_HEADERS = {
  "Content-Type": "application/json",
};

/*
 * Returns default headers list which will be used with every request.
 * If it is not logged in, removes the cookies and sends user to login
 */
function getHeaders() {
  const authTokenCookie = "auth-token";
  const userIdCookie = "user-id";
  const deviceIdCookie = "device-id";

  const authToken = cookies.get(authTokenCookie);
  const userId = cookies.get(userIdCookie);
  const deviceId = cookies.get(deviceIdCookie);

  let defaultHeaders = DEFAULT_HEADERS;

  const isAuthenticated =
    authToken != null && userId != null && deviceId != null;

  if (isAuthenticated) {
    return {
      Authorization: `Bearer ${authToken}`,
      "X-FourKitesUserId": userId,
      "X-FourKitesDeviceId": deviceId,
      ...defaultHeaders,
    };
  }
}

/*
 * Returns default headers list which will be used with every request.
 * If it is not logged in, send user to login
 */
export function checkResponse(response: Response) {
  // If valid code, returns response
  if (validStatuses.includes(response.status)) {
    return response;
  } else {
    let err = new Error(response.statusText);
    return Promise.reject(err);
  }
}

/*
 * Creates query string parameters based on object
 */
export function qs(params: any) {
  let qs = Object.keys(params)
    .map((k) => {
      if (params[k] === null || params[k] === undefined) {
        return null;
      } else if (Array.isArray(params[k])) {
        let filters = [];
        for (const filter of params[k]) {
          filters.push(
            `${encodeURIComponent(k)}=${encodeURIComponent(filter)}`
          );
        }
        return filters.join("&");
      }

      return encodeURIComponent(k) + "=" + encodeURIComponent(params[k]);
    })
    .filter((qsParam: any) => qsParam != null)
    .join("&");

  if (qs.split("?").join("").split("&").join("").length === 0) {
    return "";
  }

  return "?" + qs;
}

/*******************************************************************************
 * Axios wrappers
 *******************************************************************************/

/*
 * Wraps axios and provides more convenient get method calls with data.
 */
export async function get(
  uri: string,
  data: Object = {},
  additionalHeaders?: any,
  // withCredentials is default optional attribute. (used for user service apis)
  withCredentials: boolean = false
) {
  let newUri = uri;
  if (Object.keys(data).length > 0) {
    if (uri.includes("?")) {
      newUri = `${uri}&${qs(data)}`;
    } else {
      newUri = `${uri}?${qs(data)}`;
    }
  }

  return axios.get(newUri,  {
    withCredentials: withCredentials,
    headers: { ...getHeaders(), ...additionalHeaders },
  });
}

/*
 * Wraps axios and provides more convenient post method calls with data
 */
export async function post(uri: string, data: Object, additionalHeaders?: any, withCredentials: boolean = false) {
  // withCredentials is default optional attribute. (used for user service apis)
  return axios.post(uri, data, {
    withCredentials: withCredentials,
    headers: { ...getHeaders(), ...additionalHeaders },
  });
}

/*
 * Wraps axios and provides more convenient put method calls with data
 */
export async function put(uri: string, data: Object, additionalHeaders?: any, withCredentials: boolean = false) {
  // withCredentials is default optional attribute. (used for user service apis)
  return axios.put(uri, data, {
    withCredentials: withCredentials,
    headers: { ...getHeaders(), ...additionalHeaders },
  });
}

/*
 * Wraps axios and provides more convenient put method calls with data
 */
export async function patch(
  uri: string,
  data: Object,
  additionalHeaders?: any,
  withCredentials: boolean = false
) {
  // withCredentials is default optional attribute. (used for user service apis)
  return axios.patch(uri, data, {
    withCredentials: withCredentials,
    headers: { ...getHeaders(), ...additionalHeaders },
  });
}

/*
 * Wraps axios and provides more convenient delete method
 */
export async function remove(
  uri: string,
  data?: Object,
  additionalHeaders?: any,
  withCredentials: boolean = false
) {
  // withCredentials is default optional attribute. (used for user service apis)
  return axios.delete(uri, {
    withCredentials: withCredentials,
    headers: { ...getHeaders(), ...additionalHeaders },
    data: data,
  });
}

// Means endpoint
/*export function endpoint(uri: string) {
  return BASE_URL + uri;
}*/
