import { serviceUrls } from "api/http/apiUtils";
import { get, post, qs } from "../http/service";
import { LoadsTrackingMode } from "state/BaseTypes";

class BatchListsLoadsAdditionsApi {
  /*
   * Retrieves the batch uploas loads additions integrations for this shipper
   */
  retrieveBatchLists = async (
    shipperId: string,
    pageIndex: number,
    pageSize: number,
    modes?: LoadsTrackingMode[]
  ) => {
    let qsParameters = qs({
      mode: modes,
      pagination: JSON.stringify({
        current_page: pageIndex,
        page_size: pageSize,
      }),
    });

    const url = `${serviceUrls.selfOnboarding}/shippers/${shipperId}/loads-additions/batch-lists${qsParameters}`;

    let response = await get(url, {});
    return response;
  };

  /*
   * Get template of batch list loads additions
   */
  createExportableBatchList = async (
    shipperId: string,
    batchListId: string | number
  ) => {
    const url = `${serviceUrls.selfOnboarding}/shippers/${shipperId}/loads-additions/batch-lists/${batchListId}/exportable-lists`;

    let response = await post(url, {});
    return response;
  };

  /*
   * Creates a new email loads additions integration for this shipper
   */
  createBatchList = async (
    shipperId: string,
    mode: LoadsTrackingMode | null,
    userId: string,
    fileFormat: string,
    template: string,
    batchList: File
  ) => {
    const url = `${serviceUrls.selfOnboarding}/shippers/${shipperId}/loads-additions/batch-lists`;

    let data = new FormData();
    data.append("file", batchList);
    data.append("file_format", fileFormat);
    data.append("user_id", userId);
    data.append("template", template);

    if (mode) {
      data.append("mode", mode);
    }

    let response = await post(url, data, {
      // @ts-ignore
      "Content-Type": `multipart/form-data; boundary=${data?._boundary}`,
    });

    return response;
  };
}

const client = new BatchListsLoadsAdditionsApi();
Object.freeze(client);
export default client;
