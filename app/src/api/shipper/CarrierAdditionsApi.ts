import { serviceUrls } from "api/http/apiUtils";
import { get, post } from "../http/service";
import { LoadsTrackingMode } from "state/BaseTypes";

class CarrierAdditionsApi {
  // CARRIER ADDITIONS

  /*
   * Add carriers to this shipper's network
   */
  addCarriersToNetwork = async (
    shiperId: string,
    mode: LoadsTrackingMode,
    carriers: string[],
    user: any,
    source: string,
    tpl: string
  ) => {
    const url = `${serviceUrls.selfOnboarding}/networks/${shiperId}/carriers`;

    let response = await post(url, { mode, carriers, user, source, tpl});
    return response;
  };

  /*
   * Get status of carrier additions
   */
  getCarrierAdditionsResults = async (
    shiperId: string,
    mode: LoadsTrackingMode,
    requestId: string
  ) => {
    const url = `${serviceUrls.selfOnboarding}/networks/${shiperId}/carriers/additions-progress?request_id=${requestId}&mode=${mode}`;

    let response = await get(url);
    return response;
  };

  // CARRIERS BULK LIST

  /*
   * Sends list of carriers
   */
  sendBulkList = async (
    shiperId: string,
    mode: LoadsTrackingMode,
    bulkList: File
  ) => {
    const url = `${serviceUrls.selfOnboarding}/networks/${shiperId}/carriers/bulk-list`;

    let data = new FormData();
    data.append("file", bulkList);
    data.append("mode", mode);

    let response = await post(url, data, {
      // @ts-ignore
      "Content-Type": `multipart/form-data; boundary=${data?._boundary}`,
    });

    return response;
  };

  /*
   * Get status of carrier additions
   */
  getBulkListResults = async (
    shiperId: string,
    mode: LoadsTrackingMode,
    requestId: string
  ) => {
    const url = `${serviceUrls.selfOnboarding}/networks/${shiperId}/carriers/bulk-list?request_id=${requestId}&mode=${mode}`;

    let response = await get(url);
    return response;
  };

  /*
   * Get status of carrier additions
   */
  getBulkListTemplate = async (mode: LoadsTrackingMode) => {
    const url = `${serviceUrls.selfOnboarding}/networks/carriers/bulk-template?mode=${mode}`;

    let response = await get(url);
    return response;
  };
}

const client = new CarrierAdditionsApi();
Object.freeze(client);
export default client;
