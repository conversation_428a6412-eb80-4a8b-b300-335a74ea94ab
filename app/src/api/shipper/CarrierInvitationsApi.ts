import { serviceUrls } from "api/http/apiUtils";
import { get, post, remove } from "../http/service";
import { LoadsTrackingMode } from "state/BaseTypes";

class CarrierInvitationsApi {
  /*
   * Invites carriers outside of 4K platform (batch invitations)
   */
  inviteCarriersToNetwork = async (
    mode: LoadsTrackingMode,
    shipperId: string,
    carriers: any[],
    user: any,
    source: string,
    tpl: string
  ) => {
    const url = `${serviceUrls.selfOnboarding}/networks/${shipperId}/carriers/invitations`;

    let response = await post(url, { mode, carriers, user, source, tpl});
    return response;
  };

  /*
   * Fetchs the invitations for this network carrier
   */
  getCarrierInvitations = async (
    mode: LoadsTrackingMode,
    shipperId: string,
    networkId: string
  ) => {
    const url = `${serviceUrls.selfOnboarding}/networks/${shipperId}/carriers/${networkId}/invitations?mode=${mode}`;

    let response = await get(url);
    return response;
  };

  /*
   * Reinvites carrier to join platform or to accept the connection
   */
  resendInvitation = async (
    mode: LoadsTrackingMode,
    shipperId: string,
    networkId: string
  ) => {
    const url = `${serviceUrls.selfOnboarding}/networks/${shipperId}/carriers/${networkId}/invitations`;

    let response = await post(url, { mode });
    return response;
  };

  /*
   * Deletes a given invitation to a carrier
   */
  deleteInvitation = async (shipperId: string, invitationId: string) => {
    const url = `${serviceUrls.selfOnboarding}/networks/${shipperId}/carriers/invitations/${invitationId}`;

    let response = await remove(url, {});
    return response;
  };

  /*
   * Fetches the inviter token of this shipper/broker
   */
  getInviterToken = async (shipperId: string) => {
    const url = `${serviceUrls.selfOnboarding}/shippers/${shipperId}/inviter-tokens`;

    let response = await get(url);
    return response;
  };

  /*
   * Resends all invitations to carriers (batch resubmissions)
   */
  resendAllInvitations = async (mode: LoadsTrackingMode, shipperId: string) => {
    const url = `${serviceUrls.selfOnboarding}/networks/${shipperId}/carriers/invitations/batch-resubmissions`;

    let response = await post(url, { mode });
    return response;
  };
}

const client = new CarrierInvitationsApi();
Object.freeze(client);
export default client;
