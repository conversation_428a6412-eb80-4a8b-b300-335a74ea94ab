import { serviceUrls } from "api/http/apiUtils";
import { post } from "../http/service";

class CarrierSurveyApi {
  /*
   * Uploads a carrier survey file
   */
  uploadCarrierSurvey = async (shipperId: string, file: File) => {
    const url = `${serviceUrls.selfOnboarding}/survey/${shipperId}/carrier-surveys/upload`;

    const formData = new FormData();
    formData.append('file', file);

    const response = await post(url, formData, {
      // @ts-ignore
      "Content-Type": `multipart/form-data; boundary=${formData?._boundary}`,
    });

    return response;
  };
}

export default new CarrierSurveyApi(); 