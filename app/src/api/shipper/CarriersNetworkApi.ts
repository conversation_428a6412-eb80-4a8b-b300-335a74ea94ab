import { serviceUrls } from "api/http/apiUtils";
import { get, patch, post, remove, qs } from "../http/service";
import { LoadsTrackingMode } from "state/BaseTypes";

// Add type for carrier onboarding data
interface CarrierOnboardingData {
  date: string;
  invited: number;
  surveyCompleted: number;
  surveyViaIvr: number;
  surveyViaForm: number;
  registrationCompleted: number;
  salesforceSuccess: number;
}

interface CarrierOnboardingApiResponse {
  Success: boolean;
  data: CarrierOnboardingData[];
  metadata?: {
    shipperPermalink: string;
    generatedAt: string;
    totalRecords: number;
  };
  error?: string;
}

class CarriersNetworkApi {
  /*
   * Fetches the list of carriers
   */
  getCarriers = async (
    shiperId: string,
    mode: LoadsTrackingMode,
    networkStatus: string = "all",
    filterBy: string,
    sortBy: string,
    pagination: string
  ): Promise<any> => {
    // Only FTL mode is paginated for list of carriers for now
    const allowPagination = mode === "ftl";
    let qsParameters = qs({
      mode: mode,
      network_status: networkStatus,
      filter_by: allowPagination ? filterBy : null,
      sort_by: allowPagination ? sortBy : null,
      pagination: allowPagination ? pagination : null,
    });
    const url = `${serviceUrls.selfOnboarding}/networks/${shiperId}/carriers${qsParameters}`;
    const response = await get(url);
    return response.data;
  };

  /*
   * Fetches carrier onboarding data for Excel download
   */
  getCarrierOnboardingData = async (shipperPermalink: string): Promise<CarrierOnboardingData[]> => {
    const url = `${serviceUrls.selfOnboarding}/carrier-onboarding/data/${shipperPermalink}`;
    const response = await post(url, {});
    
    const data: CarrierOnboardingApiResponse = response.data;
    
    if (!data.Success) {
      throw new Error(data.error || 'Failed to fetch carrier onboarding data');
    }
    return data.data;
  };

  /*
   * Connects a carrier to a shipper network, passing the carrier capabilities
   * and credentials
   */
  connectCarrier = async (
    shiperId: string,
    carrierId: string,
    capabilities: any,
    mode: LoadsTrackingMode
  ): Promise<any> => {
    const url = `${serviceUrls.selfOnboarding}/networks/${shiperId}/carriers/${carrierId}/capabilities`;

    const response = await post(url, { mode, ...capabilities });
    return response.data;
  };

  /*
   * Store Credentials   from carrier to a shipper network, passing the carrier capabilities
   * and credentials
  */  
  storeCredential = async (
    shiperId: string,
    carrierId: string,
    capabilities: any,
    mode: LoadsTrackingMode
  ): Promise<any> => {
    const url = `${serviceUrls.selfOnboarding}/data_collection/${shiperId}/carriers/${carrierId}/credentials`;

    const response = await post(url, { mode, ...capabilities });
    return response.data;
  };

  /*
   * Changes capabilities of a connected carrier
   */
  changeCarrierCapabilities = async (
    shiperId: string,
    carrierId: string,
    capabilities: any,
    mode: LoadsTrackingMode
  ): Promise<any> => {
    const url = `${serviceUrls.selfOnboarding}/networks/${shiperId}/carriers/${carrierId}/capabilities`;

    const response = await patch(url, { mode, ...capabilities });
    return response.data;
  };

  /*
   * Disconnects a carrier from a shipper network, passing the carrier
   * capabilities and credentials
   */
  disconnectCarrier = async (
    shiperId: string,
    carrierId: string,
    mode: LoadsTrackingMode
  ): Promise<any> => {
    const url = `${serviceUrls.selfOnboarding}/networks/${shiperId}/carriers/${carrierId}?mode=${mode}`;

    const response = await remove(url);
    return response.data;
  };
}

const client = new CarriersNetworkApi();
Object.freeze(client);
export default client;