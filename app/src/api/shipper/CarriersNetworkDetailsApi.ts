import { serviceUrls } from "api/http/apiUtils";
import { get, post, remove } from "../http/service";
import { LoadsTrackingMode, CarrierContact } from "state/BaseTypes";
import { base64EncodeUnicode } from "view/components/base/StringUtils";

class CarriersNetworkDetailsApi {
  /*****************************************************************************
   * CARRIER DETAILS
   ****************************************************************************/

  /*
   * Fetches the list of carriers
   */
  getCarrierDetails = async (
    shiperId: string,
    carrierId: string,
    mode: LoadsTrackingMode
  ) => {
    const url = `${serviceUrls.selfOnboarding}/networks/${shiperId}/carriers/${carrierId}?mode=${mode}`;

    const response = await get(url);
    return response;
  };

  /*****************************************************************************
   * CARRIER CODES
   ****************************************************************************/

  /*
   * Adds a carrier code
   */
  createCarrierCode = async (
    shiperId: string,
    carrierId: string,
    mode: LoadsTrackingMode,
    code: string
  ) => {
    const url = `${serviceUrls.selfOnboarding}/networks/${shiperId}/carriers/${carrierId}/carrier-codes`;

    let response = await post(url, { mode, code });
    return response;
  };

  /*
   * Removes a carrier code
   */
  deleteCarrierCode = async (
    shiperId: string,
    carrierId: string,
    mode: LoadsTrackingMode,
    code: string
  ) => {
    const encodedCode = base64EncodeUnicode(code);
    const url = `${serviceUrls.selfOnboarding}/networks/${shiperId}/carriers/${carrierId}/carrier-codes/${encodedCode}?mode=${mode}`;

    let response = await remove(url);
    return response;
  };

  /*****************************************************************************
   * CARRIER CONTACTS
   ****************************************************************************/

  /*
   * Add a new contact to this network carrier, which will be invited into the platform
   */
  addCarrierContact = async (
    shiperId: string,
    networkId: string,
    mode: LoadsTrackingMode,
    contact: CarrierContact,
    user: any
  ) => {
    const url = `${serviceUrls.selfOnboarding}/networks/${shiperId}/carriers/${networkId}/contacts`;

    let response = await post(url, {
      mode,
      user,
      contact: {
        first_name: contact.firstName,
        last_name: contact.lastName,
        email: contact.email,
        secondary_emails: contact.secondaryEmails,
        position: contact.position || null,
        phones: contact.phones || null,
      },
    });
    return response;
  };

  /*****************************************************************************
   * HELP REQUESTS
   ****************************************************************************/

  /*
   * Creates support ticket for the pending invite!
   */
  initiateHelpRequest = async (shiperId: string, networkId: string) => {
    const url = `${serviceUrls.selfOnboarding}/networks/${shiperId}/carriers/${networkId}/invitations/help-requests`;
    let response = await post(url, {});
    return response;
  };

  /*****************************************************************************
   * EXPORT
   ****************************************************************************/

  /*
   * Get the network details as an exportable list
   */
  createExportableList = async (shiperId: string) => {
    const url = `${serviceUrls.selfOnboarding}/networks/${shiperId}/carriers/exportable-lists`;
    let response = await post(url, {});
    return response;
  };

  /*
   * Get general details about the export with regards to the shipper's carrier
   */
  getExportInfo = async (shiperId: string) => {
    const url = `${serviceUrls.selfOnboarding}/networks/${shiperId}/carriers/exportable-lists/info`;
    let response = await get(url);
    return response;
  };
}

const client = new CarriersNetworkDetailsApi();
Object.freeze(client);
export default client;
