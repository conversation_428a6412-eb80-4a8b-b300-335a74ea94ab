import { serviceUrls } from "api/http/apiUtils";
import { get, post, patch, remove, qs } from "../http/service";
import { LoadsTrackingMode } from "state/BaseTypes";

class EmailLoadsAdditionsIntegrationsApi {
  /*****************************************************************************
   * EMAIL INTEGRATIONS
   ****************************************************************************/

  /*
   * Retrieves the email loads additions integrations for this shipper
   */
  retrieveIntegrations = async (
    shipperId: string,
    modes?: LoadsTrackingMode[]
  ) => {
    let qsParameters = qs({ mode: modes });
    const url = `${serviceUrls.selfOnboarding}/shippers/${shipperId}/loads-additions/integrations/email${qsParameters}`;

    let response = await get(url, {});
    return response;
  };

  /*
   * Retrieves the email loads additions integrations details
   */
  retrieveIntegrationDetails = async (
    shipperId: string,
    integrationId: string | number
  ) => {
    const url = `${serviceUrls.selfOnboarding}/shippers/${shipperId}/loads-additions/integrations/email/${integrationId}`;

    let response = await get(url, {});
    return response;
  };

  /*
   * Creates a new email loads additions integration for this shipper
   */
  createIntegration = async (
    shipperId: string,
    mode: LoadsTrackingMode | null,
    userId: string,
    integration: any
  ) => {
    const url = `${serviceUrls.selfOnboarding}/shippers/${shipperId}/loads-additions/integrations/email`;

    let response = await post(url, {
      mode: mode,
      user_id: userId,
      ...integration,
    });

    return response;
  };

  /*
   * Updates an existing email loads additions integration for this shipper
   */
  updateIntegration = async (
    shipperId: string,
    integrationId: string | number,
    mode: LoadsTrackingMode | null,
    userId: string,
    integration: any
  ) => {
    const url = `${serviceUrls.selfOnboarding}/shippers/${shipperId}/loads-additions/integrations/email/${integrationId}`;

    let response = await patch(url, {
      mode: mode,
      user_id: userId,
      ...integration,
    });
    return response;
  };

  /*
   * Deletes an email loads additions integration for this shipper
   */
  deleteIntegration = async (
    shipperId: string,
    integrationId: string | number
  ) => {
    const url = `${serviceUrls.selfOnboarding}/shippers/${shipperId}/loads-additions/integrations/email/${integrationId}`;

    let response = await remove(url, {});
    return response;
  };
}

const client = new EmailLoadsAdditionsIntegrationsApi();
Object.freeze(client);
export default client;
