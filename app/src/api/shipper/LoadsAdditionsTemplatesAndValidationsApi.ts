import { serviceUrls } from "api/http/apiUtils";
import { post, get, qs } from "../http/service";
import { LoadsTrackingMode } from "state/BaseTypes";

class LoadsAdditionsTemplatesAndValidationsApi {
  /*
   * Retrievs the email loads addition template
   */
  retrieveLoadsAdditionsTemplates = async (
    shipperId: string,
    mode?: LoadsTrackingMode,
    templateName?: string
  ) => {
    let qsParameters = qs({ mode: mode, name: templateName });

    const url = `${serviceUrls.selfOnboarding}/shippers/${shipperId}/loads-additions/templates${qsParameters}`;

    let response = await get(url, {});
    return response;
  };

  /*
   * Retrievs the email loads addition template
   */
  createLoadsAdditionsValidation = async (
    shipperId: string,
    file: File,
    fileFormat: string,
    mode?: LoadsTrackingMode
  ) => {
    const url = `${serviceUrls.selfOnboarding}/shippers/${shipperId}/loads-additions/file-validations`;

    let data = new FormData();
    data.append("file", file);

    if (mode) {
      data.append("mode", mode);
    }
    if (fileFormat) {
      data.append("file_format", fileFormat);
    }

    let response = await post(url, data, {
      // @ts-ignore
      "Content-Type": `multipart/form-data; boundary=${data?._boundary}`,
    });

    return response;
  };
}

const client = new LoadsAdditionsTemplatesAndValidationsApi();
Object.freeze(client);
export default client;
