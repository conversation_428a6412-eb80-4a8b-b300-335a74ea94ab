import { serviceUrls } from "api/http/apiUtils";
import { get, post, remove, qs } from "../http/service";

import { MaintenanceWindow } from "view/modules/shipper/data-integrations/webhooks/maintenance/utils/MaintenanceUtils";

interface MaintenanceWindowsResponse {
  statusCode?: number;
  status?: number;
  schedules?: MaintenanceWindow[];
  data?: {
    schedules?: MaintenanceWindow[];
  } | MaintenanceWindow[];
}

interface CreateMaintenanceRequest {
  openTime: string;
  closeTime: string;
}

interface CreateMaintenanceResponse {
  status?: number;
  statusCode?: number;
  data?: any;
}

class MaintenanceWindowsApi {
  retrieveMaintenanceWindows = async (
    companyId: string,
    locale: string = "en"
  ): Promise<MaintenanceWindowsResponse> => {
    const qsParameters = qs({ 
      locale: locale,
      resource: "maintenance_windows" 
    });
    const url = `${serviceUrls.companies}/companies/${companyId}/maintenance_windows${qsParameters}`;
    const response = await get(url, {});
    return response.data || response;
  };

  createMaintenanceWindow = async (
    companyId: string,
    maintenanceData: CreateMaintenanceRequest,
    locale: string = "en"
  ): Promise<CreateMaintenanceResponse> => {
    const qsParameters = qs({ locale: locale });
    const url = `${serviceUrls.companies}/companies/${companyId}/maintenance_windows${qsParameters}`;
    const response = await post(url, {
      openTime: maintenanceData.openTime,
      closeTime: maintenanceData.closeTime,
    });
    return response.data || response;
  };

  deleteMaintenanceWindow = async (
    companyId: string,
    maintenanceId: string | number,
    locale: string = "en"
  ) => {
    const qsParameters = qs({ locale: locale });
    const url = `${serviceUrls.companies}/companies/${companyId}/maintenance_windows/${maintenanceId}${qsParameters}`;
    const response = await remove(url, {});
    return response.data || response;
  };
}

const client = new MaintenanceWindowsApi();
Object.freeze(client);
export default client;