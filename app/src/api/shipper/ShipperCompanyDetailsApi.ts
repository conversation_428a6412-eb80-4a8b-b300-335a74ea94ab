import { serviceUrls } from "api/http/apiUtils";
import { get, patch } from "api/http/service";

class ShipperCompanyDetailsApi {
  /*****************************************************************************
   * SHIPPER COMPANY DETAILS
   ****************************************************************************/

  /*
   * Gets the company details for this shipper
   */
  retrieveShipperCompanyDetails = async (shipperId: string) => {
    const url = `${serviceUrls.selfOnboarding}/shippers/${shipperId}`;

    let response = await get(url);
    return response;
  };

  /*
   * Gets the company details for this shipper
   */
  updateShipperCompanyDetails = async (
    shipperId: string,
    shipperCompanyDetails: any
  ) => {
    const url = `${serviceUrls.selfOnboarding}/shippers/${shipperId}`;

    let data = new FormData();

    // Adds all fields present as form data
    const patchableFields = [
      { id: "logo", shouldStringify: false },
      { id: "name", shouldStringify: false },
      { id: "address", shouldStringify: true },
    ];
    for (const p of patchableFields) {
      const fieldId = p.id;

      if (shipperCompanyDetails[fieldId]) {
        const field = p.shouldStringify
          ? JSON.stringify(shipperCompanyDetails[fieldId])
          : shipperCompanyDetails[fieldId];

        data.append(fieldId, field);
      }
    }

    let response = await patch(url, data, {
      // @ts-ignore
      "Content-Type": `multipart/form-data; boundary=${data?._boundary}`,
    });

    return response;
  };
}

const client = new ShipperCompanyDetailsApi();
Object.freeze(client);
export default client;
