import { serviceUrls } from "api/http/apiUtils";
import { get } from "api/http/service";
import { LoadsTrackingMode } from "state/BaseTypes";

class ShipperIndicatorsApi {
  /*
   * Fetches the shipper indicators
   */
  getShipperIndicators = async (shipperId: string, mode: LoadsTrackingMode) => {
    const url = `${serviceUrls.selfOnboarding}/shippers/${shipperId}/indicators?mode=${mode}`;

    let response = await get(url);
    return response;
  };
}

const client = new ShipperIndicatorsApi();
Object.freeze(client);
export default client;
