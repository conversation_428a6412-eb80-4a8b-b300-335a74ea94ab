import { serviceUrls } from "api/http/apiUtils";
import { get, post, remove } from "../http/service";
import { LoadsTrackingMode } from "state/BaseTypes";
import { base64EncodeUnicode } from "view/components/base/StringUtils";

class ShipperSettingsApi {
  /*****************************************************************************
   * LOAD REFERENCE NUMBER
   ****************************************************************************/
  /*
   * Add a load reference number format
   */
  addLoadNumberFormat = async (
    shiperId: string,
    mode: LoadsTrackingMode,
    value: string
  ) => {
    const url = `${serviceUrls.selfOnboarding}/shippers/${shiperId}/loads-additions/settings/load-number-formats`;

    let response = await post(url, { mode, value });
    return response;
  };

  /*
   * Remove a load reference number format
   */
  deleteLoadNumberFormat = async (
    shiperId: string,
    mode: LoadsTrackingMode,
    loadNumberFormat: string
  ) => {
    const encodedLoadNumberFormat = base64EncodeUnicode(loadNumberFormat);
    const url = `${serviceUrls.selfOnboarding}/shippers/${shiperId}/loads-additions/settings/load-number-formats/${encodedLoadNumberFormat}?mode=${mode}`;

    let response = await remove(url);
    return response;
  };

  /*
   * Retrieves the load number formats
   */
  retrieveLoadNumberFormats = async (shipperId: string) => {
    const url = `${serviceUrls.selfOnboarding}/shippers/${shipperId}/loads-additions/settings/load-number-formats`;

    let response = await get(url, {});
    return response;
  };
}

const client = new ShipperSettingsApi();
Object.freeze(client);
export default client;
