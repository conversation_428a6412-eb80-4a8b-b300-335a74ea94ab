import { serviceUrls } from "api/http/apiUtils";
import { get, post, patch, remove, qs } from "../http/service";
import { LoadsTrackingMode } from "state/BaseTypes";

class WebhookConfigurationsApi {
  /*****************************************************************************
   * WEBHOOK CONFIGURATIONS
   ****************************************************************************/

  /*
   * Retrieves the webhook configurations for this shipper
   */
  retrieveWebhooks = async (shipperId: string, mode?: LoadsTrackingMode[]) => {
    let qsParameters = qs({ mode: mode });
    const url = `${serviceUrls.selfOnboarding}/shippers/${shipperId}/webhooks${qsParameters}`;
    const response = await get(url, {});
    return response;
  };

  /*
   * Retrieves the webhook configuration details
   */
  retrieveWebhookDetails = async (
    shipperId: string,
    webhookId: string | number
  ) => {
    const url = `${serviceUrls.selfOnboarding}/shippers/${shipperId}/webhooks/${webhookId}`;
    const response = await get(url, {});
    return response;
  };

  /*
   * Creates a new webhook configuration for this shipper
   */
  createWebhook = async (
    shipperId: string,
    modes: LoadsTrackingMode | null,
    webhook: any
  ) => {
    const url = `${serviceUrls.selfOnboarding}/shippers/${shipperId}/webhooks`;
    const response = await post(url, {
      modes: modes,
      ...webhook,
    });
    return response;
  };

  /*
   * Updates an existing webhook configuration for this shipper
   */
  updateWebhook = async (
    shipperId: string,
    webhookId: string | number,
    modes: LoadsTrackingMode | null,
    webhook: any
  ) => {
    const url = `${serviceUrls.selfOnboarding}/shippers/${shipperId}/webhooks/${webhookId}`;
    const response = await patch(url, {
      modes: modes,
      ...webhook,
    });
    return response;
  };

  /*
   * Delete an existing webhook configuration for this shipper
   */
  deleteWebhook = async (shipperId: string, webhookId: string | number) => {
    const url = `${serviceUrls.selfOnboarding}/shippers/${shipperId}/webhooks/${webhookId}`;
    const response = await remove(url, {});
    return response;
  };
}

const client = new WebhookConfigurationsApi();
Object.freeze(client);
export default client;
