import { serviceUrls } from "api/http/apiUtils";
import { get, post, qs, remove } from "../http/service";

class WebhooksDataApi {
  /*****************************************************************************
   * CALLBACK SETTINGS
   ****************************************************************************/
  /*
   * Fetches the list of message types
   */
  retrieveCallbackEvents = async (shipperId: string): Promise<any> => {
    const url = `${serviceUrls.loadData}/api/v1/callbacks/get_subscribed_event_types?companyId=${shipperId}`;

    const response = await get(url);
    return response.data;
  };

  /*
   * Fetches the list of webhooks
   */
  retrieveWebhooksData = async (
    eventTypes: string,
    statusType: string,
    shipperId: string,
    searchValue: string,
    // TODO: fix type
    pagination: any,
    processedFrom: string,
    processedTo: string
  ): Promise<any> => {
    const url = `${serviceUrls.loadData}/api/v1/callbacks/get_callbacks`;

    const response = await post(url, {
      companyId: shipperId,
      status: statusType,
      searchIdentifier: searchValue,
      eventTypes: eventTypes,
      processedFrom,
      processedTo,
      pagination,
    });

    return response.data;
  };
}

const client = new WebhooksDataApi();
Object.freeze(client);
export default client;
