import * as Config from "./config";

export const createPendoScript = (visitor?: { id: string }) => () => {
  const element = document.createElement("script")

  element.src = `//${Config.pendo.url}`
  element.id = Config.pendo.id
  element.onload = () => {
    const init = () => {
      const potentiallyHasPendo = global as any;
      if ("pendo" in potentiallyHasPendo) {
        const globalPendo = potentiallyHasPendo.pendo as any;
        globalPendo.initialize({
          ...(visitor
            ? {
                visitor: { id: visitor.id },
              }
            : null),
        });
      } else {
        setTimeout(init, Config.pendo.flushIntervalMs);
      }
    };

    // Initialize pendo
    init();
  }

  document.body.appendChild(element)
}
