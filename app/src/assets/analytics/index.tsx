import * as Config from "./config"

import { ManagedInterval } from "./managed-interval"
import { createPendoScript } from "./create-pendo-script"

interface AnalyticsTracker {
  track: (name: string, payload: any) => any
}

let queue = [] as any[]

const pendoIsPresent = (
  success: (pendoInstance: AnalyticsTracker) => void,
  failure: () => void = () => null
) => {
  const potentiallyHasPendo = global as any

  if ("pendo" in potentiallyHasPendo) {
    const pendo = potentiallyHasPendo.pendo as AnalyticsTracker
    success(pendo)
  } else {
    failure()
  }
}

const shouldLoadPendo = (loader: () => void) => {
  const doNothing = () => {}
  const pendoScriptExists =
    document.querySelectorAll(`script#${Config.pendo.id}`).length > 0

  pendoIsPresent(doNothing, () => {
    if (!pendoScriptExists) {
      loader()
    }
  })
}

const publishAndEmptyEvents = (pendo: AnalyticsTracker) => {
  const events = [...queue]

  queue = []

  for (const event of events) {
    pendo.track(event.name, event.payload)
  }
}

// Pendo loads here
export const Analytics = (visitor?: { id: string }) => {
  const enqueue = (name: string, payload: any) => queue.push({ name, payload })
  const flushQueue = () => pendoIsPresent(publishAndEmptyEvents)

  const interval = ManagedInterval(flushQueue)

  const start = () => {
    interval.start()
    shouldLoadPendo(createPendoScript(visitor))
  }

  const stop = () => {
    flushQueue()

    interval.stop()
  }

  const track = (name: string, payload: any) => enqueue(name, payload)

  return { start, stop, track, state: { queue } }
}

Analytics()
