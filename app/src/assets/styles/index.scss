@import url("https://cdnjs.cloudflare.com/ajax/libs/react-datepicker/4.7.0/react-datepicker.min.css");
@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "~@fourkites/elemental-atoms/build/scss/elemental-atoms";

body {
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: "lato", sans-serif;
  height: 100vh;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}

// Buttons and icons

.button-content {
  display: flex;
  align-items: center;
}

.button-icon-left {
  margin-right: 16px;
}

.button-icon-right {
  margin-left: 16px;
}

// TODO: this needs to be fixed on elemental design
.elemental-Menu-container {
  background-color: white;
  z-index: 1000000 !important;
}

._pendo-badge {
  z-index: 100 !important;
  height: 32px !important;
  padding: 0 16px !important;
  display: flex !important;
  align-items: center !important;
  font-size: 14px !important;
  font-weight: bold !important;
  border-radius: 4px !important;
  cursor: pointer !important;
  margin-right: 8px !important;
  background-color: #ffbf0a !important;
  color: #0e65e5 !important;
  margin: 8px 0px 0px 12px !important;
}

// TODO: this needs to be removed after consuming latest Tooltip
.elemental-tooltip {
  white-space: pre-wrap;
}
