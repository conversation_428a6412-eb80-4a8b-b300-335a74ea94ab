<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:o="urn:schemas-microsoft-com:office:office"
  xmlns:v="urn:schemas-microsoft-com:vml"
>
  <head>
    <!--[if gte mso 9
      ]><xml
        ><o:OfficeDocumentSettings
          ><o:AllowPNG /><o:PixelsPerInch
            >96</o:PixelsPerInch
          ></o:OfficeDocumentSettings
        ></xml
      ><!
    [endif]-->
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
    <meta content="width=device-width" name="viewport" />
    <!--[if !mso]><!-->
    <meta content="IE=edge" http-equiv="X-UA-Compatible" />
    <!--<![endif]-->
    <title></title>
    <!--[if !mso]><!-->
    <!--<![endif]-->
    <style type="text/css">
      body {
        margin: 0;
        padding: 0;
        background-color: #f1f3f5;
        font-family: "Trebuchet MS", "Lucida Grande", "Lucida Sans Unicode",
          "Lucida Sans", "Tahoma", sans-serif;
      }

      table,
      td,
      tr {
        vertical-align: top;
        border-collapse: collapse;
        font-family: "Trebuchet MS", "Lucida Grande", "Lucida Sans Unicode",
          "Lucida Sans", "Tahoma", sans-serif;
      }

      * {
        line-height: inherit;
        font-family: "Trebuchet MS", "Lucida Grande", "Lucida Sans Unicode",
          "Lucida Sans", "Tahoma", sans-serif;
      }

      a[x-apple-data-detectors="true"] {
        color: inherit !important;
        text-decoration: none !important;
        font-family: "Trebuchet MS", "Lucida Grande", "Lucida Sans Unicode",
          "Lucida Sans", "Tahoma", sans-serif;
      }
    </style>
    <style id="media-query" type="text/css">
      @media (max-width: 520px) {
        .block-grid,
        .col {
          min-width: 320px !important;
          max-width: 100% !important;
          display: block !important;
        }

        .block-grid {
          width: 100% !important;
        }

        .col {
          width: 100% !important;
        }

        .col_cont {
          margin: 0 auto;
        }

        img.fullwidth,
        img.fullwidthOnMobile {
          width: 100% !important;
        }

        .no-stack .col {
          min-width: 0 !important;
          display: table-cell !important;
        }

        .no-stack.two-up .col {
          width: 50% !important;
        }

        .no-stack .col.num2 {
          width: 16.6% !important;
        }

        .no-stack .col.num3 {
          width: 25% !important;
        }

        .no-stack .col.num4 {
          width: 33% !important;
        }

        .no-stack .col.num5 {
          width: 41.6% !important;
        }

        .no-stack .col.num6 {
          width: 50% !important;
        }

        .no-stack .col.num7 {
          width: 58.3% !important;
        }

        .no-stack .col.num8 {
          width: 66.6% !important;
        }

        .no-stack .col.num9 {
          width: 75% !important;
        }

        .no-stack .col.num10 {
          width: 83.3% !important;
        }

        .video-block {
          max-width: none !important;
        }

        .mobile_hide {
          min-height: 0px;
          max-height: 0px;
          max-width: 0px;
          display: none;
          overflow: hidden;
          font-size: 0px;
        }

        .desktop_hide {
          display: block !important;
          max-height: none !important;
        }

        .img-container.big img {
          width: auto !important;
        }
      }
    </style>
    <style id="icon-media-query" type="text/css">
      @media (max-width: 520px) {
        .icons-inner {
          text-align: center;
        }

        .icons-inner td {
          margin: 0 auto;
        }
      }
    </style>
  </head>

  <body
    class="clean-body"
    style="margin: 0; padding: 0; -webkit-text-size-adjust: 100%"
  >
    <!--[if IE]><div class="ie-browser"><![endif]-->
    <table
      cellpadding="0"
      cellspacing="0"
      class="nl-container"
      role="presentation"
      style="
        background-color: #f1f3f5;
        margin: auto;
        width: 640px;
        table-layout: fixed;
        vertical-align: top;
        min-width: 320px;
        border-spacing: 0;
        border-collapse: collapse;
        mso-table-lspace: 0pt;
        mso-table-rspace: 0pt;
      "
      valign="top"
      width="100%"
    >
      <tbody>
        <tr style="vertical-align: top" valign="top">
          <td style="word-break: break-word; vertical-align: top" valign="top">
            <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td align="center" style="background-color:#FFFFFF"><![endif]-->

            <!-- HEADER -->
            <div style="background-color: transparent; margin-top: 40px">
              <div
                class="block-grid"
                style="
                  width: 640px;
                  overflow-wrap: break-word;
                  word-wrap: break-word;
                  word-break: break-word;
                  margin: 0 auto;
                  background-color: transparent;
                "
              >
                <div
                  style="
                    border-collapse: collapse;
                    display: table;
                    width: 100%;
                    background-color: transparent;
                  "
                >
                  <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:500px"><tr class="layout-full-width" style="background-color:transparent"><![endif]-->
                  <!--[if (mso)|(IE)]><td align="center" width="500" style="background-color:transparent;width:500px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
                  <div
                    class="col num12"
                    style="
                      min-width: 320px;
                      max-width: 500px;
                      display: table-cell;
                      vertical-align: top;
                      width: 500px;
                    "
                  >
                    <div class="col_cont" style="width: 100% !important">
                      <!--[if (!mso)&(!IE)]><!-->
                      <div
                        style="
                          border-top: 0px solid transparent;
                          border-left: 0px solid transparent;
                          border-bottom: 0px solid transparent;
                          border-right: 0px solid transparent;
                          padding-top: 5px;
                          padding-bottom: 5px;
                          padding-right: 0px;
                          padding-left: 0px;
                        "
                      >
                        <!--<![endif]-->
                        <table
                          cellpadding="0"
                          cellspacing="0"
                          role="presentation"
                          style="
                            table-layout: fixed;
                            vertical-align: top;
                            border-spacing: 0;
                            border-collapse: collapse;
                            mso-table-lspace: 0pt;
                            mso-table-rspace: 0pt;
                          "
                          valign="top"
                          width="100%"
                        >
                          <tr style="vertical-align: top" valign="top">
                            <td
                              align="center"
                              style="
                                word-break: break-word;
                                vertical-align: top;
                                padding-top: 5px;
                                padding-right: 0px;
                                padding-bottom: 5px;
                                padding-left: 0px;
                                text-align: center;
                              "
                              valign="top"
                            >
                              <!--[if vml]><table align="left" cellpadding="0" cellspacing="0" role="presentation" style="display:inline-block;padding-left:0px;padding-right:0px;mso-table-lspace: 0pt;mso-table-rspace: 0pt;"><![endif]-->
                              <!--[if !vml]><!-->
                              <table
                                cellpadding="0"
                                cellspacing="0"
                                class="icons-inner"
                                role="presentation"
                                style="
                                  table-layout: fixed;
                                  vertical-align: top;
                                  border-spacing: 0;
                                  border-collapse: collapse;
                                  mso-table-lspace: 0pt;
                                  mso-table-rspace: 0pt;
                                  display: inline-block;
                                  margin-right: -4px;
                                  padding-left: 0px;
                                  padding-right: 0px;
                                "
                                valign="top"
                              >
                                <!--<![endif]-->
                                <tr style="vertical-align: top" valign="top">
                                  <td
                                    align="center"
                                    style="
                                      word-break: break-word;
                                      vertical-align: top;
                                      text-align: center;
                                      padding-top: 5px;
                                      padding-bottom: 5px;
                                      padding-left: 5px;
                                      padding-right: 6px;
                                    "
                                    valign="top"
                                  >
                                    <a href="https://www.fourkites.com/"
                                      ><img
                                        align="center"
                                        alt="FourKites"
                                        class="icon"
                                        height="48"
                                        src="https://assets.fourkites.com/fk-logo/coloured_fourkites_logo_light_bg.png"
                                        style="border: 0"
                                        width="null"
                                    /></a>
                                  </td>
                                </tr>
                              </table>
                            </td>
                          </tr>
                        </table>

                        <!--[if (!mso)&(!IE)]><!-->
                      </div>
                      <!--<![endif]-->
                    </div>
                  </div>
                  <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
                  <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
                </div>
              </div>
            </div>

            <!-- CONTENT -->
            <div
              style="
                box-sizing: border-box;
                width: 640px;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                background-color: #ffffff;
                margin-top: 24px;
                padding: 24px 20px;
              "
            >
              <div
                class="block-grid"
                style="
                  width: 600px;
                  overflow-wrap: break-word;
                  word-wrap: break-word;
                  word-break: break-word;
                  margin: 0 auto;
                  background-color: transparent;
                "
              >
                <div
                  style="
                    border-collapse: collapse;
                    display: table;
                    width: 100%;
                    background-color: transparent;
                  "
                >
                  <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:500px"><tr class="layout-full-width" style="background-color:transparent"><![endif]-->
                  <!--[if (mso)|(IE)]><td align="center" width="500" style="background-color:transparent;width:500px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
                  <div
                    class="col num12"
                    style="
                      min-width: 320px;
                      max-width: 500px;
                      display: table-cell;
                      vertical-align: top;
                      width: 500px;
                    "
                  >
                    <div class="col_cont" style="width: 100% !important">
                      <!--[if (!mso)&(!IE)]><!-->
                      <div
                        style="
                          border-top: 0px solid transparent;
                          border-left: 0px solid transparent;
                          border-bottom: 0px solid transparent;
                          border-right: 0px solid transparent;
                          padding-top: 5px;
                          padding-bottom: 5px;
                          padding-right: 0px;
                          padding-left: 0px;
                        "
                      >
                        <!--<![endif]-->
                        <table
                          cellpadding="0"
                          cellspacing="0"
                          role="presentation"
                          style="
                            table-layout: fixed;
                            vertical-align: top;
                            border-spacing: 0;
                            border-collapse: collapse;
                            mso-table-lspace: 0pt;
                            mso-table-rspace: 0pt;
                          "
                          valign="top"
                          width="100%"
                        >
                          <tr style="vertical-align: top" valign="top">
                            <td
                              align="center"
                              style="
                                word-break: break-word;
                                vertical-align: top;
                                padding-top: 5px;
                                padding-right: 0px;
                                padding-bottom: 5px;
                                padding-left: 0px;
                                text-align: center;
                              "
                              valign="top"
                            >
                              <!--[if vml]><table align="left" cellpadding="0" cellspacing="0" role="presentation" style="display:inline-block;padding-left:0px;padding-right:0px;mso-table-lspace: 0pt;mso-table-rspace: 0pt;"><![endif]-->
                              <!--[if !vml]><!-->
                              <table
                                cellpadding="0"
                                cellspacing="0"
                                class="icons-inner"
                                role="presentation"
                                style="
                                  table-layout: fixed;
                                  vertical-align: top;
                                  border-spacing: 0;
                                  border-collapse: collapse;
                                  mso-table-lspace: 0pt;
                                  mso-table-rspace: 0pt;
                                  display: inline-block;
                                "
                                valign="top"
                              >
                                <!--<![endif]-->       

                          <tr style="vertical-align: top" valign="top">
                            <td
                              align="center"
                              style="
                                word-break: break-word;
                                vertical-align: top;
                                text-align: center;
                                padding-top: 32px;
                              "
                              valign="top"
                            >
                              <!--[if vml]><table align="left" cellpadding="0" cellspacing="0" role="presentation" style="display:inline-block;padding-left:0px;padding-right:0px;mso-table-lspace: 0pt;mso-table-rspace: 0pt;"><![endif]-->
                              <!--[if !vml]><!-->
                              <table
                                cellpadding="0"
                                cellspacing="0"
                                role="presentation"
                                style="
                                  table-layout: fixed;
                                  vertical-align: top;
                                  border-spacing: 0;
                                  border-collapse: collapse;
                                  mso-table-lspace: 0pt;
                                  mso-table-rspace: 0pt;
                                  display: inline-block;
                                "
                                valign="top"
                              >
                                <!--<![endif]-->
                                <tr style="vertical-align: middle" valign="top">
                                  <td
                                    align="left"
                                    style="
                                      word-break: break-word;
                                      vertical-align: middle;
                                      text-align: left;
                                      padding-top: 5px;
                                      padding-bottom: 5px;
                                      padding-right: 6px;
                                      width: 600px;
                                    "
                                    valign="top"
                                  >
                                    <span
                                      style="color: #333842; font-size: 15px; letter-spacing: 0; line-height: 24px; "
                                    >
                                      Dear 
                                      <span data-slug="contactRepresentative"
                                        >contactRepresentative</span
                                      >,<br />
                                    </span>
                                  </td>
                                </tr>
                              </table>
                            </td>
                          </tr>


                          <tr style="vertical-align: top" valign="top">
                            <td
                              align="center"
                              style="
                                word-break: break-word;
                                vertical-align: top;
                                text-align: left;
                              "
                              valign="top"
                            >
                              <!--[if vml]><table align="left" cellpadding="0" cellspacing="0" role="presentation" style="display:inline-block;padding-left:0px;padding-right:0px;mso-table-lspace: 0pt;mso-table-rspace: 0pt;"><![endif]-->
                              <!--[if !vml]><!-->
                              <table
                                cellpadding="0"
                                cellspacing="0"
                                role="presentation"
                                style="
                                  table-layout: fixed;
                                  vertical-align: top;
                                  border-spacing: 0;
                                  border-collapse: collapse;
                                  mso-table-lspace: 0pt;
                                  mso-table-rspace: 0pt;
                                  display: inline-block;
                                "
                                valign="top"
                              >
                                <!--<![endif]-->
                                <tr style="vertical-align: top" valign="top">
                                  <td 
                                    align="center"
                                    style="
                                      word-break: break-word;
                                      vertical-align: top;
                                      text-align: left;
                                      width: 600px;
                                    "
                                    valign="top"
                                  >
                                    <!-- <span
                                      style="color: #333842; font-size: 14px; letter-spacing: 0; line-height: 21px; font-weight: bold;"
                                      >About FourKites
                                    </span>
                                    <br /><br /> -->
                                    <span
                                      style="
                                        color: #333842;
                                        font-size: 14px;
                                        letter-spacing: 0;
                                        line-height: 21px;
                                      "
                                    >
                                       <span
                                      style="
                                        color: #333842;
                                        font-size: 14px;
                                        letter-spacing: 0;
                                        line-height: 21px;
                                        font-weight: bold;
                                        font-style: italic;
                                      "
                                      data-slug="requestingUserEmail"
                                    >requestingUserEmail </span
                                      >
                                      is trying to add <span data-slug="companyName">companyName</span> to your FourKites network.
                                    </span>
                                  </td>
                                </tr>
                              </table>
                            </td>
                          </tr>

                          <tr style="vertical-align: top;" valign="top">
                            <td
                              align="center"
                              style="word-break: break-word; vertical-align: top; text-align: left; padding-top: 24px;"
                              valign="top"
                            >
                              <!--[if vml]><table align="left" cellpadding="0" cellspacing="0" role="presentation" style="display:inline-block;padding-left:0px;padding-right:0px;mso-table-lspace: 0pt;mso-table-rspace: 0pt;"><![endif]-->
                              <!--[if !vml]><!-->
                              <table
                                cellpadding="0"
                                cellspacing="0"
                                role="presentation"
                                style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; display: inline-block;"
                                valign="top"
                              >
                                <!--<![endif]-->
                                <tr style="vertical-align: top;" valign="top">
                                  <td id="inviteCodeUrl"
                                    align="center"
                                    style="word-break: break-all; vertical-align: top; text-align: left; width: 600px; border-radius: 4px; padding: 16px;"
                                    valign="top"
                                  >
                                    <span
                                      style="color: #333842; font-size: 14px; font-weight: bold; letter-spacing: 0; line-height: 30px;"
                                      >You can add <span data-slug="companyName">companyName</span> to your network by:
                                    </span>
                                    <br /><br />
                                    <span id="inviteCodeUrl"
                                      style="color: #333842; font-size: 14px; letter-spacing: 0; line-height: 29px;"
                                    >
                                      1. Log into app.fourkites.com  
                                      <br />2. Go to Connectivity under Admin tab<br />
                                    3. Go to Customers tab<br />
                                    4. Click on “Connect via Invite Link”<br />
                                    5. Copy and paste the following Invite Link into the field:<br />
                                        <span style="color: #333842; font-size: 14px; font-style: italic; letter-spacing: 0; line-height: 30px;" 
                                        data-slug="inviteCode">inviteCode</span><br />
                                    6. Hit Connect<br />
                                    </span>
                                  </td>
                                </tr>
                              </table>
                            </td>
                          </tr>

                          <tr style="vertical-align: top;" valign="top">
                            <td
                              align="center"
                              style="word-break: break-word; vertical-align: top; text-align: left;"
                              valign="top"
                            >
                              <!--[if vml]><table align="left" cellpadding="0" cellspacing="0" role="presentation" style="display:inline-block;padding-left:0px;padding-right:0px;mso-table-lspace: 0pt;mso-table-rspace: 0pt;"><![endif]-->
                              <!--[if !vml]><!-->
                              <table
                                cellpadding="0"
                                cellspacing="0"
                                role="presentation"
                                style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; display: inline-block;"
                                valign="top"
                              >
                                <!--<![endif]-->
                                <tr style="vertical-align: top;" valign="top">
                                  <td
                                    align="center"
                                    style="word-break: break-word; vertical-align: top; text-align: left; width: 600px; border-radius: 4px; padding: 16px;"
                                    valign="top"
                                  >
                                    <a
                                        href="https://support-fourkites.force.com/hc/s/article/115007893148-How-to-Add-Manage-Users-Basic"
                                        target="_blank"
                                        style="color: #0565FF; font-size: 14px; letter-spacing: 0; line-height: 21px; text-decoration: none;"
                                        >Click here</a
                                    >
                                    to know how you can add 
                                    <span
                                      style="
                                        color: #333842;
                                        font-size: 14px;
                                        letter-spacing: 0;
                                        line-height: 21px;
                                        font-weight: bold;
                                        font-style: italic;
                                      "
                                       data-slug="requestingUserEmail"
                                    >requestingUserEmail </span
                                      > to FourKites.<br /><br />
                                  </td>
                                </tr>
                              </table>
                            </td>
                          </tr>


                          <tr style="vertical-align: top;" valign="top">
                            <td
                              align="center"
                              style="word-break: break-word; vertical-align: top; text-align: left;"
                              valign="top"
                            >
                              <!--[if vml]><table align="left" cellpadding="0" cellspacing="0" role="presentation" style="display:inline-block;padding-left:0px;padding-right:0px;mso-table-lspace: 0pt;mso-table-rspace: 0pt;"><![endif]-->
                              <!--[if !vml]><!-->
                              <table
                                cellpadding="0"
                                cellspacing="0"
                                role="presentation"
                                style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; display: inline-block;"
                                valign="top"
                              >
                                <!--<![endif]-->
                                <tr style="vertical-align: top;" valign="top">
                                  <td
                                    align="center"
                                    style="word-break: break-word; vertical-align: top; text-align: left; width: 600px; "
                                    valign="top"
                                  >
                                    <span
                                      style="color: #333842; font-size: 14px; letter-spacing: 0; line-height: 21px; font-weight: bold;"
                                      >Questions?
                                    </span>
                                    <br /><br />
                                    <span
                                      style="color: #333842; font-size: 14px; letter-spacing: 0; line-height: 21px;"
                                    >
                                      Learn more on how to get started here.
                                      We are happy to help. Please write
                                      to&nbsp;<a
                                        href="mailto: <EMAIL>"
                                        style="color: #0565FF; font-size: 14px; letter-spacing: 0; line-height: 21px; text-decoration: underline"
                                        ><EMAIL></a
                                      >.
                                    </span>
                                  </td>
                                </tr>
                              </table>
                            </td>
                          </tr>


                          <tr style="vertical-align: top" valign="top">
                            <td
                              align="center"
                              style="
                                word-break: break-word;
                                vertical-align: top;
                                text-align: left;
                                padding-top: 32px;
                              "
                              valign="top"
                            >
                              <!--[if vml]><table align="left" cellpadding="0" cellspacing="0" role="presentation" style="display:inline-block;padding-left:0px;padding-right:0px;mso-table-lspace: 0pt;mso-table-rspace: 0pt;"><![endif]-->
                              <!--[if !vml]><!-->
                              <table
                                cellpadding="0"
                                cellspacing="0"
                                role="presentation"
                                style="
                                  table-layout: fixed;
                                  vertical-align: top;
                                  border-spacing: 0;
                                  border-collapse: collapse;
                                  mso-table-lspace: 0pt;
                                  mso-table-rspace: 0pt;
                                  display: inline-block;
                                "
                                valign="top"
                              >
                                <!--<![endif]-->
                                <tr style="vertical-align: top" valign="top">
                                  <td
                                    align="center"
                                    style="
                                      height: 1px;
                                      width: 600px;
                                      background-color: #e9ecef;
                                    "
                                    valign="top"
                                  ></td>
                                </tr>
                              </table>
                            </td>
                          </tr>

                          <tr style="vertical-align: top" valign="top">
                            <td
                              align="center"
                              style="
                                word-break: break-word;
                                vertical-align: top;
                                text-align: left;
                              "
                              valign="top"
                            >
                              <!--[if vml]><table align="left" cellpadding="0" cellspacing="0" role="presentation" style="display:inline-block;padding-left:0px;padding-right:0px;mso-table-lspace: 0pt;mso-table-rspace: 0pt;"><![endif]-->
                              <!--[if !vml]><!-->
                              <table
                                cellpadding="0"
                                cellspacing="0"
                                role="presentation"
                                style="
                                  table-layout: fixed;
                                  vertical-align: top;
                                  border-spacing: 0;
                                  border-collapse: collapse;
                                  mso-table-lspace: 0pt;
                                  mso-table-rspace: 0pt;
                                  display: inline-block;
                                "
                                valign="top"
                              >
                                <!--<![endif]-->
                                <tr style="vertical-align: top" valign="top">
                                  <td
                                    align="center"
                                    style="
                                      word-break: break-word;
                                      vertical-align: top;
                                      text-align: left;
                                      width: 600px;
                                    "
                                    valign="top"
                                  >
                                    <span
                                      style="
                                        color: #333842;
                                        font-size: 16px;
                                        letter-spacing: 0;
                                        line-height: 24px;
                                      "
                                      >Thanks!
                                    </span>
                                  </td>
                                </tr>
                              </table>
                            </td>
                          </tr>
                        </table>

                        <!--[if (!mso)&(!IE)]><!-->
                      </div>
                      <!--<![endif]-->
                    </div>
                  </div>
                  <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
                  <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
                </div>
              </div>
            </div>

            <!-- FOOTER -->
            <div style="background-color: transparent; margin-top: 32px">
              <div
                class="block-grid"
                style="
                  width: 640px;
                  overflow-wrap: break-word;
                  word-wrap: break-word;
                  word-break: break-word;
                  margin: 0 auto;
                  background-color: transparent;
                "
              >
                <div
                  style="
                    border-collapse: collapse;
                    display: table;
                    width: 100%;
                    background-color: transparent;
                  "
                >
                  <!--[if (mso)|(IE)]><table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color:transparent;"><tr><td align="center"><table cellpadding="0" cellspacing="0" border="0" style="width:500px"><tr class="layout-full-width" style="background-color:transparent"><![endif]-->
                  <!--[if (mso)|(IE)]><td align="center" width="500" style="background-color:transparent;width:500px; border-top: 0px solid transparent; border-left: 0px solid transparent; border-bottom: 0px solid transparent; border-right: 0px solid transparent;" valign="top"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td style="padding-right: 0px; padding-left: 0px; padding-top:5px; padding-bottom:5px;"><![endif]-->
                  <div
                    class="col num12"
                    style="
                      width: 640px;
                      display: table-cell;
                      vertical-align: top;
                    "
                  >
                    <div class="col_cont" style="width: 100% !important">
                      <!--[if (!mso)&(!IE)]><!-->
                      <div
                        style="
                          border-top: 0px solid transparent;
                          border-left: 0px solid transparent;
                          border-bottom: 0px solid transparent;
                          border-right: 0px solid transparent;
                          padding-top: 5px;
                          padding-bottom: 5px;
                          padding-right: 0px;
                          padding-left: 0px;
                        "
                      >
                        <!--<![endif]-->
                        <table
                          cellpadding="0"
                          cellspacing="0"
                          role="presentation"
                          style="
                            table-layout: fixed;
                            vertical-align: top;
                            border-spacing: 0;
                            border-collapse: collapse;
                            mso-table-lspace: 0pt;
                            mso-table-rspace: 0pt;
                          "
                          valign="top"
                          width="100%"
                        >
                          <tr style="vertical-align: top;" valign="top">
                            <td
                              align="center"
                              style="word-break: break-word; vertical-align: top; padding-top: 5px; padding-right: 0px; padding-bottom: 5px; padding-left: 0px; text-align: center;"
                              valign="top"
                            >
                              <!--[if vml]><table align="left" cellpadding="0" cellspacing="0" role="presentation" style="display:inline-block;padding-left:0px;padding-right:0px;mso-table-lspace: 0pt;mso-table-rspace: 0pt;"><![endif]-->
                              <!--[if !vml]><!-->
                              <table
                                cellpadding="0"
                                cellspacing="0"
                                class="icons-inner"
                                role="presentation"
                                style="table-layout: fixed; vertical-align: top; border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt; display: inline-block; margin-right: -4px; padding-left: 0px; padding-right: 0px;"
                                valign="top"
                              >
                                <!--<![endif]-->
                                <tr style="vertical-align: top;" valign="top">
                                  <td
                                    align="center"
                                    style="word-break: break-word; vertical-align: top; text-align: center; padding-top: 5px; padding-bottom: 5px; padding-left: 30px; padding-right: 30px;"
                                    valign="top"
                                  >
                                    <span
                                      style="color: #B3BBC5;  font-size: 12px; letter-spacing: 0; line-height: 16px; text-align: center; font-style: italic;"
                                    >
                                      You are receiving this email because you are nominated as the carrier contact on FourKites, which 
                                      <span data-slug="companyName">companyName</span> uses for real-time supply chain visibility
                                    </span>
                                  </td>
                                </tr>
                              </table>
                            </td>
                          </tr>
                          <tr style="vertical-align: top" valign="top">
                            <td
                              align="center"
                              style="
                                word-break: break-word;
                                vertical-align: top;
                                padding-top: 5px;
                                padding-right: 0px;
                                padding-bottom: 5px;
                                padding-left: 0px;
                                text-align: center;
                              "
                              valign="top"
                            >
                              <!--[if vml]><table align="left" cellpadding="0" cellspacing="0" role="presentation" style="display:inline-block;padding-left:0px;padding-right:0px;mso-table-lspace: 0pt;mso-table-rspace: 0pt;"><![endif]-->
                              <!--[if !vml]><!-->
                              <table
                                cellpadding="0"
                                cellspacing="0"
                                class="icons-inner"
                                role="presentation"
                                style="
                                  table-layout: fixed;
                                  vertical-align: top;
                                  border-spacing: 0;
                                  border-collapse: collapse;
                                  mso-table-lspace: 0pt;
                                  mso-table-rspace: 0pt;
                                  display: inline-block;
                                  margin-right: -4px;
                                  padding-left: 0px;
                                  padding-right: 0px;
                                "
                                valign="top"
                              >
                                <!--<![endif]-->
                                <tr style="vertical-align: top" valign="top">
                                  <td
                                    align="center"
                                    style="
                                      word-break: break-word;
                                      vertical-align: top;
                                      text-align: center;
                                      padding-top: 5px;
                                      padding-bottom: 5px;
                                      padding-left: 5px;
                                      padding-right: 6px;
                                    "
                                    valign="top"
                                  >
                                    <a
                                      target="_blank"
                                      href="https://www.facebook.com/FourKites"
                                      ><img
                                        align="center"
                                        alt="Facebook"
                                        class="icon"
                                        height="28"
                                        src="https://assets.fourkites.com/social-media/facebook.png"
                                        style="border: 0"
                                        width="null"
                                    /></a>
                                  </td>
                                  <td
                                    align="center"
                                    valign="top"
                                    style="
                                      word-break: break-word;
                                      vertical-align: top;
                                      text-align: center;
                                      padding-top: 5px;
                                      padding-bottom: 5px;
                                      padding-left: 30px;
                                      padding-right: 30px;
                                    "
                                  >
                                    <a
                                      target="_blank"
                                      href="https://twitter.com/FourKites"
                                      ><img
                                        align="center"
                                        alt="Twitter"
                                        class="icon"
                                        height="28"
                                        src="https://assets.fourkites.com/social-media/twitter.png"
                                        style="border: 0"
                                        width="null"
                                    /></a>
                                  </td>
                                  <td
                                    align="center"
                                    style="
                                      word-break: break-word;
                                      vertical-align: top;
                                      text-align: center;
                                      padding-top: 5px;
                                      padding-bottom: 5px;
                                      padding-left: 5px;
                                      padding-right: 6px;
                                    "
                                    valign="top"
                                  >
                                    <a
                                      target="_blank"
                                      href="https://www.linkedin.com/company/fourkites-inc/"
                                      ><img
                                        align="center"
                                        alt="Facebook"
                                        class="icon"
                                        height="28"
                                        src="https://assets.fourkites.com/social-media/linkedin.png"
                                        style="border: 0"
                                        width="null"
                                    /></a>
                                  </td>
                                </tr>
                              </table>
                            </td>
                          </tr>

                          <tr style="vertical-align: top" valign="top">
                            <td
                              align="center"
                              style="
                                word-break: break-word;
                                vertical-align: top;
                                padding-top: 32px;
                                padding-right: 0px;
                                padding-bottom: 5px;
                                padding-left: 0px;
                                text-align: center;
                              "
                              valign="top"
                            >
                              <!--[if vml]><table align="left" cellpadding="0" cellspacing="0" role="presentation" style="display:inline-block;padding-left:0px;padding-right:0px;mso-table-lspace: 0pt;mso-table-rspace: 0pt;"><![endif]-->
                              <!--[if !vml]><!-->
                              <table
                                cellpadding="0"
                                cellspacing="0"
                                class="icons-inner"
                                role="presentation"
                                style="
                                  table-layout: fixed;
                                  vertical-align: top;
                                  border-spacing: 0;
                                  border-collapse: collapse;
                                  mso-table-lspace: 0pt;
                                  mso-table-rspace: 0pt;
                                  display: inline-block;
                                  margin-right: -4px;
                                  padding-left: 0px;
                                  padding-right: 0px;
                                "
                                valign="top"
                              >
                                <!--<![endif]-->
                                <tr style="vertical-align: top" valign="top">
                                  <td
                                    align="center"
                                    style="
                                      word-break: break-word;
                                      vertical-align: top;
                                      text-align: center;
                                      padding-top: 5px;
                                      padding-bottom: 5px;
                                      padding-left: 5px;
                                      padding-right: 6px;
                                    "
                                    valign="top"
                                  >
                                    <a
                                      target="_blank"
                                      href="https://www.fourkites.com/contact/"
                                      style="
                                        text-decoration: none;
                                        color: #707883;
                                        font-size: 14px;
                                        letter-spacing: 0;
                                        line-height: 16px;
                                        text-align: center;
                                      "
                                      >Contact Us&nbsp;&nbsp;&nbsp;▪</a
                                    >
                                    <a
                                      target="_blank"
                                      href="https://support-fourkites.force.com/publicKB/s/"
                                      style="
                                        text-decoration: none;
                                        color: #707883;
                                        font-size: 14px;
                                        letter-spacing: 0;
                                        line-height: 16px;
                                        text-align: center;
                                      "
                                      >&nbsp;&nbsp;&nbsp;Help</a
                                    >
                                  </td>
                                </tr>
                              </table>
                            </td>
                          </tr>

                          <tr style="vertical-align: top" valign="top">
                            <td
                              align="center"
                              style="
                                word-break: break-word;
                                vertical-align: top;
                                padding-top: 5px;
                                padding-right: 0px;
                                padding-bottom: 5px;
                                padding-left: 0px;
                                text-align: center;
                              "
                              valign="top"
                            >
                              <!--[if vml]><table align="left" cellpadding="0" cellspacing="0" role="presentation" style="display:inline-block;padding-left:0px;padding-right:0px;mso-table-lspace: 0pt;mso-table-rspace: 0pt;"><![endif]-->
                              <!--[if !vml]><!-->
                              <table
                                cellpadding="0"
                                cellspacing="0"
                                class="icons-inner"
                                role="presentation"
                                style="
                                  table-layout: fixed;
                                  vertical-align: top;
                                  border-spacing: 0;
                                  border-collapse: collapse;
                                  mso-table-lspace: 0pt;
                                  mso-table-rspace: 0pt;
                                  display: inline-block;
                                  margin-right: -4px;
                                  padding-left: 0px;
                                  padding-right: 0px;
                                "
                                valign="top"
                              >
                                <!--<![endif]-->
                                <tr style="vertical-align: top" valign="top">
                                  <td
                                    align="center"
                                    style="
                                      word-break: break-word;
                                      vertical-align: top;
                                      text-align: center;
                                      padding-top: 5px;
                                      padding-bottom: 5px;
                                      padding-left: 5px;
                                      padding-right: 6px;
                                    "
                                    valign="top"
                                  >
                                    <span
                                      style="
                                        color: #b3bbc5;
                                        font-size: 14px;
                                        letter-spacing: 0;
                                        line-height: 16px;
                                        text-align: center;
                                      "
                                    >
                                      Copyright © 2023 FourKites, All rights
                                      reserved.
                                    </span>
                                  </td>
                                </tr>
                              </table>
                            </td>
                          </tr>
                        </table>

                        <!--[if (!mso)&(!IE)]><!-->
                      </div>
                      <!--<![endif]-->
                    </div>
                  </div>
                  <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
                  <!--[if (mso)|(IE)]></td></tr></table></td></tr></table><![endif]-->
                </div>
              </div>
            </div>
            <!--[if (mso)|(IE)]></td></tr></table><![endif]-->
          </td>
        </tr>
      </tbody>
    </table>
    <!--[if (IE)]></div><![endif]-->
  </body>
</html>
