import i18n from "i18next";
import { initReactI18next } from "react-i18next";

import LanguageDetector from "i18next-browser-languagedetector";
import Backend from "i18next-http-backend";

/*
TODO: how to handle locales?
import moment from "moment";
import "moment/locale/fr"; // need to import the supported locales explicitly
import "moment/locale/it"; // The Italian
import "moment/locale/es"; // The Spanish
*/

i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    react: {
      useSuspense: false,
    },
    ns: ["common"],
    defaultNS: "common",
    fallbackNS: "common",
    fallbackLng: "en",
    debug: false,
    keySeparator: false,
    nsSeparator: false,
    returnNull: false,
    returnEmptyString: false,
    transSupportBasicHtmlNodes: true,
    transKeepBasicHtmlNodesFor: ["br", "p"],

    backend: {
      loadPath: "/locales/{{lng}}/{{ns}}.json",
    },

    detection: {
      order: [
        "querystring",
        "cookie",
        "localStorage",
        "navigator",
        "htmlTag",
        "path",
        "subdomain",
      ],

      // keys or params to lookup language from
      lookupQuerystring: "lng",
      lookupCookie: "i18next",
      lookupLocalStorage: "i18nextLng",
      lookupFromPathIndex: 0,
      lookupFromSubdomainIndex: 0,

      // cache user language on
      caches: ["localStorage", "cookie"],
      excludeCacheFor: ["cimode"], // languages to not persist (cookie, localStorage)

      // optional expire and domain for set cookie
      cookieMinutes: 10,
      cookieDomain: "fourkites",

      // optional htmlTag with lang attribute, the default is:
      htmlTag: document.documentElement,
    },

    interpolation: {
      escapeValue: false, // React should already be escaping strings
    },
  });

/*
i18n.on("languageChanged", function (lng) {
  moment.locale(lng);
});
*/

export default i18n;
