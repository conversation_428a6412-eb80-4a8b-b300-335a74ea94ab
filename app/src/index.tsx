import "react-app-polyfill/ie11";
import "react-app-polyfill/stable";

import React from "react";
import ReactDOM from "react-dom";
import { Provider } from "react-redux";
import { BrowserRouter as Router } from "react-router-dom";
import { pdfjs } from "react-pdf";

import "./i18n";

import store from "state/store";

import "assets/styles/index.scss";

import reportWebVitals from "./reportWebVitals";

import AppRouter from "router/AppRouter";
import { KitebotExternalResource } from "KitebotExternalResource";

// Configuring pdf options for all apps
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.js`;

ReactDOM.render(
  <React.StrictMode>
    <Provider store={store}>
      <KitebotExternalResource/>
      <Router>
        <AppRouter />
      </Router>
    </Provider>
  </React.StrictMode>,
  document.getElementById("root")
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
