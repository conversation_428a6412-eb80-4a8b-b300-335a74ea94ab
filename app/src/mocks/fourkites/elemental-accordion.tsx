import React, { ReactNode, useState } from 'react';

interface AccordionProps {
  title: string;
  defaultOpened?: boolean;
  children: ReactNode;
}

export const Accordion: React.FC<AccordionProps> = ({ 
  title, 
  defaultOpened = false, 
  children 
}) => {
  const [isOpen, setIsOpen] = useState(defaultOpened);

  const toggleAccordion = () => {
    setIsOpen(!isOpen);
  };

  const accordionStyle: React.CSSProperties = {
    border: '1px solid #e0e0e0',
    borderRadius: '4px',
    margin: '10px 0',
    overflow: 'hidden',
  };

  const headerStyle: React.CSSProperties = {
    backgroundColor: '#f5f5f5',
    padding: '10px 15px',
    cursor: 'pointer',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    fontWeight: 'bold',
  };

  const contentStyle: React.CSSProperties = {
    padding: isOpen ? '15px' : '0',
    maxHeight: isOpen ? '1000px' : '0',
    overflow: 'hidden',
    transition: 'all 0.3s ease',
  };

  return (
    <div style={accordionStyle}>
      <div style={headerStyle} onClick={toggleAccordion}>
        <span>{title}</span>
        <span>{isOpen ? '▲' : '▼'}</span>
      </div>
      <div style={contentStyle}>
        {isOpen && children}
      </div>
    </div>
  );
};

