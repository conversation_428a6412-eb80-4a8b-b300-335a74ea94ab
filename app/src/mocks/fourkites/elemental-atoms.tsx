import React from 'react';

interface IconProps {
  fill?: string;
  size?: string;
}

interface ClassIconProps {
  iconClass?: string;
}

export const InfoIcon: React.FC<IconProps> = ({
  fill = '#000000',
  size = '24px',
}) => {
  // Convert size to a number if it includes units
  const sizeValue = typeof size === 'string' ? 
    parseInt(size.replace(/[^0-9]/g, '')) : 
    size;
  
  return (
    <svg 
      width={size} 
      height={size} 
      viewBox="0 0 24 24" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
    >
      <path 
        d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20Z" 
        fill={fill} 
      />
      <path 
        d="M11 11H13V17H11V11ZM11 7H13V9H11V7Z" 
        fill={fill} 
      />
    </svg>
  );
};

export const AlertCircleIcon: React.FC<IconProps> = ({
  fill = '#000000',
  size = '24px',
}) => {
  return (
    <svg 
      width={size} 
      height={size} 
      viewBox="0 0 24 24" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
    >
      <path 
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20Z" 
        fill={fill} 
      />
      <path 
        d="M12 14C12.5523 14 13 13.5523 13 13C13 12.4477 12.5523 12 12 12C11.4477 12 11 12.4477 11 13C11 13.5523 11.4477 14 12 14Z"
        fill={fill} 
      />
      <path 
        d="M12 16C11.45 16 11 16.45 11 17C11 17.55 11.45 18 12 18C12.55 18 13 17.55 13 17C13 16.45 12.55 16 12 16ZM13 7H11V11H13V7Z"
        fill={fill} 
      />
    </svg>
  );
};

export const AlertTriangleIcon: React.FC<IconProps> = ({
  fill = '#000000',
  size = '24px',
}) => {
  return (
    <svg 
      width={size} 
      height={size} 
      viewBox="0 0 24 24" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
    >
      <path 
        d="M12 5.99L19.53 19H4.47L12 5.99ZM12 2L1 21H23L12 2Z"
        fill={fill} 
      />
      <path 
        d="M13 16H11V18H13V16Z"
        fill={fill} 
      />
      <path 
        d="M13 10H11V15H13V10Z"
        fill={fill} 
      />
    </svg>
  );
};

export const FourkitesIcon: React.FC<ClassIconProps> = ({
  iconClass
}) => {
  return (
    <svg 
      className={iconClass}
      width="24" 
      height="24" 
      viewBox="0 0 24 24" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
    >
      <path 
        d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20Z" 
        fill="#0077cc" 
      />
      <path 
        d="M12 6L9 12H12L11 18L15 12H12L13 6Z" 
        fill="#0077cc" 
      />
    </svg>
  );
};

export const SortIcon: React.FC<ClassIconProps> = ({
  iconClass
}) => {
  return (
    <svg 
      className={iconClass}
      width="24" 
      height="24" 
      viewBox="0 0 24 24" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
    >
      <path 
        d="M4 18H8C8.55 18 9 17.55 9 17C9 16.45 8.55 16 8 16H4C3.45 16 3 16.45 3 17C3 17.55 3.45 18 4 18ZM3 7C3 7.55 3.45 8 4 8H20C20.55 8 21 7.55 21 7C21 6.45 20.55 6 20 6H4C3.45 6 3 6.45 3 7ZM4 13H14C14.55 13 15 12.55 15 12C15 11.45 14.55 11 14 11H4C3.45 11 3 11.45 3 12C3 12.55 3.45 13 4 13Z" 
        fill="#333333" 
      />
    </svg>
  );
};

