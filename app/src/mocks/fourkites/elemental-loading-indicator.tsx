import React from 'react';

interface SpinnerProps {
  isLoading?: boolean;
  size?: 'small' | 'medium' | 'large';
  color?: string;
}

export const Spinner: React.FC<SpinnerProps> = ({
  isLoading = true,
  size = 'medium',
  color = '#0077cc',
}) => {
  if (!isLoading) {
    return null;
  }

  // Define sizes for the spinner
  const spinnerSizes = {
    small: { width: '16px', height: '16px', borderWidth: '2px' },
    medium: { width: '32px', height: '32px', borderWidth: '3px' },
    large: { width: '48px', height: '48px', borderWidth: '4px' },
  };

  const spinnerSize = spinnerSizes[size];

  // Create the spinning animation with inline styles
  const spinnerStyle: React.CSSProperties = {
    display: 'inline-block',
    width: spinnerSize.width,
    height: spinnerSize.height,
    border: `${spinnerSize.borderWidth} solid rgba(0, 0, 0, 0.1)`,
    borderRadius: '50%',
    borderTopColor: color,
    animation: 'spin 1s linear infinite',
  };

  // Inject the keyframe animation
  const keyframes = `
    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }
  `;

  return (
    <>
      <style>{keyframes}</style>
      <div style={spinnerStyle} role="status">
        <span style={{ position: 'absolute', width: '1px', height: '1px', padding: 0, margin: '-1px', overflow: 'hidden', clip: 'rect(0,0,0,0)', whiteSpace: 'nowrap', borderWidth: 0 }}>
          Loading...
        </span>
      </div>
    </>
  );
};

