import React, { ReactNode, useEffect } from 'react';

interface ButtonProps {
  label: string;
  onClick: () => void;
  theme?: 'primary' | 'secondary' | 'danger' | 'success';
  disabled?: boolean;
}

interface ModalProps {
  size?: 'small' | 'medium' | 'large' | 'fullscreen';
  title?: string;
  show: boolean;
  children: ReactNode;
  closeButtonProps?: ButtonProps;
  saveButtonProps?: ButtonProps;
  closeOnClickOutside?: boolean;
  onClose?: () => void;
}

export const Modal: React.FC<ModalProps> = ({
  size = 'medium',
  title,
  show,
  children,
  closeButtonProps,
  saveButtonProps,
  closeOnClickOutside = true,
  onClose,
}) => {
  // Add ESC key listener to close the modal
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (show && event.key === 'Escape' && onClose) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [show, onClose]);

  if (!show) {
    return null;
  }

  // Modal sizes
  const sizeStyles = {
    small: { width: '400px' },
    medium: { width: '600px' },
    large: { width: '800px' },
    fullscreen: { width: '95%', height: '95%' },
  };

  const overlayStyle: React.CSSProperties = {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1000,
  };

  const modalStyle: React.CSSProperties = {
    backgroundColor: 'white',
    borderRadius: '4px',
    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.2)',
    display: 'flex',
    flexDirection: 'column',
    maxHeight: '90vh',
    ...sizeStyles[size],
  };

  const headerStyle: React.CSSProperties = {
    padding: '16px 24px',
    borderBottom: '1px solid #e0e0e0',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  };

  const titleStyle: React.CSSProperties = {
    margin: 0,
    fontSize: '18px',
    fontWeight: 'bold',
  };

  const contentStyle: React.CSSProperties = {
    padding: '24px',
    overflowY: 'auto',
    flexGrow: 1,
  };

  const footerStyle: React.CSSProperties = {
    padding: '16px 24px',
    borderTop: '1px solid #e0e0e0',
    display: 'flex',
    justifyContent: 'flex-end',
    gap: '10px',
  };

  const buttonStyle = (theme: string = 'secondary'): React.CSSProperties => {
    const baseStyle: React.CSSProperties = {
      padding: '8px 16px',
      borderRadius: '4px',
      cursor: 'pointer',
      border: 'none',
      fontSize: '14px',
      fontWeight: 'bold',
    };

    const themeStyles: Record<string, React.CSSProperties> = {
      primary: {
        backgroundColor: '#0077cc',
        color: 'white',
      },
      secondary: {
        backgroundColor: '#f0f0f0',
        color: '#333333',
      },
      danger: {
        backgroundColor: '#dc3545',
        color: 'white',
      },
      success: {
        backgroundColor: '#28a745',
        color: 'white',
      },
    };

    return { ...baseStyle, ...themeStyles[theme] };
  };

  const closeIconStyle: React.CSSProperties = {
    cursor: 'pointer',
    fontSize: '20px',
    fontWeight: 'bold',
  };

  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget && closeOnClickOutside && onClose) {
      onClose();
    }
  };

  return (
    <div style={overlayStyle} onClick={handleOverlayClick}>
      <div style={modalStyle} onClick={(e) => e.stopPropagation()}>
        <div style={headerStyle}>
          {title && <h2 style={titleStyle}>{title}</h2>}
          {onClose && (
            <span style={closeIconStyle} onClick={onClose}>
              &times;
            </span>
          )}
        </div>
        <div style={contentStyle}>{children}</div>
        <div style={footerStyle}>
          {closeButtonProps && (
            <button
              style={buttonStyle(closeButtonProps.theme || 'secondary')}
              onClick={closeButtonProps.onClick}
              disabled={closeButtonProps.disabled}
            >
              {closeButtonProps.label}
            </button>
          )}
          {saveButtonProps && (
            <button
              style={buttonStyle(saveButtonProps.theme || 'primary')}
              onClick={saveButtonProps.onClick}
              disabled={saveButtonProps.disabled}
            >
              {saveButtonProps.label}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

