import React, { ChangeEvent } from 'react';

interface SwitchProps {
  size?: 'small' | 'medium' | 'large';
  defaultLabel?: string;
  checked?: boolean;
  onChange?: (event: ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
}

export const Switch: React.FC<SwitchProps> = ({
  size = 'medium',
  defaultLabel,
  checked = false,
  onChange,
  disabled = false,
}) => {
  const containerStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    opacity: disabled ? 0.5 : 1,
    cursor: disabled ? 'not-allowed' : 'pointer',
  };

  const switchSize = {
    small: { width: '30px', height: '16px' },
    medium: { width: '40px', height: '20px' },
    large: { width: '50px', height: '24px' },
  }[size];

  const switchStyle: React.CSSProperties = {
    position: 'relative',
    display: 'inline-block',
    width: switchSize.width,
    height: switchSize.height,
    marginRight: '10px',
  };

  const inputStyle: React.CSSProperties = {
    opacity: 0,
    width: 0,
    height: 0,
  };

  const sliderStyle: React.CSSProperties = {
    position: 'absolute',
    cursor: disabled ? 'not-allowed' : 'pointer',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: checked ? '#4CAF50' : '#ccc',
    transition: '.4s',
    borderRadius: '34px',
  };

  const knobStyle: React.CSSProperties = {
    position: 'absolute',
    content: '',
    height: parseInt(switchSize.height) - 6 + 'px',
    width: parseInt(switchSize.height) - 6 + 'px',
    left: '3px',
    bottom: '3px',
    backgroundColor: 'white',
    transition: '.4s',
    borderRadius: '50%',
    transform: checked ? `translateX(${parseInt(switchSize.width) - parseInt(switchSize.height)}px)` : 'translateX(0)',
  };

  const labelStyle: React.CSSProperties = {
    marginLeft: '5px',
    fontSize: {
      small: '12px',
      medium: '14px',
      large: '16px',
    }[size],
  };

  return (
    <label style={containerStyle}>
      <div style={switchStyle}>
        <input
          type="checkbox"
          style={inputStyle}
          checked={checked}
          onChange={onChange}
          disabled={disabled}
        />
        <span style={sliderStyle}>
          <span style={knobStyle}></span>
        </span>
      </div>
      {defaultLabel && <span style={labelStyle}>{defaultLabel}</span>}
    </label>
  );
};

