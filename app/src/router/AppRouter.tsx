import React, { Suspense, lazy, useEffect } from "react";
import {
  Switch,
  Redirect,
  Route,
  useHistory,
  useLocation,
  matchPath,
} from "react-router-dom";

import { ToastContainer, toast } from "react-toastify";

import { useAppSelector, useAppDispatch, useAnalytics } from "state/hooks";

import UsersState, { getCookies } from "state/modules/Users";

// LOGIN
const LoginPage = lazy(() => import("view/modules/login/LoginPage"));

// ONBOARDING AND SIGNUP
const CompaniesOnboardingPage = lazy(
  () => import("view/modules/onboarding/onboarding/CompaniesOnboardingPage")
);
const CompaniesSignupPage = lazy(
  () => import("view/modules/onboarding/signup/CompaniesSignupPage")
);

// EXTERNAL
const ExternalSelfServicePage = lazy(
  () => import("view/modules/external/ExternalSelfServicePage")
);

// SELECT COMPANY
const CompanySelectionPage = lazy(
  () => import("view/modules/company-selection/CompanySelectionPage")
);

// CARRIERS
const CarrierPage = lazy(() => import("view/modules/carrier/CarrierPage"));
const CarrierWizardPage = lazy(
  () => import("view/modules/carrier/wizard/CarrierWizardPage")
);

// SHIPPERS
const ShipperPage = lazy(() => import("view/modules/shipper/ShipperPage"));

// Pure 3PLs
const ThreePlPage = lazy(() => import("view/modules/threePl/ThreePlPage"));

const WelcomeScreenPage = lazy(
  () => import("view/components/self-service/carrier-onboarding-survey/WelcomeScreen")
);

// Configure toast container
toast.configure({
  autoClose: 3000,
  draggable: false,
});

const AppRouter: React.FC = (props: any) => {
  const history = useHistory();
  const location = useLocation();
  /*****************************************************************************
   * REDUX AND COOKIES
   ****************************************************************************/

  const dispatch = useAppDispatch();
  const currentUser = useAppSelector(UsersState.selectors.getCurrentUser);
  const companyType = useAppSelector(UsersState.selectors.getCompanyType);
  const companyId = useAppSelector(UsersState.selectors.getCompanyId);

  // Get cookies information
  const { authToken, userId, deviceId } = getCookies();
  const cookiesExist = authToken != null && userId != null && deviceId != null;

  // Passing user data to Pendo
  useAnalytics({ id: currentUser?.userId });

  /*****************************************************************************
   * STATE AND LOCAL VARIABLES
   ****************************************************************************/

  // Returns whether the user is at some specific route or not
  const userAtRoute = (routes: string[]) =>
    routes.some((v) => location.pathname.includes(v));

  const isLoggedIn = cookiesExist || currentUser != null;
  const isShipper = companyType?.includes("shipper");
  const isBroker = companyType?.includes("broker");
  const isCarrier = companyType?.includes("carrier");
  const isPure3Pl = companyType?.includes("3pl") && !isCarrier && !isShipper && !isBroker;

  const shouldGoToShippersPortal =
    isShipper &&
    !isBroker &&
    !isCarrier &&
    userAtRoute([
      appRoutes.login,
      appRoutes.selfServiceCarrier,
      appRoutes.companySelection,
      appRoutes.selfService3Pl,
    ]) && !isPure3Pl;
  const shouldGoToCarriersPortal =
    (!isShipper || isBroker || isCarrier) &&
    userAtRoute([
      appRoutes.login,
      appRoutes.selfServiceShipper,
      appRoutes.companySelection,
      appRoutes.selfService3Pl,
    ]) && !isPure3Pl;

  const shouldGoTo3PlPortal =
  isPure3Pl &&
  userAtRoute([
    appRoutes.login,
    appRoutes.selfServiceShipper,
    appRoutes.selfServiceCarrier,
    appRoutes.companySelection,
  ])

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  /*
   * This effect validates the cookies if they exist, or redirect to login
   */
  useEffect(() => {
    // Don't redirect if we are on carrier onboarding or on external pages
    const isExternal = history.location.pathname.includes("external");
    const isOnboarding = history.location.pathname.includes("onboarding");
    const isSignup = history.location.pathname.includes("signup");
    const isCarrierOnboardingSurvey = history.location.pathname.includes("carrier-onboarding-survey");
    if (isSignup || isOnboarding || isExternal || isCarrierOnboardingSurvey) {
      return;
    }

    // Otherwise, validates cookies or redirect to login
    if (cookiesExist) {
      dispatch(UsersState.actionCreators.validateCookies()).then(
        (areCookiesValid) => {
          if (!areCookiesValid) {
            history.push(appRoutes.login);
          }
        }
      );
    } else {
      history.push(appRoutes.login);
    }
    // eslint-disable-next-line
  }, [cookiesExist]);

  /*
   * This effect handlers route redirects after login and consistency between
   * company type and portal type
   */
  useEffect(() => {
    if (cookiesExist && !companyType) {
      // TODO: don't do this if is still loading user data
      // If is superadmin, goes to select company page
      if (!userAtRoute([appRoutes.login, appRoutes.companySelection])) {
        history.push(appRoutes.companySelection);
      }

      return;
    }

    // If user is defined, goes to appropriate page
    if (currentUser) {
      // Goes to shipper page if on login or on carrier portal routes
      if (shouldGoToShippersPortal) {
        history.push(appRoutes.selfServiceShipper);
        return;
      }

      // Goes to shipper page if on login or on shipper portal routes
      if (shouldGoToCarriersPortal) {
        history.push(appRoutes.selfServiceCarrier);
      }

      if(shouldGoTo3PlPortal){
        history.push(appRoutes.selfService3Pl);
      }
    }
    // If we don't have user yet, validate cookies
    else {
      if (cookiesExist) {
        dispatch(UsersState.actionCreators.validateCookies());
      }
    }

    // eslint-disable-next-line
  }, [location, currentUser, companyType, companyId, cookiesExist]);

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const loggedOutRoutes = (
    <>
      <Route path={appRoutes.login} component={LoginPage} />

      <Route
        path={appRoutes.onboarding}
        component={CompaniesOnboardingPage}
        exact
      />

      <Route path={appRoutes.signUp} component={CompaniesSignupPage} />

      <Route
        path={appRoutes.externalSelfService}
        component={ExternalSelfServicePage}
      />

      <Route
        path={appRoutes.selfServiceCarrierOnboardingSurvey}
        component={WelcomeScreenPage}
      />
    </>
  );

  const loggedInRoutes = (
    <>
      {/*TODO: these routes should be abstracted into SelfServiceRouter*/}
      <Route
        exact
        path={["/", appRoutes.login, appRoutes.selfService]}
        render={() => <Redirect to={appRoutes.selfServiceShipper} />}
      />

      <Route
        path={appRoutes.companySelection}
        component={CompanySelectionPage}
      />

      <Route path={appRoutes.selfServiceShipper} component={ShipperPage} />

      <Route path={appRoutes.selfServiceCarrier} component={CarrierPage} />

      <Route path={appRoutes.selfService3Pl} component={ThreePlPage} />

      <Route
        path={appRoutes.selfServiceCarrierWizard}
        component={CarrierWizardPage}
      />
    </>
  );

  return (
    <Switch>
      <Suspense fallback={""}>
        <ToastContainer />

        {isLoggedIn ? loggedInRoutes : loggedOutRoutes}
      </Suspense>
    </Switch>
  );
};

export const appRoutes = {
  // Logged out routes
  login: "/login",
  signUp: "/self-service/signup",
  onboarding: "/self-service/onboarding/:invitationToken?",
  externalSelfService: "/self-service/external",
  selfServiceCarrierOnboardingSurvey: "/self-service/carrier-onboarding-survey",

  // Logged in routes
  selfService: "/self-service",
  companySelection: "/self-service/company-selection",

  // Shippers
  selfServiceShipper: "/self-service/shipper",

  // Carriers
  selfServiceCarrier: "/self-service/carrier",
  selfServiceCarrierWizard: "/self-service/carrier-wizard",

  //3Pl
  selfService3Pl: "/self-service/3pl"
};

const matchRoute = (route: string, history: any) =>
  matchPath(history.location.pathname, {
    path: route,
    exact: false,
  });

export const getRouteId = (history: any, pageRoutes: any) => {
  const routes: string[] = Object.values(pageRoutes);
  for (let route of routes) {
    if (matchRoute(route, history)) {
      return route;
    }
  }
};

export default AppRouter;
