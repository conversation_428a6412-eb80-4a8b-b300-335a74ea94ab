import React, { lazy } from "react";
import { Redirect, Route } from "react-router-dom";

import { carrierRoutes } from "./CarrierRouter";
import { getRouteId } from "../AppRouter";

const CarrierAssetAssignmentPage = lazy(
  () =>
    import("view/modules/carrier/assets/assignment/CarrierAssetAssignmentPage")
);
const CarrierAssetManagementPage = lazy(
  () =>
    import("view/modules/carrier/assets/management/CarrierAssetManagementPage")
);

const CarrierAssetsRouter = () => {
  return (
    <>
      <Route
        exact
        path={carrierRoutes.fleet}
        render={() => <Redirect to={carrierAssetsRoutes.management} />}
      />

      <Route
        path={carrierAssetsRoutes.management}
        component={CarrierAssetManagementPage}
      />

      <Route
        path={carrierAssetsRoutes.assignment}
        component={CarrierAssetAssignmentPage}
      />
    </>
  );
};

export const carrierAssetsRoutes = {
  management: `${carrierRoutes.fleet}/management`,
  assignment: `${carrierRoutes.fleet}/assignment`,
};

export const getCarrierAssetsRouteId = (history: any) =>
  getRouteId(history, carrierAssetsRoutes);

export default CarrierAssetsRouter;
