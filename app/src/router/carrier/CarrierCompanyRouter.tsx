import { lazy } from "react";
import { Redirect, Route } from "react-router-dom";

import { carrierRoutes } from "./CarrierRouter";
import { getRouteId } from "../AppRouter";

const CarrierCompanyGeneralPage = lazy(
  () => import("view/modules/carrier/company/general/CarrierCompanyGeneralPage")
);

const CarrierOnboardingInstructionsPage = lazy(
  () =>
    import(
      "view/modules/carrier/company/onboarding-instructions/CarrierOnboardingInstructionsPage"
    )
);

const CarrierLTLConfigurationsPage = lazy(
  () =>
    import(
      "view/modules/carrier/company/fkc-configurations/LTLConfigurationsPage"
    )
);

const CarrierOceanConfigurationsPage = lazy(
  () =>
    import(
      "view/modules/carrier/company/fkc-configurations/OceanConfigurationsPage"
    )
);

const CarrierCompanyRouter = () => {
  return (
    <>
      <Route
        exact
        path={carrierRoutes.company}
        render={() => <Redirect to={carrierCompanyRoutes.general} />}
      />
      <Route
        path={carrierCompanyRoutes.general}
        component={CarrierCompanyGeneralPage}
      />
      <Route
        path={carrierCompanyRoutes.onboardingInstructions}
        component={CarrierOnboardingInstructionsPage}
      />
      <Route
        path={carrierCompanyRoutes.ltlConfigurations}
        component={CarrierLTLConfigurationsPage}
      />

      <Route
        path={carrierCompanyRoutes.oceanConfigurations}
        component={CarrierOceanConfigurationsPage}
      />

      {/*If no match, redict to shipment integrations*/}
      <Redirect to={carrierCompanyRoutes.general} />
    </>
  );
};
export const carrierCompanyRoutes = {
  general: `${carrierRoutes.company}/general`,
  onboardingInstructions: `${carrierRoutes.company}/onboarding-instructions`,
  oceanConfigurations: `${carrierRoutes.company}/ocean-configurations`,
  ltlConfigurations: `${carrierRoutes.company}/ltl-configurations`,
};

export const getCarrierCompanyRouteId = (history: any) =>
  getRouteId(history, carrierCompanyRoutes);

export default CarrierCompanyRouter;
