import React, { lazy, useEffect } from "react";
import { Route, useHistory, useLocation } from "react-router-dom";

import { useAppSelector } from "state/hooks";
import UsersState from "state/modules/Users";

import { appRoutes, getRouteId } from "../AppRouter";

const CarrierOverviewPage = lazy(
  () => import("view/modules/carrier/overview/CarrierOverviewPage")
);
const CustomersNetworkPage = lazy(
  () => import("view/modules/carrier/customers-network/CustomersNetworkPage")
);
const CarriersNetworkPage = lazy(
  () => import("view/modules/shipper/carriers-network/CarriersNetworkPage")
);
const CarrierTrackingIntegrationsPage = lazy(
  () => import("view/modules/carrier/tracking/CarrierTrackingIntegrationsPage")
);
const CarrierAssetsPage = lazy(
  () => import("view/modules/carrier/assets/CarrierAssetsPage")
);
const CarrierCompanyPage = lazy(
  () => import("view/modules/carrier/company/CarrierCompanyPage")
);
const DataIntegrationsPage = lazy(
  () => import("view/modules/shipper/data-integrations/DataIntegrationsPage")
);

const CarrierRouter = () => {
  const history = useHistory();
  const location = useLocation();

  const companyType = useAppSelector(UsersState.selectors.getCompanyType);

  /*
   * When company type changes, we do a safe check to not show invalid pages
   */
  useEffect(() => {
    const userAtRoute = (routes: string[]) =>
      routes.some((v) => location.pathname.includes(v));

    const isBroker = companyType?.includes("broker");

    // Handle special case to avoid carriers page for non-brokers
    if (!isBroker && userAtRoute([carrierRoutes.carriers])) {
      history.push(carrierRoutes.customers);
    }
  }, [companyType]);

  /*
   * If we don't have a valid current route, we redirect to overview
   */
  useEffect(() => {
    const currentRoute = getCarrierRouteId(history);

    if (!currentRoute) {
      history.push(carrierRoutes.overview);
    }
  }, [location]);

  return (
    <>
      <Route
        exact
        path={carrierRoutes.overview}
        component={CarrierOverviewPage}
      />

      <Route
        exact
        path={carrierRoutes.customers}
        component={CustomersNetworkPage}
      />

      <Route
        exact
        path={carrierRoutes.carriers}
        component={CarriersNetworkPage}
      />

      <Route
        path={carrierRoutes.tracking}
        component={CarrierTrackingIntegrationsPage}
      />

      <Route path={carrierRoutes.fleet} component={CarrierAssetsPage} />

      <Route path={carrierRoutes.company} component={CarrierCompanyPage} />

      <Route
        path={carrierRoutes.dataIntegrations}
        component={DataIntegrationsPage}
      />
    </>
  );
};

export const carrierRoutes = {
  overview: `${appRoutes.selfServiceCarrier}/overview`,
  customers: `${appRoutes.selfServiceCarrier}/customers`,
  carriers: `${appRoutes.selfServiceCarrier}/carriers`,
  tracking: `${appRoutes.selfServiceCarrier}/tracking`,
  fleet: `${appRoutes.selfServiceCarrier}/fleet`,
  company: `${appRoutes.selfServiceCarrier}/company`,
  dataIntegrations: `${appRoutes.selfServiceCarrier}/data-integrations`,
};

export const getCarrierRouteId = (history: any) =>
  getRouteId(history, carrierRoutes);

export default CarrierRouter;
