import React, { lazy } from "react";
import { Redirect, Route } from "react-router-dom";

import { carrierRoutes } from "./CarrierRouter";
import { getRouteId } from "../AppRouter";

const EldGpsIntegrationsPage = lazy(
  () => import("view/modules/carrier/tracking/eld-gps/EldGpsIntegrationsPage")
);

const DispatcherAPILogsPage = lazy(
  () => import("view/modules/carrier/tracking/dispatcher-logs/DispatcherAPILogsPage")
);
const EdiLocationFilesIntegrationsPage = lazy(
  () =>
    import(
      "view/modules/carrier/tracking/edi-location/EdiLocationFilesIntegrationsPage"
    )
);

const CarrierTrackingIntegrationsRouter = () => {
  return (
    <>
      <Route
        exact
        path={carrierRoutes.tracking}
        render={() => (
          <Redirect to={carrierTrackingIntegrationsRoutes.eldGps} />
        )}
      />

      <Route
        path={carrierTrackingIntegrationsRoutes.dispatcherLogs}
        component={DispatcherAPILogsPage}
      />

      <Route
        path={carrierTrackingIntegrationsRoutes.eldGps}
        component={EldGpsIntegrationsPage}
      />

      <Route
        path={carrierTrackingIntegrationsRoutes.ediLocation}
        component={EdiLocationFilesIntegrationsPage}
      />
    </>
  );
};

export const carrierTrackingIntegrationsRoutes = {
  eldGps: `${carrierRoutes.tracking}/eld-gps`,
  ediLocation: `${carrierRoutes.tracking}/edi-location`,
  dispatcherLogs: `${carrierRoutes.tracking}/dispatcher-logs`,
};

export const getCarrierTrackingIntegrationsRouteId = (history: any) =>
  getRouteId(history, carrierTrackingIntegrationsRoutes);

export default CarrierTrackingIntegrationsRouter;
