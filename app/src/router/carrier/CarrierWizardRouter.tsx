import React, { lazy } from "react";
import { Route } from "react-router-dom";

import { appRoutes } from "../AppRouter";

const CarrierWizardStartPage = lazy(
  () => import("view/modules/carrier/wizard/start/CarrierWizardStartPage")
);
const CarrierWizardEldGpsTrackingPage = lazy(
  () =>
    import(
      "view/modules/carrier/wizard/eld-gps/CarrierWizardEldGpsTrackingPage"
    )
);
const CarrierWizardMobileTrackingPage = lazy(
  () =>
    import("view/modules/carrier/wizard/mobile/CarrierWizardMobileTrackingPage")
);

export const carrierWizardRoutes = {
  start: `${appRoutes.selfServiceCarrierWizard}`,
  mobileTracking: `${appRoutes.selfServiceCarrierWizard}/mobile`,
  eldGpsTracking: `${appRoutes.selfServiceCarrierWizard}/eld-gps`,
};

const CarrierWizardRouter = () => {
  return (
    <>
      <Route
        exact
        path={carrierWizardRoutes.start}
        component={CarrierWizardStartPage}
      />
      <Route
        exact
        path={carrierWizardRoutes.eldGpsTracking}
        component={CarrierWizardEldGpsTrackingPage}
      />
      <Route
        exact
        path={carrierWizardRoutes.mobileTracking}
        component={CarrierWizardMobileTrackingPage}
      />
    </>
  );
};

export default CarrierWizardRouter;
