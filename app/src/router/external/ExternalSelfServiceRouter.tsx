import React, { lazy } from "react";
import { Redirect, Route } from "react-router-dom";

import { appRoutes, getRouteId } from "../AppRouter";

const ExternalCompaniesOnboardingPage = lazy(
  () =>
    import("view/modules/external/onboarding/ExternalCompaniesOnboardingPage")
);
const ExternalCarrierWizardPage = lazy(
  () => import("view/modules/external/wizard/ExternalCarrierWizardPage")
);

const ExternalSelfServiceRouter = () => {
  return (
    <>
      <Route
        exact
        path={externalSelfServiceRoutes.onboarding}
        component={ExternalCompaniesOnboardingPage}
      />
      <Route
        exact
        path={externalSelfServiceRoutes.carrierWizard}
        component={ExternalCarrierWizardPage}
      />
    </>
  );
};

export const externalSelfServiceRoutes = {
  onboarding: `${appRoutes.externalSelfService}/onboarding`,
  carrierWizard: `${appRoutes.externalSelfService}/wizard`,
};

export const getExternalSelfServiceRouteId = (history: any) =>
  getRouteId(history, externalSelfServiceRoutes);

export default ExternalSelfServiceRouter;
