import { fourkitesUrls } from "api/http/apiUtils";

export const onNav = (url: string) => {
  sessionStorage.removeItem("fkcUserDetails");
  window.location.href = url;
};

export const onNewWindow = (url: string) => {
  let a = document.createElement("a");
  a.target = "_blank";
  a.href = url;
  a.click();
};

/*
 * Setting the coookie with expiration time for SSO and normal users
 * to use KB/Community Url
 */
export const setCookie = () => {
  const domains = [
    "app",
    "app-staging",
    "app-mirror",
    "app-uat",
    "app-eu",
    "app-fkdev",
    "app-automation",
    "app-demo",
    "mp-dev",
    "mp-staging",
    "local",
    "mp",
    "app-stress-test01",
    "app-tgib",
    "app-qat",
    "app-dev",
  ];
  const hosts = window.location.host.split(".");
  if (hosts.length && hosts[0] && domains.indexOf(hosts[0]) === -1) {
    const host = hosts[0],
      today = new Date();
    today.setDate(today.getDate() + 30);
    const cookie =
      "subdomain=" +
      encodeURIComponent(host) +
      ";path=/;domain=.fourkites.com;";
    document.cookie = cookie;
  }
};

export const onHelpClick = () => {
  setCookie();
  onNewWindow(fourkitesUrls.helpCenter);
};

export const onCarrierHelpClick = () => {
  setCookie();
  onNewWindow(fourkitesUrls.carrierHelpCenter);
};

export const onELDHelpClick = () => {
  setCookie();
  onNewWindow(fourkitesUrls.eldHelpCenter);
};

export const onCommunitySelect = () => {
  setCookie();
  onNewWindow(fourkitesUrls.community);
};

export const onContactSupportClick = () => {
  setCookie();
  onNewWindow(fourkitesUrls.contactSupport);
};

export const onWebhooksKnowledgeBaseClick = () => {
  setCookie();
  onNewWindow(fourkitesUrls.webhooksKnowledgeBase);
};

export const onStandardCallbacksKnowledgeBaseClick = () => {
  setCookie();
  onNewWindow(fourkitesUrls.standardCallbacksKnowledgeBase);
};

export const onSharedPartnersOverviewClick = () => {
  setCookie();
  onNewWindow(fourkitesUrls.sharedPartnersOverview);
};

export const onWebhooksLogsKnowledgeBaseClick = () => {
  setCookie();
  onNewWindow(fourkitesUrls.webhooksLogsKnowledgeBase);
};
