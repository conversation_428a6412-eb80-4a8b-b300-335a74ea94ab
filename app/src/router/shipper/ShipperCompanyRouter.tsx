import { lazy } from "react";
import { Redirect, Route } from "react-router-dom";

import { shipperRoutes } from "./ShipperRouter";

const ShipperCompanyGeneralPage = lazy(
  () => import("view/modules/shipper/company/general/ShipperCompanyGeneralPage")
);

export const shipperCompanyRoutes = {
  general: `${shipperRoutes.company}/general`,
};

const ShipperCompanyRouter = () => {
  return (
    <>
      <Route
        exact
        path={shipperRoutes.company}
        render={() => <Redirect to={shipperCompanyRoutes.general} />}
      />
      <Route
        path={shipperCompanyRoutes.general}
        component={ShipperCompanyGeneralPage}
      />
      {/*If no match, redict to shipment integrations*/}
      <Redirect to={shipperCompanyRoutes.general} />
    </>
  );
};

export default ShipperCompanyRouter;
