import React, { lazy } from "react";
import { Redirect, Route } from "react-router-dom";
import { carrierRoutes } from "../carrier/CarrierRouter";
import { shipperRoutes } from "./ShipperRouter";
import { getRouteId } from "../AppRouter";

const SampleReferenceNumbersPage = lazy(
  () =>
    import(
      "view/modules/shipper/data-integrations/loads-additions/load-matching/SampleReferenceNumbersPage"
    )
);

const BatchUploadsLoadsAdditionsPage = lazy(
  () =>
    import(
      "view/modules/shipper/data-integrations/loads-additions/batch-uploads/BatchUploadsLoadsAdditionsPage"
    )
);

const EmailLoadsAdditionsPage = lazy(
  () =>
    import(
      "view/modules/shipper/data-integrations/loads-additions/email/EmailLoadsAdditionsPage"
    )
);

const PlatformApiPage = lazy(
  () =>
    import(
      "view/modules/shipper/data-integrations/loads-additions/advanced/PlatformApiPage"
    )
);

const WebhooksConfigurationPage = lazy(
  () =>
    import(
      "view/modules/shipper/data-integrations/webhooks/configurations/WebhooksConfigurationPage"
    )
);

const LoadsAdditionsFileValidationsPage = lazy(
  () =>
    import(
      "view/modules/shipper/data-integrations/validations/LoadsAdditionsFileValidationsPage"
    )
);

const LogsErrorPage = lazy(
  () =>
    import(
      "view/modules/shipper/data-integrations/webhooks/logs/LogsAndErrorPage"
    )
);

const MaintenanceSchedulePage = lazy(
  () =>
    import(
      "view/modules/shipper/data-integrations/webhooks/maintenance/MaintenanceSchedulePage"
    )
);

const ShipperDataIntegrationsRouter = () => {
  return (
    <>
      <Route
        exact
        path={baseRoute}
        render={() => <Redirect to={dataIntegrationRoutes.loadMatching} />}
      />
      <Route
        exact
        path={dataIntegrationRoutes.loadMatching}
        component={SampleReferenceNumbersPage}
      />
      <Route
        exact
        path={dataIntegrationRoutes.batchUploads}
        component={BatchUploadsLoadsAdditionsPage}
      />
      <Route
        path={dataIntegrationRoutes.email}
        component={EmailLoadsAdditionsPage}
      />
      <Route
        path={dataIntegrationRoutes.platformApi}
        component={PlatformApiPage}
      />
      <Route
        path={dataIntegrationRoutes.webhooks}
        component={WebhooksConfigurationPage}
      />
      <Route
        path={dataIntegrationRoutes.fileValidations}
        component={LoadsAdditionsFileValidationsPage}
      />
      <Route
        path={dataIntegrationRoutes.webhookLogs}
        component={LogsErrorPage}
      />
      <Route
        path={dataIntegrationRoutes.webhookMaintenance}
        component={MaintenanceSchedulePage}
      />
    </>
  );
};

const isCarrierPortal = window?.location?.href?.includes(
  "/self-service/carrier/"
);

const baseRoute = isCarrierPortal
  ? carrierRoutes.dataIntegrations
  : shipperRoutes.dataIntegrations;

export const dataIntegrationRoutes = {
  loadMatching: `${baseRoute}/load-matching`,
  batchUploads: `${baseRoute}/batch-uploads`,
  email: `${baseRoute}/email`,
  platformApi: `${baseRoute}/platform-api`,
  fileValidations: `${baseRoute}/file-validations`,
  webhooks: `${baseRoute}/webhooks`,
  webhookLogs: `${baseRoute}/webhook-logs`,
  webhookMaintenance: `${baseRoute}/webhook-maintenance`,
};

export const getShipperDataIntegrationsRouteId = (history: any) =>
  getRouteId(history, dataIntegrationRoutes);

export default ShipperDataIntegrationsRouter;