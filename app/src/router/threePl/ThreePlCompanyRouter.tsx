import { lazy } from "react";
import { Redirect, Route } from "react-router-dom";

import { threePlRoutes } from "./ThreePlRouter";

const ThreePlCompanyGeneralPage = lazy(
  () => import("view/modules/threePl/company/general/ThreePlCompanyGeneralPage")
);

export const threePlCompanyRoutes = {
  general: `${threePlRoutes.company}/general`,
};

const ThreePlCompanyRouter = () => {
  return (
    <>
      <Route
        exact
        path={threePlRoutes.company}
        render={() => <Redirect to={threePlCompanyRoutes.general} />}
      />
      <Route
        path={threePlCompanyRoutes.general}
        component={ThreePlCompanyGeneralPage}
      />
      {/*If no match, redict to shipment integrations*/}
      <Redirect to={threePlCompanyRoutes.general} />
    </>
  );
};

export default ThreePlCompanyRouter;
