import React, { lazy, useEffect } from "react";
import { Route, useHistory, useLocation } from "react-router-dom";

import { useAppSelector } from "state/hooks";
import UsersState from "state/modules/Users";

import { appRoutes, getRouteId } from "../AppRouter";

const ThreePlOverviewPage = lazy(
  () => import("view/modules/threePl/overview/ThreePlOverviewPage")
);

const ThreePlCompanyPage = lazy(
  () => import("view/modules/threePl/company/ThreePlCompanyPage")
);

const ThreePlRouter = () => {
  const history = useHistory();
  const location = useLocation();

  const companyType = useAppSelector(UsersState.selectors.getCompanyType);

  /*
   * If we don't have a valid current route, we redirect to overview
   */
  useEffect(() => {
    const currentRoute = getThreePlRouteId(history);

    if (!currentRoute) {
      history.push(threePlRoutes.overview);
    }
  }, [location]);

  return (
    <>
      <Route
        exact
        path={threePlRoutes.overview}
        component={ThreePlOverviewPage}
      />

      <Route path={threePlRoutes.company} component={ThreePlCompanyPage} />
    </>
  );
};

export const threePlRoutes = {
  overview: `${appRoutes.selfService3Pl}/overview`,
  company: `${appRoutes.selfService3Pl}/company`,
};

export const getThreePlRouteId = (history: any) =>
  getRouteId(history, threePlRoutes);

export default ThreePlRouter;
