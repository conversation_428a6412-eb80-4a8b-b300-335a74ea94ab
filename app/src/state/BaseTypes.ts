export type LoadsTrackingMode = "ltl" | "ftl" | "parcel" | "ocean" | "air";

export type NetworkStatusType =
  | "invited"
  | "in_progress"
  | "connected"
  | "disconnected"
  | "all";
/*
  | "rail"
  | "courier";*/

export type FleetMode = "otr" | "ocean" | "rail" | "air";

export type CarrierContact = {
  firstName: string;
  lastName: string;
  email: string;
  phones?: string[];
  secondaryEmails?: string[];
  avatar?: string;
  position?: string;
  messaging?: string;
};

export type LocationProvider = {
  id: "string";
  type: "truck" | "trailer";
  alias?: string;
  name?: string;
  credentials?: any;
};

export const typedModeFromAction = (action: any) => {
  const mode: LoadsTrackingMode = action?.meta?.arg.mode as LoadsTrackingMode;

  return mode;
};

export const typedFleetModeFromAction = (action: any) => {
  const fleetMode: FleetMode = action?.meta?.arg.fleetMode as FleetMode;

  return fleetMode;
};

export type PaginationType = {
  pageSize: number;
  pageIndex: number;
  sortBy?: any;
};
