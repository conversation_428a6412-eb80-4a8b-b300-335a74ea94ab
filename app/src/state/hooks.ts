import { useEffect, useRef, useCallback } from "react";
import { TypedUseSelectorHook, useDispatch, useSelector } from "react-redux";
import { RootState, AppDispatch } from "./store";
import { Analytics } from "assets/analytics";

// Use throughout your app instead of plain `useDispatch` and `useSelector`
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

export const useDebounce = (callback: Function, timeout: any, deps: any) => {
  const timeoutId = useRef();

  useEffect(() => {
    clearTimeout(timeoutId.current);
    // @ts-ignore
    timeoutId.current = setTimeout(callback, timeout);

    return () => clearTimeout(timeoutId.current);
  }, deps);
};

/*
 * Hook used to integrate with pendo
 */
export const useAnalytics = (visitor: { id: string }) => {
  const tracking = useRef(Analytics(visitor));

  useEffect(() => {
    const currentTracking = tracking.current;
    currentTracking.start();

    return () => {
      currentTracking.stop();
    };
  }, [tracking]);

  return tracking.current;
};

/*
 * Hook used to auto refresh ELD GPS Providers
 */
export const useAutoRefreshEldIntegrations = (
  eldGpsIntegrations: any,
  callback: Function
) => {
  const AUTO_REFRESH_INTERVAL = 3000;
  let autoRefreshGpsIntegrationsIntervalId = useRef<any>(null);

  /**
   * Check for providers with validation inprogress status
   */
  const checkForIntegrationsWithValidationsInProgress = () => {
    const hasInprogressProviders = eldGpsIntegrations.filter(
      (integration: any) =>
        integration?.status?.value === "validation in progress"
    );
    return hasInprogressProviders;
  };

  /**
   * Refresh GPS integrations
   */
  const autoRefreshGpsIntegrations = () => {
    const hasInprogressProviders = checkForIntegrationsWithValidationsInProgress();
    return setInterval(() => {
      if (hasInprogressProviders.length > 0) {
        callback();
      }
    }, AUTO_REFRESH_INTERVAL);
  };

  /**
   * Set auto refresh interval
   */
  const setAutoRefreshGPSInterval = () => {
    autoRefreshGpsIntegrationsIntervalId.current = autoRefreshGpsIntegrations();
  };

  /**
   * Clear auto refresh interval
   */
  const clearAutoRefreshGPSInterval = () => {
    clearInterval(autoRefreshGpsIntegrationsIntervalId.current);
  };

  useEffect(() => {
    setAutoRefreshGPSInterval();
    return () => clearAutoRefreshGPSInterval();
  }, [eldGpsIntegrations]);

  return { setAutoRefreshGPSInterval, clearAutoRefreshGPSInterval };
};
