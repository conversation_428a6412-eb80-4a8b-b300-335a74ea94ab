import {
  createAsyncThunk,
  createSelector,
  createSlice,
} from "@reduxjs/toolkit";
import CommonCompanyContactsApi from "api/CommonCompanyContactsApi";
import { RootState, Selector } from "state/store";
import { AddContactPayload } from "view/components/self-service/company-management/carrier-details/modals/AddYourContactModal.types";
const MODULE_NAME = "commonCompanyContacts";

/*******************************************************************************
 * ACTIONS
 ******************************************************************************/

/*
 * Gets the details for the company
 */
const retrieveCompanyContacts = createAsyncThunk<
  any,
  { companyId: string; scac: string },
  {}
>(
  `${MODULE_NAME}/retrieveCompanyContacts`,
  async ({ companyId, scac }, thunkAPI) => {
    const response = await CommonCompanyContactsApi.retrieveCompanyContacts(
      companyId,
      scac
    );

    return response?.data;
  }
);

/*
 * Creates a new company contact
 */
export const createCompanyContact = createAsyncThunk<
  any,
  { companyId: string; scac: string; payload: AddContactPayload },
  {}
>(
  `${MODULE_NAME}/createCompanyContact`,
  async ({ companyId, scac, payload }, thunkAPI) => {
    const response = await CommonCompanyContactsApi.createCompanyContact(
      companyId,
      scac,
      payload
    );
    return response?.data;
  }
);

export const updateCompanyContact = createAsyncThunk<
  any,
  { contactId: number; companyId: string; payload: AddContactPayload },
  {}
>(
  `${MODULE_NAME}/updateCompanyContact`,
  async ({ contactId, companyId, payload }, thunkAPI) => {
    const response = await CommonCompanyContactsApi.updateCompanyContact(
      contactId,
      companyId,
      payload
    );
    return response?.data;
  }
);

export const deleteCompanyContact = createAsyncThunk<
  any,
  { contactId: number; companyId: string },
  {}
>(
  `${MODULE_NAME}/deleteCompanyContact`,
  async ({ contactId, companyId }, thunkAPI) => {
    const response = await CommonCompanyContactsApi.deleteCompanyContact(
      contactId,
      companyId
    );
    return response?.data;
  }
);

/*******************************************************************************
 * SELECTORS
 ******************************************************************************/

export const getCommonCompanyContacts = (): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.data,
    (data: any) => data
  );

export const isLoadingCompanyContacts = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.loading,
    (data: boolean) => data
  );

/*******************************************************************************
 * SLICE
 ******************************************************************************/

// Define the initial state using that type
const initialCommonCompanyContactsState = {
  data: null,
  loading: false,
  error: false,
};

const CompanyContactsSlice = createSlice({
  name: MODULE_NAME,
  initialState: initialCommonCompanyContactsState,
  reducers: {
    resetStore: () => initialCommonCompanyContactsState,
  },
  extraReducers: (builder) => {
    builder.addCase(retrieveCompanyContacts.pending, (state) => {
      state.loading = true;
    });
    builder.addCase(retrieveCompanyContacts.fulfilled, (state, action) => {
      state.data = action.payload;
      state.loading = false;
    });
    builder.addCase(retrieveCompanyContacts.rejected, (state) => {
      state.loading = false;
      state.error = true;
    });

    // Handle the new action
    builder.addCase(createCompanyContact.pending, (state) => {
      state.loading = true;
    });
    builder.addCase(createCompanyContact.fulfilled, (state, action) => {
      state.data = action.payload;
      state.loading = false;
    });
    builder.addCase(createCompanyContact.rejected, (state) => {
      state.loading = false;
      state.error = true;
    });

    // Handle the new action
    builder.addCase(updateCompanyContact.pending, (state) => {
      state.loading = true;
    });
    builder.addCase(updateCompanyContact.fulfilled, (state, action) => {
      // state.data = action.payload;
      state.loading = false;
    });
    builder.addCase(updateCompanyContact.rejected, (state) => {
      state.loading = false;
      state.error = true;
    });

    // Handle the new action
    builder.addCase(deleteCompanyContact.pending, (state) => {
      state.loading = true;
    });
    builder.addCase(deleteCompanyContact.fulfilled, (state, action) => {
      // state.data = action.payload;
      state.loading = false;
    });
    builder.addCase(deleteCompanyContact.rejected, (state) => {
      state.loading = false;
      state.error = true;
    });
  },
});

export const CommonCompanyContactsState = {
  actions: {
    resetCompanyContacts: CompanyContactsSlice.actions.resetStore,
    retrieveCompanyContacts,
    createCompanyContact,
    updateCompanyContact,
    deleteCompanyContact,
  },
  selectors: {
    getCommonCompanyContacts,
    isLoadingCompanyContacts,
  },
};

export default CompanyContactsSlice;
