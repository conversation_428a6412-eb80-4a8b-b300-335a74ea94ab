import {
  createSlice,
  createAsyncThunk,
  createSelector,
} from "@reduxjs/toolkit";
import { RootState, Selector } from "state/store";
import { LoadsTrackingMode } from "state/BaseTypes";

import companiesApi from "api/CompaniesApi";

const MODULE_NAME = "companiesSearch";

/*******************************************************************************
 * ACTIONS
 ******************************************************************************/

/*
 * Gets the details fo a given carrier
 */
export const searchCompanies = createAsyncThunk<
  any,
  {
    companyId: string;
    companyType: string;
    mode: LoadsTrackingMode;
    query: string;
  },
  {}
>(
  `${MODULE_NAME}/searchCompanies`,
  async ({ companyId, companyType, mode, query }, thunkAPI) => {
    const response = await companiesApi.searchCompanies(
      companyId,
      companyType,
      mode,
      query
    );

    return response.data;
  }
);

/*
 * Gets the details fo a given carrier
 */
export const searchCompaniesExternally = createAsyncThunk<
  any,
  {
    query: string;
    invitationToken: string;
  },
  {}
>(
  `${MODULE_NAME}/searchCompaniesExternally`,
  async ({ query, invitationToken }, thunkAPI) => {
    const response = await companiesApi.searchCompaniesExternally(
      query,
      invitationToken
    );

    return response?.data;
  }
);

/*******************************************************************************
 * SELECTORS
 ******************************************************************************/

export const getSearchedCompanies = (): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.searchedCompanies?.data,
    (data: any) => data
  );

export const isSearching = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.searchedCompanies?.searching,
    (data: any) => data
  );

/*******************************************************************************
 * SLICE
 ******************************************************************************/

const companiesSearchSlice = createSlice({
  name: MODULE_NAME,
  initialState: {
    searchedCompanies: {
      data: [] as any[],
      searching: false,
      error: false,
    },
  },
  reducers: {},
  extraReducers: (builder) => {
    /***************************************************************************
     * CARRIER DETAILS
     **************************************************************************/

    [searchCompaniesExternally, searchCompanies].forEach((actionCreator) => {
      builder.addCase(actionCreator.fulfilled, (state, action) => {
        state.searchedCompanies.data = action.payload;
        state.searchedCompanies.searching = false;
        state.searchedCompanies.error = false;
      });
      builder.addCase(actionCreator.pending, (state, action) => {
        state.searchedCompanies.data = [];
        state.searchedCompanies.searching = true;
        state.searchedCompanies.error = false;
      });
      builder.addCase(actionCreator.rejected, (state, action) => {
        state.searchedCompanies.searching = false;
        state.searchedCompanies.error = true;
      });
    });
  },
});

export const CompaniesSearchState = {
  actions: {
    searchCompanies,
    searchCompaniesExternally,
  },
  selectors: {
    getSearchedCompanies,
    isSearching,
  },
};

// Action creators are generated for each case reducer function
export default companiesSearchSlice;
