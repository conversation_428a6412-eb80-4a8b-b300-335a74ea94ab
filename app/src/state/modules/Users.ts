// TODO: this state module is based on legacy code and should be improved when
// we have the opportunity to do so, moving to redux toolkit

import Cookies from "universal-cookie";

import companiesApi from "api/CompaniesApi";
import usersApi from "api/UsersApi";
import { AppDispatch } from "state/store";

const STORE_MOUNT_POINT = "users";

// ACTIONS
const LOADING = "Users/LOADING";
const SUCCESSFUL_LOGIN = "Users/SUCCESSFUL_LOGIN";
const FAILED_LOGIN = "Users/FAILED_LOGIN";
const SUCCESSFUL_LOGOUT = "Users/SUCCESSFUL_LOGOUT";
const FAILED_LOGOUT = "Users/FAILED_LOGOUT";
const VALIDATE_KEY_SUCCESS = "Users/VALIDATE_KEY_SUCCESS";
const VALIDATE_KEY_FAILURE = "Users/VALIDATE_KEY_FAILURE";
const SET_EXTERNAL_COMPANY_ID = "Users/SET_EXTERNAL_COMPANY_ID";

/*******************************************************************************
 * TYPES
 ******************************************************************************/

type CompanyConfig = {
  id: string;
  name: string;
};

type Company = {
  active: boolean;
  status: string;
};

type User = {
  role: string;
  termsAgreed: boolean;
  companyAdmin: boolean;
  modules: string[];
  companyId: string;
};

type UserResponse = {
  data: {
    user: User;
    authToken: string;
    deviceId: string;
    frontendVersion: string;
  };
};

type LogoutFunction = () => void;

type ExternalCompany = {
  companyId: string;
  companyType: string;
  companyName?: string;
};

/*******************************************************************************
 * COOKIES AND STORAGE
 ******************************************************************************/

export const cookies = new Cookies();

// TODO: we need to set sessionStrage because that's how the company selector
// works. We need to change that inside EDS
const handleComanyDataStorage = (companyConfig: CompanyConfig) => {
  const savedCompanyContext = sessionStorage.getItem("companyContextId");

  if (companyConfig != null && !savedCompanyContext) {
    const reformattedCompany = {
      id: companyConfig.id,
      description: companyConfig.name,
    };
    sessionStorage.setItem(
      "companyContextId",
      JSON.stringify(reformattedCompany)
    );
  }
};

/*
 * Saves cookies on local storage
 */
const saveCookie = (authToken: string, userId: string, deviceId: string) => {
  cookies.set("auth-token", authToken, { path: "/", sameSite: "lax" });
  cookies.set("user-id", userId, { path: "/", sameSite: "lax" });
  cookies.set("device-id", deviceId, { path: "/", sameSite: "lax" });
};

/*
 * Clears locally saved cookies
 */
export const clearCookies = () => {
  cookies.remove("auth-token", { path: "/", sameSite: "lax" });
  cookies.remove("user-id", { path: "/", sameSite: "lax" });
  cookies.remove("device-id", { path: "/", sameSite: "lax" });
  cookies.remove("company-id", { path: "/", sameSite: "lax" });
  cookies.remove("company-name", { path: "/", sameSite: "lax" });
};

/*
 * Returns list of cookies saved
 */
export const getCookies = () => {
  const authToken = cookies.get("auth-token");
  const userId = cookies.get("user-id");
  const deviceId = cookies.get("device-id");

  // note: Company config is set on localStorage on main platform APP
  let companyConfig = window.localStorage.tfCompanyConfig;
  companyConfig = companyConfig ? JSON.parse(companyConfig) : null;
  const companyId = companyConfig?.id || null;
  const companyName = companyConfig?.name || null;

  handleComanyDataStorage(companyConfig);

  return {
    authToken,
    userId,
    deviceId,
    companyId,
    companyName,
  };
};

/*******************************************************************************
 * HELPERS
 ******************************************************************************/

/*
 * Verifies if a given password is allowed or not
 */
const isPasswordAllowed = async (password: string) => {
  const response = await usersApi.getBlacklistedPasswords(password);

  // If response is 204, then the password is allowed by API
  return response.status === 204;
};

/*
 * Verifies if company is valid or not
 */
const isValidCompany = (company: Company) => {
  return company.status !== "rejected" && company.active;
};

/*
 * Verifies whether a user is company admin or not
 */
const checkIsCompanyAdmin = (user: User) => {
  return (
    user.companyAdmin &&
    user.role === "admin" &&
    user.modules.length &&
    user.modules.includes("admin")
  );
};

/*
 * Verifies whether a user has connectivity license or not
 */
const checkForLicense = (user: User) => {
  return user.modules.includes("connectivity");
};

/*
 * Verifies whether a user accepted FourKites Terms & Conditions
 */
const checkIsTermsAgreed = (user: User) => {
  return user.termsAgreed;
};
/*******************************************************************************
 * API CALLS
 ******************************************************************************/

// TODO: this needs to be moved into its own API file

/*
 * Sets a selected company if user is a super admin
 */
const failedValidation = (emptyCookies = true) => {
  return (dispatch: AppDispatch) => {
    if (emptyCookies) {
      clearCookies();
    }

    dispatch({
      type: VALIDATE_KEY_FAILURE,
    });
  };
};

const getArchbeeJwt = async () => {
  const { authToken, userId, deviceId } = getCookies();

  try {
    const jwt = await usersApi.getArchbeeJwt(authToken, userId, deviceId);
    return jwt;
  } catch (err) {
    console.error("Error fetching Archbee JWT:", err);
    return undefined;
  }
};


/*******************************************************************************
 * USER ACTION CREATORS
 ******************************************************************************/

/*
 * Performs user login
 */
const userLogin = (username: string, password: string) => {
  return async (dispatch: AppDispatch) => {
    dispatch({ type: LOADING });

    try {
      const userResponse = await usersApi.doUserLogin(username, password);
      const { authToken, user, deviceId } = userResponse.data;
      const isSuperAdmin = user.superAdmin;

      let company = null;
      if (!isSuperAdmin) {
        const companyResponse =
          await companiesApi.getCompanyDetailsWithPassword(
            user.company.id,
            username,
            password
          );
        company = companyResponse.data.company;
      }

      // a valid user role is admin
      // allowing non admins also into FourKites Connect since SELF-2290
      // const validUserRole = user.role === "admin" || isSuperAdmin;
      // user's company must be active (active: true) and
      // not rejected (status !== 'rejected')
      const validCompany = user.superAdmin ? true : isValidCompany(company);

      if (validCompany) {
        dispatch({
          type: SUCCESSFUL_LOGIN,
          currentUser: userResponse.data.user,
          isSuperAdmin: user.superAdmin,
          companyId: isSuperAdmin ? null : user.company.id,
          companyType: isSuperAdmin ? null : user.companyType,
          companyName: null,
          companyCountry: null,
          companyLicenses: [],
          directAssignment: {
            apiToken: user.company.directTrackingInfoAssignmentGuid,
            urlToken: null, // Where does the token live?
          },
          isCompanyAdmin: checkIsCompanyAdmin(user),
          termsAgreed: checkIsTermsAgreed(user),
        });

        saveCookie(authToken, user.userId, deviceId);
      } else {
        dispatch({
          type: FAILED_LOGIN,
          payload: "Invalid Permissions",
        });
      }
    } catch (err) {
      console.error(err);
      dispatch({
        type: FAILED_LOGIN,
        payload: "Invalid Email or Password",
      });
    }
  };
};

/*
 * Validates user Cookies
 */
const validateCookies = () => {
  return async (dispatch: AppDispatch) => {
    dispatch({ type: LOADING });

    try {
      const currentUserDetails = sessionStorage.getItem('fkcUserDetails');
      const userResponse = currentUserDetails !== null ? JSON.parse(currentUserDetails) : await usersApi.getUserDetails();

      const { user } = userResponse?.data;
      const isSuperAdmin = user.superAdmin;
      // const validUserRole = user.role === "admin" || isSuperAdmin;
      // If user is admin, they should have connectivity license to access app
      const hasConnectivityLicense = isSuperAdmin
        ? true
        : checkForLicense(user);

      if (hasConnectivityLicense) {
        if (isSuperAdmin) {
          dispatch(validateSuperAdmin(userResponse));
        } else {
          dispatch(validateNonSuperAdmin(userResponse));
        }

        // Return valid cookies
        return true;
      } else {
        dispatch(failedValidation(false));
        // Return invalid cookies
        return false;
      }
    } catch (err) {
      console.log(err);
      dispatch(failedValidation());
      // Return invalid cookies
      return false;
    }
  };
};

/*
 * Logs out an user
 */
const userLogout = (onLogout: LogoutFunction) => {
  return async (dispatch: AppDispatch) => {
    try {
      await usersApi.doUserLogout();
      clearCookies();
      dispatch({
        type: SUCCESSFUL_LOGOUT,
      });

      onLogout();
    } catch (err: any) {
      console.error(err);
      clearCookies();
      dispatch({ type: FAILED_LOGOUT, payload: err.error });
    } finally {
      sessionStorage.removeItem("fkcUserDetails");
    }
  };
};

/*
 * Set terms and conditions agreed
 */
const setTermsAgreed = () => {
  const { userId } = getCookies();
  return async () => {
    try {
      await usersApi.updateTermsAgreed(userId);
    } catch (err) {
      console.error(err);
    }
  };
};

/*
 * Reset password using email id
 */
const passwordReset = (emailId: string) => {
  // const response = usersApi.doPasswordReset(emailId);
  // return response;
  return async () => {
    try {
      const response = await usersApi.doPasswordReset(emailId);
      return response.data.statusCode;
    } catch (err) {
      console.error(err);
      return 400;
    }
  };
};

/*******************************************************************************
 * USER AND COMPANY ACTION CREATORS
 ******************************************************************************/

/*
 * Validates if user is a super admin or not
 */
const validateSuperAdmin = (userResponse: UserResponse) => {
  const { companyId } = getCookies();
  return async (dispatch: AppDispatch) => {
    dispatch({ type: LOADING });

    // Scenarios:
    // 1. no company id stored in cookies
    // 2. company id stored in cookies -> invalid company
    // 3. company id stored in cookies -> valid company
    try {
      if (!companyId) {
        // scenario 1: if no company id in cookies, navigate to intro page
        dispatch({
          type: VALIDATE_KEY_SUCCESS,
          currentUser: userResponse.data.user,
          isSuperAdmin: true,
          companyId: null,
          companyType: null,
          companyName: null,
          companyCountry: null,
          companyLicenses: [],
          directAssignment: {
            apiToken: null,
            urlToken: null, // Where does the token live?
          },
          isCompanyAdmin: checkIsCompanyAdmin(userResponse.data.user),
          termsAgreed: checkIsTermsAgreed(userResponse.data.user),
        });
      } else if (companyId) {
        // Check company validation if company id exists in cookies
        const companyResponse = await companiesApi.getCompanyDetailsWithToken(
          companyId
        );
        const { company } = companyResponse.data;

        const validCompany = isValidCompany(company);
        if (!validCompany) {
          // scenario 2: company is not valid
          dispatch({
            type: VALIDATE_KEY_SUCCESS,
            currentUser: userResponse.data.user,
            isSuperAdmin: true,
            companyId: null,
            companyType: null,
            companyName: null,
            companyCountry: null,
            companyLicenses: [],
            directAssignment: {
              apiToken: null,
              urlToken: null, // Where does the token live?
            },
            isCompanyAdmin: checkIsCompanyAdmin(userResponse.data.user),
            termsAgreed: checkIsTermsAgreed(userResponse.data.user),
          });
        } else {
          // scenario 3: company is valid
          dispatch({
            type: VALIDATE_KEY_SUCCESS,
            currentUser: userResponse.data.user,
            isSuperAdmin: true,
            companyId: company.id,
            companyType: company.type,
            companyName: company.name,
            companyCountry: company.country,
            companyLicenses: company.modules,
            directAssignment: {
              apiToken: company.directTrackingInfoAssignmentGuid,
              urlToken: null, // Where does the token live?
            },
            isCompanyAdmin: checkIsCompanyAdmin(userResponse.data.user),
            termsAgreed: checkIsTermsAgreed(userResponse.data.user),
          });
        }
      }
    } catch (err) {
      dispatch(failedValidation());
    }
  };
};

/*
 * Validates if user is a regular user
 */
const validateNonSuperAdmin = (userResponse: UserResponse) => {
  return async (dispatch: AppDispatch) => {
    dispatch({ type: LOADING });

    try {
      const companyId = userResponse.data.user.companyId;
      const companyResponse = await companiesApi.getCompanyDetailsWithToken(
        companyId
      );

      const { company } = companyResponse.data;
      const validCompany = isValidCompany(company);

      if (validCompany) {
        dispatch({
          type: VALIDATE_KEY_SUCCESS,
          currentUser: userResponse.data.user,
          isSuperAdmin: false,
          companyId: company.id,
          companyType: company.type,
          companyName: company.name,
          companyCountry: company.country,
          companyLicenses: company.modules,
          directAssignment: {
            apiToken: company.directTrackingInfoAssignmentGuid,
            urlToken: null, // Where does the token live?
          },
          isCompanyAdmin: checkIsCompanyAdmin(userResponse.data.user),
          termsAgreed: checkIsTermsAgreed(userResponse.data.user),
        });
      } else {
        dispatch(failedValidation());
      }
    } catch (err) {
      dispatch(failedValidation());
    }
  };
};

/*
 * Sets a selected company if user is a super admin
 */
const setSelectedCompany = (companyId: string) => {
  return async (dispatch: AppDispatch, getState: any) => {
    const users = getState().users;
    if (companyId === users.companyId) {
      return;
    }

    dispatch({ type: LOADING });

    try {
      const companyResponse = await companiesApi.getCompanyDetailsWithToken(
        companyId
      );
      const { company } = companyResponse.data;
      const validCompany = isValidCompany(company);

      if (validCompany) {
        if(users.isSuperAdmin) {
          dispatch({
            type: VALIDATE_KEY_SUCCESS,
            currentUser: users.currentUser,
            isSuperAdmin: true,
            companyId: company.id,
            companyType: company.type,
            companyName: company.name,
            companyCountry: company.country,
            companyLicenses: company.modules,
            directAssignment: {
              apiToken: company.directTrackingInfoAssignmentGuid,
              urlToken: null, // Where does the token live?
            },
            isCompanyAdmin: checkIsCompanyAdmin(users.currentUser),
            termsAgreed: checkIsTermsAgreed(users.currentUser),
          });
        } else {
          const userResponse = await usersApi.getUserDetailsWithCompanyID(company.id);
          const { user } = userResponse.data;
          sessionStorage.setItem("fkcUserDetails", JSON.stringify(userResponse));
          console.log(user);
          dispatch({
            type: VALIDATE_KEY_SUCCESS,
            currentUser: userResponse.data.user,
            isSuperAdmin: false,
            companyId: company.id,
            companyType: company.type,
            companyName: company.name,
            companyCountry: company.country,
            companyLicenses: company.modules,
            directAssignment: {
              apiToken: company.directTrackingInfoAssignmentGuid,
              urlToken: null, // Where does the token live?
            },
            isCompanyAdmin: checkIsCompanyAdmin(userResponse.data.user),
            termsAgreed: checkIsTermsAgreed(userResponse.data.user),
          });
        }
      } else {
        dispatch(failedValidation());
      }
    } catch (err) {
      dispatch(failedValidation());
    }
  };
};

/*
 * This is used to store company defined externally
 */
const setExternalCompany = (company: ExternalCompany) => {
  return async (dispatch: AppDispatch) => {
    dispatch({
      type: SET_EXTERNAL_COMPANY_ID,
      companyId: company.companyId,
      companyType: company.companyType,
      companyName: company.companyName,
    });
  };
};

/*******************************************************************************
 * REDUCER
 ******************************************************************************/

// TODO: change isSuperAdmin default to false
const initialState = {
  currentUser: null,
  companyId: null,
  companyType: null,
  companyName: null,
  companyCountry: null,
  error: null,
  loading: false,
  isSuperAdmin: null,
  directAssignment: {
    apiToken: null,
    urlToken: null,
  },
  isCompanyAdmin: false,
  termsAgreed: false,
  companyLicenses: [],
};

const UsersReducer = (state = initialState, action: any) => {
  switch (action.type) {
    case LOADING:
      return {
        ...state,
        loading: true,
      };
    case VALIDATE_KEY_SUCCESS:
      return {
        ...state,
        loading: false,
        currentUser: action.currentUser,
        isSuperAdmin: action.isSuperAdmin,
        companyId: action.companyId,
        companyType: action.companyType,
        companyName: action.companyName,
        companyCountry: action.companyCountry,
        companyLicenses: action.companyLicenses,
        // TODO: where is the best way for this to live?
        directAssignment: {
          apiToken: action.directAssignment.apiToken,
          urlToken: action.directAssignment.urlToken,
        },
        isCompanyAdmin: action.isCompanyAdmin,
        termsAgreed: action.termsAgreed,
      };
    case VALIDATE_KEY_FAILURE:
      return {
        ...state,
        loading: false,
      };
    case SUCCESSFUL_LOGIN:
      return {
        ...state,
        error: null,
        loading: false,
        currentUser: action.currentUser,
        isSuperAdmin: action.isSuperAdmin,
        companyId: action.companyId,
        companyType: action.companyType,
        companyName: action.companyName,
        companyCountry: action.companyCountry,
        companyLicenses: action.companyLicenses,
        // TODO: where is the best way for this to live?
        directAssignment: {
          apiToken: action.directAssignment.apiToken,
          urlToken: action.directAssignment.urlToken,
        },
        isCompanyAdmin: action.isCompanyAdmin,
        termsAgreed: action.termsAgreed,
      };
    case FAILED_LOGIN:
      return {
        ...state,
        loading: false,
        error: action.payload,
      };
    case SUCCESSFUL_LOGOUT:
      return {
        ...initialState,
        loading: false,
      };
    case FAILED_LOGOUT:
      return {
        ...state,
        error: action.payload,
        loading: false,
      };
    case SET_EXTERNAL_COMPANY_ID:
      return {
        ...state,
        companyId: action.companyId,
        companyType: action.companyType,
        companyName: action.companyName,
      };
    default:
      return state;
  }
};

/*******************************************************************************
 * SELECTORS
 ******************************************************************************/

const getCurrentUser = (state: any) => state[STORE_MOUNT_POINT].currentUser;
const getAuthError = (state: any) => state[STORE_MOUNT_POINT].error;
const getLoadingStatus = (state: any) => state[STORE_MOUNT_POINT].loading;
const getIsSuperAdmin = (state: any) => state[STORE_MOUNT_POINT].isSuperAdmin;
const getCompanyId = (state: any) => state[STORE_MOUNT_POINT].companyId;
const getCompanyType = (state: any) => state[STORE_MOUNT_POINT].companyType;
const getCompanyName = (state: any) => state[STORE_MOUNT_POINT].companyName;
const getCompanyCountry = (state: any) =>
  state[STORE_MOUNT_POINT].companyCountry;
const getCompanyDirectAssignmentInfo = (state: any) =>
  state[STORE_MOUNT_POINT].directAssignment;
const getIsCompanyAdmin = (state: any) =>
  state[STORE_MOUNT_POINT].isCompanyAdmin;
const getTermsAgreed = (state: any) => state[STORE_MOUNT_POINT].termsAgreed;
const getCompanyLicenses = (state: any) =>
  state[STORE_MOUNT_POINT].companyLicenses;

export const UsersState = {
  mountPoint: STORE_MOUNT_POINT,
  actions: {
    SUCCESSFUL_LOGIN,
    SUCCESSFUL_LOGOUT,
  },
  actionCreators: {
    userLogin,
    userLogout,
    validateCookies,
    setSelectedCompany,
    setExternalCompany,
    setTermsAgreed,
    passwordReset,
  },
  helpers: {
    isPasswordAllowed,
    getArchbeeJwt,
  },
  selectors: {
    getCurrentUser,
    getCompanyId,
    getCompanyType,
    getCompanyName,
    getCompanyCountry,
    getAuthError,
    getLoadingStatus,
    getIsSuperAdmin,
    getCompanyDirectAssignmentInfo,
    getIsCompanyAdmin,
    getTermsAgreed,
    getCompanyLicenses,
  },
  reducer: UsersReducer,
};

export default UsersState;
