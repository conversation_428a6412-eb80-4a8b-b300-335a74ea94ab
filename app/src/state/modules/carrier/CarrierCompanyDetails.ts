import {
  createSlice,
  createAsyncThunk,
  createSelector,
} from "@reduxjs/toolkit";
import { RootState, Selector } from "state/store";

import carrierCompanyDetailsApi from "api/carrier/CarrierCompanyDetailsApi";

const MODULE_NAME = "carrierCompanyDetails";

type CarrierCompanyDetailsData = {
  carrierCompanyDetails: {
    data: any;
    loading: boolean;
    editing: boolean;
    error: boolean;
  };
};

/*******************************************************************************
 * ACTIONS
 ******************************************************************************/

/*
 * Gets the details for this carrier company
 */
export const retrieveCarrierCompanyDetails = createAsyncThunk<
  any,
  { carrierId: string },
  {}
>(
  `${MODULE_NAME}/retrieveCarrierCompanyDetails`,
  async ({ carrierId }, thunkAPI) => {
    const response =
      await carrierCompanyDetailsApi.retrieveCarrierCompanyDetails(carrierId);

    return response?.data;
  }
);

/*
 * Updates the details for this carrier company
 */
export const updateCarrierCompanyDetails = createAsyncThunk<
  any,
  { carrierId: string; carrierCompanyDetails: any },
  {}
>(
  `${MODULE_NAME}/updateCarrierCompanyDetails`,
  async ({ carrierId, carrierCompanyDetails }, thunkAPI) => {
    const response = await carrierCompanyDetailsApi.updateCarrierCompanyDetails(
      carrierId,
      carrierCompanyDetails
    );

    return response?.data;
  }
);

/*******************************************************************************
 * SELECTORS
 ******************************************************************************/

export const carrierCompanyDetails = (): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.carrierCompanyDetails?.data,
    (data: any) => data
  );

export const isLoadingCarrierCompanyDetails = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.carrierCompanyDetails?.loading,
    (data: boolean) => data
  );

export const isEditingCarrierCompanyDetails = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.carrierCompanyDetails?.editing,
    (data: boolean) => data
  );

/*******************************************************************************
 * SLICE
 ******************************************************************************/

// Define the initial state using that type
const initialCarrierCompanyDetailsState = {
  data: {},
  loading: true,
  editing: false,
  error: false,
};
const initialState: CarrierCompanyDetailsData = {
  carrierCompanyDetails: initialCarrierCompanyDetailsState,
};

const carrierInvitationsSlice = createSlice({
  name: MODULE_NAME,
  initialState: initialState,
  reducers: {},
  extraReducers: (builder) => {
    /***************************************************************************
     * RETRIEVE COMPANY DETAILS
     **************************************************************************/

    builder.addCase(
      retrieveCarrierCompanyDetails.fulfilled,
      (state, action) => {
        state.carrierCompanyDetails.data = action.payload;
        state.carrierCompanyDetails.loading = false;
      }
    );
    builder.addCase(retrieveCarrierCompanyDetails.rejected, (state, action) => {
      state.carrierCompanyDetails.loading = false;
    });
    builder.addCase(retrieveCarrierCompanyDetails.pending, (state, action) => {
      state.carrierCompanyDetails.loading = true;
    });

    /***************************************************************************
     * EDIT COMPANY DETAILS
     **************************************************************************/

    builder.addCase(updateCarrierCompanyDetails.fulfilled, (state, action) => {
      state.carrierCompanyDetails.editing = false;
    });
    builder.addCase(updateCarrierCompanyDetails.rejected, (state, action) => {
      state.carrierCompanyDetails.editing = false;
    });
    builder.addCase(updateCarrierCompanyDetails.pending, (state, action) => {
      state.carrierCompanyDetails.editing = true;
    });
  },
});

export const CarrierCompanyDetailsState = {
  actions: {
    retrieveCarrierCompanyDetails,
    updateCarrierCompanyDetails,
  },
  selectors: {
    carrierCompanyDetails,
    isLoadingCarrierCompanyDetails,
    isEditingCarrierCompanyDetails,
  },
};

// Action creators are generated for each case reducer function
export default carrierInvitationsSlice;
