import {
  createSlice,
  createAsyncThunk,
  createSelector,
} from "@reduxjs/toolkit";
import { RootState, Selector } from "state/store";

import CarrierConfigurationsApi from "api/carrier/CarrierConfigurationsApi";

const MODULE_NAME = "carrierConfigurations";

type CarrierConfigurationsData = {
  carrierConfigurations: {
    data: any;
    loading: boolean;
    editing: boolean;
    error: boolean;
  };
};

/*******************************************************************************
 * ACTIONS
 ******************************************************************************/

export const retrieveCarrierConfigurations = createAsyncThunk<
  any,
  { carrierId: string; mode: string },
  {}
>(
  `${MODULE_NAME}/retrieveCarrierConfigurations`,
  async ({ carrierId, mode  }, thunkAPI) => {
    const response =
      await CarrierConfigurationsApi.retrieveLtlConfigurationsForCarrier(
        carrierId,
        mode
      );

    return response?.data;
  }
);

export const updateCarrierConfigurations = createAsyncThunk<
  any,
  { carrierId: string; ltlConfigurations: any },
  {}
>(
  `${MODULE_NAME}/updateCarrierConfigurations`,
  async ({ carrierId, ltlConfigurations }, thunkAPI) => {
    const response = await CarrierConfigurationsApi.updateLtlConfigurations(
      carrierId,
      ltlConfigurations
    );

    return response?.data;
  }
);

/*******************************************************************************
 * SELECTORS
 ******************************************************************************/

export const carrierConfigurations = (): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.carrierConfigurations?.data,
    (data: any) => data
  );

export const isLoadingCarrierConfigurations = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.carrierConfigurations?.loading,
    (data: boolean) => data
  );

export const isEditingCarrierConfigurations = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.carrierConfigurations?.editing,
    (data: boolean) => data
  );

/*******************************************************************************
 * SLICE
 ******************************************************************************/

const initialCarrierCompanyDetailsState = {
  data: {},
  loading: true,
  editing: false,
  error: false,
};
const initialState: CarrierConfigurationsData = {
  carrierConfigurations: initialCarrierCompanyDetailsState,
};

const carrierConfigurationsSlice = createSlice({
  name: MODULE_NAME,
  initialState: initialState,
  reducers: {},
  extraReducers: (builder) => {
    /***************************************************************************
     * RETRIEVE COMPANY CONFIGURATIONS
     **************************************************************************/

    builder.addCase(
      retrieveCarrierConfigurations.fulfilled,
      (state, action) => {
        state.carrierConfigurations.data = action.payload;
        state.carrierConfigurations.loading = false;
      }
    );
    builder.addCase(retrieveCarrierConfigurations.rejected, (state, action) => {
      state.carrierConfigurations.loading = false;
    });
    builder.addCase(retrieveCarrierConfigurations.pending, (state, action) => {
      state.carrierConfigurations.loading = true;
    });

    /***************************************************************************
     * EDIT COMPANY CONFIGURATIONS
     **************************************************************************/

    builder.addCase(updateCarrierConfigurations.fulfilled, (state, action) => {
      state.carrierConfigurations.editing = false;
    });
    builder.addCase(updateCarrierConfigurations.rejected, (state, action) => {
      state.carrierConfigurations.editing = false;
    });
    builder.addCase(updateCarrierConfigurations.pending, (state, action) => {
      state.carrierConfigurations.editing = true;
    });
  },
});

export const CarrierConfigurationsState = {
  actions: {
    retrieveCarrierConfigurations,
    updateCarrierConfigurations,
  },
  selectors: {
    carrierConfigurations,
    isLoadingCarrierConfigurations,
    isEditingCarrierConfigurations,
  },
};

export default carrierConfigurationsSlice;
