import {
  createSlice,
  createAsyncThunk,
  createSelector,
  PayloadAction,
} from "@reduxjs/toolkit";
import { RootState, Selector } from "state/store";

import carrierFleetAssetsApi from "api/carrier/CarrierFleetAssetsApi";
import { FleetMode, typedFleetModeFromAction } from "state/BaseTypes";

/*******************************************************************************
 * TYPES
 ******************************************************************************/

const MODULE_NAME = "carrierFleetAssets";

type CarrierFleetAssetsModeData = {
  data: any[];
  filters: {
    assetNumber: string | null;
    loadStatus: "loads_assigned" | "available" | null;
    trackingStatus: "tracking" | "not_tracking" | null;
    assetType: string | null;
  };
  retrieving: boolean;
  editing: boolean;
  error: boolean;
  pagination: {
    count: number;
    current: number;
    size: number;
    total: number;
  };
};

type CarrierFleetAssetsData = {
  assets: {
    otr: CarrierFleetAssetsModeData;
    ocean: CarrierFleetAssetsModeData;
    air: CarrierFleetAssetsModeData;
    rail: CarrierFleetAssetsModeData;
  };
  assetDetails: {
    location: {
      data: any;
      retrieving: boolean;
      error: boolean;
    };
    pingLocation: {
      data: {
        lat: string | undefined;
        lon: string | undefined;
        locatedAt: string | undefined;
        speed: string | undefined;
        provider: string | undefined;
      };
      retrieving: boolean;
      error: boolean;
    };
  };
};

/*******************************************************************************
 * ACTIONS
 ******************************************************************************/

/**************** CARRIER ASSETS ****************/

/*
 * Retrieves list of fleet assets
 */
export const retrieveFleetAssets = createAsyncThunk<
  any,
  {
    fleetMode: FleetMode;
    carrierId: string;
    fleetId: number | string;
    pageSize?: number;
    pageIndex?: number;
  },
  {}
>(
  `${MODULE_NAME}/retrieveFleetAssets`,
  async ({ carrierId, fleetMode, fleetId, pageIndex, pageSize }, thunkAPI) => {
    // Get filters for call
    const state: any = thunkAPI.getState();
    const pagination = state[MODULE_NAME]?.assets[fleetMode]?.pagination;
    const { size, current } = pagination;

    const filters = state[MODULE_NAME]?.assets[fleetMode]?.filters;
    const { assetNumber, loadStatus, trackingStatus, assetType } = filters;

    const response = await carrierFleetAssetsApi.retrieveFleetAssets(
      carrierId,
      fleetId,
      assetNumber,
      assetType,
      loadStatus,
      trackingStatus,
      pageIndex || current,
      pageSize || size
    );

    return response?.data;
  }
);

/*
 * Create fleet assets
 */
export const createFleetAssets = createAsyncThunk<
  any,
  {
    fleetMode: FleetMode;
    carrierId: string;
    fleetId: number | string;
    assets: string[];
  },
  {}
>(
  `${MODULE_NAME}/createFleetAssets`,
  async ({ carrierId, fleetId, assets }, thunkAPI) => {
    const response = await carrierFleetAssetsApi.createFleetAssets(
      carrierId,
      fleetId,
      assets
    );

    return response?.data?.details;
  }
);

/*
 * Delete fleet asset
 */
export const deleteFleetAsset = createAsyncThunk<
  any,
  {
    fleetMode: FleetMode;
    carrierId: string;
    fleetId: number | string;
    assetId: string;
  },
  {}
>(
  `${MODULE_NAME}/deleteFleetAssets`,
  async ({ carrierId, fleetId, assetId }, thunkAPI) => {
    const response = await carrierFleetAssetsApi.deleteFleetAsset(
      carrierId,
      fleetId,
      assetId
    );

    return response?.data;
  }
);

/*
 * Retrieves ping location of fleet asset, using the GPS provider to query it
 */
export const retrieveFleetAssetPingLocation = createAsyncThunk<
  any,
  {
    carrierId: string;
    assetId: string;
    assetType: string;
  },
  {}
>(
  `${MODULE_NAME}/retrieveFleetPingAssetLocation`,
  async ({ carrierId, assetId, assetType }, thunkAPI) => {
    const response = await carrierFleetAssetsApi.retrieveFleetAssetPingLocation(
      carrierId,
      assetId,
      assetType
    );

    return response?.data;
  }
);

/*
 * Retrieves location of fleet asset
 */
export const retrieveFleetAssetLocation = createAsyncThunk<
  any,
  {
    carrierId: string;
    fleetId: number | string;
    assetId: string;
  },
  {}
>(
  `${MODULE_NAME}/retrieveFleetAssetLocation`,
  async ({ carrierId, fleetId, assetId }, thunkAPI) => {
    const response = await carrierFleetAssetsApi.retrieveFleetAssetLocation(
      carrierId,
      fleetId,
      assetId
    );

    return response?.data;
  }
);

const onClearError = (state: any, { payload }: PayloadAction<any>) => {
  state.assets[payload.fleetMode].error = false;
};

const onChangeAssetsFilters = (state: any, { payload }: PayloadAction<any>) => {
  const { fleetMode, assetNumber, loadStatus, trackingStatus, assetType } =
    payload;

  if (assetType !== undefined) {
    state.assets[fleetMode].filters.assetType = assetType;
  }

  if (loadStatus !== undefined) {
    state.assets[fleetMode].filters.loadStatus = loadStatus;
  }

  if (trackingStatus !== undefined) {
    state.assets[fleetMode].filters.trackingStatus = trackingStatus;
  }

  // Change filterBy parameter based on user query
  if (assetNumber != null) {
    state.assets[fleetMode].filters.assetNumber = assetNumber;
  }
};

/*******************************************************************************
 * SELECTORS
 ******************************************************************************/

const fleetAssetsByMode = (fleetMode: FleetMode): Selector<any[]> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.assets[fleetMode]?.data,
    (data: any[]) => data
  );

const fleetAssetsPaginationByMode = (fleetMode: FleetMode): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.assets[fleetMode]?.pagination,
    (data: any) => data
  );

export const isRetrievingAssetsByMode = (
  fleetMode: FleetMode
): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.assets[fleetMode]?.retrieving,
    (data: boolean) => data
  );

export const isEditingAssetsByMode = (
  fleetMode: FleetMode
): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.assets[fleetMode]?.editing,
    (data: boolean) => data
  );

export const hasErrorByMode = (fleetMode: FleetMode): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.assets[fleetMode]?.error,
    (data: boolean) => data
  );

export const assetsFiltersByMode = (fleetMode: FleetMode): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.assets[fleetMode]?.filters,
    (data: any) => data
  );

// Asset details location

export const assetLocation = (): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.assetDetails?.location?.data,
    (data: any) => data
  );

export const isRetrievingAssetLocation = (): Selector<boolean> =>
  createSelector(
    (state: RootState) =>
      state[MODULE_NAME]?.assetDetails?.location?.retrieving,
    (data: boolean) => data
  );

// Asset ping location

export const assetPingLocation = (): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.assetDetails?.pingLocation?.data,
    (data: any) => data
  );

export const isRetrievingAssetPingLocation = (): Selector<boolean> =>
  createSelector(
    (state: RootState) =>
      state[MODULE_NAME]?.assetDetails?.pingLocation?.retrieving,
    (data: boolean) => data
  );

/*******************************************************************************
 * SLICE
 ******************************************************************************/

// Define the initial state using that type
const initialAssetsState = {
  data: [] as any[],
  filters: {
    assetNumber: null,
    loadStatus: null,
    trackingStatus: null,
    assetType: null,
  },
  pagination: {
    current: 1,
    size: 1,
    total: 0,
    count: 0,
  },
  retrieving: false,
  editing: false,
  error: false,
};

const initialState: CarrierFleetAssetsData = {
  assets: {
    otr: initialAssetsState,
    ocean: initialAssetsState,
    rail: initialAssetsState,
    air: initialAssetsState,
  },
  assetDetails: {
    location: {
      data: null,
      retrieving: false,
      error: false,
    },
    pingLocation: {
      data: {
        lat: undefined,
        lon: undefined,
        locatedAt: undefined,
        speed: undefined,
        provider: undefined,
      },
      retrieving: false,
      error: false,
    },
  },
};

const fleetAssetsSlice = createSlice({
  name: MODULE_NAME,
  initialState,
  reducers: {
    clearError: onClearError,
    changeAssetsFilters: onChangeAssetsFilters,
  },
  extraReducers: (builder) => {
    /***************************************************************************
     * CARRIER ASSETS
     **************************************************************************/

    // FLEET ASSETS LIST
    builder.addCase(retrieveFleetAssets.fulfilled, (state, action) => {
      state.assets[typedFleetModeFromAction(action)].data =
        action.payload.details;
      state.assets[typedFleetModeFromAction(action)].pagination =
        action.payload.pagination;
      state.assets[typedFleetModeFromAction(action)].retrieving = false;
      state.assets[typedFleetModeFromAction(action)].error = false;
    });
    builder.addCase(retrieveFleetAssets.rejected, (state, action) => {
      state.assets[typedFleetModeFromAction(action)].retrieving = false;
      state.assets[typedFleetModeFromAction(action)].error = true;
    });
    builder.addCase(retrieveFleetAssets.pending, (state, action) => {
      state.assets[typedFleetModeFromAction(action)].retrieving = true;
      state.assets[typedFleetModeFromAction(action)].error = false;
    });

    [createFleetAssets, deleteFleetAsset].forEach((actionCreator) => {
      builder.addCase(actionCreator.fulfilled, (state, action) => {
        state.assets[typedFleetModeFromAction(action)].editing = false;
        state.assets[typedFleetModeFromAction(action)].error = false;
      });
      builder.addCase(actionCreator.pending, (state, action) => {
        state.assets[typedFleetModeFromAction(action)].editing = true;
        state.assets[typedFleetModeFromAction(action)].error = false;
      });
      builder.addCase(actionCreator.rejected, (state, action) => {
        state.assets[typedFleetModeFromAction(action)].editing = false;
        state.assets[typedFleetModeFromAction(action)].error = true;
      });
    });

    /***************************************************************************
     * ASSET LOCATION
     **************************************************************************/

    builder.addCase(retrieveFleetAssetLocation.fulfilled, (state, action) => {
      state.assetDetails.location.data = action.payload;
      state.assetDetails.location.retrieving = false;
      state.assetDetails.location.error = false;
    });
    builder.addCase(retrieveFleetAssetLocation.rejected, (state) => {
      state.assetDetails.location.retrieving = false;
      state.assetDetails.location.error = true;
    });
    builder.addCase(retrieveFleetAssetLocation.pending, (state) => {
      state.assetDetails.location.retrieving = true;
      state.assetDetails.location.error = false;
    });

    /***************************************************************************
     * ASSET PING LOCATION
     **************************************************************************/

    builder.addCase(
      retrieveFleetAssetPingLocation.fulfilled,
      (state, action) => {
        const { latitude, longitude, located_at, speed, provider } =
          action.payload;

        state.assetDetails.pingLocation.data = {
          lat: latitude,
          lon: longitude,
          locatedAt: located_at,
          speed: speed,
          provider: provider,
        };
        state.assetDetails.pingLocation.retrieving = false;
        state.assetDetails.pingLocation.error = false;
      }
    );
    builder.addCase(retrieveFleetAssetPingLocation.rejected, (state) => {
      state.assetDetails.pingLocation.retrieving = false;
      state.assetDetails.pingLocation.error = true;
    });
    builder.addCase(retrieveFleetAssetPingLocation.pending, (state) => {
      state.assetDetails.pingLocation.retrieving = true;
      state.assetDetails.pingLocation.error = false;
    });
  },
});

export const CarrierFleetAssetsState = {
  actions: {
    retrieveFleetAssets,
    createFleetAssets,
    // TODO: update,
    deleteFleetAsset,
    clearError: fleetAssetsSlice.actions.clearError,
    changeAssetsFilters: fleetAssetsSlice.actions.changeAssetsFilters,
    // Asset details
    retrieveFleetAssetLocation,
    // Ping location
    retrieveFleetAssetPingLocation,
  },
  selectors: {
    fleetAssetsByMode,
    fleetAssetsPaginationByMode,
    isRetrievingAssetsByMode,
    isEditingAssetsByMode,
    hasErrorByMode,
    assetsFiltersByMode,
    // Asset details location
    assetLocation,
    isRetrievingAssetLocation,
    // Asset ping location
    assetPingLocation,
    isRetrievingAssetPingLocation,
  },
};

// Action creators are generated for each case reducer function
export default fleetAssetsSlice;
