import {
  createSlice,
  createAsyncThunk,
  createSelector,
  PayloadAction,
} from "@reduxjs/toolkit";
import { RootState, Selector } from "state/store";
import { FleetMode, typedFleetModeFromAction } from "state/BaseTypes";

import carrierFleetAssetsApi from "api/carrier/CarrierFleetAssetsApi";

/*******************************************************************************
 * TYPES
 ******************************************************************************/

const MODULE_NAME = "carrierFleets";

type CarrierFleetModeData = {
  data: any[];
  retrieving: boolean;
  editing: boolean;
  error: boolean;
};

type CarrierFleetsData = {
  fleets: {
    otr: CarrierFleetModeData;
    ocean: CarrierFleetModeData;
    air: CarrierFleetModeData;
    rail: CarrierFleetModeData;
  };
  selectedFleetId: string | undefined;
};

/*******************************************************************************
 * ACTIONS
 ******************************************************************************/

/**************** CARRIER FLEETS ****************/

/*
 * Create carrier on assets service
 */
export const createCarrier = createAsyncThunk<
  any,
  {
    carrierId: string;
  },
  {}
>(`${MODULE_NAME}/createCarrier`, async ({ carrierId }, thunkAPI) => {
  const response = await carrierFleetAssetsApi.createCarrier(carrierId);

  return response?.data?.details;
});

/*
 * Retrieves list of fleets for this carrier
 */
export const retrieveFleets = createAsyncThunk<
  any,
  { carrierId: string; fleetMode: FleetMode; isDefault?: boolean },
  {}
>(
  `${MODULE_NAME}/retrieveFleets`,
  async ({ carrierId, fleetMode, isDefault = false }, thunkAPI) => {
    const response = await carrierFleetAssetsApi.retrieveFleets(
      carrierId,
      fleetMode,
      isDefault
    );

    return response.data;
  }
);

/*
 * Create fleet assets
 */
export const createFleet = createAsyncThunk<
  any,
  {
    carrierId: string;
    fleetMode: FleetMode;
    fleetName: string;
  },
  {}
>(
  `${MODULE_NAME}/createFleet`,
  async ({ carrierId, fleetMode, fleetName }, thunkAPI) => {
    const response = await carrierFleetAssetsApi.createFleet(
      carrierId,
      fleetMode,
      fleetName
    );

    return response.data;
  }
);

/*
 * Delete fleet asset
 */
export const deleteFleet = createAsyncThunk<
  any,
  {
    carrierId: string;
    fleetId: number | string;
    fleetMode: FleetMode;
  },
  {}
>(
  `${MODULE_NAME}/deleteFleet`,
  async ({ carrierId, fleetId, fleetMode }, thunkAPI) => {
    const response = await carrierFleetAssetsApi.deleteFleet(
      carrierId,
      fleetId,
      fleetMode
    );

    return response.data;
  }
);

const onClearError = (state: any, { payload }: PayloadAction<any>) => {
  const fleetMode = payload as FleetMode;
  state[fleetMode].error = false;
};

/*******************************************************************************
 * SELECTORS
 ******************************************************************************/

const fleetsByMode = (fleetMode: FleetMode): Selector<any[]> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.fleets[fleetMode]?.data,
    (data: any[]) => data
  );

export const isRetrievingFleetsByMode = (
  fleetMode: FleetMode
): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.fleets[fleetMode]?.retrieving,
    (data: boolean) => data
  );

export const isEditingFleetsByMode = (
  fleetMode: FleetMode
): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.fleets[fleetMode]?.editing,
    (data: boolean) => data
  );

export const hasErrorByMode = (fleetMode: FleetMode): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.fleets[fleetMode]?.error,
    (data: boolean) => data
  );

/*******************************************************************************
 * SLICE
 ******************************************************************************/

// Define the initial state using that type
const initialFleetsState = {
  data: [] as any[],
  retrieving: false,
  editing: false,
  error: false,
};

const initialState: CarrierFleetsData = {
  fleets: {
    otr: initialFleetsState,
    ocean: initialFleetsState,
    rail: initialFleetsState,
    air: initialFleetsState,
  },
  selectedFleetId: undefined,
};

const fleetsSlice = createSlice({
  name: MODULE_NAME,
  initialState,
  reducers: {
    clearError: onClearError,
  },
  extraReducers: (builder) => {
    /***************************************************************************
     * CARRIER ASSETS
     **************************************************************************/

    // FLEET ASSETS LIST
    builder.addCase(retrieveFleets.fulfilled, (state, action) => {
      state.fleets[typedFleetModeFromAction(action)].data = action.payload;
      state.fleets[typedFleetModeFromAction(action)].retrieving = false;
      state.fleets[typedFleetModeFromAction(action)].error = false;
    });
    builder.addCase(retrieveFleets.rejected, (state, action) => {
      state.fleets[typedFleetModeFromAction(action)].retrieving = false;
      state.fleets[typedFleetModeFromAction(action)].error = true;
    });
    builder.addCase(retrieveFleets.pending, (state, action) => {
      state.fleets[typedFleetModeFromAction(action)].retrieving = true;
      state.fleets[typedFleetModeFromAction(action)].error = false;
    });

    [createFleet, deleteFleet].forEach((actionCreator) => {
      builder.addCase(actionCreator.fulfilled, (state, action) => {
        state.fleets[typedFleetModeFromAction(action)].editing = false;
        state.fleets[typedFleetModeFromAction(action)].error = false;
      });
      builder.addCase(actionCreator.pending, (state, action) => {
        state.fleets[typedFleetModeFromAction(action)].editing = true;
        state.fleets[typedFleetModeFromAction(action)].error = false;
      });
      builder.addCase(actionCreator.rejected, (state, action) => {
        state.fleets[typedFleetModeFromAction(action)].editing = false;
        state.fleets[typedFleetModeFromAction(action)].error = true;
      });
    });
  },
});

export const CarrierFleetsState = {
  actions: {
    createCarrier,
    // Fleets
    retrieveFleets,
    createFleet,
    // Update,
    deleteFleet,
    clearError: fleetsSlice.actions.clearError,
    // Asset details
  },
  selectors: {
    fleetsByMode,
    isRetrievingFleetsByMode,
    isEditingFleetsByMode,
    hasErrorByMode,
  },
};

// Action creators are generated for each case reducer function
export default fleetsSlice;
