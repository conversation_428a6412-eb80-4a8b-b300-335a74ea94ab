import {
  createSlice,
  createAsyncThunk,
  createSelector,
} from "@reduxjs/toolkit";
import { RootState, Selector } from "state/store";
import { LoadsTrackingMode, typedModeFromAction } from "state/BaseTypes";

import carrierIndicatorsApi from "api/carrier/CarrierIndicatorsApi";

const MODULE_NAME = "carrierIndicators";

type CarrierIndicatorsModeData = {
  data: any;
  loading: boolean;
  error: boolean;
};

type CarrierIndicatorsData = {
  ltl: CarrierIndicatorsModeData;
  ftl: CarrierIndicatorsModeData;
  parcel: CarrierIndicatorsModeData;
  ocean: CarrierIndicatorsModeData;
  air: CarrierIndicatorsModeData; 
};

/*******************************************************************************
 * ACTIONS
 ******************************************************************************/

/*
 * Gets the shipper indicators
 */
export const getCarrierIndicators = createAsyncThunk<
  any,
  { carrierId: string; mode: LoadsTrackingMode },
  {}
>(
  `${MODULE_NAME}/getCarrierIndicators`,
  async ({ carrierId, mode }, thunkAPI) => {
    const response = await carrierIndicatorsApi.getCarrierIndicators(
      carrierId,
      mode
    );

    return response?.data;
  }
);

/*******************************************************************************
 * SELECTORS
 ******************************************************************************/

export const carrierIndicators = (): Selector<any> =>
  createSelector(
    (state: RootState) => {
      const valueOrZero = (value: any) => value || 0;

      // TODO: add other modes later
      const ftl = state[MODULE_NAME].ftl?.data;
      const ltl = state[MODULE_NAME].ltl?.data;
      const parcel = state[MODULE_NAME].parcel?.data;
      const ocean = state[MODULE_NAME].ocean?.data;
      const air = state[MODULE_NAME].air?.data;

      return {
        trackingIntegrations: {
          eldGps: {
            total: valueOrZero(ftl?.tracking_integrations?.eld_gps?.total),
            active: valueOrZero(ftl?.tracking_integrations?.eld_gps?.active),
            error: valueOrZero(ftl?.tracking_integrations?.eld_gps?.error),
            pending: valueOrZero(ftl?.tracking_integrations?.eld_gps?.pending),
          },
        },
        customers: {
          total: {
            total:
              valueOrZero(ftl?.customers?.total) +
              valueOrZero(ltl?.customers?.total) +
              valueOrZero(parcel?.customers?.total) +
              valueOrZero(ocean?.customers?.total) +
              valueOrZero(air?.customers?.total),
            connected:
              valueOrZero(ftl?.customers?.connected) +
              valueOrZero(ltl?.customers?.connected) +
              valueOrZero(parcel?.customers?.connected) +
              valueOrZero(ocean?.customers?.connected) +
              valueOrZero(air?.customers?.connected),
            inProgress:
              valueOrZero(ftl?.customers?.in_progress) +
              valueOrZero(ltl?.customers?.in_progress) +
              valueOrZero(parcel?.customers?.in_progress) +
              valueOrZero(ocean?.customers?.in_progress) +
              valueOrZero(air?.customers?.in_progress),
            disconnected:
              valueOrZero(ftl?.customers?.disconnected) +
              valueOrZero(ltl?.customers?.disconnected) +
              valueOrZero(parcel?.customers?.disconnected) +
              valueOrZero(ocean?.customers?.disconnected) +
              valueOrZero(air?.customers?.disconnected),
          },
          ftl: {
            total: valueOrZero(ftl?.customers?.total),
            connected: valueOrZero(ftl?.customers?.connected),
            inProgress: valueOrZero(ftl?.customers?.in_progress),
            disconnected: valueOrZero(ftl?.customers?.disconnected),
          },
          ltl: {
            total: valueOrZero(ltl?.customers?.total),
            connected: valueOrZero(ltl?.customers?.connected),
            inProgress: valueOrZero(ltl?.customers?.in_progress),
            disconnected: valueOrZero(ltl?.customers?.disconnected),
          },
          parcel: {
            total: valueOrZero(parcel?.customers?.total),
            connected: valueOrZero(parcel?.customers?.connected),
            inProgress: valueOrZero(parcel?.customers?.in_progress),
            disconnected: valueOrZero(parcel?.customers?.disconnected),
          },
          ocean: {
            total: valueOrZero(ocean?.customers?.total),
            connected: valueOrZero(ocean?.customers?.connected),
            inProgress: valueOrZero(ocean?.customers?.in_progress),
            disconnected: valueOrZero(ocean?.customers?.disconnected),
          },
          air: { // Added air mode section
            total: valueOrZero(air?.customers?.total),
            connected: valueOrZero(air?.customers?.connected),
            inProgress: valueOrZero(air?.customers?.in_progress),
            disconnected: valueOrZero(air?.customers?.disconnected),
          },
        },
      };
    },
    (data: any) => data
  );

export const isLoadingCarrierIndicators = (): Selector<boolean> =>
  createSelector(
    (state: RootState) =>
      state[MODULE_NAME].ltl?.loading || state[MODULE_NAME].ftl?.loading,
    (data: boolean) => data
  );

/*******************************************************************************
 * SLICE
 ******************************************************************************/

const initialCarrierIndicatorsModeState = {
  data: {},
  loading: true,
  error: false,
};
const initialShippersIndicatorsState = {
  ltl: initialCarrierIndicatorsModeState,
  ftl: initialCarrierIndicatorsModeState,
  parcel: initialCarrierIndicatorsModeState,
  ocean: initialCarrierIndicatorsModeState,
  air: initialCarrierIndicatorsModeState,
};
const initialState: CarrierIndicatorsData = {
  ...initialShippersIndicatorsState,
};

const carrierIndicatorsSlice = createSlice({
  name: MODULE_NAME,
  initialState: initialState,
  reducers: {},
  extraReducers: (builder) => {
    /***************************************************************************
     * LOAD SHIPPER DETAILS
     **************************************************************************/

    builder.addCase(getCarrierIndicators.fulfilled, (state, action) => {
      state[typedModeFromAction(action)].data = action?.payload;
      state[typedModeFromAction(action)].loading = false;
    });
    builder.addCase(getCarrierIndicators.rejected, (state, action) => {
      state[typedModeFromAction(action)].loading = false;
    });
    builder.addCase(getCarrierIndicators.pending, (state, action) => {
      state[typedModeFromAction(action)].loading = true;
    });
  },
});

export const CarrierIndicatorsState = {
  actions: {
    getCarrierIndicators,
  },
  selectors: {
    carrierIndicators,
    isLoadingCarrierIndicators,
  },
};

export default carrierIndicatorsSlice;
