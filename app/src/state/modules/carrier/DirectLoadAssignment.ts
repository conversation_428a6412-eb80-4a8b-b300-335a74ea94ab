import {
  createSlice,
  createAsyncThunk,
  createSelector,
  PayloadAction,
} from "@reduxjs/toolkit";
import { RootState, Selector } from "state/store";

import directLoadAssignmentApi from "api/carrier/DirectLoadAssignmentApi";

/*******************************************************************************
 * TYPES
 ******************************************************************************/

const MODULE_NAME = "directLoadAssignment";

type DirectLoadAssignmentData = {
  data: any;
  retrieving: boolean;
  error: boolean;
};

/*******************************************************************************
 * ACTIONS
 ******************************************************************************/

/*
 * Gets the information of directly assigned loads
 */
export const retrieveDirectAssignmentLoads = createAsyncThunk<
  any,
  { apiToken: string },
  {}
>(
  `${MODULE_NAME}/retrieveDirectAssignmentLoads`,
  async ({ apiToken }, thunkAPI) => {
    const response =
      await directLoadAssignmentApi.retrieveDirectAssignmentLoads(apiToken);
    return response.data;
  }
);

const onClearError = (state: any, { payload }: PayloadAction<any>) => {
  state.error = false;
};

/*******************************************************************************
 * SELECTORS
 ******************************************************************************/

const directAssignmentLoads = (): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.data,
    (data: any) => data
  );

export const isRetrieving = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.retrieving,
    (data: boolean) => data
  );

export const hasError = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.error,
    (data: boolean) => data
  );

/*******************************************************************************
 * SLICE
 ******************************************************************************/

const initialState: DirectLoadAssignmentData = {
  data: {},
  retrieving: false,
  error: false,
};

const directLoadAssignmentSlice = createSlice({
  name: MODULE_NAME,
  initialState,
  reducers: {
    clearError: onClearError,
  },
  extraReducers: (builder) => {
    /***************************************************************************
     * LOADS INFO
     **************************************************************************/

    builder.addCase(
      retrieveDirectAssignmentLoads.fulfilled,
      (state, action) => {
        state.data = action.payload;
        state.retrieving = false;
        state.error = false;
      }
    );
    builder.addCase(retrieveDirectAssignmentLoads.rejected, (state, action) => {
      state.retrieving = false;
      state.error = true;
    });
    builder.addCase(retrieveDirectAssignmentLoads.pending, (state, action) => {
      state.retrieving = true;
      state.error = false;
    });
  },
});

export const DirectLoadAssignmentState = {
  actions: {
    retrieveDirectAssignmentLoads,
    clearError: directLoadAssignmentSlice.actions.clearError,
  },
  selectors: {
    directAssignmentLoads,
    isRetrieving,
    hasError,
  },
};

// Action creators are generated for each case reducer function
export default directLoadAssignmentSlice;
