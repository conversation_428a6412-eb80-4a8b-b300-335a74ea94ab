import {
  createSlice,
  createAsyncThunk,
  createSelector,
  PayloadAction,
} from "@reduxjs/toolkit";
import { LoadsTrackingMode, LocationProvider } from "state/BaseTypes";
import { RootState, Selector } from "state/store";

import eldGpsIntegrationsApi from "api/carrier/EldGpsIntegrationsApi";

const MODULE_NAME = "eldGpsIntegrations";

type EldGpsIntegrationsData = {
  data: any[];
  details: any;
  retrieving: boolean;
  creating: boolean;
  updating: boolean;
  deleting: boolean;
  error: boolean;
};

/*******************************************************************************
 * ACTIONS
 ******************************************************************************/

const getExternalAccessToken = (thunkAPI: any) => {
  return thunkAPI.getState().externalParameters.externalAccessToken;
};

/*
 * Gets the ELD GPS integrations list
 */
export const retrieveIntegrations = createAsyncThunk<
  any,
  {
    carrierId: string;
    modes: LoadsTrackingMode[];
  },
  {}
>(
  `${MODULE_NAME}/retrieveIntegrations`,
  async ({ carrierId, modes }, thunkAPI) => {
    const response = await eldGpsIntegrationsApi.retrieveIntegrations(
      carrierId,
      modes,
      // Use token in case app is used externally
      getExternalAccessToken(thunkAPI)
    );

    return response.data;
  }
);

/*
 * Gets the ELD GPS integration details
 */
export const retrieveIntegrationDetails = createAsyncThunk<
  any,
  {
    carrierId: string;
    integrationId: string | number;
  },
  {}
>(
  `${MODULE_NAME}/retrieveIntegrationDetails`,
  async ({ carrierId, integrationId }, thunkAPI) => {
    const response = await eldGpsIntegrationsApi.retrieveIntegrationDetails(
      carrierId,
      integrationId,
      // Use token in case app is used externally
      getExternalAccessToken(thunkAPI)
    );

    return response.data;
  }
);

/*
 * Adds a new ELD integration for this carrier
 */
export const createIntegration = createAsyncThunk<
  any,
  {
    carrierId: string;
    modes: LoadsTrackingMode[];
    userId: string;
    provider: LocationProvider;
    assetNumber?: string;
  },
  {}
>(
  `${MODULE_NAME}/createIntegration`,
  async ({ carrierId, modes, userId, provider, assetNumber }, thunkAPI) => {
    const response = await eldGpsIntegrationsApi.createIntegration(
      carrierId,
      modes,
      userId,
      provider,
      assetNumber,
      // Use token in case app is used externally
      getExternalAccessToken(thunkAPI)
    );

    return response.data;
  }
);

/*
 * Updates an existing ELD integration for this carrier
 */
export const updateIntegration = createAsyncThunk<
  any,
  {
    carrierId: string;
    integrationId: string | number;
    modes: LoadsTrackingMode[];
    userId: string;
    provider: LocationProvider;
    assetNumber?: string;
    completeIntegrationManually?: boolean;
  },
  {}
>(
  `${MODULE_NAME}/updateIntegration`,
  async (
    {
      carrierId,
      modes,
      integrationId,
      userId,
      provider,
      assetNumber,
      completeIntegrationManually,
    },
    thunkAPI
  ) => {
    const response = await eldGpsIntegrationsApi.updateIntegration(
      carrierId,
      integrationId,
      modes,
      userId,
      provider,
      assetNumber,
      completeIntegrationManually,
      // Use token in case app is used externally
      getExternalAccessToken(thunkAPI)
    );

    return response.data;
  }
);

/*
 * Removes an existing ELD integration for this carrier
 */
export const deleteIntegration = createAsyncThunk<
  any,
  {
    carrierId: string;
    integrationId: string | number;
    modes: LoadsTrackingMode[];
  },
  {}
>(
  `${MODULE_NAME}/deleteIntegration`,
  async ({ carrierId, modes, integrationId }, thunkAPI) => {
    const response = await eldGpsIntegrationsApi.deleteIntegration(
      carrierId,
      integrationId,
      modes,
      // Use token in case app is used externally
      getExternalAccessToken(thunkAPI)
    );

    return response.data;
  }
);

/*
 * Creates a help request for this integration
 */
export const createIntegrationHelpRequest = createAsyncThunk<
  any,
  {
    carrierId: string;
    modes: LoadsTrackingMode[];
    provider: LocationProvider;
    userMessage: string;
  },
  {}
>(
  `${MODULE_NAME}/createIntegrationHelpRequest`,
  async ({ carrierId, modes, provider, userMessage }, thunkAPI) => {
    const response = await eldGpsIntegrationsApi.createIntegrationHelpRequest(
      carrierId,
      modes,
      provider,
      userMessage,
      // Use token in case app is used externally
      getExternalAccessToken(thunkAPI)
    );

    return response.data;
  }
);

/*
 * NOTE: this is exclusevely used for peoplente
 * Sends information so we can create the data share agreement
 */

export const createDataShareAgreement = createAsyncThunk<
  any,
  {
    carrierId: string;
    dsaFields: {
      cid: string;
      first_name: string;
      last_name: string;
      position: string;
    };
    signature: File;
  },
  {}
>(
  `${MODULE_NAME}/createDataShareAgreement`,
  async ({ carrierId, dsaFields, signature }, thunkAPI) => {
    const response = await eldGpsIntegrationsApi.createDataShareAgreement(
      carrierId,
      dsaFields,
      signature,
      // Use token in case app is used externally
      getExternalAccessToken(thunkAPI)
    );

    return response.data;
  }
);

const onClearError = (state: any, {}: PayloadAction<any>) => {
  state.details.error = false;
};

/*******************************************************************************
 * SELECTORS
 ******************************************************************************/

export const eldGpsIntegrations = (): Selector<any[]> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.data,
    (data: any[]) => data
  );

export const eldGpsIntegrationDetails = (): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.details,
    (data: any) => data
  );

export const isCreating = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.creating,
    (data: any) => data
  );

export const isRetrieving = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.retrieving,
    (data: any) => data
  );

export const isUpdating = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.updating,
    (data: any) => data
  );

export const isDeleting = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.deleting,
    (data: any) => data
  );

/*******************************************************************************
 * SLICE
 ******************************************************************************/

// Define the initial state using that type
const initialState: EldGpsIntegrationsData = {
  data: [],
  details: {},
  retrieving: false,
  creating: false,
  updating: false,
  deleting: false,
  error: false,
};

const eldGpsIntegrationsSlice = createSlice({
  name: MODULE_NAME,
  initialState: initialState,
  reducers: {
    clearError: onClearError,
  },
  extraReducers: (builder) => {
    /***************************************************************************
     * LIST
     **************************************************************************/

    builder.addCase(retrieveIntegrations.fulfilled, (state, action) => {
      state.data = action.payload;
      state.retrieving = false;
      state.error = false;
    });
    builder.addCase(retrieveIntegrations.pending, (state, action) => {
      state.retrieving = true;
      state.error = false;
    });
    builder.addCase(retrieveIntegrations.rejected, (state, action) => {
      state.retrieving = false;
      state.error = true;
    });

    /***************************************************************************
     * DETAILS
     **************************************************************************/

    builder.addCase(retrieveIntegrationDetails.fulfilled, (state, action) => {
      state.details = action.payload;
      state.retrieving = false;
      state.error = false;
    });
    builder.addCase(retrieveIntegrationDetails.pending, (state, action) => {
      state.retrieving = true;
      state.error = false;
    });
    builder.addCase(retrieveIntegrationDetails.rejected, (state, action) => {
      state.retrieving = false;
      state.error = true;
    });

    /***************************************************************************
     * CREATE
     **************************************************************************/

    [createIntegration].forEach((actionCreator) => {
      builder.addCase(actionCreator.fulfilled, (state, action) => {
        const data = state.data || [];
        state.data = data.concat(action.payload);
        state.creating = false;
        state.error = false;
      });
      builder.addCase(actionCreator.pending, (state, action) => {
        state.creating = true;
        state.error = false;
      });
      builder.addCase(actionCreator.rejected, (state, action) => {
        state.creating = false;
        state.error = true;
      });
    });
  },
});

export const EldGpsIntegrationsState = {
  actions: {
    createIntegration,
    retrieveIntegrations,
    retrieveIntegrationDetails,
    updateIntegration,
    deleteIntegration,
    createIntegrationHelpRequest,
    createDataShareAgreement,
    clearError: eldGpsIntegrationsSlice.actions.clearError,
  },
  selectors: {
    eldGpsIntegrations,
    eldGpsIntegrationDetails,
    isCreating,
    isRetrieving,
    isUpdating,
    isDeleting,
  },
};

// Action creators are generated for each case reducer function
export default eldGpsIntegrationsSlice;
