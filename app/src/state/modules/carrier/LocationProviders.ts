import {
  createSlice,
  createAsyncThunk,
  createSelector,
} from "@reduxjs/toolkit";
import { RootState, Selector } from "state/store";
import _ from 'lodash';
import locationProvidersApi from "api/carrier/LocationProviderApi";

const MODULE_NAME = "locationProviders";

type LocationProvidersData = {
  locationProviders: {
    data: any[];
    selectedLocationProviders: any[];
    combinedLocationProviders: any[];
    loading: boolean;
    creating: boolean;
    error: boolean;
  };
};

/*******************************************************************************
 * ACTIONS
 ******************************************************************************/

const getExternalAccessToken = (thunkAPI: any) => {
  return thunkAPI.getState().externalParameters.externalAccessToken;
};

/*
 * Gets the list of location providers that match parameters
 */
export const getLocationProviders = createAsyncThunk<
  any,
  { showPopular: boolean; query: string },
  {}
>(
  `${MODULE_NAME}/getLocationProviders`,
  async ({ showPopular, query }, thunkAPI) => {
    const response = await locationProvidersApi.getLocationProviders(
      showPopular,
      query,
      getExternalAccessToken(thunkAPI)
    );

    return response?.data;
  }
);

/*
 * Adds a new location provider
 */
export const addLocationProvider = createAsyncThunk<
  any,
  { carrierId: string; locationProvider: any },
  {}
>(
  `${MODULE_NAME}/addLocationProvider`,
  async ({ carrierId, locationProvider }, thunkAPI) => {
    const response = await locationProvidersApi.addLocationProvider(
      carrierId,
      locationProvider,
      getExternalAccessToken(thunkAPI)
    );

    return response?.data;
  }
);

/*******************************************************************************
 * SELECTORS
 ******************************************************************************/

export const locationProviders = (): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.locationProviders?.data,
    (data: any) => data
  );

export const selectedLocationProviders = (): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.locationProviders?.selectedLocationProviders,
    (data: any) => data
  );

  export const combinedLocationProviders = (): Selector<any> =>
    createSelector(
      (state: RootState) => state[MODULE_NAME]?.locationProviders?.combinedLocationProviders,
      (data: any) => data
    );

export const isLoadingLocationProviders = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.locationProviders?.loading,
    (data: boolean) => data
  );

export const isCreatingLocationProviders = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.locationProviders?.creating,
    (data: boolean) => data
  );

/*******************************************************************************
 * SLICE
 ******************************************************************************/

// Define the initial state using that type
const initialLocationProvidersData = {
  data: [],
  selectedLocationProviders: [],
  combinedLocationProviders: [],
  loading: true,
  creating: false,
  error: false,
};
const initialState: LocationProvidersData = {
  locationProviders: initialLocationProvidersData,
};

const locationProvidersSlice = createSlice({
  name: MODULE_NAME,
  initialState: initialState,
  reducers: {
    setSelectedLocationProviders: (state, action) => {
      state.locationProviders.selectedLocationProviders = action.payload;
    },


    setCombinedLocationProviders: (state, action) => {
      state.locationProviders.combinedLocationProviders = action.payload;
    },
  },
  extraReducers: (builder) => {
    /***************************************************************************
     * LOADS LOCATION PROVIDERS
     **************************************************************************/

    builder.addCase(getLocationProviders.fulfilled, (state, action) => {
      state.locationProviders.data = action.payload;
      state.locationProviders.combinedLocationProviders = _.unionBy(state.locationProviders.selectedLocationProviders, state.locationProviders.data, "id");
      state.locationProviders.loading = false;
      state.locationProviders.error = false;
    });
    builder.addCase(getLocationProviders.pending, (state, action) => {
      state.locationProviders.loading = true;
      state.locationProviders.error = false;
    });
    builder.addCase(getLocationProviders.rejected, (state, action) => {
      state.locationProviders.loading = false;
      state.locationProviders.error = true;
    });

    /***************************************************************************
     * CREATES LOCATION PROVIDERS
     **************************************************************************/

    builder.addCase(addLocationProvider.fulfilled, (state, action) => {
      // TODO
      //state.locationProviders.data = action.payload;
      state.locationProviders.creating = false;
      state.locationProviders.error = false;
    });
    builder.addCase(addLocationProvider.pending, (state, action) => {
      state.locationProviders.creating = true;
      state.locationProviders.error = false;
    });
    builder.addCase(addLocationProvider.rejected, (state, action) => {
      state.locationProviders.creating = false;
      state.locationProviders.error = true;
    });
  },
});

/*******************************************************************************
 * ACTIONS EXPORT
 ******************************************************************************/

export const { setSelectedLocationProviders } = locationProvidersSlice.actions;
export const { setCombinedLocationProviders } = locationProvidersSlice.actions;

export const LocationProvidersState = {
  actions: {
    getLocationProviders,
    addLocationProvider,
    setSelectedLocationProviders,
    setCombinedLocationProviders,
  },
  selectors: {
    locationProviders,
    selectedLocationProviders,
    combinedLocationProviders,
    isLoadingLocationProviders,
    isCreatingLocationProviders,
  },
};

export default locationProvidersSlice;
