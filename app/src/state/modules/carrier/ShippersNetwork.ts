import {
  createSlice,
  createAsyncThunk,
  createSelector,
  PayloadAction,
} from "@reduxjs/toolkit";
import { RootState, Selector } from "state/store";
import {
  LoadsTrackingMode,
  NetworkStatusType,
  typedModeFromAction,
} from "state/BaseTypes";

import shippersNetworkApi from "api/carrier/ShippersNetworkApi";

/*******************************************************************************
 * TYPES
 ******************************************************************************/

const MODULE_NAME = "shippersNetwork";

type ShippersModeData = {
  data: any[];
  filters: {
    query: string | null;
    networkStatus: NetworkStatusType;
    filterBy: {
      columnName: string;
      value: string;
      filterType: "contains" | "in" | "equals";
    }[];
  };
  pagination: {
    totalItems: number;
    currentPage: number;
    pageSize: number;
    totalPages: number;
  };
  loading: boolean;
  editing: boolean;
  error: boolean;
};

type ShippersData = {
  shippers: {
    ltl: ShippersModeData;
    ftl: ShippersModeData;
    parcel: ShippersModeData;
    ocean: ShippersModeData;
    air: ShippersModeData;
  };
};

/*******************************************************************************
 * HELPERS
 ******************************************************************************/

const DEFAULT_PAGINATION_PARAMETERS = {
  currentPage: 1,
  pageSize: 25,
};

const getFilterByParams = (
  filterBy?: {
    columnName: string;
    additionalColumnNames?: string;
    value: string;
    filterType: "contains" | "in" | "equals";
  }[]
): string => {
  if (!filterBy) {
    return JSON.stringify([]);
  }

  return JSON.stringify(
    filterBy?.map((f: any) => ({
      column_name: f?.columnName,
      additional_column_names: f?.additionalColumnNames || null,
      value: f?.value,
      filter_type: f?.filterType,
    }))
  );
};

const getSortByParams = (
  sortBy: { columnName: string; isDesc: boolean } | undefined
) => {
  if (!sortBy) {
    return JSON.stringify([]);
  }

  return JSON.stringify([
    {
      column_name: sortBy.columnName,
      is_desc: sortBy.isDesc,
    },
  ]);
};

const getPaginationParams = (pageSize?: number, currentPage?: number) => {
  return JSON.stringify({
    current_page: currentPage
      ? currentPage
      : DEFAULT_PAGINATION_PARAMETERS.currentPage,
    page_size: pageSize ? pageSize : DEFAULT_PAGINATION_PARAMETERS.pageSize,
  });
};

/*******************************************************************************
 * ACTIONS
 ******************************************************************************/

/**************** SHIPPERS NETWORK ****************/

/*
 * Gets the list of shippers, including infr about which ones are in this
 * shippers network
 */
export const getShippers = createAsyncThunk<
  any,
  {
    carrierId: string;
    mode: LoadsTrackingMode;
    pageSize?: number;
    currentPage?: number;
    sortBy?: {
      columnName: string;
      isDesc: boolean;
    };
  },
  {}
>(
  `${MODULE_NAME}/getShippers`,
  async ({ carrierId, mode, pageSize, currentPage, sortBy }, thunkAPI) => {
    // Get filters for call
    const state: any = thunkAPI.getState();
    const filters = state[MODULE_NAME]?.shippers[mode]?.filters;
    const { networkStatus, filterBy } = filters;

    const filterByParams = getFilterByParams(filterBy);
    const sortByParams = getSortByParams(sortBy);
    const paginationParams = getPaginationParams(pageSize, currentPage);

    const response = await shippersNetworkApi.getShippers(
      carrierId,
      mode,
      networkStatus,
      filterByParams,
      sortByParams,
      paginationParams
    );
    return response;
  }
);

/*
 * Disconnects this shipper from the carrier network
 */
export const disconnectShipper = createAsyncThunk<
  any,
  { shipperId: string; carrierId: string; mode: LoadsTrackingMode },
  {}
>(
  `${MODULE_NAME}/disconnectShipper`,
  async ({ shipperId, carrierId, mode }, thunkAPI) => {
    const response = await shippersNetworkApi.disconnectShipper(
      carrierId,
      shipperId,
      mode
    );

    return response;
  }
);

const onClearError = (state: any, { payload }: PayloadAction<any>) => {
  const mode = payload as LoadsTrackingMode;
  state.shippers[mode].error = false;
};

const onChangeNetworkFilters = (
  state: any,
  { payload }: PayloadAction<any>
) => {
  const { mode, networkStatus, query } = payload;

  if (networkStatus != null) {
    state.shippers[mode].filters.networkStatus =
      networkStatus as NetworkStatusType;
  }

  // Change filterBy parameter based on user query
  if (query != null) {
    state.shippers[mode].filters.query = query;
    state.shippers[mode].filters.filterBy = [
      {
        // XXX: BE needs this specifically in order to be able to filter by name
        columnName: "target_company_name",
        additionalColumnNames: ["usdot", "carrier_codes"],
        value: query,
        filterType: "contains",
      },
      // TODO: add other possibility of filters
    ];
  }
};

/*******************************************************************************
 * SELECTORS
 ******************************************************************************/

const shippersByMode = (mode: LoadsTrackingMode): Selector<any[]> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.shippers[mode]?.data,
    (data: any[]) => data
  );

export const isLoadingShippersByMode = (
  mode: LoadsTrackingMode
): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.shippers[mode]?.loading,
    (data: boolean) => data
  );

export const isEditingShippersByMode = (
  mode: LoadsTrackingMode
): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.shippers[mode]?.editing,
    (data: boolean) => data
  );

export const hasErrorByMode = (mode: LoadsTrackingMode): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.shippers[mode]?.error,
    (data: boolean) => data
  );

export const networkFiltersByMode = (mode: LoadsTrackingMode): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.shippers[mode]?.filters,
    (data: any) => data
  );

export const paginationByMode = (mode: LoadsTrackingMode): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.shippers[mode]?.pagination,
    (data: any) => data
  );

/*******************************************************************************
 * SLICE
 ******************************************************************************/

// Define the initial state using that type
const initialShippersState = {
  data: [] as any[],
  filters: {
    query: null,
    networkStatus: "connected" as NetworkStatusType,
    filterBy: [],
  },
  sortBy: [],
  pagination: {
    currentPage: 1,
    pageSize: 25,
    totalItems: 0,
    totalPages: 0,
  },
  loading: true,
  editing: false,
  error: false,
};

const initialState: ShippersData = {
  shippers: {
    ltl: initialShippersState,
    ftl: initialShippersState,
    parcel: initialShippersState,
    ocean: initialShippersState,
    air: initialShippersState,
  },
};

const shippersSlice = createSlice({
  name: MODULE_NAME,
  initialState,
  reducers: {
    clearError: onClearError,
    changeNetworkFilters: onChangeNetworkFilters,
  },
  extraReducers: (builder) => {
    // Customers list
    builder.addCase(getShippers.fulfilled, (state, action) => {
      // List
      state.shippers[typedModeFromAction(action)].data =
        action?.payload?.details;
      // Pagination
      const { current_page, page_size, total_items, total_pages } =
        action?.payload?.pagination || {};
      state.shippers[typedModeFromAction(action)].pagination = {
        currentPage: current_page,
        pageSize: page_size,
        totalItems: total_items,
        totalPages: total_pages,
      };
      // Status
      state.shippers[typedModeFromAction(action)].loading = false;
      state.shippers[typedModeFromAction(action)].error = false;
    });
    builder.addCase(getShippers.rejected, (state, action) => {
      // Reset the list in case we've got an error
      state.shippers[typedModeFromAction(action)].data = [];
      state.shippers[typedModeFromAction(action)].loading = false;
      state.shippers[typedModeFromAction(action)].error = true;
    });
    builder.addCase(getShippers.pending, (state, action) => {
      state.shippers[typedModeFromAction(action)].loading = true;
      state.shippers[typedModeFromAction(action)].error = false;
    });
  },
});

export const ShippersNetworkState = {
  actions: {
    getShippers,
    disconnectShipper,
    clearError: shippersSlice.actions.clearError,
    changeNetworkFilters: shippersSlice.actions.changeNetworkFilters,
  },
  selectors: {
    shippersByMode,
    isLoadingShippersByMode,
    isEditingShippersByMode,
    hasErrorByMode,
    networkFiltersByMode,
    paginationByMode,
  },
};

// Action creators are generated for each case reducer function
export default shippersSlice;
