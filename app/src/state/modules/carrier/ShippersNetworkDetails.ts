import {
  createSlice,
  createAsyncThunk,
  createSelector,
} from "@reduxjs/toolkit";
import { RootState, Selector } from "state/store";
import { LoadsTrackingMode } from "state/BaseTypes";

import shippersNetworkDetailsApi from "api/carrier/ShippersNetworkDetailsApi";

const MODULE_NAME = "shippersNetworkDetails";

type ShipperDetailsData = {
  shipperDetails: {
    data: any;
    loading: boolean;
    editing: boolean;
    error: boolean;
    editingCustomerCodes: boolean;
  };
};

/*******************************************************************************
 * ACTIONS
 ******************************************************************************/

/*
 * Gets the details for this shipper
 */
const retrieveShipperDetails = createAsyncThunk<
  any,
  { carrierId: string; shipperId: string; mode: LoadsTrackingMode },
  {}
>(
  `${MODULE_NAME}/retrieveShipperDetails`,
  async ({ carrierId, shipperId, mode }, thunkAPI) => {
    const response = await shippersNetworkDetailsApi.retrieveShipperDetails(
      carrierId,
      shipperId,
      mode
    );

    return response?.data?.details;
  }
);


  // EXPORT EXCEL
  /*
   * Get all carrier network details of a shipper & export as excel through email
   *
   */
  export const exportShipperList = createAsyncThunk<
    any,
    {
      carrierId: string;
    },
    {}
  >(`${MODULE_NAME}/exportable-list`, async ({ carrierId }, thunkAPI) => {
    const response = await shippersNetworkDetailsApi.createExportableList(
      carrierId
    );
    return response;
  });


    /*
  * Get general details about the export with regards to the shipper's carrier
  *
  */
  export const getExportInfo = createAsyncThunk<
  any,
  {
    carrierId: string;
  },
  {}
  >(`${MODULE_NAME}/exportable-lists/info`, async ({ carrierId }, thunkAPI) => {
  const response = await shippersNetworkDetailsApi.getExportInfo(carrierId);
  return response.data;
  });

/*
 * Accepts this invitation to connect to shipper's network (called by carrier)
 */
const acceptShipperInvitation = createAsyncThunk<
  any,
  {
    mode: LoadsTrackingMode;
    carrierId: string;
    networkId: string;
    accepted: boolean;
  },
  {}
>(
  `${MODULE_NAME}/acceptShipperInvitation`,
  async ({ carrierId, networkId, mode, accepted }, thunkAPI) => {
    const response = await shippersNetworkDetailsApi.acceptShipperInvitation(
      mode,
      carrierId,
      networkId,
      accepted
    );

    return response?.data;
  }
);

/*
 * Connects to a shipper using it's network token
 */
const connectToShipperViaToken = createAsyncThunk<
  any,
  {
    mode: LoadsTrackingMode;
    carrierId: string;
    inviterToken: string;
  },
  {}
>(
  `${MODULE_NAME}/connectToShipperViaToken`,
  async ({ mode, carrierId, inviterToken }, thunkAPI) => {
    const response = await shippersNetworkDetailsApi.connectToShipperViaToken(
      mode,
      carrierId,
      inviterToken
    );

    return response?.data;
  }
);

/**************** CUSTOMER CODES ****************/

/*
 * Add a customer code to a given carrier
 */
export const createCustomerCode = createAsyncThunk<
  any,
  {
    shipperId: string;
    carrierId: string;
    mode: LoadsTrackingMode;
    code: string;
  },
  {}
>(
  `${MODULE_NAME}/createCustomerCode`,
  async ({ shipperId, carrierId, mode, code }, thunkAPI) => {
    const response = await shippersNetworkDetailsApi.createCustomerCode(
      shipperId,
      carrierId,
      mode,
      code
    );

    return { code: response.data.data.code };
  }
);

/*
 * Removes a customer code from a given carrier
 */
export const deleteCustomerCode = createAsyncThunk<
  any,
  {
    shipperId: string;
    carrierId: string;
    mode: LoadsTrackingMode;
    code: string;
  },
  {}
>(
  `${MODULE_NAME}/deleteCustomerCode`,
  async ({ shipperId, carrierId, mode, code }, thunkAPI) => {
    const response = await shippersNetworkDetailsApi.deleteCustomerCode(
      shipperId,
      carrierId,
      mode,
      code
    );

    return { code: response.data.data.code };
  }
);

/*******************************************************************************
 * SELECTORS
 ******************************************************************************/

export const shipperDetails = (): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.shipperDetails?.data,
    (data: any) => data
  );

export const isLoadingShipperDetails = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.shipperDetails?.loading,
    (data: boolean) => data
  );

export const isEditingShipperDetails = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.shipperDetails?.editing,
    (data: boolean) => data
  );

export const isEditingCustomerCodes = (): Selector<boolean> =>
  createSelector(
    (state: RootState) =>
      state[MODULE_NAME]?.shipperDetails?.editingCustomerCodes,
    (data: any) => data
  );

/*******************************************************************************
 * SLICE
 ******************************************************************************/

// Define the initial state using that type
const initialShippersNetworkDetailsState = {
  data: {},
  loading: true,
  editing: false,
  error: false,
  editingCustomerCodes: false,
};
const initialState: ShipperDetailsData = {
  shipperDetails: initialShippersNetworkDetailsState,
};

const shippersNetworkDetailsSlice = createSlice({
  name: MODULE_NAME,
  initialState: initialState,
  reducers: {},
  extraReducers: (builder) => {
    /***************************************************************************
     * LOAD SHIPPER DETAILS
     **************************************************************************/

    builder.addCase(retrieveShipperDetails.fulfilled, (state, action) => {
      state.shipperDetails.data = action?.payload;
      state.shipperDetails.loading = false;
    });
    builder.addCase(retrieveShipperDetails.rejected, (state) => {
      state.shipperDetails.loading = false;
    });
    builder.addCase(retrieveShipperDetails.pending, (state) => {
      state.shipperDetails.loading = true;
    });

    /***************************************************************************
     * CUSTOMER CODES
     **************************************************************************/

    builder.addCase(createCustomerCode.fulfilled, (state, action) => {
      // Adds the code to the customer_codes field in details
      const data = state.shipperDetails?.data || {};
      state.shipperDetails.data = {
        ...data,
        customer_codes: {
          ...data?.customer_codes,
          custom: [...(data.customer_codes?.custom || []), action.payload.code],
        },
      };
      state.shipperDetails.editingCustomerCodes = false;
    });

    builder.addCase(deleteCustomerCode.fulfilled, (state, action) => {
      // Removes the code from the customer_codes field in details
      const data = state.shipperDetails?.data || {};
      state.shipperDetails.data = {
        ...data,
        customer_codes: {
          ...data?.customer_codes,
          custom: data?.customer_codes?.custom?.filter(
            (code: string) => code !== action.payload.code
          ),
        },
      };
      state.shipperDetails.editingCustomerCodes = false;
    });

    // Updates loading for other states
    [createCustomerCode, deleteCustomerCode].forEach((actionCreator) => {
      builder.addCase(actionCreator.rejected, (state) => {
        state.shipperDetails.editingCustomerCodes = false;
      });
      builder.addCase(actionCreator.pending, (state) => {
        state.shipperDetails.editingCustomerCodes = true;
      });
    });

    /***************************************************************************
     * ACCEPT SHIPPER INVITATIONS
     **************************************************************************/

    [acceptShipperInvitation, connectToShipperViaToken].forEach(
      (actionCreator) => {
        builder.addCase(actionCreator.fulfilled, (state) => {
          state.shipperDetails.editing = false;
          state.shipperDetails.error = false;
        });
        builder.addCase(actionCreator.rejected, (state) => {
          state.shipperDetails.editing = false;
          state.shipperDetails.error = true;
        });
        builder.addCase(actionCreator.pending, (state) => {
          state.shipperDetails.editing = true;
          state.shipperDetails.error = false;
        });
      }
    );
  },
});

export const ShippersNetworkDetailsState = {
  actions: {
    retrieveShipperDetails,
    acceptShipperInvitation,
    connectToShipperViaToken,
    createCustomerCode,
    deleteCustomerCode,
    exportShipperList,
    getExportInfo,
  },
  selectors: {
    shipperDetails,
    isLoadingShipperDetails,
    isEditingShipperDetails,
    isEditingCustomerCodes,
  },
};

export default shippersNetworkDetailsSlice;
