import {
  createSlice,
  createAsyncThunk,
  createSelector,
} from "@reduxjs/toolkit";
import { RootState, Selector } from "state/store";

import DispatcherLogsApi from "api/carrier/DispatcherLogsApi";

const MODULE_NAME = "dispatcherLogsData";

/*******************************************************************************
 * TYPES
 ******************************************************************************/

type DispatcherLogsData = {
  data: any[]
  loading: boolean;
  creating: boolean;
  deleting: boolean;
  error: boolean;
  pagination: {
    totalItems: number;
    currentPage: number;
    pageSize: number;
    totalPages: number;
  };
};

/*******************************************************************************
 * HELPERS
 ******************************************************************************/

const DEFAULT_PAGINATION_PARAMETERS = {
  currentPage: 1,
  pageSize: 10,
};

const getPaginationParams = (pageSize?: number, currentPage?: number) => {
  return {
    current_page: currentPage
      ? currentPage
      : DEFAULT_PAGINATION_PARAMETERS.currentPage,
    page_size: currentPage ? pageSize : DEFAULT_PAGINATION_PARAMETERS.pageSize,
  };
};

/*******************************************************************************
 * ACTIONS
 ******************************************************************************/

export const retrieveDispatcherLogs = createAsyncThunk<
  any,
  {
    fromTimestamp: string;
    toTimestamp: string;
    carrierId: string;
    loadIdentifier: string;
    shipper: string;
    pageSize?: number;
    currentPage?: number;
  },
  {}
>(
  `${MODULE_NAME}/retrieveDispatcherLogs`,
  async (
    {
      fromTimestamp,
      toTimestamp,
      carrierId,
      loadIdentifier,
      shipper,
      pageSize,
      currentPage,
    },
    thunkAPI
  ) => {
    
    const paginationParams = getPaginationParams(pageSize, currentPage);
    const response = await DispatcherLogsApi.retrieveDispatcherLogs(
      fromTimestamp,
      toTimestamp,
      carrierId,
      loadIdentifier,
      shipper,
      paginationParams
    );

    return response;
  }
);

/*******************************************************************************
 * SELECTORS
 ******************************************************************************/
export const paginationDetails = (): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.pagination,
    (data: any) => data
  );

export const dispatcherLogs = (): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.data,
    (data: any) => data
  );

export const isRetrievingDispatcherLogs = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.loading,
    (data: boolean) => data
  );

export const hasError = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.error,
    (data: boolean) => data
  );

/*******************************************************************************
 * SLICE
 ******************************************************************************/

const initialState: DispatcherLogsData = {
  data: [] as any[],
  pagination: {
    currentPage: 1,
    pageSize: 10,
    totalItems: 0,
    totalPages: 0,
  },
  loading: false,
  creating: false,
  deleting: false,
  error: false,
};

const dispatcherDataSlice = createSlice({
  name: MODULE_NAME,
  initialState: initialState,
  reducers: {},
  extraReducers: (builder) => {
    /***************************************************************************
     * RETRIEVE LIST
     **************************************************************************/
    builder.addCase(retrieveDispatcherLogs.fulfilled, (state, action) => {
      state.data = action?.payload || [];
      // Pagination
      const { current_page, page_size, total_items, total_pages } =
        action?.payload?.pagination || {};
      state.pagination = {
        currentPage: current_page,
        pageSize: page_size,
        totalItems: total_items,
        totalPages: total_pages,
      };

      state.loading = false;
      state.error = false;
    });

    builder.addCase(retrieveDispatcherLogs.pending, (state, action) => {
      state.loading = true;
      state.error = false;
    });

    builder.addCase(retrieveDispatcherLogs.rejected, (state, action) => {
      state.loading = false;
      state.error = true;
    });
  },
});

export const DispatcherLogsState = {
  actions: {
    retrieveDispatcherLogs,
  },
  selectors: {
    paginationDetails,
    dispatcherLogs,
    isRetrievingDispatcherLogs,
    hasError,
  },
};

export default dispatcherDataSlice;
