import {
  createSlice,
  createAsyncThunk,
  createSelector,
  PayloadAction,
} from "@reduxjs/toolkit";
import { RootState, Selector } from "state/store";
import { CarrierContact } from "state/BaseTypes";

import {
  loginUser as keycloackLoginUser,
  signupUser as keycloackSignupUser,
  isUserAuthenticated as kcIsUserAuthenticated,
} from "./KeycloakUtils";

import externalOboardingApi from "api/external/ExternalOnboardingApi";

const MODULE_NAME = "externalOboarding";

type ExternalOboardingData = {
  user: {
    data: any;
    loading: boolean;
    creating: boolean;
    invalid: boolean;
    error: boolean;
  };
  carrier: {
    data: any;
    loading: boolean;
    creating: boolean;
    invalid: boolean;
    error: boolean;
  };
};

/*******************************************************************************
 * KEICLOAK ACTIONS
 ******************************************************************************/

export const isUserAuthenticated = async () => {
  return await kcIsUserAuthenticated();
};

export const loginUser = (
  redirectUri: string,
  onAuthenticatedCallback?: Function
) => {
  return keycloackLoginUser(redirectUri, onAuthenticatedCallback);
};

export const signupUser = async (
  redirectUri: string,
  email: string,
  password: string,
  onAuthenticatedCallback: Function
) => {
  return await keycloackSignupUser(
    redirectUri,
    email,
    password,
    onAuthenticatedCallback
  );
};

/*******************************************************************************
 * ACTIONS
 ******************************************************************************/

/*
 * Gets carrier details by looking it up by USDOT
 */
export const getCarrierDetails = createAsyncThunk<
  any,
  {
    m2mToken: string;
    usdot?: string;
    mcNumber?: string;
  },
  {}
>(
  `${MODULE_NAME}/getCarrierDetails`,
  async ({ m2mToken, usdot, mcNumber }, thunkAPI) => {
    const response = await externalOboardingApi.getCarrierDetails(
      m2mToken,
      usdot,
      mcNumber
    );

    return response.data;
  }
);

/*
 * Gets carrier details by looking it up by USDOT
 */
export const getUserDetails = createAsyncThunk<
  any,
  {
    m2mToken: string;
    carrierId: string;
    userEmail: string;
  },
  {}
>(
  `${MODULE_NAME}/getUserDetails`,
  async ({ m2mToken, carrierId, userEmail }, thunkAPI) => {
    const response = await externalOboardingApi.getUserDetails(
      m2mToken,
      carrierId,
      userEmail
    );

    return response.data;
  }
);

/*
 * Creates a new carrier company account, but using m2m token as form of authentication
 */
export const createCarrier = createAsyncThunk<
  any,
  {
    m2mToken: string;
    carrier: any;
  },
  {}
>(`${MODULE_NAME}/createCarrier`, async ({ m2mToken, carrier }, thunkAPI) => {
  const response = await externalOboardingApi.createCarrier(m2mToken, carrier);

  return response.data;
});

/*
 * Creates a new user related to this carrier, but using m2m token as form of authentication
 */
export const createCarrierUser = createAsyncThunk<
  any,
  {
    m2mToken: string;
    carrierId: string;
    user: CarrierContact;
    role: string;
    password: string;
    passwordConfirmation: string;
  },
  {}
>(
  `${MODULE_NAME}/createCarrierUser`,
  async (
    { m2mToken, carrierId, user, role, password, passwordConfirmation },
    thunkAPI
  ) => {
    const response = await externalOboardingApi.createCarrierUser(
      m2mToken,
      carrierId,
      user,
      role,
      password,
      passwordConfirmation
    );

    return response.data;
  }
);

const onClearError = (state: any, { payload }: PayloadAction<any>) => {
  state.details.error = false;
};

/*******************************************************************************
 * SELECTORS
 ******************************************************************************/

export const carrierDetails = (): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.carrier?.data,
    (data: any) => data
  );

export const carrierIsLoading = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.carrier?.loading,
    (data: any) => data
  );

export const carrierIsCreating = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.carrier?.creating,
    (data: any) => data
  );

export const carrierIsInvalid = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.carrier?.invalid,
    (data: any) => data
  );

export const carrierHasError = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.carrier?.error,
    (data: any) => data
  );

export const userDetails = (): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.user?.data,
    (data: any) => data
  );

export const userIsLoading = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.user?.loading,
    (data: any) => data
  );

export const userIsCreating = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.user?.creating,
    (data: any) => data
  );

export const userIsInvalid = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.user?.invalid,
    (data: any) => data
  );

export const userHasError = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.user?.error,
    (data: any) => data
  );

/*******************************************************************************
 * SLICE
 ******************************************************************************/

// Define the initial state using that type
const initialexternalOboardingState = {
  data: null,
  loading: false,
  creating: false,
  invalid: false,
  error: false,
};

const initialState: ExternalOboardingData = {
  carrier: initialexternalOboardingState,
  user: initialexternalOboardingState,
};

const externalOboardingSlice = createSlice({
  name: MODULE_NAME,
  initialState: initialState,
  reducers: {
    clearError: onClearError,
  },
  extraReducers: (builder) => {
    /***************************************************************************
     * CARRIER DETAILS
     **************************************************************************/

    builder.addCase(getCarrierDetails.fulfilled, (state, action) => {
      // TODO: change if it's not a list anymore
      state.carrier.data = action.payload[0];
      state.carrier.loading = false;
      state.carrier.invalid = false;
      state.carrier.error = false;
    });
    builder.addCase(getCarrierDetails.pending, (state, action) => {
      state.carrier.loading = true;
      state.carrier.invalid = false;
      state.carrier.error = false;
    });
    builder.addCase(getCarrierDetails.rejected, (state, action) => {
      state.carrier.loading = false;
      state.carrier.invalid = true;
      state.carrier.error = true;
    });

    /***************************************************************************
     * USER DETAILS
     **************************************************************************/

    builder.addCase(getUserDetails.fulfilled, (state, action) => {
      state.user.data = action.payload;
      state.user.loading = false;
      state.user.invalid = false;
      state.user.error = false;
    });
    builder.addCase(getUserDetails.pending, (state, action) => {
      state.user.loading = true;
      state.user.invalid = false;
      state.user.error = false;
    });
    builder.addCase(getUserDetails.rejected, (state, action) => {
      state.user.loading = false;
      state.user.invalid = true;
      state.user.error = true;
    });

    /***************************************************************************
     * CREATE CARRIER
     **************************************************************************/

    builder.addCase(createCarrier.fulfilled, (state, action) => {
      state.carrier.creating = false;
      state.carrier.error = false;
    });
    builder.addCase(createCarrier.pending, (state, action) => {
      state.carrier.creating = true;
      state.carrier.error = false;
    });
    builder.addCase(createCarrier.rejected, (state, action) => {
      state.carrier.creating = false;
      state.carrier.error = true;
    });

    /***************************************************************************
     * CREATE USER
     **************************************************************************/

    builder.addCase(createCarrierUser.fulfilled, (state, action) => {
      state.user.creating = false;
      state.user.error = false;
    });
    builder.addCase(createCarrierUser.pending, (state, action) => {
      state.user.creating = true;
      state.user.error = false;
    });
    builder.addCase(createCarrierUser.rejected, (state, action) => {
      state.user.creating = false;
      state.user.error = true;
    });
  },
});

export const ExternalOnboardingState = {
  actions: {
    getCarrierDetails,
    getUserDetails,
    createCarrier,
    createCarrierUser,
    clearError: externalOboardingSlice.actions.clearError,
  },
  selectors: {
    carrierDetails,
    carrierIsLoading,
    carrierIsCreating,
    carrierIsInvalid,
    carrierHasError,
    userDetails,
    userIsLoading,
    userIsCreating,
    userIsInvalid,
    userHasError,
  },
};

// Action creators are generated for each case reducer function
export default externalOboardingSlice;
