import { createSlice, createSelector, PayloadAction } from "@reduxjs/toolkit";

import { RootState, Selector } from "state/store";

const MODULE_NAME = "externalParameters";

type ExternalParametersData = {
  externalAcessToken: string | null;
  externalUser: string | null;
};

/*******************************************************************************
 * SLICE
 ******************************************************************************/

const initialState: ExternalParametersData = {
  externalAcessToken: null,
  externalUser: null,
};

export const externalUser = (): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.externalUser,
    (data: any) => data
  );

const externalParametersSlice = createSlice({
  name: MODULE_NAME,
  initialState: initialState,
  reducers: {
    setExternalAccessTokenAndUser: (
      state: any,
      { payload }: PayloadAction<any>
    ) => {
      const { externalAccessToken, externalUser } = payload;
      state.externalAccessToken = externalAccessToken;
      state.externalUser = externalUser;
    },
  },
  extraReducers: {},
});

export const ExternalParametersState = {
  actions: {
    setExternalAccessTokenAndUser:
      externalParametersSlice.actions.setExternalAccessTokenAndUser,
  },
  selectors: {
    externalUser,
  },
};

// Action creators are generated for each case reducer function
export default externalParametersSlice;
