import Keycloak from "keycloak-js";
import jose from "node-jose";

/*******************************************************************************
 * KEYCLOAK RELATED ACTIONS
 ******************************************************************************/

// We need to know this because of different public folder locations locally
// and on server
const isRunninLocally = !window.location.href.includes("fourkites.com");

// Get app environment var
const appEnvironmentVar = window?.appConfig?.app_environment
  ? window?.appConfig?.app_environment
  : "dev";

// Get right keycloak file depending on app env
const appEnvironment =
  appEnvironmentVar !== "prod" ? `-${appEnvironmentVar}` : "";
const keycloakFilename = `keycloak${appEnvironment}.json`;
const keycloakConfigLocation = isRunninLocally
  ? `/${keycloakFilename}`
  : `/self-service/${keycloakFilename}`;

// Gets public key
const publicKeyFilename =
  appEnvironmentVar === "prod" ? "node-jose-key-prod" : "node-jose-key";
const publicKeyFile = isRunninLocally
  ? `/${publicKeyFilename}`
  : `/self-service/${publicKeyFilename}`;

// Gets location of SSO file
const silentCheckSsoLocation = isRunninLocally
  ? "/silent-check-sso.html"
  : "/self-service/silent-check-sso.html";

/**
 * Initializes Keycloak instance and calls the provided callback function if
 * successfully authenticated.
 *
 * @param onAuthenticatedCallback
 */
export const initKeycloak = (
  redirectUri: string,
  keyCloackInstance: any,
  onAuthenticatedCallback?: Function
) => {
  const doLogin = keyCloackInstance.login;

  keyCloackInstance
    .init({
      onLoad: "login-required",
      responseMode: "query",
      redirectUri: redirectUri,
    })
    .then((authenticated: boolean) => {
      if (authenticated) {
        // TODO: what to do?
        if (onAuthenticatedCallback) {
          onAuthenticatedCallback();
        }
      } else {
        doLogin();
      }
    });
};

export const signupUser = async (
  redirectUri: string,
  email: string,
  password: string,
  onAuthenticatedCallback: Function
) => {
  //@ts-ignore
  const _kc = new Keycloak(keycloakConfigLocation);

  // TODO: workaround for keycloak
  _kc._createLoginUrl = _kc.createLoginUrl;

  let publicKey = await fetch(publicKeyFile);
  //@ts-ignore
  publicKey = await publicKey.text();
  const keystore = jose.JWK.createKeyStore();

  keystore.add(publicKey, "pem").then(function (key) {
    jose.JWE.createEncrypt({ format: "compact" }, key)
      .update(
        JSON.stringify({
          username: email,
          password: password,
        })
      )
      .final()
      .then(function (encryptedData) {
        /**
         * For some reason, just passing the parameter loginHint to the init method
         * is not working. That's why I created this workaround here that makes this
         * parameter to reach the other side!
         */
        _kc.createLoginUrl = () => {
          return _kc._createLoginUrl({
            loginHint: encryptedData,
          });
        };

        // TODO: Call _kc.init Init here!!!
        return initKeycloak(redirectUri, _kc, onAuthenticatedCallback);
      });
  });
};

export const loginUser = (
  redirectUri: string,
  onAuthenticatedCallback?: Function
) => {
  //@ts-ignore
  const _kc = new Keycloak(keycloakConfigLocation);

  return initKeycloak(redirectUri, _kc, onAuthenticatedCallback);
};

/*
 * Verifies whether user is authenticated or not, and redirects if logged
 */
export const isUserAuthenticated = async () => {
  //@ts-ignore
  const keyCloackInstance: any = new Keycloak(keycloakConfigLocation);

  const authenticated: boolean = await keyCloackInstance.init({
    onLoad: "check-sso",
    silentCheckSsoRedirectUri: window.location.origin + silentCheckSsoLocation,
    responseMode: "query",
  });

  return authenticated;
};
