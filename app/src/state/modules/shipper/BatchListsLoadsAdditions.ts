import {
  createSlice,
  createAsyncThunk,
  createSelector,
  PayloadAction,
} from "@reduxjs/toolkit";
import { LoadsTrackingMode } from "state/BaseTypes";
import { RootState, Selector } from "state/store";

import batchListsApi from "api/shipper/BatchListsLoadsAdditionsApi";

const MODULE_NAME = "batchListsLoadsAdditions";

type BatchListsLoadsAdditionsData = {
  data: any[];
  pagination: {
    totalItems: number;
    currentPage: number;
    pageSize: number;
    totalPages: number;
  };
  retrieving: boolean;
  creating: boolean;
  updating: boolean;
  deleting: boolean;
  error: boolean;
};

/*******************************************************************************
 * ACTIONS
 ******************************************************************************/

/*
 * Gets the batch lists list
 */
export const retrieveBatchLists = createAsyncThunk<
  any,
  {
    shipperId: string;
    pageIndex: number;
    pageSize: number;
    modes?: LoadsTrackingMode[];
  },
  {}
>(
  `${MODULE_NAME}/retrieveBatchLists`,
  async ({ shipperId, modes, pageIndex, pageSize }, thunkAPI) => {
    const state: any = thunkAPI.getState();
    const pagination = state[MODULE_NAME]?.pagination;

    const response = await batchListsApi.retrieveBatchLists(
      shipperId,
      pageIndex || pagination.currentPage,
      pageSize || pagination.pageSize,
      modes
    );

    return response?.data;
  }
);

/*
 * Gets the batch list details
 */
export const createExportableBatchList = createAsyncThunk<
  any,
  {
    shipperId: string;
    batchListId: string | number;
  },
  {}
>(
  `${MODULE_NAME}/createExportableBatchList`,
  async ({ shipperId, batchListId }, thunkAPI) => {
    const response = await batchListsApi.createExportableBatchList(
      shipperId,
      batchListId
    );

    return response?.data;
  }
);

/*
 * Adds a new ELD integration for this carrier
 */
export const createBatchList = createAsyncThunk<
  any,
  {
    shipperId: string;
    mode: LoadsTrackingMode | null;
    userId: string;
    fileFormat: string;
    template: string;
    batchList: File;
  },
  {}
>(
  `${MODULE_NAME}/createBatchList`,
  async (
    { shipperId, mode, userId, fileFormat, template, batchList },
    thunkAPI
  ) => {
    const response = await batchListsApi.createBatchList(
      shipperId,
      mode,
      userId,
      fileFormat,
      template,
      batchList
    );

    return response?.data;
  }
);

const onClearError = (state: any, { payload }: PayloadAction<any>) => {
  state.details.error = false;
};

/*******************************************************************************
 * SELECTORS
 ******************************************************************************/

export const batchLists = (): Selector<any[]> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.data,
    (data: any[]) => data
  );

const batchListsPagination = (): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.pagination,
    (data: any) => data
  );

export const isCreating = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.creating,
    (data: any) => data
  );

export const isRetrieving = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.retrieving,
    (data: any) => data
  );

/*******************************************************************************
 * SLICE
 ******************************************************************************/

// Define the initial state using that type
const initialState: BatchListsLoadsAdditionsData = {
  data: [],
  pagination: {
    currentPage: 1,
    pageSize: 1,
    totalPages: 0,
    totalItems: 0,
  },
  retrieving: false,
  creating: false,
  updating: false,
  deleting: false,
  error: false,
};

const batchListsLoadsAdditionsSlice = createSlice({
  name: MODULE_NAME,
  initialState: initialState,
  reducers: {
    clearError: onClearError,
  },
  extraReducers: (builder) => {
    /***************************************************************************
     * RETRIEVE LIST
     **************************************************************************/

    builder.addCase(retrieveBatchLists.fulfilled, (state, action) => {
      const { details, pagination } = action.payload;

      state.data = details;
      state.pagination = {
        currentPage: pagination.current_page,
        pageSize: pagination.page_size,
        totalPages: pagination.total_pages,
        totalItems: pagination.total_items,
      };
      state.retrieving = false;
      state.error = false;
    });
    builder.addCase(retrieveBatchLists.pending, (state, action) => {
      state.retrieving = true;
      state.error = false;
    });
    builder.addCase(retrieveBatchLists.rejected, (state, action) => {
      state.retrieving = false;
      state.error = true;
    });

    /***************************************************************************
     * RETRIEVE file
     **************************************************************************/

    builder.addCase(createExportableBatchList.fulfilled, (state, action) => {
      state.retrieving = false;
      state.error = false;
    });
    builder.addCase(createExportableBatchList.pending, (state, action) => {
      state.retrieving = true;
      state.error = false;
    });
    builder.addCase(createExportableBatchList.rejected, (state, action) => {
      state.retrieving = false;
      state.error = true;
    });

    /***************************************************************************
     * CREATE
     **************************************************************************/

    builder.addCase(createBatchList.fulfilled, (state, action) => {
      const data = state.data || [];
      state.data = data.concat(action.payload);
      state.creating = false;
      state.error = false;
    });
    builder.addCase(createBatchList.pending, (state, action) => {
      state.creating = true;
      state.error = false;
    });
    builder.addCase(createBatchList.rejected, (state, action) => {
      state.creating = false;
      state.error = true;
    });
  },
});

export const BatchListsLoadsAdditionsState = {
  actions: {
    createBatchList,
    retrieveBatchLists,
    createExportableBatchList,
    clearError: batchListsLoadsAdditionsSlice.actions.clearError,
  },
  selectors: {
    batchLists,
    batchListsPagination,
    isCreating,
    isRetrieving,
  },
};

// Action creators are generated for each case reducer function
export default batchListsLoadsAdditionsSlice;
