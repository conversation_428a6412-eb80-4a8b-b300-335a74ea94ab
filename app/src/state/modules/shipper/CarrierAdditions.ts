import {
  createSlice,
  createAsyncThunk,
  createSelector,
  PayloadAction,
} from "@reduxjs/toolkit";
import carrierAdditionsApi from "api/shipper/CarrierAdditionsApi";

import { RootState, Selector } from "state/store";
import { LoadsTrackingMode, typedModeFromAction } from "state/BaseTypes";

import { CarrierAdditionsData } from "./CarrierAdditions.types";

const MODULE_NAME = "carrierAdditions";

/*******************************************************************************
 * ACTIONS
 ******************************************************************************/

/*
 * Adds an existing carrier to a shipper's network
 */
export const addCarriersToNetwork = createAsyncThunk<
  any,
  { shipperId: string; mode: LoadsTrackingMode; carriers: any[]; user: any; source: string; tpl: string;},
  {}
>(
  `${MODULE_NAME}/addCarriersToNetwork`,
  async ({ shipperId, mode, carriers, user, source, tpl}, thunkAPI) => {
    const response = await carrierAdditionsApi.addCarriersToNetwork(
      shipperId,
      mode,
      carriers,
      user,
      source,
      tpl
    );

    return response.data;
  }
);

export const getCarrierAdditionsResults = createAsyncThunk<
  any,
  { shipperId: string; mode: LoadsTrackingMode; requestId: string },
  {}
>(
  `${MODULE_NAME}/getCarrierAdditionsResults`,
  async ({ shipperId, mode, requestId }, thunkAPI) => {
    const response = await carrierAdditionsApi.getCarrierAdditionsResults(
      shipperId,
      mode,
      requestId
    );

    return response.data;
  }
);

/*
 * Sends list of carriers to be processed
 */
export const sendBulkList = createAsyncThunk<
  any,
  { shipperId: string; mode: LoadsTrackingMode; bulkList: File },
  {}
>(
  `${MODULE_NAME}/sendBulkList`,
  async ({ shipperId, mode, bulkList }, thunkAPI) => {
    const response = await carrierAdditionsApi.sendBulkList(
      shipperId,
      mode,
      bulkList
    );

    return response.data;
  }
);

/*
 * Get status of carrier additions
 */
export const getBulkListResults = createAsyncThunk<
  any,
  { shipperId: string; mode: LoadsTrackingMode; requestId: string },
  {}
>(
  `${MODULE_NAME}/getBulkListResults`,
  async ({ shipperId, mode, requestId }, thunkAPI) => {
    const response = await carrierAdditionsApi.getBulkListResults(
      shipperId,
      mode,
      requestId
    );

    return response.data;
  }
);

/*
 * Get status of carrier additions
 */
export const getBulkListTemplate = createAsyncThunk<
  any,
  { mode: LoadsTrackingMode },
  {}
>(`${MODULE_NAME}/getBulkListTemplate`, async ({ mode }, thunkAPI) => {
  const response = await carrierAdditionsApi.getBulkListTemplate(mode);

  return response;
});

const onClearAdditions = (state: any, { payload }: PayloadAction<any>) => {
  const mode = payload as LoadsTrackingMode;
  state.addition[mode] = initialAdditionState;
};

const onClearAdditionError = (state: any, { payload }: PayloadAction<any>) => {
  const mode = payload as LoadsTrackingMode;
  state.addition[mode].error = false;
};

const onClearBulk = (state: any, { payload }: PayloadAction<any>) => {
  const mode = payload as LoadsTrackingMode;
  state.bulk[mode] = initialBulkState;
};

const onClearBulkError = (state: any, { payload }: PayloadAction<any>) => {
  const mode = payload as LoadsTrackingMode;
  state.bulk[mode].error = false;
};

/*******************************************************************************
 * Helpers
 ******************************************************************************/

const adjustProgressPercentage = (
  currentProgressPercentage: number,
  progressPercentage: number
) => {
  // NOTE: as we return only 4 states, we slightly update the progress again.
  // This allows to make up to 2500 calls to get progress.
  // TODO: handle case where 2500 calls is not enought, what should we do?
  return progressPercentage <= currentProgressPercentage
    ? currentProgressPercentage + 0.01
    : progressPercentage;
};

/*******************************************************************************
 * SELECTORS
 ******************************************************************************/

const carrierAdditionRequestIdByMode = (
  mode: LoadsTrackingMode
): Selector<string> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME].addition[mode].requestId,
    (data: string) => data
  );

const carrierAdditionProgressByMode = (
  mode: LoadsTrackingMode
): Selector<number> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME].addition[mode].progress,
    (data: number) => data
  );

const carrierAdditionErrorByMode = (
  mode: LoadsTrackingMode
): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME].addition[mode].error,
    (data: boolean) => data
  );

const carrierBulkRequestIdByMode = (
  mode: LoadsTrackingMode
): Selector<string> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME].bulk[mode].requestId,
    (data: string) => data
  );

const carrierBulkProgressByMode = (mode: LoadsTrackingMode): Selector<number> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME].bulk[mode].progress,
    (data: number) => data
  );

const carrierBulkResultsByMode = (mode: LoadsTrackingMode): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME].bulk[mode].results,
    (data: any) => data
  );

const carrierBulkErrorByMode = (mode: LoadsTrackingMode): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME].bulk[mode].error,
    (data: boolean) => data
  );

/*******************************************************************************
 * SLICE
 ******************************************************************************/

// Define the initial state using that type
const initialAdditionState = {
  requestId: "",
  progress: 0,
  processing: false,
  error: false,
  results: {
    success: [],
    errors: [],
  },
};
const initialBulkState = {
  requestId: "",
  progress: 0,
  processing: false,
  error: false,
  results: {
    carriersOnPlatform: [],
    newCarriers: [],
    carriersWithErrors: [],
  },
};
const initialState: CarrierAdditionsData = {
  addition: {
    ltl: initialAdditionState,
    ftl: initialAdditionState,
    parcel: initialAdditionState,
    ocean: initialAdditionState,
    air: initialAdditionState,
  },
  bulk: {
    ltl: initialBulkState,
    ftl: initialBulkState,
    parcel: initialBulkState,
    ocean: initialBulkState,
    air: initialBulkState,
  },
};

const carrierAdditionsSlice = createSlice({
  name: MODULE_NAME,
  initialState,
  reducers: {
    clearAdditions: onClearAdditions,
    clearAdditionError: onClearAdditionError,
    clearBulk: onClearBulk,
    clearBulkError: onClearBulkError,
  },
  extraReducers: (builder) => {
    /***************************************************************************
     * ADDITIONS
     **************************************************************************/

    builder.addCase(addCarriersToNetwork.fulfilled, (state, action) => {
      const data = action.payload.data;

      state.addition[typedModeFromAction(action)].requestId = data.request_id;
      state.addition[typedModeFromAction(action)].progress = 0;
      state.addition[typedModeFromAction(action)].processing = false;
      state.addition[typedModeFromAction(action)].error = false;
    });
    builder.addCase(addCarriersToNetwork.rejected, (state, action) => {
      state.addition[typedModeFromAction(action)].requestId = "";
      state.addition[typedModeFromAction(action)].progress = 0;
      state.addition[typedModeFromAction(action)].processing = false;
      state.addition[typedModeFromAction(action)].error = true;
    });
    builder.addCase(addCarriersToNetwork.pending, (state, action) => {
      state.addition[typedModeFromAction(action)].processing = true;
      state.addition[typedModeFromAction(action)].error = false;
    });

    // PROGRESS
    builder.addCase(getCarrierAdditionsResults.fulfilled, (state, action) => {
      const data = action.payload.data;

      const progress = data?.progress?.percentage;
      const resultsData = data.results;
      const currentProgress =
        state.addition[typedModeFromAction(action)].progress;

      state.addition[
        typedModeFromAction(action)
      ].progress = adjustProgressPercentage(currentProgress, progress);
      state.addition[typedModeFromAction(action)].processing = false;
      state.addition[typedModeFromAction(action)].error = false;
    });
    builder.addCase(getCarrierAdditionsResults.rejected, (state, action) => {
      state.addition[typedModeFromAction(action)].progress = 0;
      state.addition[typedModeFromAction(action)].processing = false;
      state.addition[typedModeFromAction(action)].error = true;
    });
    builder.addCase(getCarrierAdditionsResults.pending, (state, action) => {
      state.addition[typedModeFromAction(action)].processing = true;
      state.addition[typedModeFromAction(action)].error = false;
    });

    /***************************************************************************
     * BULK LIST
     **************************************************************************/

    builder.addCase(sendBulkList.fulfilled, (state, action) => {
      const data = action.payload.data;

      state.bulk[typedModeFromAction(action)].requestId = data.request_id;
      state.bulk[typedModeFromAction(action)].progress = 0;
      state.bulk[typedModeFromAction(action)].processing = false;
      state.bulk[typedModeFromAction(action)].error = false;
    });
    builder.addCase(sendBulkList.rejected, (state, action) => {
      state.bulk[typedModeFromAction(action)].requestId = "";
      state.bulk[typedModeFromAction(action)].progress = 0;
      state.bulk[typedModeFromAction(action)].processing = true;
      state.bulk[typedModeFromAction(action)].error = false;
    });
    builder.addCase(sendBulkList.pending, (state, action) => {
      state.bulk[typedModeFromAction(action)].processing = true;
      state.bulk[typedModeFromAction(action)].error = false;
    });

    // PROGRESS
    builder.addCase(getBulkListResults.fulfilled, (state, action) => {
      const data = action.payload.data;

      const progress = data?.progress?.percentage;
      const progressState = data?.progress?.state;
      const resultsData = data.results;
      const currentProgress = state.bulk[typedModeFromAction(action)].progress;

      state.bulk[
        typedModeFromAction(action)
      ].progress = adjustProgressPercentage(currentProgress, progress);
      state.bulk[typedModeFromAction(action)].results = {
        carriersOnPlatform: resultsData?.existing || [],
        newCarriers: resultsData?.new || [],
        carriersWithErrors: resultsData?.errors || [],
      };

      state.bulk[typedModeFromAction(action)].processing = false;
      // Notify error if state failed
      state.bulk[typedModeFromAction(action)].error =
        progressState === "failed";
    });
    builder.addCase(getBulkListResults.rejected, (state, action) => {
      state.bulk[typedModeFromAction(action)].progress = 0;
      state.bulk[typedModeFromAction(action)].processing = false;
      state.bulk[typedModeFromAction(action)].error = true;
    });
    builder.addCase(getBulkListResults.pending, (state, action) => {
      state.bulk[typedModeFromAction(action)].processing = true;
      state.bulk[typedModeFromAction(action)].error = false;
    });
  },
});

export const CarrierAdditionsState = {
  actions: {
    addCarriersToNetwork,
    getCarrierAdditionsResults,
    clearAdditions: carrierAdditionsSlice.actions.clearAdditions,
    clearAdditionError: carrierAdditionsSlice.actions.clearAdditionError,
    getBulkListTemplate,
    sendBulkList,
    getBulkListResults,
    clearBulk: carrierAdditionsSlice.actions.clearBulk,
    clearBulkError: carrierAdditionsSlice.actions.clearBulkError,
  },
  selectors: {
    carrierAdditionProgressByMode,
    carrierAdditionRequestIdByMode,
    carrierAdditionErrorByMode,
    carrierBulkResultsByMode,
    carrierBulkRequestIdByMode,
    carrierBulkProgressByMode,
    carrierBulkErrorByMode,
  },
};

export default carrierAdditionsSlice;
