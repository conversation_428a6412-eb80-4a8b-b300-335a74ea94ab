export type AdditionStateData = {
  requestId: string;
  progress: number;
  processing: boolean;
  error: boolean;
  results: {
    success: any[];
    errors: any[];
  };
};

export type BulkStateData = {
  requestId: string;
  progress: number;
  processing: boolean;
  error: boolean;
  results: {
    carriersOnPlatform: any[];
    newCarriers: any[];
    carriersWithErrors: any[];
  };
};

export type CarrierAdditionsData = {
  addition: {
    ltl: AdditionStateData;
    ftl: AdditionStateData;
    parcel: AdditionStateData;
    ocean: AdditionStateData;
    air: AdditionStateData;
  };
  bulk: {
    ltl: BulkStateData;
    ftl: BulkStateData;
    parcel: BulkStateData;
    ocean: BulkStateData;
    air: BulkStateData;
  };
};
