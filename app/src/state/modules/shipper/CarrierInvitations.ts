import {
  createSlice,
  createAsyncThunk,
  createSelector,
} from "@reduxjs/toolkit";
import { RootState, Selector } from "state/store";
import { LoadsTrackingMode, typedModeFromAction } from "state/BaseTypes";

import invitationsApi from "api/shipper/CarrierInvitationsApi";

const MODULE_NAME = "carrierInvitations";

type CarrierInvitationsModeData = {
  data: any;
  sending: boolean;
  loading: boolean;
  editing: boolean;
  error: boolean;
};

type CarrierInvitationsData = {
  invitations: {
    ltl: CarrierInvitationsModeData;
    ftl: CarrierInvitationsModeData;
    parcel: CarrierInvitationsModeData;
    ocean: CarrierInvitationsModeData;
    air: CarrierInvitationsModeData;
  };
  inviterToken: string;
};

/*******************************************************************************
 * ACTIONS
 ******************************************************************************/

/*
 * Invites carriers to join the platform
 */
export const inviteCarriersToNetwork = createAsyncThunk<
  any,
  {
    shipperId: string;
    mode: LoadsTrackingMode;
    carriers: any[];
    user: any;
    source: string;
    tpl: string;
  },
  {}
>(
  `${MODULE_NAME}/inviteCarriersToNetwork`,
  async ({ shipperId, mode, carriers, user, source, tpl}, thunkAPI) => {
    const response = await invitationsApi.inviteCarriersToNetwork(
      mode,
      shipperId,
      carriers,
      user,
      source,
      tpl
    );

    //return response?.data;
  }
);

/*
 * Gets the invitations for this carrier
 */
export const getCarrierInvitations = createAsyncThunk<
  any,
  { shipperId: string; networkId: string; mode: LoadsTrackingMode },
  {}
>(
  `${MODULE_NAME}/getCarrierInvitations`,
  async ({ shipperId, networkId, mode }, thunkAPI) => {
    const response = await invitationsApi.getCarrierInvitations(
      mode,
      shipperId,
      networkId
    );

    return response?.data;
  }
);

/*
 * Resends invitation to carrier
 */
export const resendInvitation = createAsyncThunk<
  any,
  {
    shipperId: string;
    networkId: string;
    mode: LoadsTrackingMode;
  },
  {}
>(
  `${MODULE_NAME}/resendInvitation`,
  async ({ shipperId, networkId, mode }, thunkAPI) => {
    const response = await invitationsApi.resendInvitation(
      mode,
      shipperId,
      networkId
    );

    return response?.data;
  }
);

/*
 * Get the invitation token of the shipper/broker
 *
 */
export const getInviterToken = createAsyncThunk<any, { shipperId: string }, {}>(
  `${MODULE_NAME}/getInviterToken`,
  async ({ shipperId }, thunkAPI) => {
    const response = await invitationsApi.getInviterToken(shipperId);
    return response.data.inviter_token;
  }
);

/*
 * Resend Invites - batch resubmissios to carrier
 */
export const resendAllInvitations = createAsyncThunk<
  any,
  {
    shipperId: string;
    mode: LoadsTrackingMode;
  },
  {}
>(
  `${MODULE_NAME}/resendAllInvitations`,
  async ({ shipperId, mode }, thunkAPI) => {
    const response = await invitationsApi.resendAllInvitations(mode, shipperId);

    return response?.data;
  }
);

/*
 * Deletes an invitation to carrier
 */
export const deleteInvitation = createAsyncThunk<
  any,
  {
    shipperId: string;
    invitationId: string;
  },
  {}
>(
  `${MODULE_NAME}/deleteInvitation`,
  async ({ shipperId, invitationId }, thunkAPI) => {
    const response = await invitationsApi.deleteInvitation(
      shipperId,
      invitationId
    );

    return response?.data;
  }
);
/*******************************************************************************
 * SELECTORS
 ******************************************************************************/

export const carrierInvitationsByMode = (
  mode: LoadsTrackingMode
): Selector<any[]> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.invitations[mode]?.data,
    (data: any) => data
  );

const carrierInvitationsErrorByMode = (
  mode: LoadsTrackingMode
): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME].invitations[mode].error,
    (data: boolean) => data
  );

export const isLoadingCarrierInvitationsByMode = (
  mode: LoadsTrackingMode
): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.invitations[mode]?.loading,
    (data: any) => data
  );

export const isEditingCarrierInvitationsByMode = (
  mode: LoadsTrackingMode
): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.invitations[mode]?.editing,
    (data: any) => data
  );

export const isSendingCarrierInvitationsByMode = (
  mode: LoadsTrackingMode
): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.invitations[mode]?.sending,
    (data: any) => data
  );

export const inviterToken = (): Selector<string> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.inviterToken,
    (data: string) => data
  );

/*******************************************************************************
 * SLICE
 ******************************************************************************/

// Define the initial state using that type
const initialCarrierInvitationsState = {
  data: [] as any[],
  sending: false,
  loading: true,
  editing: false,
  error: false,
};
const initialState: CarrierInvitationsData = {
  invitations: {
    ltl: initialCarrierInvitationsState,
    ftl: initialCarrierInvitationsState,
    parcel: initialCarrierInvitationsState,
    ocean: initialCarrierInvitationsState,
    air: initialCarrierInvitationsState,
  },
  inviterToken: "",
};

const carrierInvitationsSlice = createSlice({
  name: MODULE_NAME,
  initialState: initialState,
  reducers: {},
  extraReducers: (builder) => {
    /***************************************************************************
     * SEND INVITATIONS
     **************************************************************************/

    builder.addCase(inviteCarriersToNetwork.fulfilled, (state, action) => {
      state.invitations[typedModeFromAction(action)].sending = false;
      state.invitations[typedModeFromAction(action)].error = false;
    });
    builder.addCase(inviteCarriersToNetwork.pending, (state, action) => {
      state.invitations[typedModeFromAction(action)].sending = true;
      state.invitations[typedModeFromAction(action)].error = false;
    });
    builder.addCase(inviteCarriersToNetwork.rejected, (state, action) => {
      state.invitations[typedModeFromAction(action)].sending = false;
      state.invitations[typedModeFromAction(action)].error = true;
    });

    /***************************************************************************
     * LOAD INVITATIONS
     **************************************************************************/

    builder.addCase(getCarrierInvitations.fulfilled, (state, action) => {
      state.invitations[typedModeFromAction(action)].data = action.payload;
      state.invitations[typedModeFromAction(action)].loading = false;
      state.invitations[typedModeFromAction(action)].error = false;
    });
    builder.addCase(getCarrierInvitations.pending, (state, action) => {
      state.invitations[typedModeFromAction(action)].loading = true;
      state.invitations[typedModeFromAction(action)].error = false;
    });
    builder.addCase(getCarrierInvitations.rejected, (state, action) => {
      state.invitations[typedModeFromAction(action)].loading = false;
      state.invitations[typedModeFromAction(action)].error = true;
    });

    /***************************************************************************
     * RESEND INVITATIONS
     **************************************************************************/

    builder.addCase(resendInvitation.fulfilled, (state, action) => {
      state.invitations[typedModeFromAction(action)].editing = false;
      state.invitations[typedModeFromAction(action)].error = false;
    });
    builder.addCase(resendInvitation.rejected, (state, action) => {
      state.invitations[typedModeFromAction(action)].editing = false;
      state.invitations[typedModeFromAction(action)].error = true;
    });
    builder.addCase(resendInvitation.pending, (state, action) => {
      state.invitations[typedModeFromAction(action)].editing = true;
      state.invitations[typedModeFromAction(action)].error = false;
    });

    /***************************************************************************
     * GET INVITER TOKEN
     **************************************************************************/

    builder.addCase(getInviterToken.fulfilled, (state, action) => {
      state.inviterToken = action.payload;
    });
    builder.addCase(getInviterToken.pending, (state, action) => {
      state.inviterToken = "";
    });
    builder.addCase(getInviterToken.rejected, (state, action) => {
      state.inviterToken = "";
    });

    /***************************************************************************
     * RESEND INVITES - BATCH RESUBMISSIONS
     **************************************************************************/

    builder.addCase(resendAllInvitations.fulfilled, (state, action) => {
      state.invitations[typedModeFromAction(action)].sending = false;
      state.invitations[typedModeFromAction(action)].error = false;
    });
    builder.addCase(resendAllInvitations.pending, (state, action) => {
      state.invitations[typedModeFromAction(action)].sending = true;
      state.invitations[typedModeFromAction(action)].error = false;
    });
    builder.addCase(resendAllInvitations.rejected, (state, action) => {
      state.invitations[typedModeFromAction(action)].sending = false;
      state.invitations[typedModeFromAction(action)].error = true;
    });
  },
});

export const CarrierInvitationsState = {
  actions: {
    inviteCarriersToNetwork,
    getCarrierInvitations,
    deleteInvitation,
    resendInvitation,
    getInviterToken,
    resendAllInvitations,
  },
  selectors: {
    carrierInvitationsByMode,
    carrierInvitationsErrorByMode,
    isSendingCarrierInvitationsByMode,
    isLoadingCarrierInvitationsByMode,
    isEditingCarrierInvitationsByMode,
    inviterToken,
  },
};

export default carrierInvitationsSlice;
