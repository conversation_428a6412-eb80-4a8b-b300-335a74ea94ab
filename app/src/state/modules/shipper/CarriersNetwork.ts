import {
  createSlice,
  createAsyncThunk,
  createSelector,
  PayloadAction,
} from "@reduxjs/toolkit";
import { RootState, Selector } from "state/store";
import {
  LoadsTrackingMode,
  NetworkStatusType,
  typedModeFromAction,
} from "state/BaseTypes";

import carriersNetworkApi from "api/shipper/CarriersNetworkApi";

/*******************************************************************************
 * TYPES
 ******************************************************************************/

const MODULE_NAME = "carriersNetwork";

type CarriersModeData = {
  data: any[];
  filters: {
    query: string | null;
    networkStatus: NetworkStatusType;
    filterBy: {
      columnName: string;
      value: string;
      filterType: "contains" | "in" | "equals";
    }[];
  };
  pagination: {
    totalItems: number;
    currentPage: number;
    pageSize: number;
    totalPages: number;
  };
  loading: boolean;
  editing: boolean;
  error: boolean;
};

type CarriersData = {
  carriers: {
    ltl: CarriersModeData;
    ftl: CarriersModeData;
    parcel: CarriersModeData;
    ocean: CarriersModeData;
    air: CarriersModeData;
  };
};

/*******************************************************************************
 * HELPERS
 ******************************************************************************/

const DEFAULT_PAGINATION_PARAMETERS = {
  currentPage: 1,
  pageSize: 25,
};

const getFilterByParams = (
  filterBy?: {
    columnName: string;
    additionalColumnNames?: string;
    value: string;
    filterType: "contains" | "in" | "equals";
  }[]
): string => {
  if (!filterBy) {
    return JSON.stringify([]);
  }

  return JSON.stringify(
    filterBy?.map((f: any) => ({
      column_name: f?.columnName,
      additional_column_names: f?.additionalColumnNames || null,
      value: f?.value,
      filter_type: f?.filterType,
    }))
  );
};

const getSortByParams = (
  sortBy: { columnName: string; isDesc: boolean } | undefined
): string => {
  if (!sortBy) {
    return JSON.stringify([]);
  }

  return JSON.stringify([
    {
      column_name: sortBy.columnName,
      is_desc: sortBy.isDesc,
    },
  ]);
};

const getPaginationParams = (pageSize?: number, currentPage?: number) => {
  return JSON.stringify({
    current_page: currentPage
      ? currentPage
      : DEFAULT_PAGINATION_PARAMETERS.currentPage,
    page_size: pageSize ? pageSize : DEFAULT_PAGINATION_PARAMETERS.pageSize,
  });
};

/*******************************************************************************
 * ACTIONS
 ******************************************************************************/

/**************** CARRIERS NETWORK ****************/

/*
 * Gets the list of carriers, including infor about which carriers are in this
 * shippers network
 */
export const getCarriers = createAsyncThunk<
  any,
  {
    shipperId: string;
    mode: LoadsTrackingMode;
    pageSize?: number;
    currentPage?: number;
    sortBy?: {
      columnName: string;
      isDesc: boolean;
    };
  },
  {}
>(
  `${MODULE_NAME}/getCarriers`,
  async ({ shipperId, mode, pageSize, currentPage, sortBy }, thunkAPI) => {
    // Get filters for call
    const state: any = thunkAPI.getState();
    const filters = state[MODULE_NAME]?.carriers[mode]?.filters;
    const { networkStatus, filterBy } = filters;

    const filterByParams = getFilterByParams(filterBy);
    const sortByParams = getSortByParams(sortBy);
    const paginationParams = getPaginationParams(pageSize, currentPage);

    const response = await carriersNetworkApi.getCarriers(
      shipperId,
      mode,
      networkStatus,
      filterByParams,
      sortByParams,
      paginationParams
    );

    return response;
  }
);

/**************** CARRIERS CAPABILITIES ****************/

/*
 * Connects a carrier to a shipper network, passing the carrier capabilities
 * and credentials
 */
export const connectCarrier = createAsyncThunk<
  any,
  {
    shipperId: string;
    carrierId: string;
    capabilities: any;
    mode: LoadsTrackingMode;
  },
  {}
>(
  `${MODULE_NAME}/connectCarrier`,
  async ({ shipperId, carrierId, capabilities, mode }, thunkAPI) => {
    const response = await carriersNetworkApi.connectCarrier(
      shipperId,
      carrierId,
      capabilities,
      mode
    );

    return response;
  }
);


/*
 * Store Credentials   from carrier to a shipper network, passing the carrier capabilities
 * and credentials
 */
export const storeCredential = createAsyncThunk<
  any,
  {
    shipperId: string;
    carrierId: string;
    capabilities: any;
    mode: LoadsTrackingMode;
  },
  {}
>(
  `${MODULE_NAME}/storeCredential`,
  async ({ shipperId, carrierId, capabilities, mode }, thunkAPI) => {
    const response = await carriersNetworkApi.storeCredential(
      shipperId,
      carrierId,
      capabilities,
      mode
    );

    return response;
  }
);

/*
 * Changes capabilities and credentials of carrier in this shipper netowork
 */
export const changeCarrierCapabilities = createAsyncThunk<
  any,
  {
    shipperId: string;
    carrierId: string;
    capabilities: any;
    mode: LoadsTrackingMode;
  },
  {}
>(
  `${MODULE_NAME}/changeCarrierCapabilities`,
  async ({ shipperId, carrierId, capabilities, mode }, thunkAPI) => {
    const response = await carriersNetworkApi.changeCarrierCapabilities(
      shipperId,
      carrierId,
      capabilities,
      mode
    );

    return response;
  }
);

/*
 * Disconnects this carrier from the shipper network
 */
export const disconnectCarrier = createAsyncThunk<
  any,
  { shipperId: string; carrierId: string; mode: LoadsTrackingMode },
  {}
>(
  `${MODULE_NAME}/disconnectCarrier`,
  async ({ shipperId, carrierId, mode }, thunkAPI) => {
    const response = await carriersNetworkApi.disconnectCarrier(
      shipperId,
      carrierId,
      mode
    );

    return response;
  }
);

const onClearError = (state: any, { payload }: PayloadAction<any>) => {
  const mode = payload as LoadsTrackingMode;
  state.carriers[mode].error = false;
};

const onChangeNetworkFilters = (
  state: any,
  { payload }: PayloadAction<any>
) => {
  const { mode, networkStatus, query } = payload;

  if (networkStatus != null) {
    state.carriers[mode].filters.networkStatus =
      networkStatus as NetworkStatusType;
  }

  // Change filterBy parameter based on user query
  if (query != null) {
    state.carriers[mode].filters.query = query;
    state.carriers[mode].filters.filterBy = [
      {
        // NOTE: BE needs this specifically in order to be able to filter by name
        columnName: "target_company_name",
        additionalColumnNames: ["usdot", "carrier_codes"],
        value: query,
        filterType: "contains",
      },
      // TODO: add other possibility of filters
    ];
  }
};

/*******************************************************************************
 * SELECTORS
 ******************************************************************************/

const carriersByMode = (mode: LoadsTrackingMode): Selector<any[]> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.carriers[mode]?.data,
    (data: any[]) => data
  );

export const isLoadingCarriersByMode = (
  mode: LoadsTrackingMode
): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.carriers[mode]?.loading,
    (data: boolean) => data
  );

export const isEditingCarriersByMode = (
  mode: LoadsTrackingMode
): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.carriers[mode]?.editing,
    (data: boolean) => data
  );

export const hasErrorByMode = (mode: LoadsTrackingMode): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.carriers[mode]?.error,
    (data: boolean) => data
  );

export const networkFiltersByMode = (mode: LoadsTrackingMode): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.carriers[mode]?.filters,
    (data: any) => data
  );

export const paginationByMode = (mode: LoadsTrackingMode): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.carriers[mode]?.pagination,
    (data: any) => data
  );

/*******************************************************************************
 * SLICE
 ******************************************************************************/

// Define the initial state using that type
const initialCarrierStateForFtlMode = {
  data: [] as any[],
  filters: {
    query: null,
    networkStatus: "connected" as NetworkStatusType,
    filterBy: [],
  },
  sortBy: [],
  pagination: {
    currentPage: 1,
    pageSize: 25,
    totalItems: 0,
    totalPages: 0,
  },
  loading: true,
  editing: false,
  error: false,
};

const initialCarrierStateForOtherModes = {
  data: [] as any[],
  filters: {
    query: null,
    networkStatus: "all" as NetworkStatusType,
    filterBy: [],
  },
  sortBy: [],
  pagination: {
    currentPage: 1,
    pageSize: 25,
    totalItems: 0,
    totalPages: 0,
  },
  loading: true,
  editing: false,
  error: false,
};

const initialState: CarriersData = {
  carriers: {
    ltl: initialCarrierStateForOtherModes,
    ftl: initialCarrierStateForFtlMode,
    parcel: initialCarrierStateForOtherModes,
    ocean: initialCarrierStateForOtherModes,
    air: initialCarrierStateForOtherModes,
  },
};

const carriersSlice = createSlice({
  name: MODULE_NAME,
  initialState,
  reducers: {
    clearError: onClearError,
    changeNetworkFilters: onChangeNetworkFilters,
  },
  extraReducers: (builder) => {
    /***************************************************************************
     * CARRIERS NETWORK LIST
     **************************************************************************/

    builder.addCase(getCarriers.fulfilled, (state, action) => {
      // List
      state.carriers[typedModeFromAction(action)].data =
        action?.payload?.details || [];
      // Pagination
      const { current_page, page_size, total_items, total_pages } =
        action?.payload?.pagination || {};
      state.carriers[typedModeFromAction(action)].pagination = {
        currentPage: current_page,
        pageSize: page_size,
        totalItems: total_items,
        totalPages: total_pages,
      };
      // Status
      state.carriers[typedModeFromAction(action)].loading = false;
      state.carriers[typedModeFromAction(action)].error = false;
    });
    builder.addCase(getCarriers.rejected, (state, action) => {
      // Reset the list in case we've got an error
      state.carriers[typedModeFromAction(action)].data = [];
      state.carriers[typedModeFromAction(action)].loading = false;
      state.carriers[typedModeFromAction(action)].error = true;
    });
    builder.addCase(getCarriers.pending, (state, action) => {
      state.carriers[typedModeFromAction(action)].loading = true;
      state.carriers[typedModeFromAction(action)].error = false;
    });

    /***************************************************************************
     * CARRIER CAPABILITIES
     **************************************************************************/

    [connectCarrier, changeCarrierCapabilities, disconnectCarrier].forEach(
      (actionCreator) => {
        builder.addCase(actionCreator.fulfilled, (state, action) => {
          state.carriers[typedModeFromAction(action)].editing = false;
          state.carriers[typedModeFromAction(action)].error = false;
        });
        builder.addCase(actionCreator.rejected, (state, action) => {
          state.carriers[typedModeFromAction(action)].editing = false;
          state.carriers[typedModeFromAction(action)].error = true;
        });
        builder.addCase(actionCreator.pending, (state, action) => {
          state.carriers[typedModeFromAction(action)].editing = true;
          state.carriers[typedModeFromAction(action)].error = false;
        });
      }
    );
  },
});

export const CarriersNetworkState = {
  actions: {
    getCarriers,
    connectCarrier,
    disconnectCarrier,
    changeCarrierCapabilities,
    storeCredential,
    clearError: carriersSlice.actions.clearError,
    changeNetworkFilters: carriersSlice.actions.changeNetworkFilters,
  },
  selectors: {
    carriersByMode,
    isLoadingCarriersByMode,
    isEditingCarriersByMode,
    hasErrorByMode,
    networkFiltersByMode,
    paginationByMode,
  },
};

// Action creators are generated for each case reducer function
export default carriersSlice;
