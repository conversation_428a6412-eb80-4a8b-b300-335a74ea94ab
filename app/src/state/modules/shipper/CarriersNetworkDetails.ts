import {
  createSlice,
  createAsyncThunk,
  createSelector,
} from "@reduxjs/toolkit";
import { RootState, Selector } from "state/store";
import { LoadsTrackingMode, CarrierContact } from "state/BaseTypes";

import carriersNetworkDetailsApi from "api/shipper/CarriersNetworkDetailsApi";

const MODULE_NAME = "carriersNetworkDetails";

/*******************************************************************************
 * ACTIONS
 ******************************************************************************/

/**************** CARRIER DETAILS ****************/

/*
 * Gets the details fo a given carrier
 */
export const getCarrierDetails = createAsyncThunk<
  any,
  { shipperId: string; carrierId: string; mode: LoadsTrackingMode },
  {}
>(
  `${MODULE_NAME}/getCarrierDetails`,
  async ({ shipperId, carrierId, mode }, thunkAPI) => {
    // TODO: manage this here in the future to not fetch details again every
    // time, rather storing it in redux after it has been initally fetched. This
    // adds complexity to our redux login but makes the app faster. Do it after
    // core functionaly is shipped

    const response = await carriersNetworkDetailsApi.getCarrierDetails(
      shipperId,
      carrierId,
      mode
    );

    return response?.data?.details;
  }
);

/**************** CARRIER CODES ****************/

/*
 * Add a carrier code to a given carrier
 */
export const createCarrierCode = createAsyncThunk<
  any,
  {
    shipperId: string;
    carrierId: string;
    mode: LoadsTrackingMode;
    code: string;
  },
  {}
>(
  `${MODULE_NAME}/createCarrierCode`,
  async ({ shipperId, carrierId, mode, code }, thunkAPI) => {
    const response = await carriersNetworkDetailsApi.createCarrierCode(
      shipperId,
      carrierId,
      mode,
      code
    );

    return { code: response.data.data.code };
  }
);

/*
 * Removes a carrier code from a given carrier
 */
export const deleteCarrierCode = createAsyncThunk<
  any,
  {
    shipperId: string;
    carrierId: string;
    mode: LoadsTrackingMode;
    code: string;
  },
  {}
>(
  `${MODULE_NAME}/deleteCarrierCode`,
  async ({ shipperId, carrierId, mode, code }, thunkAPI) => {
    const response = await carriersNetworkDetailsApi.deleteCarrierCode(
      shipperId,
      carrierId,
      mode,
      code
    );

    return { code: response.data.data.code };
  }
);

// CARRIER CONTACTS

/*
 * Adds a carrier contact
 */
export const addCarrierContact = createAsyncThunk<
  any,
  {
    shipperId: string;
    networkId: string;
    mode: LoadsTrackingMode;
    contact: CarrierContact;
    user: any;
  },
  {}
>(
  `${MODULE_NAME}/addCarrierContact`,
  async ({ shipperId, networkId, mode, contact, user }, thunkAPI) => {
    const response = await carriersNetworkDetailsApi.addCarrierContact(
      shipperId,
      networkId,
      mode,
      contact,
      user
    );

    return response.data.data;
  }
);

// HELP REQUESTS

/*
 * Initiate help request for the pending invite
 */
export const initiateHelpRequest = createAsyncThunk<
  any,
  {
    shipperId: string;
    networkId: string;
  },
  {}
>(
  `${MODULE_NAME}/initiateHelpRequest`,
  async ({ shipperId, networkId }, thunkAPI) => {
    const response = await carriersNetworkDetailsApi.initiateHelpRequest(
      shipperId,
      networkId
    );

    return response;
  }
);

// EXPORT EXCEL
/*
 * Get all carrier network details of a shipper & export as excel through email
 *
 */
export const exportCarrierList = createAsyncThunk<
  any,
  {
    shipperId: string;
  },
  {}
>(`${MODULE_NAME}/exportable-list`, async ({ shipperId }, thunkAPI) => {
  const response = await carriersNetworkDetailsApi.createExportableList(
    shipperId
  );
  return response;
});

/*
 * Get general details about the export with regards to the shipper's carrier
 *
 */
export const getExportInfo = createAsyncThunk<
  any,
  {
    shipperId: string;
  },
  {}
>(`${MODULE_NAME}/exportable-lists/info`, async ({ shipperId }, thunkAPI) => {
  const response = await carriersNetworkDetailsApi.getExportInfo(shipperId);
  return response.data;
});

/*******************************************************************************
 * SELECTORS
 ******************************************************************************/

export const carrierDetails = (): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.carrierDetails?.data,
    (data: any) => data
  );

export const isLoadingCarrierDetails = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.carrierDetails?.loading,
    (data: any) => data
  );

export const isEditingCarrierCodes = (): Selector<boolean> =>
  createSelector(
    (state: RootState) =>
      state[MODULE_NAME]?.carrierDetails?.editingCarrierCodes,
    (data: any) => data
  );

export const isEditingCarrierContacts = (): Selector<boolean> =>
  createSelector(
    (state: RootState) =>
      state[MODULE_NAME]?.carrierDetails?.editingCarrierContacts,
    (data: any) => data
  );

/*******************************************************************************
 * SLICE
 ******************************************************************************/

const carriersNetworkDetailsSlice = createSlice({
  name: MODULE_NAME,
  initialState: {
    carrierDetails: {
      data: {} as any,
      loading: false,
      editingCarrierCodes: false,
      editingCarrierContacts: false,
    },
  },
  reducers: {},
  extraReducers: (builder) => {
    /***************************************************************************
     * CARRIER DETAILS
     **************************************************************************/

    builder.addCase(getCarrierDetails.fulfilled, (state, action) => {
      state.carrierDetails.data = action.payload;
      state.carrierDetails.loading = false;
    });
    builder.addCase(getCarrierDetails.pending, (state) => {
      state.carrierDetails.loading = true;
    });
    builder.addCase(getCarrierDetails.rejected, (state) => {
      state.carrierDetails.loading = false;
    });

    /***************************************************************************
     * CARRIER CODES
     **************************************************************************/

    builder.addCase(createCarrierCode.fulfilled, (state, action) => {
      // Adds the code to the carrier_codes field in details
      const data = state.carrierDetails?.data || {};

      // TODO: improve this
      state.carrierDetails.data = {
        ...data,
        carrier_codes: {
          ...data?.carrier_codes,
          custom: [...(data.carrier_codes?.custom || []), action.payload.code],
        },
      };
      state.carrierDetails.editingCarrierCodes = false;
    });
    builder.addCase(deleteCarrierCode.fulfilled, (state, action) => {
      // Removes the code to the carrier_codes field in details
      const data = state.carrierDetails?.data || {};
      state.carrierDetails.data = {
        ...data,
        carrier_codes: {
          ...data?.carrier_codes,
          custom: data?.carrier_codes?.custom?.filter(
            (code: string) => code !== action.payload.code
          ),
        },
      };
      state.carrierDetails.editingCarrierCodes = false;
    });

    // Updates loading for other states
    [createCarrierCode, deleteCarrierCode].forEach((actionCreator) => {
      builder.addCase(actionCreator.rejected, (state) => {
        state.carrierDetails.editingCarrierCodes = false;
      });
      builder.addCase(actionCreator.pending, (state) => {
        state.carrierDetails.editingCarrierCodes = true;
      });
    });

    /***************************************************************************
     * CARRIER CONTACTS
     **************************************************************************/

    // TODO: update contacts on details
    // Updates loading for other states
    builder.addCase(addCarrierContact.fulfilled, (state) => {
      state.carrierDetails.editingCarrierContacts = false;
    });
    builder.addCase(addCarrierContact.rejected, (state) => {
      state.carrierDetails.editingCarrierContacts = false;
    });
    builder.addCase(addCarrierContact.pending, (state) => {
      state.carrierDetails.editingCarrierContacts = true;
    });
  },
});

export const CarriersNetworkDetailsState = {
  actions: {
    getCarrierDetails,
    createCarrierCode,
    deleteCarrierCode,
    addCarrierContact,
    initiateHelpRequest,
    exportCarrierList,
    getExportInfo,
  },
  selectors: {
    carrierDetails,
    isLoadingCarrierDetails,
    isEditingCarrierCodes,
    isEditingCarrierContacts,
  },
};

export default carriersNetworkDetailsSlice;
