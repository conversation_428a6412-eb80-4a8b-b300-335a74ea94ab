import {
  createSlice,
  createAsyncThunk,
  createSelector,
  PayloadAction,
} from "@reduxjs/toolkit";
import { LoadsTrackingMode } from "state/BaseTypes";
import { RootState, Selector } from "state/store";

import emailIntegrationsApi from "api/shipper/EmailLoadsAdditionsIntegrationsApi";

const MODULE_NAME = "emailLoadsAdditionsIntegrations";

type EmailLoadsAdditionsIntegrationsData = {
  data: any[];
  details: any;
  retrieving: boolean;
  creating: boolean;
  updating: boolean;
  deleting: boolean;
  error: boolean;
};

/*******************************************************************************
 * ACTIONS
 ******************************************************************************/

/*
 * Gets the email load addition integrations list
 */
export const retrieveIntegrations = createAsyncThunk<
  any,
  {
    shipperId: string;
    modes?: LoadsTrackingMode[];
  },
  {}
>(
  `${MODULE_NAME}/retrieveIntegrations`,
  async ({ shipperId, modes }, thunkAPI) => {
    const response = await emailIntegrationsApi.retrieveIntegrations(
      shipperId,
      modes
    );

    return response?.data?.data;
  }
);

/*
 * Gets the email load addition integration details
 */
export const retrieveIntegrationDetails = createAsyncThunk<
  any,
  {
    shipperId: string;
    integrationId: string | number;
  },
  {}
>(
  `${MODULE_NAME}/retrieveIntegrationDetails`,
  async ({ shipperId, integrationId }, thunkAPI) => {
    const response = await emailIntegrationsApi.retrieveIntegrationDetails(
      shipperId,
      integrationId
    );

    return response?.data?.data;
  }
);

/*
 * Adds a new email load addition integrations for this carrier
 */
export const createIntegration = createAsyncThunk<
  any,
  {
    shipperId: string;
    mode: LoadsTrackingMode | null;
    userId: string;
    integration: any;
  },
  {}
>(
  `${MODULE_NAME}/createIntegration`,
  async ({ shipperId, mode, userId, integration }, thunkAPI) => {
    const response = await emailIntegrationsApi.createIntegration(
      shipperId,
      mode,
      userId,
      integration
    );

    return response?.data;
  }
);

/*
 * Updates an existing email load addition integration for this shipper
 */
export const updateIntegration = createAsyncThunk<
  any,
  {
    shipperId: string;
    integrationId: string | number;
    mode: LoadsTrackingMode | null;
    userId: string;
    integration: any;
  },
  {}
>(
  `${MODULE_NAME}/updateIntegration`,
  async ({ shipperId, integrationId, mode, userId, integration }, thunkAPI) => {
    const response = await emailIntegrationsApi.updateIntegration(
      shipperId,
      integrationId,
      mode,
      userId,
      integration
    );

    return response?.data;
  }
);

/*
 * Removes an existing email load addition integration for this carrier
 */
export const deleteIntegration = createAsyncThunk<
  any,
  {
    shipperId: string;
    integrationId: string | number;
  },
  {}
>(
  `${MODULE_NAME}/deleteIntegration`,
  async ({ shipperId, integrationId }, thunkAPI) => {
    const response = await emailIntegrationsApi.deleteIntegration(
      shipperId,
      integrationId
    );

    return response?.data;
  }
);

const onClearError = (state: any, { payload }: PayloadAction<any>) => {
  state.details.error = false;
};

/*******************************************************************************
 * SELECTORS
 ******************************************************************************/

export const emailIntegrations = (): Selector<any[]> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.data,
    (data: any[]) => data
  );

export const emailIntegrationsDetails = (): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.details,
    (data: any) => data
  );

export const isCreating = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.creating,
    (data: any) => data
  );

export const isRetrieving = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.retrieving,
    (data: any) => data
  );

export const isUpdating = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.updating,
    (data: any) => data
  );

export const isDeleting = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.deleting,
    (data: any) => data
  );

/*******************************************************************************
 * SLICE
 ******************************************************************************/

// Define the initial state using that type
const initialState: EmailLoadsAdditionsIntegrationsData = {
  data: [],
  details: {},
  retrieving: false,
  creating: false,
  updating: false,
  deleting: false,
  error: false,
};

const emailIntegrationsSlice = createSlice({
  name: MODULE_NAME,
  initialState: initialState,
  reducers: {
    clearError: onClearError,
  },
  extraReducers: (builder) => {
    /***************************************************************************
     * RETRIEVE LIST
     **************************************************************************/

    builder.addCase(retrieveIntegrations.fulfilled, (state, action) => {
      state.data = action.payload;
      state.retrieving = false;
      state.error = false;
    });
    builder.addCase(retrieveIntegrations.pending, (state) => {
      state.retrieving = true;
      state.error = false;
    });
    builder.addCase(retrieveIntegrations.rejected, (state) => {
      state.retrieving = false;
      state.error = true;
    });

    /***************************************************************************
     * RETRIEVE DETAILS
     **************************************************************************/

    builder.addCase(retrieveIntegrationDetails.fulfilled, (state, action) => {
      state.details = action.payload;
      state.retrieving = false;
      state.error = false;
    });
    builder.addCase(retrieveIntegrationDetails.pending, (state) => {
      state.retrieving = true;
      state.error = false;
    });
    builder.addCase(retrieveIntegrationDetails.rejected, (state) => {
      state.retrieving = false;
      state.error = true;
    });

    /***************************************************************************
     * CREATE
     **************************************************************************/

    builder.addCase(createIntegration.fulfilled, (state, action) => {
      const data = state.data || [];
      state.data = data.concat(action.payload);
      state.creating = false;
      state.error = false;
    });
    builder.addCase(createIntegration.pending, (state) => {
      state.creating = true;
      state.error = false;
    });
    builder.addCase(createIntegration.rejected, (state) => {
      state.creating = false;
      state.error = true;
    });

    /***************************************************************************
     * UPDATE
     **************************************************************************/

    builder.addCase(updateIntegration.fulfilled, (state, action) => {
      state.details = action.payload;
      state.updating = false;
      state.error = false;
    });
    builder.addCase(updateIntegration.pending, (state) => {
      state.updating = true;
      state.error = false;
    });
    builder.addCase(updateIntegration.rejected, (state) => {
      state.updating = false;
      state.error = true;
    });

    /***************************************************************************
     * DELETE
     **************************************************************************/

    builder.addCase(deleteIntegration.fulfilled, (state, action) => {
      const data = state.data || [];
      state.data = data?.filter(
        (integration: any) => integration?.id !== action?.payload?.id
      );
      state.updating = false;
      state.error = false;
    });
    builder.addCase(deleteIntegration.pending, (state) => {
      state.updating = true;
      state.error = false;
    });
    builder.addCase(deleteIntegration.rejected, (state) => {
      state.updating = false;
      state.error = true;
    });
  },
});

export const EmailLoadsAdditionsIntegrationsState = {
  actions: {
    createIntegration,
    retrieveIntegrations,
    retrieveIntegrationDetails,
    updateIntegration,
    deleteIntegration,
    clearError: emailIntegrationsSlice.actions.clearError,
  },
  selectors: {
    emailIntegrations,
    emailIntegrationsDetails,
    isCreating,
    isRetrieving,
    isUpdating,
    isDeleting,
  },
};

// Action creators are generated for each case reducer function
export default emailIntegrationsSlice;
