import {
  createSlice,
  createAsyncThunk,
  createSelector,
  PayloadAction,
} from "@reduxjs/toolkit";
import { LoadsTrackingMode } from "state/BaseTypes";
import { RootState, Selector } from "state/store";

import templatesAndValidationsApi from "api/shipper/LoadsAdditionsTemplatesAndValidationsApi";

const MODULE_NAME = "loadsAdditionsTemplatesAndValidations";

type LoadsAdditionsTemplatesAndValidationsData = {
  templates: any[];
  retrieving: boolean;
  validation: any;
  validating: boolean;
  error: boolean;
};

/*******************************************************************************
 * ACTIONS
 ******************************************************************************/

/*
 * Removes an existing email load addition integration for this carrier
 */
export const retrieveLoadsAdditionsTemplates = createAsyncThunk<
  any,
  {
    shipperId: string;
    mode?: LoadsTrackingMode;
    templateName?: string;
  },
  {}
>(
  `${MODULE_NAME}/retrieveLoadsAdditionsTemplates`,
  async ({ shipperId, mode, templateName }, thunkAPI) => {
    const response =
      await templatesAndValidationsApi.retrieveLoadsAdditionsTemplates(
        shipperId,
        mode,
        templateName
      );

    return response?.data;
  }
);

/*
 * Removes an existing email load addition integration for this carrier
 */
export const createLoadsAdditionsValidation = createAsyncThunk<
  any,
  {
    shipperId: string;
    file: File;
    fileFormat: string;
    mode?: LoadsTrackingMode;
  },
  {}
>(
  `${MODULE_NAME}/createLoadsAdditionsValidation`,
  async ({ shipperId, file, fileFormat, mode }, thunkAPI) => {
    const response =
      await templatesAndValidationsApi.createLoadsAdditionsValidation(
        shipperId,
        file,
        fileFormat,
        mode
      );

    return response?.data?.data;
  }
);

const onClearError = (state: any) => {
  state.error = false;
};

const onClearValidation = (state: any) => {
  state.validation = null;
};

/*******************************************************************************
 * SELECTORS
 ******************************************************************************/

export const loadsAdditionsTemplates = (): Selector<any[]> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.templates,
    (data: any) => data
  );

export const isRetrieving = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.retrieving,
    (data: any) => data
  );

export const loadsAdditionsValidation = (): Selector<any[]> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.validation,
    (data: any) => data
  );

export const isValidating = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.validating,
    (data: any) => data
  );

/*******************************************************************************
 * SLICE
 ******************************************************************************/

// Define the initial state using that type
const initialState: LoadsAdditionsTemplatesAndValidationsData = {
  templates: [],
  retrieving: false,
  validation: null,
  validating: false,
  error: false,
};

const templatesAndValidationsSlice = createSlice({
  name: MODULE_NAME,
  initialState: initialState,
  reducers: {
    clearError: onClearError,
    clearValidation: onClearValidation,
  },
  extraReducers: (builder) => {
    /***************************************************************************
     * RETRIEVE TEMPLATES
     **************************************************************************/

    builder.addCase(
      retrieveLoadsAdditionsTemplates.fulfilled,
      (state, action) => {
        state.templates = action.payload;
        state.retrieving = false;
        state.error = false;
      }
    );
    builder.addCase(retrieveLoadsAdditionsTemplates.pending, (state) => {
      state.retrieving = true;
      state.error = false;
    });
    builder.addCase(retrieveLoadsAdditionsTemplates.rejected, (state) => {
      state.retrieving = false;
      state.error = true;
    });

    /***************************************************************************
     * VALIDATE FILE
     **************************************************************************/

    builder.addCase(
      createLoadsAdditionsValidation.fulfilled,
      (state, action) => {
        state.validation = action.payload;
        state.validating = false;
        state.error = false;
      }
    );
    builder.addCase(createLoadsAdditionsValidation.pending, (state) => {
      state.validating = true;
      state.error = false;
    });
    builder.addCase(createLoadsAdditionsValidation.rejected, (state) => {
      state.validating = false;
      state.error = true;
    });
  },
});

export const LoadsAdditionsTemplatesAndValidationsState = {
  actions: {
    retrieveLoadsAdditionsTemplates,
    createLoadsAdditionsValidation,
    clearValidation: templatesAndValidationsSlice.actions.clearValidation,
    clearError: templatesAndValidationsSlice.actions.clearError,
  },
  selectors: {
    loadsAdditionsTemplates,
    loadsAdditionsValidation,
    isRetrieving,
    isValidating,
  },
};

// Action creators are generated for each case reducer function
export default templatesAndValidationsSlice;
