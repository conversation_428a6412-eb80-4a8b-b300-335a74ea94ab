import {
  createSlice,
  createAsyncThunk,
  createSelector,
} from "@reduxjs/toolkit";
import { RootState, Selector } from "state/store";

import shipperCompanyDetailsApi from "api/shipper/ShipperCompanyDetailsApi";

const MODULE_NAME = "shipperCompanyDetails";

type ShipperCompanyDetailsData = {
  shipperCompanyDetails: {
    data: any;
    loading: boolean;
    editing: boolean;
    error: boolean;
  };
};

/*******************************************************************************
 * ACTIONS
 ******************************************************************************/

/*
 * Gets the details for this shipper company
 */
export const retrieveShipperCompanyDetails = createAsyncThunk<
  any,
  { shipperId: string },
  {}
>(
  `${MODULE_NAME}/retrieveShipperCompanyDetails`,
  async ({ shipperId }, thunkAPI) => {
    const response =
      await shipperCompanyDetailsApi.retrieveShipperCompanyDetails(shipperId);

    return response?.data;
  }
);

/*
 * Updates the details for this shipper company
 */
export const updateShipperCompanyDetails = createAsyncThunk<
  any,
  { shipperId: string; shipperCompanyDetails: any },
  {}
>(
  `${MODULE_NAME}/updateShipperCompanyDetails`,
  async ({ shipperId, shipperCompanyDetails }, thunkAPI) => {
    const response = await shipperCompanyDetailsApi.updateShipperCompanyDetails(
      shipperId,
      shipperCompanyDetails
    );

    return response?.data;
  }
);

/*******************************************************************************
 * SELECTORS
 ******************************************************************************/

export const shipperCompanyDetails = (): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.shipperCompanyDetails?.data,
    (data: any) => data
  );

export const isLoadingShipperCompanyDetails = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.shipperCompanyDetails?.loading,
    (data: boolean) => data
  );

export const isEditingShipperCompanyDetails = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.shipperCompanyDetails?.editing,
    (data: boolean) => data
  );

/*******************************************************************************
 * SLICE
 ******************************************************************************/

// Define the initial state using that type
const initialShipperCompanyDetailsState = {
  data: {},
  loading: true,
  editing: false,
  error: false,
};
const initialState: ShipperCompanyDetailsData = {
  shipperCompanyDetails: initialShipperCompanyDetailsState,
};

const shipperCompanyDetailsSlice = createSlice({
  name: MODULE_NAME,
  initialState: initialState,
  reducers: {},
  extraReducers: (builder) => {
    /***************************************************************************
     * RETRIEVE COMPANY DETAILS
     **************************************************************************/

    builder.addCase(
      retrieveShipperCompanyDetails.fulfilled,
      (state, action) => {
        state.shipperCompanyDetails.data = action.payload;
        state.shipperCompanyDetails.loading = false;
      }
    );
    builder.addCase(retrieveShipperCompanyDetails.rejected, (state, action) => {
      state.shipperCompanyDetails.loading = false;
    });
    builder.addCase(retrieveShipperCompanyDetails.pending, (state, action) => {
      state.shipperCompanyDetails.loading = true;
    });

    /***************************************************************************
     * EDIT COMPANY DETAILS
     **************************************************************************/

    builder.addCase(updateShipperCompanyDetails.fulfilled, (state, action) => {
      state.shipperCompanyDetails.editing = false;
    });
    builder.addCase(updateShipperCompanyDetails.rejected, (state, action) => {
      state.shipperCompanyDetails.editing = false;
    });
    builder.addCase(updateShipperCompanyDetails.pending, (state, action) => {
      state.shipperCompanyDetails.editing = true;
    });
  },
});

export const ShipperCompanyDetailsState = {
  actions: {
    retrieveShipperCompanyDetails,
    updateShipperCompanyDetails,
  },
  selectors: {
    shipperCompanyDetails,
    isLoadingShipperCompanyDetails,
    isEditingShipperCompanyDetails,
  },
};

export default shipperCompanyDetailsSlice;
