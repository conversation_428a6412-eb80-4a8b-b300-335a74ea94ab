import {
  createSlice,
  createAsyncThunk,
  createSelector,
} from "@reduxjs/toolkit";
import { RootState, Selector } from "state/store";
import { LoadsTrackingMode, typedModeFromAction } from "state/BaseTypes";

import shipperIndicatorsApi from "api/shipper/ShipperIndicatorsApi";

const MODULE_NAME = "shipperIndicators";

type ShipperIndicatorsModeData = {
  data: any;
  loading: boolean;
  error: boolean;
};

type ShipperIndicatorsData = {
  ltl: ShipperIndicatorsModeData;
  ftl: ShipperIndicatorsModeData;
  parcel: ShipperIndicatorsModeData;
  ocean: ShipperIndicatorsModeData;
  air: ShipperIndicatorsModeData;
};

/*******************************************************************************
 * ACTIONS
 ******************************************************************************/

/*
 * Gets the shipper indicators
 */
export const getShipperIndicators = createAsyncThunk<
  any,
  { shipperId: string; mode: LoadsTrackingMode },
  {}
>(
  `${MODULE_NAME}/getShipperIndicators`,
  async ({ shipperId, mode }, thunkAPI) => {
    const response: any = await shipperIndicatorsApi.getShipperIndicators(
      shipperId,
      mode
    );

    return response?.data;
  }
);

/*******************************************************************************
 * SELECTORS
 ******************************************************************************/

export const shipperIndicators = (): Selector<any> =>
  createSelector(
    (state: RootState) => {
      const valueOrZero = (value: any) => value || 0;

      // TODO: add other modes later
      const ftl = state[MODULE_NAME].ftl?.data;
      const ltl = state[MODULE_NAME].ltl?.data;
      const parcel = state[MODULE_NAME].parcel?.data;
      const ocean = state[MODULE_NAME].ocean?.data;
      const air = state[MODULE_NAME].air?.data;

      return {
        invitations: {
          total: valueOrZero(ftl?.invitations?.total),
          pendingAccountCreation: valueOrZero(ftl?.invitations?.invited),
          dataSharingRequested: valueOrZero(
            ftl?.invitations?.waiting_to_accept
          ),
          resubmissions: valueOrZero(ftl?.invitations?.resubmissions),
        },
        carriers: {
          total: {
            total:
              valueOrZero(ftl?.carriers?.total) +
              valueOrZero(ltl?.carriers?.total) +
              valueOrZero(parcel?.carriers?.total) +
              valueOrZero(ocean?.carriers?.total) +
              valueOrZero(air?.carriers?.total),
            connected:
              valueOrZero(ftl?.carriers?.connected) +
              valueOrZero(ltl?.carriers?.connected) +
              valueOrZero(parcel?.carriers?.connected) +
              valueOrZero(ocean?.carriers?.connected) +
              valueOrZero(air?.carriers?.connected),
            inProgress:
              valueOrZero(ftl?.carriers?.in_progress) +
              valueOrZero(ltl?.carriers?.in_progress) +
              valueOrZero(parcel?.carriers?.in_progress) +
              valueOrZero(ocean?.carriers?.in_progress) +
              valueOrZero(air?.carriers?.in_progress),
            disconnected:
              valueOrZero(ftl?.carriers?.disconnected) +
              valueOrZero(ltl?.carriers?.disconnected) +
              valueOrZero(parcel?.carriers?.disconnected) +
              valueOrZero(ocean?.carriers?.disconnected) +
              valueOrZero(air?.carriers?.disconnected),
          },
          ftl: {
            total: valueOrZero(ftl?.carriers?.total),
            connected: valueOrZero(ftl?.carriers?.connected),
            inProgress: valueOrZero(ftl?.carriers?.in_progress),
            disconnected: valueOrZero(ftl?.carriers?.disconnected),
          },
          ltl: {
            total: valueOrZero(ltl?.carriers?.total),
            connected: valueOrZero(ltl?.carriers?.connected),
            inProgress: valueOrZero(ltl?.carriers?.in_progress),
            disconnected: valueOrZero(ltl?.carriers?.disconnected),
          },
          parcel: {
            total: valueOrZero(parcel?.carriers?.total),
            connected: valueOrZero(parcel?.carriers?.connected),
            inProgress: valueOrZero(parcel?.carriers?.in_progress),
            disconnected: valueOrZero(parcel?.carriers?.disconnected),
          },
          ocean: {
            total: valueOrZero(ocean?.carriers?.total),
            connected: valueOrZero(ocean?.carriers?.connected),
            inProgress: valueOrZero(ocean?.carriers?.in_progress),
            disconnected: valueOrZero(ocean?.carriers?.disconnected),
          },
          air: {
            total: valueOrZero(air?.carriers?.total),
            connected: valueOrZero(air?.carriers?.connected),
            inProgress: valueOrZero(air?.carriers?.in_progress),
            disconnected: valueOrZero(air?.carriers?.disconnected),
          },
        },
      };
    },
    (data: any) => data
  );

// NOTE: returning for all modes
export const isLoadingShipperIndicators = (): Selector<boolean> =>
  createSelector(
    (state: RootState) =>
      // TODO: add other modes later
      state[MODULE_NAME].ltl?.loading ||
      state[MODULE_NAME].ftl?.loading ||
      state[MODULE_NAME].parcel?.loading,
    (data: boolean) => data
  );

/*******************************************************************************
 * SLICE
 ******************************************************************************/

// Define the initial state using that type
const initialShippersIndicatorsModeState = {
  data: {},
  loading: true,
  error: false,
};
const initialShippersIndicatorsState = {
  ltl: initialShippersIndicatorsModeState,
  ftl: initialShippersIndicatorsModeState,
  parcel: initialShippersIndicatorsModeState,
  ocean: initialShippersIndicatorsModeState,
  air: initialShippersIndicatorsModeState,
};
const initialState: ShipperIndicatorsData = {
  ...initialShippersIndicatorsState,
};

const shippersNetworkDetailsSlice = createSlice({
  name: MODULE_NAME,
  initialState: initialState,
  reducers: {},
  extraReducers: (builder) => {
    /***************************************************************************
     * LOAD SHIPPER DETAILS
     **************************************************************************/

    builder.addCase(getShipperIndicators.fulfilled, (state, action) => {
      state[typedModeFromAction(action)].data = action?.payload;
      state[typedModeFromAction(action)].loading = false;
    });
    builder.addCase(getShipperIndicators.rejected, (state, action) => {
      state[typedModeFromAction(action)].loading = false;
    });
    builder.addCase(getShipperIndicators.pending, (state, action) => {
      state[typedModeFromAction(action)].loading = true;
    });
  },
});

export const ShipperIndicatorsState = {
  actions: {
    getShipperIndicators,
  },
  selectors: {
    shipperIndicators,
    isLoadingShipperIndicators,
  },
};

export default shippersNetworkDetailsSlice;
