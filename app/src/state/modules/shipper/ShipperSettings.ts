import {
  createSlice,
  createAsyncThunk,
  createSelector,
} from "@reduxjs/toolkit";
import { RootState, Selector } from "state/store";
import { LoadsTrackingMode } from "state/BaseTypes";

import shipperSettingsApi from "api/shipper/ShipperSettingsApi";

const MODULE_NAME = "shipperSettings";

/*******************************************************************************
 * TYPES
 ******************************************************************************/

type ShipperSettingsData = {
  loadNumberFormats: string[];
  retrieving: boolean;
  creating: boolean;
  deleting: boolean;
  error: boolean;
};

/*******************************************************************************
 * ACTIONS
 ******************************************************************************/
/*
 * Retrieves the load number formats
 */
export const retrieveLoadNumberFormats = createAsyncThunk<
  any,
  {
    shipperId: string;
  },
  {}
>(
  `${MODULE_NAME}/retrieveLoadNumberFormats`,
  async ({ shipperId }, thunkAPI) => {
    const response = await shipperSettingsApi.retrieveLoadNumberFormats(
      shipperId
    );

    return response?.data?.data?.load_number_formats;
  }
);

/*
 * Add a load number format
 */
export const addLoadNumberFormat = createAsyncThunk<
  any,
  {
    shipperId: string;
    mode: LoadsTrackingMode;
    value: string;
  },
  {}
>(
  `${MODULE_NAME}/addLoadNumberFormat`,
  async ({ shipperId, mode, value }, thunkAPI) => {
    const response = await shipperSettingsApi.addLoadNumberFormat(
      shipperId,
      mode,
      value
    );

    return { loadNumberFormat: response?.data?.data?.value };
  }
);

/*
 * Remove a load number format
 */
export const deleteLoadNumberFormat = createAsyncThunk<
  any,
  {
    shipperId: string;
    mode: LoadsTrackingMode;
    loadNumberFormat: string;
  },
  {}
>(
  `${MODULE_NAME}/deleteLoadNumberFormat`,
  async ({ shipperId, mode, loadNumberFormat }, thunkAPI) => {
    const response = await shipperSettingsApi.deleteLoadNumberFormat(
      shipperId,
      mode,
      loadNumberFormat
    );

    return { loadNumberFormat: response?.data?.data?.value };
  }
);

/*******************************************************************************
 * SELECTORS
 ******************************************************************************/

export const loadNumberFormats = (): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.loadNumberFormats,
    (data: String[]) => data
  );

/*******************************************************************************
 * SLICE
 ******************************************************************************/

const initialState: ShipperSettingsData = {
  loadNumberFormats: [],
  retrieving: false,
  creating: false,
  deleting: false,
  error: false,
};

const shipperSettingsSlice = createSlice({
  name: MODULE_NAME,
  initialState: initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(retrieveLoadNumberFormats.fulfilled, (state, action) => {
      state.loadNumberFormats = action.payload;
      state.retrieving = false;
      state.error = false;
    });

    builder.addCase(retrieveLoadNumberFormats.pending, (state, action) => {
      state.retrieving = true;
      state.error = false;
    });

    builder.addCase(retrieveLoadNumberFormats.rejected, (state, action) => {
      state.retrieving = false;
      state.error = true;
    });

    builder.addCase(addLoadNumberFormat.fulfilled, (state, action) => {
      const data = state?.loadNumberFormats || [];
      state.loadNumberFormats = [...data, action.payload.loadNumberFormat];
    });

    builder.addCase(deleteLoadNumberFormat.fulfilled, (state, action) => {
      const data = state?.loadNumberFormats || [];
      state.loadNumberFormats = data.filter(
        (loadNumberFormat: string) =>
          loadNumberFormat !== action.payload.loadNumberFormat
      );
    });

    [addLoadNumberFormat, deleteLoadNumberFormat].forEach((actionCreator) => {
      builder.addCase(actionCreator.rejected, (state) => {
        state.creating = false;
        state.error = true;
      });
      builder.addCase(actionCreator.pending, (state) => {
        state.creating = true;
        state.error = false;
      });
    });
  },
});

export const ShipperSettingsState = {
  actions: {
    retrieveLoadNumberFormats,
    addLoadNumberFormat,
    deleteLoadNumberFormat,
  },
  selectors: {
    loadNumberFormats,
  },
};

export default shipperSettingsSlice;
