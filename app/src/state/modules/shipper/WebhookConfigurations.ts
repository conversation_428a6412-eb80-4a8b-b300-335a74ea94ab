import {
  createSlice,
  createAsyncThunk,
  createSelector,
  PayloadAction,
} from "@reduxjs/toolkit";
import { LoadsTrackingMode } from "state/BaseTypes";
import { RootState, Selector } from "state/store";

import webhookConfigurationsApi from "api/shipper/WebhookConfigurationsApi";

const MODULE_NAME = "WebhookConfiguration";

type WebhookConfigurations = {
  data: any[];
  details: any;
  loadSourceConfigurations: any[];
  retrieving: boolean;
  creating: boolean;
  updating: boolean;
  deleting: boolean;
  error: boolean;
};

/*******************************************************************************
 * ACTIONS
 ******************************************************************************/

/*
 * Retrieve the webhook configurations list
 */
export const retrieveWebhooks = createAsyncThunk<
  any,
  {
    shipperId: string;
    mode?: LoadsTrackingMode[];
  },
  {}
>(`${MODULE_NAME}/retrieveWebhooks`, async ({ shipperId, mode }, thunkAPI) => {
  const response = await webhookConfigurationsApi.retrieveWebhooks(
    shipperId,
    mode
  );

  return response?.data;
});

/*
 * Gets the webhook configuration details
 */
export const retrieveWebhookDetails = createAsyncThunk<
  any,
  {
    shipperId: string;
    webhookId: string | number;
  },
  {}
>(
  `${MODULE_NAME}/retrieveWebhookDetails`,
  async ({ shipperId, webhookId }, thunkAPI) => {
    const response = await webhookConfigurationsApi.retrieveWebhookDetails(
      shipperId,
      webhookId
    );

    return response?.data;
  }
);

/*
 * Adds a new webhook configuration for this shipper
 */
export const createWebhook = createAsyncThunk<
  any,
  {
    shipperId: string;
    modes: LoadsTrackingMode | null;
    webhook: any;
  },
  {}
>(
  `${MODULE_NAME}/createWebhook`,
  async ({ shipperId, modes, webhook }, thunkAPI) => {
    const response = await webhookConfigurationsApi.createWebhook(
      shipperId,
      modes,
      webhook
    );

    return response?.data;
  }
);

/*
 * Updates an existing webhook configuration for this shipper
 */
export const updateWebhook = createAsyncThunk<
  any,
  {
    shipperId: string;
    webhookId: string | number;
    modes: LoadsTrackingMode | null;
    webhook: any;
  },
  {}
>(
  `${MODULE_NAME}/updateWebhook`,
  async ({ shipperId, webhookId, modes, webhook }, thunkAPI) => {
    const response = await webhookConfigurationsApi.updateWebhook(
      shipperId,
      webhookId,
      modes,
      webhook
    );

    return response?.data;
  }
);

/*
 * Removes an existing webhook configuration for this carrier
 */
export const deleteWebhook = createAsyncThunk<
  any,
  {
    shipperId: string;
    webhookId: string | number;
  },
  {}
>(
  `${MODULE_NAME}/deleteWebhook`,
  async ({ shipperId, webhookId }, thunkAPI) => {
    const response = await webhookConfigurationsApi.deleteWebhook(
      shipperId,
      webhookId
    );

    return response?.data;
  }
);

const onClearError = (state: any, { payload }: PayloadAction<any>) => {
  state.details.error = false;
};

/*******************************************************************************
 * SELECTORS
 ******************************************************************************/

export const webhookConfigurations = (): Selector<any[]> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.data,
    (data: any[]) => data
  );

export const webhookConfigurationsDetails = (): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.details,
    (data: any) => data
  );

export const isCreating = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.creating,
    (data: any) => data
  );

export const isRetrieving = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.retrieving,
    (data: any) => data
  );

export const isUpdating = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.updating,
    (data: any) => data
  );

/*******************************************************************************
 * SLICE
 ******************************************************************************/

// Define the initial state using that type
const initialState: WebhookConfigurations = {
  data: [],
  details: {},
  loadSourceConfigurations: [],
  retrieving: false,
  creating: false,
  updating: false,
  deleting: false,
  error: false,
};

const webhookConfigurationSlice = createSlice({
  name: MODULE_NAME,
  initialState: initialState,
  reducers: {
    clearError: onClearError,
  },
  extraReducers: (builder) => {
    /***************************************************************************
     * RETRIEVE LIST
     **************************************************************************/

    builder.addCase(retrieveWebhooks.fulfilled, (state, action) => {
      state.data = action.payload;
      state.retrieving = false;
      state.error = false;
    });
    builder.addCase(retrieveWebhooks.pending, (state) => {
      state.retrieving = true;
      state.error = false;
    });
    builder.addCase(retrieveWebhooks.rejected, (state) => {
      state.retrieving = false;
      state.error = true;
    });

    /***************************************************************************
     * RETRIEVE DETAILS
     **************************************************************************/

    builder.addCase(retrieveWebhookDetails.fulfilled, (state, action) => {
      state.details = action.payload;
      state.retrieving = false;
      state.error = false;
    });
    builder.addCase(retrieveWebhookDetails.pending, (state) => {
      state.retrieving = true;
      state.error = false;
    });
    builder.addCase(retrieveWebhookDetails.rejected, (state) => {
      state.retrieving = false;
      state.error = true;
    });

    /***************************************************************************
     * CREATE
     **************************************************************************/

    builder.addCase(createWebhook.fulfilled, (state, action) => {
      const data = state.data || [];
      state.data = data.concat(action.payload);
      state.creating = false;
      state.error = false;
    });
    builder.addCase(createWebhook.pending, (state) => {
      state.creating = true;
      state.error = false;
    });
    builder.addCase(createWebhook.rejected, (state) => {
      state.creating = false;
      state.error = true;
    });

    /***************************************************************************
     * UPDATE
     **************************************************************************/

    builder.addCase(updateWebhook.fulfilled, (state, action) => {
      state.details = action.payload;
      state.updating = false;
      state.error = false;
    });
    builder.addCase(updateWebhook.pending, (state) => {
      state.updating = true;
      state.error = false;
    });
    builder.addCase(updateWebhook.rejected, (state) => {
      state.updating = false;
      state.error = true;
    });

    /***************************************************************************
     * DELETE
     **************************************************************************/

    builder.addCase(deleteWebhook.fulfilled, (state, action) => {
      const data = state.data || [];
      state.data = data?.filter(
        (integration: any) => integration?.id !== action?.payload?.id
      );
      state.updating = false;
      state.error = false;
    });
    builder.addCase(deleteWebhook.pending, (state) => {
      state.updating = true;
      state.error = false;
    });
    builder.addCase(deleteWebhook.rejected, (state) => {
      state.updating = false;
      state.error = true;
    });
  },
});

export const WebhookConfigurationState = {
  actions: {
    createWebhook,
    retrieveWebhooks,
    retrieveWebhookDetails,
    updateWebhook,
    deleteWebhook,
    clearError: webhookConfigurationSlice.actions.clearError,
  },
  selectors: {
    webhookConfigurations,
    webhookConfigurationsDetails,
    isCreating,
    isRetrieving,
    isUpdating,
  },
};

// Action creators are generated for each case reducer function
export default webhookConfigurationSlice;
