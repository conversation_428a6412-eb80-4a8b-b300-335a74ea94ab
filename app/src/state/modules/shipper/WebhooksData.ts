import {
  createSlice,
  createAsyncThunk,
  createSelector,
} from "@reduxjs/toolkit";
import { RootState, Selector } from "state/store";

import webhooksDataApi from "api/shipper/WebhooksDataApi";

const MODULE_NAME = "webhooksData";

/*******************************************************************************
 * TYPES
 ******************************************************************************/

type WebhooksData = {
  data: any[];
  events: any[];
  loading: boolean;
  creating: boolean;
  deleting: boolean;
  error: boolean;
  pagination: {
    totalItems: number;
    currentPage: number;
    pageSize: number;
    totalPages: number;
  };
};

/*******************************************************************************
 * HELPERS
 ******************************************************************************/

const DEFAULT_PAGINATION_PARAMETERS = {
  currentPage: 1,
  pageSize: 10,
};

const getPaginationParams = (pageSize?: number, currentPage?: number) => {
  return {
    current_page: currentPage
      ? currentPage
      : DEFAULT_PAGINATION_PARAMETERS.currentPage,
    page_size: currentPage ? pageSize : DEFAULT_PAGINATION_PARAMETERS.pageSize,
  };
};

/*******************************************************************************
 * ACTIONS
 ******************************************************************************/
/*
 * Load callback events
 */
export const retrieveCallbackEvents = createAsyncThunk<
  any,
  {
    shipperId: string;
  },
  {}
>(`${MODULE_NAME}/retrieveCallbackEvents`, async ({ shipperId }, thunkAPI) => {
  const response = await webhooksDataApi.retrieveCallbackEvents(shipperId);
  return response;
});

/*
 * Load webhooks data
 */
export const retrieveWebhooksData = createAsyncThunk<
  any,
  {
    eventTypes: string;
    statusType: string;
    shipperId: string;
    searchValue: string
    pageSize?: number;
    currentPage?: number;
    processedFrom: string;
    processedTo: string;
  },
  {}
>(
  `${MODULE_NAME}/retrieveWebhooksData`,
  async (
    {
      eventTypes,
      statusType,
      shipperId,
      searchValue,
      pageSize,
      currentPage,
      processedFrom,
      processedTo,
    },
    thunkAPI
  ) => {
    const paginationParams = getPaginationParams(pageSize, currentPage);

    const response = await webhooksDataApi.retrieveWebhooksData(
      eventTypes,
      statusType,
      shipperId,
      searchValue,
      // TODO: fix type
      paginationParams,
      processedFrom,
      processedTo
    );

    return response;
  }
);

/*******************************************************************************
 * SELECTORS
 ******************************************************************************/
export const paginationDetails = (): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.pagination,
    (data: any) => data
  );

export const webhooksData = (): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.data,
    (data: any) => data
  );

export const eventsData = (): Selector<any> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.events,
    (data: any) => data
  );

export const isRetrievingWebhooksData = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.loading,
    (data: boolean) => data
  );

export const hasError = (): Selector<boolean> =>
  createSelector(
    (state: RootState) => state[MODULE_NAME]?.error,
    (data: boolean) => data
  );

/*******************************************************************************
 * SLICE
 ******************************************************************************/

const initialState: WebhooksData = {
  data: [] as any[],
  events: [] as any[],
  pagination: {
    currentPage: 1,
    pageSize: 10,
    totalItems: 0,
    totalPages: 0,
  },
  loading: false,
  creating: false,
  deleting: false,
  error: false,
};

const webhooksDataSlice = createSlice({
  name: MODULE_NAME,
  initialState: initialState,
  reducers: {},
  extraReducers: (builder) => {
    /***************************************************************************
     * RETRIEVE LIST
     **************************************************************************/
    builder.addCase(retrieveWebhooksData.fulfilled, (state, action) => {
      state.data = action?.payload?.callbacks || [];
      // Pagination
      const { current_page, page_size, total_items, total_pages } =
        action?.payload?.pagination || {};
      state.pagination = {
        currentPage: current_page,
        pageSize: page_size,
        totalItems: total_items,
        totalPages: total_pages,
      };

      state.loading = false;
      state.error = false;
    });

    builder.addCase(retrieveWebhooksData.pending, (state, action) => {
      state.loading = true;
      state.error = false;
    });

    builder.addCase(retrieveWebhooksData.rejected, (state, action) => {
      state.loading = false;
      state.error = true;
    });

    /***************************************************************************
     * RETRIEVE EVENTS
     **************************************************************************/

    builder.addCase(retrieveCallbackEvents.fulfilled, (state, action) => {
      state.events = action?.payload?.event_types || [];
      state.loading = false;
      state.error = false;
    });

    builder.addCase(retrieveCallbackEvents.pending, (state, action) => {
      state.loading = true;
      state.error = false;
    });

    builder.addCase(retrieveCallbackEvents.rejected, (state, action) => {
      state.loading = false;
      state.error = true;
    });
  },
});

export const WebhooksDataState = {
  actions: {
    retrieveWebhooksData,
    retrieveCallbackEvents,
  },
  selectors: {
    paginationDetails,
    webhooksData,
    eventsData,
    isRetrievingWebhooksData,
    hasError,
  },
};

export default webhooksDataSlice;
