import { configureStore } from "@reduxjs/toolkit";

// SHIPPER PORTAL
import shipperCompanyDetailsSlice from "state/modules/shipper/ShipperCompanyDetails";
import carriersNetworkSlice from "state/modules/shipper/CarriersNetwork";
import carrierDetailsSlice from "state/modules/shipper/CarriersNetworkDetails";
import carrierAdditionsSlice from "state/modules/shipper/CarrierAdditions";
import carrierInvitationsSlice from "state/modules/shipper/CarrierInvitations";
import shipperIndicatorsSlice from "state/modules/shipper/ShipperIndicators";
import emailLoadsAdditionsIntegrationsSlice from "state/modules/shipper/EmailLoadsAdditionsIntegrations";
import batchListsLoadsAdditionsSlice from "state/modules/shipper/BatchListsLoadsAdditions";
import templatesAndValidationsSlice from "state/modules/shipper/LoadsAdditionsTemplatesAndValidations";
import shipperSettingsSlice from "state/modules/shipper/ShipperSettings";
import webhookConfigurationSlice from "state/modules/shipper/WebhookConfigurations";
import webhooksDataSlice from "state/modules/shipper/WebhooksData";
import dispatcherLogsSlice from "state/modules/carrier/dispatcherLogsData";

// CARRIER PORTAL
import carrierCompanyDetailsSlice from "state/modules/carrier/CarrierCompanyDetails";
import carrierFleetSlice from "state/modules/carrier/CarrierFleets";
import carrierFleetAssetsSlice from "state/modules/carrier/CarrierFleetAssets";
import carrierIndicatorsSlice from "state/modules/carrier/CarrierIndicators";
import locationProvidersSlice from "state/modules/carrier/LocationProviders";
import shippersSlice from "state/modules/carrier/ShippersNetwork";
import shipperDetailsSlice from "state/modules/carrier/ShippersNetworkDetails";
import eldGpsIntegrationsSlice from "state/modules/carrier/EldGpsIntegrations";
import directAssignmentLoadsSlice from "state/modules/carrier/DirectLoadAssignment";
import carrierConfigurationsSlice from "state/modules/carrier/CarrierConfigurations";

// COMPANIES AND USERS
import companiesSearchSlice from "state/modules/CompaniesSearch";
import companiesOnboardingSlice from "state/modules/CompaniesOnboarding";

// EXTERNAL
import externalOnboardingSlice from "state/modules/external/ExternalOnboarding";
import externalParametersSlice from "state/modules/external/ExternalParameters";

// COMMON
import commonCompanyContactsSlice from "state/modules/CommonCompanyContacts";

import usersState from "state/modules/Users";

/***************************************************************************
 * STATE TYPES
 **************************************************************************/

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
export type Selector<S> = (state: RootState) => S;

/***************************************************************************
 * STORE
 **************************************************************************/

const store = configureStore({
  reducer: {
    // SHIPPERS DATA
    [shipperCompanyDetailsSlice.name]: shipperCompanyDetailsSlice.reducer,
    [carriersNetworkSlice.name]: carriersNetworkSlice.reducer,
    [carrierDetailsSlice.name]: carrierDetailsSlice.reducer,
    [carrierAdditionsSlice.name]: carrierAdditionsSlice.reducer,
    [carrierInvitationsSlice.name]: carrierInvitationsSlice.reducer,
    [shipperIndicatorsSlice.name]: shipperIndicatorsSlice.reducer,
    [emailLoadsAdditionsIntegrationsSlice.name]:
      emailLoadsAdditionsIntegrationsSlice.reducer,
    [batchListsLoadsAdditionsSlice.name]: batchListsLoadsAdditionsSlice.reducer,
    [templatesAndValidationsSlice.name]: templatesAndValidationsSlice.reducer,
    [shipperSettingsSlice.name]: shipperSettingsSlice.reducer,
    [webhookConfigurationSlice.name]: webhookConfigurationSlice.reducer,
    [webhooksDataSlice.name]: webhooksDataSlice.reducer,
    [dispatcherLogsSlice.name]: dispatcherLogsSlice.reducer,

    // CARRIERS DATA
    [carrierCompanyDetailsSlice.name]: carrierCompanyDetailsSlice.reducer,
    [carrierFleetSlice.name]: carrierFleetSlice.reducer,
    [carrierFleetAssetsSlice.name]: carrierFleetAssetsSlice.reducer,
    [carrierIndicatorsSlice.name]: carrierIndicatorsSlice.reducer,
    [locationProvidersSlice.name]: locationProvidersSlice.reducer,
    [shippersSlice.name]: shippersSlice.reducer,
    [shipperDetailsSlice.name]: shipperDetailsSlice.reducer,
    [eldGpsIntegrationsSlice.name]: eldGpsIntegrationsSlice.reducer,
    [directAssignmentLoadsSlice.name]: directAssignmentLoadsSlice.reducer,
    [carrierConfigurationsSlice.name]: carrierConfigurationsSlice.reducer,

    // COMPANIES AND USERS
    [companiesSearchSlice.name]: companiesSearchSlice.reducer,
    [companiesOnboardingSlice.name]: companiesOnboardingSlice.reducer,
    users: usersState.reducer,

    // EXTERNAL
    [externalOnboardingSlice.name]: externalOnboardingSlice.reducer,
    [externalParametersSlice.name]: externalParametersSlice.reducer,

    // COMMON
    [commonCompanyContactsSlice.name]: commonCompanyContactsSlice.reducer,
  },
});

export default store;
