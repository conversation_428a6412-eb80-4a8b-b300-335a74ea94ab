import { getInitialMockAppState } from "tests/InitialMockAppState";

const initialMockAppState = getInitialMockAppState();

export const getCarriersMockAppState = () => {
  return {
    ...initialMockAppState,
    eldGpsIntegrations: {
      data: [],
      details: {},
      retrieving: true,
      creating: false,
      updating: false,
      deleting: false,
      error: false,
    },
    directLoadAssignment: { data: {}, retrieving: false, error: true },
  };
};
