export const getInitialMockAppState = () => {
  return {
    carriersNetwork: {
      carriers: {
        ltl: {
          data: [],
          filters: { query: null, networkStatus: "all", filterBy: [] },
          sortBy: [],
          pagination: {
            currentPage: 1,
            pageSize: 25,
            totalItems: 0,
            totalPages: 0,
          },
          loading: true,
          editing: false,
          error: false,
        },
        ftl: {
          data: [],
          filters: { query: null, networkStatus: "connected", filterBy: [] },
          sortBy: [],
          pagination: {
            currentPage: 1,
            pageSize: 25,
            totalItems: 0,
            totalPages: 0,
          },
          loading: true,
          editing: false,
          error: false,
        },
        parcel: {
          data: [],
          filters: { query: null, networkStatus: "all", filterBy: [] },
          sortBy: [],
          pagination: {
            currentPage: 1,
            pageSize: 25,
            totalItems: 0,
            totalPages: 0,
          },
          loading: true,
          editing: false,
          error: false,
        },
        ocean: {
          data: [],
          filters: { query: null, networkStatus: "all", filterBy: [] },
          sortBy: [],
          pagination: {
            currentPage: 1,
            pageSize: 25,
            totalItems: 0,
            totalPages: 0,
          },
          loading: true,
          editing: false,
          error: false,
        },
      },
    },
    batchListsLoadsAdditions: {
      data: [],
      retrieving: false,
      creating: false,
      updating: false,
      deleting: false,
      error: false,
    },
    carrierAdditions: {
      addition: {
        ltl: {
          requestId: "",
          progress: 0,
          processing: false,
          error: false,
          results: { success: [], errors: [] },
        },
        ftl: {
          requestId: "",
          progress: 0,
          processing: false,
          error: false,
          results: { success: [], errors: [] },
        },
        parcel: {
          requestId: "",
          progress: 0,
          processing: false,
          error: false,
          results: { success: [], errors: [] },
        },
        ocean: {
          requestId: "",
          progress: 0,
          processing: false,
          error: false,
          results: { success: [], errors: [] },
        },
      },
      bulk: {
        ltl: {
          requestId: "",
          progress: 0,
          processing: false,
          error: false,
          results: {
            carriersOnPlatform: [],
            newCarriers: [],
            carriersWithErrors: [],
          },
        },
        ftl: {
          requestId: "",
          progress: 0,
          processing: false,
          error: false,
          results: {
            carriersOnPlatform: [],
            newCarriers: [],
            carriersWithErrors: [],
          },
        },
        parcel: {
          requestId: "",
          progress: 0,
          processing: false,
          error: false,
          results: {
            carriersOnPlatform: [],
            newCarriers: [],
            carriersWithErrors: [],
          },
        },
        ocean: {
          requestId: "",
          progress: 0,
          processing: false,
          error: false,
          results: {
            carriersOnPlatform: [],
            newCarriers: [],
            carriersWithErrors: [],
          },
        },
      },
    },
    carrierCompanyDetails: {
      carrierCompanyDetails: {
        data: {},
        loading: true,
        editing: false,
        error: false,
      },
    },
    carrierFleets: {
      fleets: {
        otr: { data: [], retrieving: false, editing: false, error: false },
      },
    },
    carrierFleetAssets: {
      assets: {
        data: [],
        pagination: { current: 1, size: 1, total: 0, count: 0 },
        retrieving: false,
        editing: false,
        error: false,
      },
      assetDetails: {
        location: { data: null, retrieving: false, error: false },
        pingLocation: { data: {}, retrieving: false, error: false },
      },
    },
    carrierIndicators: {
      ltl: {
        data: {
          mode: "ltl",
          tracking_integrations: {
            eld_gps: { total: 0, active: 0, error: 0, pending: 0 },
          },
          customers: { active: 0, connected: 0, pending: 0 },
        },
        loading: false,
        error: false,
      },
      ftl: {
        data: {
          mode: "ftl",
          tracking_integrations: {
            eld_gps: { total: 0, active: 0, error: 0, pending: 0 },
          },
          customers: { active: 0, connected: 0, pending: 0 },
        },
        loading: false,
        error: false,
      },
      parcel: { data: {}, loading: true, error: false },
      ocean: { data: {}, loading: true, error: false },
    },
    carrierInvitations: {
      invitations: {
        ltl: {
          data: [],
          sending: false,
          loading: true,
          editing: false,
          error: false,
        },
        ftl: {
          data: [],
          sending: false,
          loading: true,
          editing: false,
          error: false,
        },
        parcel: {
          data: [],
          sending: false,
          loading: true,
          editing: false,
          error: false,
        },
        ocean: {
          data: [],
          sending: false,
          loading: true,
          editing: false,
          error: false,
        },
      },
      inviterToken: "",
    },
    carriersNetworkDetails: {
      carrierDetails: {
        data: {},
        loading: false,
        editingCarrierCodes: false,
        editingCarrierContacts: false,
      },
    },
    directLoadAssignment: { data: {}, retrieving: true, error: false },
    eldGpsIntegrations: {
      data: [],
      details: {},
      retrieving: false,
      creating: false,
      updating: false,
      deleting: false,
      error: false,
    },
    emailLoadsAdditionsIntegrations: {
      data: [],
      details: {},
      retrieving: false,
      creating: false,
      updating: false,
      deleting: false,
      error: false,
    },
    loadsAdditionsTemplatesAndValidations: {
      templates: [],
      retrieving: false,
      validation: null,
      validating: false,
      error: false,
    },
    locationProviders: {
      locationProviders: {
        data: [],
        loading: true,
        creating: false,
        error: false,
      },
    },
    shipperSettings: {
      loadNumberFormats: [],
      retrieving: false,
      creating: false,
      deleting: false,
      error: false,
    },
    shipperIndicators: {
      ltl: { data: {}, loading: true, error: false },
      ftl: { data: {}, loading: true, error: false },
      parcel: { data: {}, loading: true, error: false },
      ocean: { data: {}, loading: true, error: false },
    },
    shippersNetwork: {
      shippers: {
        ltl: {
          data: [],
          filters: { query: null, networkStatus: "all", filterBy: [] },
          sortBy: [],
          pagination: {
            currentPage: 1,
            pageSize: 25,
            totalItems: 0,
            totalPages: 0,
          },
          loading: true,
          editing: false,
          error: false,
        },
        ftl: {
          data: [],
          filters: { query: null, networkStatus: "connected", filterBy: [] },
          sortBy: [],
          pagination: {},
          loading: false,
          editing: false,
          error: false,
        },
        parcel: {
          data: [],
          filters: { query: null, networkStatus: "all", filterBy: [] },
          sortBy: [],
          pagination: {
            currentPage: 1,
            pageSize: 25,
            totalItems: 0,
            totalPages: 0,
          },
          loading: true,
          editing: false,
          error: false,
        },
        ocean: {
          data: [],
          filters: { query: null, networkStatus: "all", filterBy: [] },
          sortBy: [],
          pagination: {
            currentPage: 1,
            pageSize: 25,
            totalItems: 0,
            totalPages: 0,
          },
          loading: true,
          editing: false,
          error: false,
        },
      },
    },
    shippersNetworkDetails: {
      shipperDetails: {
        data: {},
        loading: true,
        editing: false,
        error: false,
        editingCustomerCodes: false,
      },
    },
    companiesSearch: {
      searchedCompanies: { data: [], searching: false, error: false },
    },
    companiesOnboarding: {
      details: {
        data: null,
        loading: false,
        creating: false,
        error: false,
        invalid: false,
      },
    },
    users: {
      currentUser: {
        userId: "arpitsabharwal",
        gcmRegistrationId: null,
        firstName: "arpit.sabharwal",
        lastName: null,
        name: "arpit.sabharwal",
        emailAddress: "<EMAIL>",
        profileImageUrl:
          "https://www.gravatar.com/avatar/701b46caa160ea35f96012e0037c93d5",
        phone: null,
        driver: false,
        privateFleetDriver: null,
        driverId: null,
        privateFleetUserId: null,
        company: { id: null, description: null },
        companyId: null,
        companyName: null,
        CompanyPrivateFleetExternalId: null,
        role: "user",
        superAdmin: true,
        systemAdmin: false,
        companyAdmin: false,
        groupAdmin: false,
        contactRepresentative: false,
        termsAgreed: false,
        isAnalyticsEnabled: false,
        enableExecDashboard: true,
        active: true,
        authyId: null,
        locked: false,
        subscribedToEmails: false,
        permissions: {
          loads: {
            view: false,
            create: false,
            update: false,
            delete: false,
            export: false,
          },
          addresses: {
            view: true,
            create: true,
            update: true,
            delete: true,
            export: true,
          },
          notificationRules: {
            view: true,
            create: true,
            update: true,
            delete: true,
          },
          individualNotificationRules: {
            view: true,
            create: true,
            update: true,
            delete: true,
          },
          settings: { view: true, update: true },
          generalSettings: { view: true, update: true },
          thirdPartySettings: { view: true, update: true },
          trackingSettings: { view: true, update: true },
          usageSettings: { view: true, update: true },
          usageBetaSettings: { view: true, update: true },
          emailNotificationSettings: { view: true, update: true },
          customerSettings: { view: true, update: true },
          relationshipSettings: { view: true, update: true },
          trackingReports: { execute: false },
          marketplace: { access: false },
          temperatureTrackingSettings: {
            view: true,
            create: true,
            update: true,
            delete: true,
          },
          privateExchangeLoadSettings: { view: true, update: true },
          privateExchangeCapacitySettings: { view: true, update: true },
          privateExchangeNetworkSettings: { view: true, update: true },
          privateExchangeShipperNetworkSettings: { view: true, update: true },
          privateExchangeCarrierNetworkSettings: { view: true, update: true },
          privateExchangeShipperPartnershipSettings: {
            view: true,
            update: true,
          },
          privateExchangeCarrierPartnershipSettings: {
            view: true,
            update: true,
          },
          privateExchangeNetworkEvaluationSettings: {
            view: true,
            update: true,
          },
        },
        timezone: {},
        status: null,
        verified: true,
        deviceType: null,
        osVersion: null,
        deviceDetailsUpdatedAt: "2022-07-21T06:57:05Z",
        statusUpdatedAt: null,
        updatedAt: "2022-07-21T06:57:05.725Z",
        realTimeNotification: {
          enabled: true,
          channels: ["fk-gl-ch-broadcast", "fk-u-ch-2566"],
          channelGroupPrefix: "fk-u-cg-2566-",
          subscriptionKey: "******************************************",
        },
        dateFormat: null,
        distanceUnit: null,
        apiCredentialsDeactivated: false,
        carrierlinkAppVersion: null,
        carrierlinkStatus: null,
        defaultInsightBucket: "performance_analytics",
        defaultFilterId: null,
        tableauUserId: null,
        tableauActionPerformedAt: 0,
        isDynamicYardUser: false,
        notificationConfiguration: {
          keepInEmailCC: false,
          carrierConnectionStatus: false,
          customerConnectionStatus: true,
        },
        insightsLicensedBuckets: ["BA", "TC", "PA", "BM"],
        hideReports: false,
        hideCarrierFilter: false,
        hideCustomerFilter: false,
        allowAllShipperReferenceNumbers: true,
        allowAllCarrierScacs: true,
        allowAllStopIds: true,
        allowAllShippers: true,
        allowAllCarriers: true,
        allowAllShipTos: true,
        allowAllCustomers: true,
        allowAllDestinationRegions: true,
        allowAllTerminalRegions: true,
        allowAllOriginRegions: true,
        allowAllOriginCities: true,
        allowAllTerminalCities: true,
        allowAllDestinationCities: true,
        allowAllOriginStops: true,
        allowAllDestinationStops: true,
        allowAllTerminalStops: true,
        allowAllTags: true,
        originZip: [],
        terminalZip: [],
        destinationZip: [],
        allowAnyNotPickupLocations: "any",
        allowAnyNotTerminalLocations: "any",
        allowAnyNotDeliveryLocations: "any",
        allowAnyNotTags: "any",
        allowAnyNotShipTos: "any",
        allowAnyNotCarriers: "any",
        allowAnyNotCustomers: "any",
        allowAnyNotShippers: "any",
        userConfigurations: { landPage: "load", showBeta: false },
        createdBy: "<EMAIL>",
        modules: ["admin"],
      },
      companyId: "arpit-carrier-2",
      companyType: ["carrier"],
      companyName: "Arpit Carrier 2",
      error: null,
      loading: false,
      isSuperAdmin: true,
      directAssignment: {
        apiToken: "2fd00c24-9253-4c6d-b619-d7fbb641cce5",
        urlToken: null,
      },
      isCompanyAdmin: false,
      termsAgreed: false,
    },
    externalOboarding: {
      carrier: {
        data: null,
        loading: false,
        creating: false,
        invalid: false,
        error: false,
      },
      user: {
        data: null,
        loading: false,
        creating: false,
        invalid: false,
        error: false,
      },
    },
    externalParameters: { externalAcessToken: null, externalUser: null },
  };
};
