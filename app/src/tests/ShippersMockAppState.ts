import { getInitialMockAppState } from "tests/InitialMockAppState";

const initialMockAppState = getInitialMockAppState();

export const getShippersMockAppState = () => {
  return {
    ...initialMockAppState,
    carriersNetwork: {
      carriers: {
        ltl: {},
        ftl: {
          data: [
            {
              connectivity: {
                checks: {
                  is_onboarded_to_platform: true,
                  is_connected_to_my_network: true,
                  has_connected_location_data_integrations: false,
                  has_connected_asset_assignment_integrations: false,
                  has_tracked_loads: true,
                },
                status: "ok",
                is_qa_hidden: false,
              },
              load_volume: {
                value: 16,
              },
              tracking_quality: {
                overall: {
                  assigned_percentage: 0,
                  tracked_percentage: 0,
                },
                mine: {
                  assigned_percentage: 100,
                  tracked_percentage: 100,
                },
              },
              location_data_integrations: [],
              asset_assignment_integrations: [],
              id: 6624,
              permalink: "test-ss-carrier-85",
              name: "Test SS Carrier 85",
              type: "carrier",
              status: "connected",
              capabilities: [
                {
                  id: "tracking",
                  name: "Tracking",
                  enabled: true,
                },
              ],
              identifications: [
                {
                  type: "usdot",
                  value: "18465",
                },
                {
                  type: "mc",
                  value: "10085",
                },
              ],
              carrier_codes: {
                custom: ["TESTSS85"],
                default: [],
              },
              usdot: "18465",
            },
            {
              connectivity: {
                checks: {
                  is_onboarded_to_platform: true,
                  is_connected_to_my_network: true,
                  has_connected_location_data_integrations: false,
                  has_connected_asset_assignment_integrations: false,
                  has_tracked_loads: false,
                },
                status: "awaiting_loads_additions",
                is_qa_hidden: false,
              },
              load_volume: {
                value: 0,
              },
              tracking_quality: {
                mine: {
                  assigned_percentage: 0,
                  tracked_percentage: 0,
                },
              },
              id: 6630,
              permalink: "lincoln-trucking-lightning-division",
              name: "Lincoln Trucking Lightning Division",
              type: "carrier",
              status: "connected",
              capabilities: [
                {
                  id: "tracking",
                  name: "Tracking",
                  enabled: true,
                },
              ],
              identifications: [
                {
                  type: "usdot",
                  value: "1823932",
                },
                {
                  type: "mc",
                  value: "661796",
                },
              ],
              carrier_codes: {
                custom: ["TESTSS3"],
                default: [],
              },
              usdot: "1823932",
            },
          ],
          filters: {
            query: null,
            networkStatus: "connected",
            filterBy: [],
          },
          loading: false,
          editing: false,
          error: false,
          pagination: {
            currentPage: 1,
            pageSize: 25,
            totalItems: 2,
            totalPages: 1,
          },
        },
        parcel: {},
        ocean: {},
      },
    },
    shipperIndicators: {
      ltl: { data: {}, loading: true, error: false },
      ftl: {
        data: {
          carriers: {
            total: 524,
            active: 524,
            noLoads: 0,
            errors: [],
          },
          invitations: {
            total: 37,
            invited: 36,
            onboarded: 1,
            resubmissions: {
              enabled: true,
              seconds_until_enabled: 0,
              last_sent: null,
            },
          },
        },
        loading: false,
        error: false,
      },
      parcel: { data: {}, loading: true, error: false },
      ocean: { data: {}, loading: true, error: false },
    },
    carrierFleets: { fleets: { otr: {} }, selectedFleetId: undefined },
  };
};
