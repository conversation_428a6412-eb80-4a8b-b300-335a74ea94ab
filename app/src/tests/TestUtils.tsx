import { Provider, useSelector } from "react-redux";
import { render } from "@testing-library/react";
import { MemoryRouter } from "react-router-dom";

export const mockedUseSelector = useSelector as jest.Mock<any>;

export const renderWithRedux = (component: any, store: any) => {
  return {
    ...render(
      <MemoryRouter>
        <Provider store={store}>{component}</Provider>
      </MemoryRouter>
    ),
    store,
  };
};

export const changeState = (localMockState: any) => {
  mockedUseSelector.mockImplementation((callback) => {
    return callback(localMockState);
  });
};

export const toBeValidAssertion = (component: any) => {
  expect(component).toBeValid();
};

export const toHaveTextContentAssertion = (
  component: any,
  textContent: any
) => {
  expect(component).toHaveTextContent(textContent);
};

export const mockUseLocation = (
  pathname: any,
  search: any,
  hash: any,
  key: any
) => {
  jest.mock("react-router-dom", () => ({
    ...jest.requireActual("react-router-dom"),
    useLocation: () => ({
      pathname: pathname,
      search: search,
      hash: hash,
      key: key,
    }),
  }));
};
