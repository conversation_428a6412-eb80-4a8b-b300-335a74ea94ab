/**
 * Utility functions for date calculations and formatting
 */

/**
 * Calculates the deadline date (14 calendar days from a given date)
 * @param fromDate - The starting date (survey creation date)
 * @returns The deadline date as a formatted string
 */
export const calculateDeadlineDate = (fromDate: string | Date): string => {
  const startDate = new Date(fromDate);
  const deadlineDate = new Date(startDate);
  
  // Add 14 calendar days
  deadlineDate.setDate(startDate.getDate() + 14);
  
  // Format the date as "Month DD, YYYY"
  return deadlineDate.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

/**
 * Calculates the deadline date from current date (for fallback)
 * @returns The deadline date as a formatted string (14 days from now)
 */
export const calculateDeadlineFromNow = (): string => {
  return calculateDeadlineDate(new Date());
};

/**
 * Checks if a date string is valid
 * @param dateString - The date string to validate
 * @returns boolean indicating if the date is valid
 */
export const isValidDate = (dateString: string): boolean => {
  const date = new Date(dateString);
  return !isNaN(date.getTime());
};

/**
 * Formats a date string to a readable format
 * @param dateString - The date string to format
 * @returns Formatted date string
 */
export const formatDate = (dateString: string): string => {
  if (!isValidDate(dateString)) {
    return '';
  }
  
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};
