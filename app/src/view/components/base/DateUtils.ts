export const sortByCreatedAtDesc = (a: any, b: any) => {
  var c: any = new Date(a?.created_at);
  var d: any = new Date(b?.created_at);
  return d - c;
};

export const formatDateDays = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString([], {
    month: "short",
    day: "2-digit",
    year: "numeric",
  });
};

export const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString);
  return `${date.toLocaleTimeString([], {
    hour: "2-digit",
    minute: "2-digit",
  })}`;
};

export const timeZoneAbbreviated = (): string => {
  const timeZone = new Date().toString().match(/\((.+)\)/)![1];
  // Chrome: "Thu Aug 06 2020 16:21:38 GMT+0530 (India Standard Time)"
  // Safari: "Thu Aug 06 2020 16:24:03 GMT+0530 (IST)"
  const isTimezoneFromSafari = timeZone.includes(" ");
  if (isTimezoneFromSafari) {
    return timeZone
      .split(" ")
      .map((item) => item[0])
      .join("");
  } else {
    return timeZone;
  }
};

export const getUserLocaleDate = (utcDate: string) => {
  if (utcDate == null) {
    return "Unknown";
  }

  const isUtcFormatted = utcDate?.includes(".") && utcDate?.includes("Z");
  let timezoneDate = isUtcFormatted ? utcDate : utcDate + ".000Z";
  return `${formatDateDays(timezoneDate)}, ${formatDateTime(timezoneDate)}`;
};

export const timePassedSinceDate = (utcDate: string) => {
  let now = new Date().getTime();
  let lastDate = new Date(utcDate).getTime();
  let diff = now - lastDate;

  // calculate (and subtract) whole days
  let days = Math.floor(diff / 86400000);
  // calculate (and subtract) whole hours
  let hours = Math.floor(diff / 3600000) % 24;
  diff -= hours * 3600;
  // calculate (and subtract) whole minutes
  let minutes = Math.floor(diff / 60000) % 60;
  diff -= minutes * 60;

  let daysStr = days > 0 ? `${days} day${days > 1 ? "s" : ""} ` : "";
  let hoursStr = hours > 0 ? `${hours} hour${hours > 1 ? "s" : ""} ` : "";
  let minsStr = minutes > 0 ? `${minutes} min${minutes > 1 ? "s" : ""}` : "";
  let ellapsedTime = `${daysStr}${hoursStr}${minsStr}`;

  return ellapsedTime !== "" ? ellapsedTime + " ago" : "Now";
};

export const daysPassedSinceDate = (utcDate: string) => {
  let now = new Date().getTime();
  let lastDate = new Date(utcDate).getTime();
  let diff = now - lastDate;

  // calculate (and subtract) whole days
  let days = Math.floor(diff / 86400000);
  return days;
};

export const secondsToHm = (digit: number) => {
  digit = Number(digit);
  if (digit < 60) {
    return `${digit} seconds`;
  }
  const hour = Math.floor(digit / 3600);
  const min = Math.floor((digit % 3600) / 60);

  var hourDisplay = hour > 0 ? hour + (hour == 1 ? " hour " : " hours ") : "";
  var minDisplay = min > 0 ? min + (min == 1 ? " minute " : " minutes ") : "";
  return hourDisplay + minDisplay;
};

export const convertDatetoUtcString = (date: string) => {
  return new Date(date).toUTCString();
};

export const convertDateToUtcDate = (date: Date) => {
  return new Date(
    date.getUTCFullYear(),
    date.getUTCMonth(),
    date.getUTCDate(),
    date.getUTCHours(),
    date.getUTCMinutes(),
    date.getUTCSeconds()
  );
};

/* if provided date is already in UTC and just formatting is needed 
to present date in standard UI date time format(output will look like:- 
Mon, 11 Dec 2023 16:16:24 GMT)*/  
export const formatDate = (timestamp: string) => {
  const dateObject = new Date(timestamp);

  const options: Intl.DateTimeFormatOptions = {
    weekday: 'short',
    day: '2-digit',
    month: 'short',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  };

  const formattedDate = dateObject.toLocaleString('en-US', options);

  const [weekday, day, month, year, time] = formattedDate.split(', ');
  return `${weekday}, ${day} ${month} ${year+' GMT'}`;
};
