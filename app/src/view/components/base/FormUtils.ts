/**
 * Gets the first name, technically gets all words leading up to the last
 */
export const getFirstName = (fullName: string) => {
  if (!fullName) {
    return "";
  }

  var arr = fullName?.split(" ");
  if (arr?.length === 1) {
    return arr[0];
  }
  return arr.slice(0, -1).join(" ") || ""; // returns "Paul Steve"
};

/**
 * Gets the last name (e.g. the last word in the supplied string)
 */
export const getLastName = (fullName: string, ifNone: any) => {
  if (!fullName) {
    return "";
  }

  var arr = fullName?.split(" ");
  if (arr?.length === 1) {
    return ifNone;
  }
  return arr.slice(-1).join(" ");
};

// FORM VALIDATION

/**
 * Verifies whether an email is valid or not by regex matchic
 */
export const isEmailInvalid = (email: string) => {
  const re =
    /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return !re.test(String(email).toLowerCase());
};

/**
 * Returns whether a field has a valid north america phone
 */
export const isNorthAmericaPhoneInvalid = (value: string) => {
  if (!value) {
    return true;
  }

  //const re = /^\(?([2-9][0-8][0-9])\)?[-.●]?([2-9][0-9]{2})[-.●]?([0-9]{4})$/;

  const reUsCanada =
    /^(\+?\d{1}?\s?)?((\(\d{3}\))|(\d{3}))?[\s.-]?\d{3}?[\s.-]?\d{4}$/;
  const reMexico =
    /^(\+?\d{2}?\s?)?((\(\d{2}\))|(\d{2}))?[\s.-]?\d{4}?[\s.-]?\d{4}$/;

  return !(reUsCanada.test(value) || reMexico.test(value));
};

/**
 * Returns whether an input field is invalid
 */
export const isFieldInvalid = (fieldValue: string) => {
  return fieldValue == null || fieldValue?.trim() === "";
};

/**
 * Returns whether a url field is invalid
 */
export const isUrlInvalid = (url: string) => {
  try {
    new URL(url);
  } catch (e) {
    console.error(e);
    return true;
  }
  return false;
};

/**
 * Returns whether a field has only numbers
 */
export const fieldHasOnlyNumbers = (value: string) => {
  if (!value) {
    return true;
  }

  return /^\d+$/.test(value);
};

export const isValidUSPostalCode = (value: string) => {
  const usPostalCodePattern = /^\d{5}(?:[-\s]\d{4})?$/;
  return usPostalCodePattern.test(value);
};

export const isValidCanadianPostalCode = (value: string): boolean => {
  const canadianPostalCodeRegex = /^[A-Za-z]\d[A-Za-z] ?\d[A-Za-z]\d$/;
  return canadianPostalCodeRegex.test(value);
};
