import {
  BoxMovingIcon,
  TruckIcon,
  TruckLoadedIcon,
  ShipIcon,
  TrainIcon,
  Flight3Icon,
  BoxOpenedIcon,
  JoinInnerIcon,
} from "@fourkites/elemental-atoms";

export const getIconForMode = (mode: string) => {
  let Icon = TruckIcon;

  const lowercaseMode = mode?.toLowerCase();

  switch (lowercaseMode) {
    case "courier":
      Icon = BoxMovingIcon;
      break;
    case "parcel":
      Icon = BoxOpenedIcon;
      break;
    case "ltl":
      Icon = TruckLoadedIcon;
      break;
    case "ftl":
      Icon = TruckIcon;
      break;
    case "ocean":
      Icon = ShipIcon;
      break;
    case "rail":
      Icon = TrainIcon;
      break;
    case "air":
      Icon = Flight3Icon;
      break;
    default:
      Icon = JoinInnerIcon;
      break;
  }

  return Icon;
};

export const getModeIconAndDescription = (mode: string) => {
  const modeDescriptions = {
    parcel: "Parcel",
    courier: "Courier",
    ltl: "LTL",
    ftl: "FTL",
    rail: "Rail",
    ocean: "Ocean",
    air: "Air",
  };

  return {
    //@ts-ignore
    Icon: getIconForMode(mode),
    //@ts-ignore
    description: modeDescriptions[mode] || "-",
  };
};
