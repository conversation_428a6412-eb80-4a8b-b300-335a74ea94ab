/*
 * Converts any string to kebab case
 */
export const toKebabCase = (value: string) =>
  value
    .replace(/([a-z])([A-Z])/g, "$1-$2")
    .replace(/[\s_]+/g, "-")
    .toLowerCase();

/*
 * Converts string from kebab case to camel case
 */
export const fromKebabToCamelCase = (value: string) => {
  let x = value.replace(/-./g, (x) => x[1].toUpperCase());

  return x.replace(/^./, x[0].toUpperCase());
};

/*
 * Convert the snake case to camel case with spaces
 */
export const snakeCaseToSpacedCamelCase = (name: string) => {
  if (name && name.length) {
    let i,
      fragments = name.split("_");
    for (i = 0; i < fragments.length; i++) {
      fragments[i] =
        fragments[i].charAt(0).toUpperCase() + fragments[i].slice(1);
    }
    return fragments.join(" ");
  }
  return "";
};

/*
 * Starts the string with upper case
 */
export const startWithUpperCase = (value: string) => {
  if (!value) {
    return value;
  }

  return value?.length > 0 ? value?.replace(/^./, value[0].toUpperCase()) : "";
};

/*
 * Convert the string to base 64 encoded format
 */
export const base64EncodeUnicode = (value: string) => {
  // First we use encodeURIComponent to get percent-encoded UTF-8,
  // Then we convert the percent encodings into raw bytes which
  // Can be fed into btoa.
  return btoa(
    encodeURIComponent(value).replace(
      /%([0-9A-F]{2})/g,
      function toSolidBytes(match: any, p1: any) {
        // @ts-ignore
        return String.fromCharCode("0x" + p1);
      }
    )
  );
};
