import React, { Fragment } from "react";
import { CaretRightIcon } from "@fourkites/elemental-atoms";

import styles from "./BreadcrumbsHeader.module.scss";
import BreadcrumbsHeaderProps from "./BreadcrumbsHeader.types";

const BreadcrumbsHeader = ({ titles, children }: BreadcrumbsHeaderProps) => {
  return (
    <div
      className={styles.headerContainer}
      data-test-id="breadcrumbs-header-container"
    >
      <div className={styles.headerLeft}>
        {titles?.map((title: string, idx: number) => (
          <Fragment key={idx}>
            <h5
              data-testid="bread-crumbs-header-title"
              className={styles.headerTitle}
            >
              {title}
            </h5>
            {idx < titles.length - 1 && (
              <CaretRightIcon iconClass={styles.breadcrumbIcon} />
            )}
          </Fragment>
        ))}
      </div>

      <div
        className={styles.headerRight}
        data-test-id="breadcrumbs-header-elements"
      >
        {children}
        {/*
        {children?.map((component: React.Component, idx: number) => (
          <div
            className={
              idx < children!.length - 1 ? styles.headerRightInnerComponent : ""
            }
          >
            {component}
          </div>
        ))}
        */}
      </div>
    </div>
  );
};

export default BreadcrumbsHeader;
