import React from "react";
import { useTranslation } from "react-i18next";

import { Tooltip } from "@fourkites/elemental-tooltip";

import styles from "./CodesIndicator.module.scss";

const CodesIndicator = ({ codes }: any) => {
  const { t } = useTranslation();

  const companyCodes =
    codes != null ? [...codes?.default, ...codes?.custom] : [];

  // COMPANY CODES
  // Show only 2 first codes
  const companyCodesText =
    companyCodes?.slice(0, 2)?.join(", ") +
    (companyCodes?.length > 2 ? ` +${companyCodes?.length - 2} MORE` : "");

  const showCodes = companyCodes?.length > 0;

  if (!showCodes) {
    return null;
  }

  return (
    <Tooltip text={companyCodes?.join(", ")}>
      <span className={styles.container}>
        {t("CODES: ")} {companyCodesText}
      </span>
    </Tooltip>
  );
};

export default CodesIndicator;
