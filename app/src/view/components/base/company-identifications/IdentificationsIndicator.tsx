import React from "react";
import { useTranslation } from "react-i18next";

import { Tooltip } from "@fourkites/elemental-tooltip";

import styles from "./IdentificationsIndicator.module.scss";

const IdentificationsIndicator = ({ type, label, identifications }: any) => {
  const { t } = useTranslation();

  // Company identification of type
  let companyIdentification = getCompanyIdentification(identifications, type);

  // XXX: backend wrongly returns empty string, so we need this check
  if (!companyIdentification || !companyIdentification?.length) {
    return <>--</>;
  }

  // Show only 2 first usdots
  const companyIdentificationText =
    companyIdentification?.slice(0, 2)?.join(", ") +
    (companyIdentification?.length > 2
      ? ` +${companyIdentification?.length - 2} MORE`
      : "");

  return (
    <Tooltip text={companyIdentification?.join(", ")}>
      <span className={styles.container} data-testid="company-usdot">
        {t(`${label}: `)} {companyIdentificationText}
      </span>
    </Tooltip>
  );
};

export type CompanyIdentificationType = "vat" | "mc" | "ein" | "usdot" | "scac";

type CompanyIdentification = {
  type: CompanyIdentificationType;
  value: string;
};

export const getCompanyIdentification = (
  identifications: CompanyIdentification[],
  identificationType: CompanyIdentificationType
) => {
  return identifications
    ?.filter(
      (entry) =>
        entry?.type === identificationType &&
        entry?.value !== "" &&
        entry?.value != null
    )
    ?.map((item) => item?.value);
};

export default IdentificationsIndicator;
