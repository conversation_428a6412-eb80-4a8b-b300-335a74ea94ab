@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.contact {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;

  & > div:first-child {
    display: flex;
    align-items: center;
    flex: 5;
  }

  > div > span {
    display: flex;
    flex-direction: column;
    margin-left: 16px;

    > label[id="position"] {
      font: $typography-small-regular;
      color: $color-neutral-700;
    }
  }

  > span[id="contact-icons"] {
    display: flex;
    flex-direction: row;
  }
}

.contactIconContent {
  display: flex;
  align-items: center;
  padding: 8px 16px 8px 16px;

  > button {
    padding: 0;
    background-color: transparent;
    border: none;
    cursor: pointer;
  }
}

.contactIcon {
  height: 24px;
  width: 24px;
  margin-left: 16px;
  cursor: pointer;
}

.copyIcon {
  height: 20px;
  width: 20px;
  margin-left: 16px;
  cursor: pointer;
}

.moreActionsContainer {
  width: auto;
  > span {
    > button {
      width: 100%;
      display: flex;
      align-items: center;
      cursor: pointer;
      font: $typography-small-regular;
      gap: 8px;
      padding-top: 4px;
      padding-bottom: 4px;
    }
  }
}

.editButton {
  &:hover {
    background-color: $color-neutral-400;
  }
}

.deleteButton {
  &:hover {
    background-color: $color-accent-cherry-300;
  }
  color: $color-accent-cherry-500;
}

.flyoutContent {
  display: flex;
  flex-direction: column;
  justify-content: left;
  padding-top: 0px;
  padding-bottom: 0px;
  position: relative;
  width: 100%;
  border-radius: 4px;
  box-shadow: 0 6px 14px 3px rgba(0, 0, 0, 0.1);
  background-color: white;
}
