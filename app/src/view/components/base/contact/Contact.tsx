import { useTranslation } from "react-i18next";

import {
  MailIcon,
  PhoneIcon,
  CopyIcon,
  MenuKebabFillIcon,
  Edit3Icon,
  TrashFullIcon,
} from "@fourkites/elemental-atoms";
import { Avatar } from "@fourkites/elemental-avatar";

import FlyoutMenu from "view/components/base/flyout-menu/FlyoutMenu";
import { startWithUpperCase } from "view/components/base/StringUtils";

import styles from "./Contact.module.scss";
import ContactProps from "./Contact.types";

const Contact = ({
  contact,
  contactInline = false,
  allowMoreActions = false,
  onEditClick,
  onDeleteClick,
}: ContactProps) => {
  const { t } = useTranslation();

  const copyToClipboard = (content: any) =>
    navigator.clipboard.writeText(content);

  const { firstName, lastName, email, secondaryEmails, phones, position } =
    contact;

  const parsedFirstName = startWithUpperCase(firstName);
  const parsedLastName = startWithUpperCase(lastName);

  // TODO: make tooltips work
  const emails = [...([email] || []), ...(secondaryEmails || [])];
  const parsedEmails =
    emails?.length === 0
      ? "No emails"
      : emails.length > 1
      ? `${emails[0]} +${emails.length - 1}`
      : emails[0];

  const parsedPhones =
    !phones || phones.length === 0
      ? null
      : phones.length > 1
      ? `${phones[0]} +${phones.length - 1}`
      : phones[0];

  const moreActionsFlyoutMenu = (
    <FlyoutMenu
      anchor={
        <MenuKebabFillIcon iconClass={styles.contactIcon} fill="#21252a" />
      }
    >
      <div className={styles.flyoutContent}>
        <div className={styles.moreActionsContainer}>
          <span className={styles.contactIconContent}>
            <button
              data-test-id="contact-email-container"
              className={styles.editButton}
              onClick={onEditClick}
            >
              <Edit3Icon iconClass={styles.contactIcon} />
              {t("Edit Contact")}
            </button>
          </span>
          <span className={styles.contactIconContent}>
            <button
              data-test-id="contact-email-container"
              className={styles.deleteButton}
              onClick={onDeleteClick}
            >
              <TrashFullIcon iconClass={styles.contactIcon} fill="red" />
              {t("Delete Contact")}
            </button>
          </span>
        </div>
      </div>
    </FlyoutMenu>
  );

  return (
    <div className={styles.contact}>
      <div data-test-id="contact-details">
        <Avatar
          imageSrc={contact.avatar || ""}
          size={contactInline ? "md" : "lg"}
          name={{
            firstName: parsedFirstName,
            lastName: parsedLastName,
          }}
        />
        <span>
          <label data-test-id="contact-name">{`${parsedFirstName || ""} ${
            parsedLastName || ""
          }`}</label>
          {contactInline ? (
            <label data-test-id="contact-position" id="position">
              {emails.length > 0 || (phones && phones?.length > 0) ? (
                <>
                  {parsedEmails} {parsedPhones ? `| ${parsedPhones}` : ""}
                </>
              ) : (
                "No email or phones"
              )}
            </label>
          ) : (
            <label id="position">{position || "-"}</label>
          )}
        </span>
      </div>

      {!contactInline && (
        <span id="contact-icons">
          {emails.length > 0 && (
            <FlyoutMenu
              anchor={
                <MailIcon iconClass={styles.contactIcon} fill="#0e65e5" />
              }
            >
              <div>
                {emails.map((email: string) => (
                  <span
                    data-test-id="contact-email-container"
                    className={styles.contactIconContent}
                  >
                    {email}
                    <button
                      data-test-id="copy-email-button"
                      onClick={() => copyToClipboard(email)}
                    >
                      <CopyIcon iconClass={styles.copyIcon} fill="#21252a" />
                    </button>
                  </span>
                ))}
              </div>
            </FlyoutMenu>
          )}
          {phones && phones.length > 0 && (
            <FlyoutMenu
              anchor={
                <PhoneIcon iconClass={styles.contactIcon} fill="#21252a" />
              }
            >
              <div>
                {phones.map((phones: string) => (
                  <span
                    data-test-id="contact-phone-container"
                    className={styles.contactIconContent}
                  >
                    {phones}
                    <button
                      data-test-id="copy-phone-button"
                      onClick={() => copyToClipboard(phones)}
                    >
                      <CopyIcon iconClass={styles.copyIcon} fill="#21252a" />
                    </button>
                  </span>
                ))}
              </div>
            </FlyoutMenu>
          )}
          {allowMoreActions && moreActionsFlyoutMenu}
        </span>
      )}
    </div>
  );
};

export default Contact;
