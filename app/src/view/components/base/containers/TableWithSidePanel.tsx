import React from "react";

import styles from "./TableWithSidePanel.module.scss";

const TableWithSidePanel = ({ table, sidePanel }: any) => {
  return (
    <div className={styles.container}>
      <div className={styles.tableContainer}>{table}</div>

      {sidePanel !== null && (
        <div className={styles.sidePanelContainer}>{sidePanel}</div>
      )}
    </div>
  );
};

export default TableWithSidePanel;
