import React, { useEffect, useState } from "react";

import { Input } from "@fourkites/elemental-input";

import styles from "./EditableCell.module.scss";

const EditableCell = ({
  label,
  initialValue,
  errorLabel,
  required = true,
  invalid = false,
  disabled = false,
  onUpdate,
}: any) => {
  // We need to keep and update the state of the cell normally
  const [value, setValue] = useState(initialValue);

  const onChange = (e: any) => {
    setValue(e.target.value);
  };

  // We'll only update the external data when the input is blurred
  const onBlur = () => {
    onUpdate(value);
  };

  // If the initialValue is changed external, sync it up with our state
  useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  return (
    <div
      className={disabled ? styles.inputWrapperDisabled : styles.inputWrapper}
    >
      <Input
        size="small"
        label={label}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        required={required}
        invalid={invalid}
        disabled={disabled}
        errorLabel={errorLabel}
      />
    </div>
  );
};

export default EditableCell;
