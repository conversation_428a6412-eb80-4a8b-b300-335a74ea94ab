@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container {
  display: flex;
  flex-direction: column;
  margin-bottom: 24px;

  > div {
    cursor: auto !important;
    box-shadow: none !important;
  }

  > div > div[id=header] {
    display: flex;
    align-items: center;
    flex-direction: row;
    justify-content: space-between;
    padding: 12px 24px 12px 24px;

    > button {
      display: flex;
      align-items: center;
    }

    > div > button {
      margin-right: 16px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  > div > div[id=content] {
    padding: 17px 24px 17px 24px;
  }

  > div > div > h5 {
    color: $color-neutral-800;
    font-size: 18px;
    font-weight: 600;
    line-height: 22px;
    margin: 0;
  }
}

.divider {
  display: block;
  height: 1px;
  width: 100%;
  background-color: $color-neutral-300;
}
