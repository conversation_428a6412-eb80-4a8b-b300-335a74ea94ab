import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Button, Edit1Icon } from "@fourkites/elemental-atoms";

import Card from "view/components/base/card/Card";

import styles from "./EditableSection.module.scss";

const EditableSection = ({
  title,
  children,
  editable,
  onEdit,
  onSave,
  onCancel,
  areFieldsValid = true,
}: any) => {
  const { t } = useTranslation();

  const [isEditing, setIsEditing] = useState<boolean>(false);

  return (
    <div className={styles.container}>
      <Card>
        <div id="header">
          <h5 data-test-id="company-section-title">{title}</h5>

          {!isEditing ? (
            editable && (
              <Button
                className={styles.carrierAuthorizationButton}
                theme="tertiary"
                size="medium"
                data-test-id={`btn-${title}-edit`}
                onClick={() => {
                  setIsEditing(true);
                  onEdit();
                }}
              >
                <Edit1Icon fill="#0e65e5" iconClass={"button-icon-left"} />
                {t("Edit")}
              </Button>
            )
          ) : (
            <div>
              <Button
                className={styles.carrierAuthorizationButton}
                size="medium"
                theme="secondary"
                data-test-id={`btn-${title}-cancel`}
                onClick={() => {
                  setIsEditing(false);
                  onCancel();
                }}
              >
                {t("Cancel")}
              </Button>
              <Button
                className={styles.carrierAuthorizationButton}
                size="medium"
                data-test-id={`btn-${title}-save`}
                onClick={() => {
                  if (areFieldsValid) {
                    setIsEditing(false);
                  }
                  onSave();
                }}
              >
                {t("Save")}
              </Button>
            </div>
          )}
        </div>

        <div className={styles.divider} />

        <div id="content">{children}</div>
      </Card>
    </div>
  );
};

export default EditableSection;
