import { useTranslation } from "react-i18next";

import { Select } from "@fourkites/elemental-select";

const FileFormatSelector = ({
  errorLabel,
  value,
  onChange,
  showError,
  required,
  disabled,
  enabledFormats,
}: any) => {
  const { t } = useTranslation();

  // Gets only formats which are enabled, by subsetting the object to see ifmode
  // the key is available
  const filtereOptions = Object.keys(FILE_FORMATS)
    .filter((key) => enabledFormats?.includes(key))
    .reduce((cur, key) => {
      return Object.assign(cur, {
        //@ts-ignore
        [key]: FILE_FORMATS[key],
      });
    }, {});
  const options: string[] = Object.values(filtereOptions);

  return (
    <Select
      label={`${t("File Format")}`}
      errorLabel={errorLabel}
      options={options}
      value={value}
      showError={showError}
      onChange={onChange}
      required={required}
      disabled={disabled}
    />
  );
};

export const FILE_FORMATS = {
  xml: "XML",
  xml20: "XML2.0",
  csv: "CSV",
  xlsx: "XLSX",
  edi: "EDI",
};

export const getFileFormatValue = (fileFormatKey: string) => {
  // @ts-ignore
  return FILE_FORMATS[fileFormatKey];
};

export const getFileFormatKeyFromValue = (fileFormatValue: string) => {
  return (
    Object.keys(FILE_FORMATS).find(
      // @ts-ignore
      (key: any) => FILE_FORMATS[key] === fileFormatValue
    ) || "csv"
  );
};

export default FileFormatSelector;
