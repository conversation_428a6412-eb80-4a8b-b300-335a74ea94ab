import React from "react";
import { useTranslation } from "react-i18next";

import { Select } from "@fourkites/elemental-select";

import { LoadsTrackingMode } from "state/BaseTypes";

import { getIconForMode } from "view/components/base/ModeUtils";

import styles from "./ModeSelector.module.scss";

const ModeSelector = ({
  errorLabel,
  value,
  onChange,
  showError,
  required,
  disabled,
  enabledModes,
}: any) => {
  const { t } = useTranslation();

  // Gets only fromates which are enabled, by subsetting the object to see if
  // the key is available
  const filtereOptions = Object.keys(ALL_MODES)
    .filter((key) => enabledModes?.includes(key))
    .reduce((cur, key) => {
      return Object.assign(cur, {
        //@ts-ignore
        [key]: ALL_MODES[key],
      });
    }, {});
  const options: string[] = Object.values(filtereOptions);

  return (
    <Select
      label={`${t("Shipment Mode")}`}
      errorLabel={errorLabel}
      options={options}
      value={value}
      renderValue={(values: string[]) => {
        return (
          <span>
            {values?.map((v: string, index: number) => {
              const Icon = getIconForMode(v);
              return (
                <span key={index} className={styles.modeIcon}>
                  <Icon size={"18px"} />

                  <label>{value}</label>
                </span>
              );
            })}
          </span>
        );
      }}
      showError={showError}
      onChange={onChange}
      required={required}
      disabled={disabled}
    />
  );
};

export const ALL_MODES = {
  generic: "Generic",
  parcel: "Parcel",
  ltl: "LTL",
  ocean: "Ocean",
  air: "Air",
  //TODO: these modes don't have templates, so we are not allowing them
  courier: "Courier",
  ftl: "FTL",
  rail: "Rail",
};

export const getModeKeyFromValue = (modeValue: string) => {
  if (modeValue === "Generic") {
    return null;
  }

  return (
    (Object.keys(ALL_MODES).find(
      // @ts-ignore
      (key: any) => ALL_MODES[key] === modeValue
    ) as LoadsTrackingMode) || null
  );
};

export const getModeValue = (modeKey: string) => {
  // @ts-ignore
  return ALL_MODES[modeKey] || "Generic";
};

export default ModeSelector;
