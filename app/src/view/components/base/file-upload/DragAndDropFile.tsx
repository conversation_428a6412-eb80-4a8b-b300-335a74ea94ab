import { useState, useRef } from "react";

import styles from "./FileUpload.module.scss";

const DragAndDropFile = ({ type, processDrop, setError, children }: any) => {
  let [dragOverlay, setDragOverlay] = useState(false);
  let dragCounter = useRef(0);

  const handleDrag = (e: any) => {
    preventBrowserDefaults(e);
  };

  const handleDragIn = (e: any) => {
    preventBrowserDefaults(e);
    dragCounter.current++;
    if (e?.dataTransfer?.items?.length > 0) {
      setDragOverlay(true);
    }
  };

  const handleDragOut = (e: any) => {
    preventBrowserDefaults(e);
    dragCounter.current--;
    if (dragCounter.current === 0) {
      setDragOverlay(false);
    }
  };

  const handleDrop = (e: any) => {
    const files = e?.dataTransfer?.files;
    preventBrowserDefaults(e);
    setDragOverlay(false);
    setError(false);
    dragCounter.current = 0;

    processDrop(files);
  };

  const baseClass =
    type === "text-area" ? styles.textAreaContainer : styles.inputContainer;
  const overlayClass =
    type === "text-area"
      ? styles.textAreaContainerOverlay
      : styles.inputContainerOverlay;

  return (
    <div
      className={dragOverlay ? overlayClass : baseClass}
      data-test-id="drag-and-drop-box"
      onDragEnter={handleDragIn}
      onDragLeave={handleDragOut}
      onDragOver={handleDrag}
      onDrop={handleDrop}
    >
      {children}
    </div>
  );
};

export const preventBrowserDefaults = (e: any) => {
  e.preventDefault();
  e.stopPropagation();
};

export default DragAndDropFile;
