@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.container {
  display: flex;
}

.textAreaContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  align-content: center;
  justify-content: center;
  box-sizing: border-box;
  height: 300px;
  width: 1024px;
  border: 1px dashed $color-neutral-500;
  border-radius: 4px;
  background-color: $color-neutral-50;
}

.textAreaContainerOverlay {
  composes: textAreaContainer;
  border: 1px dashed $color-primary-500;
  background-color: $color-primary-100;
}

.inputContainer {
  display: flex;
  align-items: center;
  align-content: center;
  text-align: left;
  justify-content: space-around;
  box-sizing: border-box;
  height: 32px;
  width: 354px;
  border: 1px solid $color-neutral-500;
  border-radius: 4px;
  background-color: $color-neutral-00;
  margin-right: 16px;
}

.inputContainerOverlay {
  composes: inputContainer;
  border: 1px $color-primary-500;
  background-color: $color-primary-100;
}

.fileContainer {
  display: flex;
  flex-direction: column;
  text-align: center;
  align-items: center;
  align-content: center;

  > label {
    color: $color-neutral-600;
    font-size: 16px;
    letter-spacing: 0;
    line-height: 27px;
    text-align: center;
    margin-top: 16px;
    margin-bottom: 16px;
  }

  > div[id="button-wrapper"] {
    display: flex;

    > button {
      display: flex;
      align-items: center;
      margin-right: 16px;

      &:last-child {
        margin-right: 0;
      }
    }
  }
}

.inputFileContainer {
  composes: fileContainer;
  flex-direction: row;
  width: 100%;
  padding-left: 12px;
  padding-right: 12px;

  > label {
    width: 100%;
    text-align: left;
  }
}

.error {
  max-width: 600px;
  color: red;
}

.uploadIcon {
  height: 64px;
  width: 64px;
  opacity: 0.25;
}
