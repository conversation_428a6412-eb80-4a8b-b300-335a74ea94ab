import { useState } from "react";
import { useTranslation } from "react-i18next";

import { Button, UploadIcon, AttachmentIcon } from "@fourkites/elemental-atoms";

import DragAndDropFile from "./DragAndDropFile";
import { fileValidator } from "./FileUtils";

import styles from "./FileUpload.module.scss";
import FileUploadProps from "./FileUpload.types";

const FileUpload = ({
  type = "text-area",
  askForConfirmation = false,
  allowedFileFormats,
  onConfirmFile,
  maxFileSizeMb = 20,
}: FileUploadProps) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [loaderState, setLoaderState] = useState(FILE_UPLOADER_STATE.INIT);
  const [error, setError] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [files, setFiles] = useState<any[]>([]);
  const [data, setData] = useState<any[]>([]);

  // AllowedFileFormats includes windows excel formats
  const config = {
    allowedFileFormats: allowedFileFormats
      ? allowedFileFormats
      : DEFAULT_FILE_FORMATS,
    maxFileSizeMb: maxFileSizeMb,
    filesLimit: 1,
  };

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const onFileRead = (files: any[], data: any) => {
    setTimeout(() => {
      setLoaderState(FILE_UPLOADER_STATE.PROCESSING);
    }, 500);

    setTimeout(() => {
      setLoaderState(FILE_UPLOADER_STATE.SUCCESS);
      if (askForConfirmation) {
        setFiles(files);
        setData(data);
      } else {
        onConfirmFile(files, data);
      }
    }, 1000);
  };

  const readFile = (files: any[]) => {
    const { isValidFile, errorMessage } = fileValidator(files, config);

    // We don't allow passing files other than CSV
    if (!isValidFile) {
      if (errorMessage) {
        setErrorMessage(errorMessage);
        setError(errorMessage ? true : false);
      }

      return false;
    }

    const reader = new FileReader();
    reader.readAsDataURL(files[0]);

    reader.onload = (loadEvt: any) => {
      onFileRead(files, loadEvt?.target?.result);
    };
  };

  const onUploadFile = () => {
    setError(false);

    var input = document.createElement("input");
    input.type = "file";

    input.onchange = (e: any) => {
      //const files = e?.dataTransfer?.files;
      const files = e.target.files;
      // setting up the reader
      readFile(files);
    };

    input.click();
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div data-test-id="file-upload-container">
      {error && <label className={styles.error}>{errorMessage}</label>}

      <div className={styles.container}>
        <DragAndDropFile
          type={type}
          setError={setError}
          processDrop={readFile}
          config={config}
        >
          {type === "text-area" && (
            <TextAreaDragAndDrop
              loaderState={loaderState}
              data={data}
              files={files}
              askForConfirmation={askForConfirmation}
              setData={setData}
              setLoaderState={setLoaderState}
              onUploadFile={onUploadFile}
              onConfirmFile={onConfirmFile}
            />
          )}

          {type === "input" && <InputDragAndDrop loaderState={loaderState} />}
        </DragAndDropFile>

        {type === "input" && (
          <Button size="medium" onClick={onUploadFile}>
            {t("Upload File")}
            <UploadIcon fill="white" iconClass={"button-icon-right"} />
          </Button>
        )}
      </div>

      {type === "input" && (
        <DataValidation
          data={data}
          files={files}
          loaderState={loaderState}
          askForConfirmation={askForConfirmation}
          setData={setData}
          setLoaderState={setLoaderState}
          onConfirmFile={onConfirmFile}
        />
      )}
    </div>
  );
};

const TextAreaDragAndDrop = ({
  loaderState,
  data,
  files,
  askForConfirmation,
  setData,
  setLoaderState,
  onUploadFile,
  onConfirmFile,
}: any) => {
  const { t } = useTranslation();

  return (
    <div>
      {loaderState === FILE_UPLOADER_STATE.INIT && (
        <div
          data-test-id="file-upload-container"
          className={styles.fileContainer}
        >
          <UploadIcon fill="#868e96" iconClass={styles.uploadIcon} />
          <label>
            {t("Drop File Here to Upload")}
            <br />
            {t("(or)")}
          </label>
          <Button size="large" onClick={onUploadFile}>
            <AttachmentIcon fill="white" iconClass={"button-icon-left"} />
            {t("Choose File from Your Computer")}
          </Button>
        </div>
      )}

      <DataValidation
        data={data}
        files={files}
        loaderState={loaderState}
        askForConfirmation={askForConfirmation}
        setData={setData}
        setLoaderState={setLoaderState}
        onConfirmFile={onConfirmFile}
      />
    </div>
  );
};

const InputDragAndDrop = ({ loaderState }: any) => {
  const { t } = useTranslation();

  if (loaderState !== FILE_UPLOADER_STATE.INIT) {
    return null;
  }

  return (
    <div
      data-test-id="file-upload-container"
      className={styles.inputFileContainer}
    >
      <label>{t("Drag and drop file here")}</label>
      <AttachmentIcon size={"20px"} iconClass={"button-icon-right"} />
    </div>
  );
};

const DataValidation = ({
  files,
  data,
  loaderState,
  askForConfirmation,
  setData,
  setLoaderState,
  onConfirmFile,
}: any) => {
  const { t } = useTranslation();

  return (
    <>
      {loaderState === FILE_UPLOADER_STATE.PROCESSING && (
        <div className={styles.fileContainer}>
          <label>{t("Processing...")}</label>
        </div>
      )}

      {data.length > 0 && loaderState === FILE_UPLOADER_STATE.SUCCESS && (
        <div className={styles.fileContainer}>
          <label>
            {t("File Upload done!")} {files[0]?.name}
          </label>

          {askForConfirmation && (
            <div id="button-wrapper">
              <Button
                theme="secondary"
                variant="outline"
                size="large"
                onClick={() => {
                  setData([]);
                  setLoaderState(FILE_UPLOADER_STATE.INIT);
                }}
              >
                {t("Remove")}
              </Button>
              <Button size="large" onClick={() => onConfirmFile(files, data)}>
                {t("Confirm File")}
              </Button>
            </div>
          )}
        </div>
      )}

      {loaderState === FILE_UPLOADER_STATE.FAILURE && (
        <div className={styles.fileContainer}>
          <label>{t("File Upload failed. Please try again!")}</label>
        </div>
      )}
    </>
  );
};

const DEFAULT_FILE_FORMATS = [
  "csv",
  "text/csv",
  "application/vnd.ms-excel",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
];

const FILE_UPLOADER_STATE = {
  INIT: "INIT",
  PROCESSING: "PROCESSING",
  SUCCESS: "SUCCESS",
  FAILURE: "FAILURE",
};

export default FileUpload;
