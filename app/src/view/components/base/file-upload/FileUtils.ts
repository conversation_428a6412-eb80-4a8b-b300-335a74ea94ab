export const fileValidator = (files: any[], config: any) => {
  let result = {
    isValidFile: false,
    errorMessage: "",
  };

  const { allowedFileFormats, maxFileSizeMb, filesLimit } = config;
  const { length } = files;
  const type = files[0]?.type;
  const size = files[0]?.size;

  // General validation
  if (!size || length === 0) {
    return {
      ...result,
      errorMessage: "An error has occurred, please try again!",
    };
  }

  // File size validation
  if (size / 1024 / 1024 > maxFileSizeMb) {
    return {
      ...result,
      errorMessage: `File size exceeded the limit of ${maxFileSizeMb}MB`,
    };
  }

  // File type validation valiation
  if (!type || !allowedFileFormats.includes(type)) {
    return {
      ...result,
      errorMessage: `Supported file formats are only ${allowedFileFormats?.join(
        ", "
      )}`,
    };
  }

  // Files limit validation
  if (length > filesLimit) {
    const filesLimitErrorMessage =
      filesLimit > 1
        ? `Only ${filesLimit} files are allowed to upload`
        : `Only one file is allowed to upload`;

    return {
      ...result,
      errorMessage: filesLimitErrorMessage,
    };
  }

  // All ok
  result.isValidFile = true;
  result.errorMessage = "";
  return result;
};

export const downloadCsv = (data: string, fileName: string) => {
  var a = document.createElement("a");
  a.href = "data:attachment/csv," + encodeURIComponent(data);
  a.target = "_blank";
  a.download = `${fileName}.csv`;

  document.body.appendChild(a);
  a.click();
};

// TODO: make it safe for big files, today it isnt (up to 1000 rows maybe)
export const csvToArray = (fileAsText: string, delimiter = ",") => {
  // slice from start of text to the first \n index
  // use split to create an array from string by delimiter

  // Normalize the file with \n instead of \r
  const breakNormalizedFile = fileAsText.replace(/\r/g, "\n");

  // Use whatever break we see first, be it \r or \n
  const headerBreaker = breakNormalizedFile.indexOf("\n");

  // Split header row
  const headers = breakNormalizedFile.slice(0, headerBreaker).split(delimiter);

  // Only preview 10 lines
  const MAX_LINES = 10;

  // slice from \n index + 1 to the end of file
  // use split to create an array of each csv value row
  const rows = breakNormalizedFile.slice(headerBreaker + 1).split("\n");

  const previewRows = rows.slice(0, MAX_LINES);

  // Map the rows
  // split values from each row into an array
  // use headers.reduce to create an object
  // object properties derived from headers:values
  // the object passed as an element of the array
  const arr = previewRows.map(function (row) {
    const values = row.split(delimiter);
    const el = headers.reduce((object: any, header: any, index: any) => {
      object[header] = values[index];
      return object;
    }, {});
    return el;
  });

  // return the array
  return arr;
};
