@import "@fourkites/elemental-atoms/build/scss/colors/index";

.elemental-FloatingChip-container {
  display: flex;
  align-items: center;
  width: 90%;

  .elemental-FloatingChip-divider {
    border-bottom: 1px solid $color-neutral-300;
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    border-top: none;
    border-left: none;
    border-right: none;

    &.chip-margin-right {
      margin-right: 1.5%;
    }

    &.chip-margin-left {
      margin-left: 1.5%;
    }
  }

  .elemental-FloatingChip-text {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    outline: 0px;
    font-weight: 600;
    font-size: 16px;
    line-height: 1.75;
    min-height: auto;
    border-radius: 24px;
    min-width: 48px;
    width: auto;
    box-shadow: rgb(0 0 0 / 20%) 0px 3px 5px -1px, rgb(0 0 0 / 14%) 0px 6px 10px 0px,
      rgb(0 0 0 / 12%) 0px 1px 18px 0px;
    color: $color-accent-honey-700;
    background-color: #fff7de;
    border: 2px solid $color-accent-honey-500;

    &.elemental-FloatingChip-small {
      height: 36x;
      padding: 0px 16px;
    }

    &.elemental-FloatingChip-medium {
      padding: 0px 24px;
      height: 40px;
    }

    &.elemental-FloatingChip-large {
      height: 48px;
      padding: 0px 28px;
    }
  }
}
