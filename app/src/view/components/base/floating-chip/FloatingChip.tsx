import React from "react";
import classNames from "classnames";

import FloatingChipProps from "./FloatingChip.types";
import "./FloatingChip.scss";

const FloatingChip = ({ content, size = "medium" }: FloatingChipProps) => {
  const classes = classNames(
    "elemental-FloatingChip-text",
    `elemental-FloatingChip-${size}`
  );

  return (
    <div className="elemental-FloatingChip-container">
      <hr className="elemental-FloatingChip-divider chip-margin-right" />
      <button className={classes} data-testid="floating-chip-button">
        {content}
      </button>
      <hr className="elemental-FloatingChip-divider  chip-margin-left" />
    </div>
  );
};

export default FloatingChip;
