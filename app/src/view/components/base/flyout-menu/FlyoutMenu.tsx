import React, { useRef, useEffect } from "react";

import "./FlyoutMenu.scss";

const FlyoutMenu = ({ anchor, children }: any) => {
  const [isMenuOpen, setMenuOpen] = React.useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: any) => {
      if (
        menuRef.current &&
        !menuRef.current?.contains(event.target) &&
        buttonRef.current &&
        !buttonRef.current?.contains(event.target)
      ) {
        setMenuOpen(false);
      }
    };

    document.addEventListener("click", handleClickOutside, false);

    return () => {
      document.removeEventListener("click", handleClickOutside, false);
    };
  }, [isMenuOpen]);

  return (
    <div ref={menuRef}>
      <button
        data-testid="flyout-menu-button"
        className="flyout-menu-anchor"
        disabled={false}
        ref={buttonRef}
        onClick={() => {
          setMenuOpen(!isMenuOpen);
        }}
      >
        {anchor}
      </button>
      {isMenuOpen && (
        <FlyoutMenuDropdown anchorRef={buttonRef} menuRef={menuRef}>
          {children}
        </FlyoutMenuDropdown>
      )}
    </div>
  );
};

const FlyoutMenuDropdown = ({ children, menuRef, anchorRef }: any) => {
  const flyoutRef = useRef<HTMLDivElement>(null);

  const { x, y } = anchorRef.current.getClientRects().length
    ? anchorRef.current.getClientRects()[0]
    : { x: 100, y: 100 };

  // Assuming height is fixed at 200
  const showOnTop = window.innerHeight - y < 200;

  return (
    <div ref={flyoutRef}>
      <div
        ref={menuRef}
        className={"flyout-menu"}
        style={{
          ...(showOnTop ? { top: y - 36 } : { top: y + 28 }),
          left: x - 180,
        }}
      >
        {children}
      </div>
    </div>
  );
};

export default FlyoutMenu;
