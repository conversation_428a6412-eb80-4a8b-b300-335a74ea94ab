import React, { useState, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";

import {
  Button,
  PlusIcon,
  ChevronDownIcon,
  LockFillIcon,
} from "@fourkites/elemental-atoms";

import styles from "./FlyoutSelector.module.scss";
import FlyoutSelectorProps from "./FlyoutSelector.types";
import { Tooltip } from "@fourkites/elemental-tooltip";

const FlyoutSelector = ({ children, icon }: FlyoutSelectorProps) => {
  const { t } = useTranslation();

  const wrapperRef = useRef(null);
  const isButtonDisabled = !children;

  const [isOutside, setIsOutside] = useState<boolean>(true);

  const addInviteButton = (
    <Button
      theme="tertiary"
      size="medium"
      onClick={() => setIsOutside(false)}
      data-testid="add-or-invite-carrier"
      disabled={isButtonDisabled}
    >
      {icon}
      {t("Add / Invite Carrier")}
      <ChevronDownIcon fill="#0e65e5" iconClass={"button-icon-right"} />
    </Button>
  );

  const contentcomponent = (
    <div>
      {t("Invitation of FTL carriers is only available in USA and Canada")}
    </div>
  );

  const addInviteButtonWithTooltipInfo = (
    <div className={styles.addInviteButtonTooltipInfo}>
      <Tooltip
        placement="bottom"
        contentComponent={contentcomponent}
        theme="dark"
      >
        <span className={styles.tooltipOverlay}></span>
      </Tooltip>
      {addInviteButton}
    </div>
  );

  useEffect(() => {
    const handleClickOutside = (event: any) => {
      // @ts-ignore
      if (wrapperRef?.current && !wrapperRef?.current.contains(event.target)) {
        setIsOutside(true);
      }
    };
    // Bind the event listener
    document.addEventListener("mousedown", handleClickOutside);
    // Unbind the event listener on clean up
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [wrapperRef]);

  return (
    <div
      data-testid="flyout-selector-add-invite-button"
      ref={wrapperRef}
      className={styles.container}
    >
      {isButtonDisabled ? addInviteButtonWithTooltipInfo : addInviteButton}
      {!isOutside && <div>{children}</div>}
    </div>
  );
};
export default FlyoutSelector;
