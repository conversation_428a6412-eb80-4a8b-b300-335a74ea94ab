import React from "react";
import { useTranslation } from "react-i18next";

import { Tooltip } from "@fourkites/elemental-tooltip";

import premierCarrierIcon from "assets/img/premierCarrierIcon.svg";
import sustainableCarrierIcon from "assets/img/sustainableCarrierIcon.svg";

import styles from "./HighlightsIndicator.module.scss";

const HighlightsIndicator = ({ highlights }: any) => {
  const { t } = useTranslation();

  const showPremierCarrier = !!highlights?.is_premier_carrier;
  const showSustainableCarrier = !!highlights?.is_sustainable_carrier;

  return (
    <>
      {showPremierCarrier && (
        <Tooltip text={t("Premier Carrier")}>
          <span className={styles.premierWrapper}>
            <img src={premierCarrierIcon} className={styles.premierIcon} />
            {t("PREMIER")}
          </span>
        </Tooltip>
      )}

      {showSustainableCarrier && (
        <Tooltip text={t("Smartway Partner")}>
          <span>
            <img
              src={sustainableCarrierIcon}
              className={styles.sustainableIcon}
            />
          </span>
        </Tooltip>
      )}
    </>
  );
};

export default HighlightsIndicator;
