@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.container{
  > div {
    display: flex;
    flex-direction: column;
    width: 580px;
  }
}

.header {
  padding: 0px 24px 24px 24px;
}

.headerRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24px;

  > button {
    display: flex;
    align-content: center;
    align-items: center;
    justify-content: center;
  }

  > div[id=integration-status] {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  > div > label[id=integration-status] {
    font: $typography-standard-bold;
    color: $color-accent-mint-500;
    margin-left: 8px;
  }

  > label[id="next-sync"] {
    color: $color-neutral-700;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 28px;
    text-align: right;
  }
}

.title {
  color: $color-neutral-900;
  font-size: 22px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 24px;
}

.divider {
  display: block;
  height: 1px;
  width: 100%;
  background-color: $color-neutral-300;
}

.content {
  display: flex;
  padding: 0px 24px 24px 24px;
}

.contentColumn {
  display: flex;
  width:100%;
  flex-direction: column;

  > label[id="description"] {
    margin-top: 24px;
    color: $color-neutral-700;
    font-size: 16px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 28px;
  }

  > label[id="value"] {
    color: $color-neutral-700;
    font-size: 18px;
    font-weight: 900;
    letter-spacing: 0;
    line-height: 28px;
  }
}
