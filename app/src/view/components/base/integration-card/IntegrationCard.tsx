import React, { Fragment } from "react";
import { useTranslation } from "react-i18next";

import { Button, Edit1Icon, CheckCircleIcon } from "@fourkites/elemental-atoms";

import Card from "view/components/base/card/Card";

import styles from "./IntegrationCard.module.scss";
import IntegrationCardProps from "./IntegrationCard.types";

const IntegrationCard = ({}: IntegrationCardProps) => {
  const { t } = useTranslation();

  return (
    <div className={styles.container}>
      <Card>
        <div>
          <div className={styles.header}>
            <div className={styles.headerRow}>
              <label className={styles.title}>
                Integration without mode on name
              </label>
              <Button size="small" variant="flat">
                <Edit1Icon fill="#0e65e5" iconClass={"button-icon-left"} />
                {t("Edit")}
              </Button>
            </div>
            <div className={styles.headerRow}>
              <div id="integration-status">
                <CheckCircleIcon fill="#24A148" />
                <label id="integration-status">{t("Operational")}</label>
              </div>
              <label id="next-sync">{t("Next sync in 15 min")}</label>
            </div>
          </div>

          <div className={styles.divider} />

          <div className={styles.content}>
            <div className={styles.contentColumn}>
              <label id="description">{t("Server URL")}</label>
              <label id="value">sftp.fourkites.com</label>
              <label id="description">{t("Source Directory")}</label>
              <label id="value">/sandoz_inc</label>
            </div>
            <div className={styles.contentColumn}>
              <label id="description">{t("File Format")}</label>
              <label id="value">CSV</label>
              <label id="description">{t("File Template")}</label>
              <label id="value">Loads-Tender</label>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default IntegrationCard;
