import React from "react";

import {
  MonitorIcon,
  SmartphoneIcon,
  MailIcon,
  FileTextIcon,
  LocateIcon,
  CheckIcon,
  XCircleInvertedIcon,
  AlertTriangleIcon,
  CommandIcon,
  ClipboardIcon,
  FileEmptyIcon,
} from "@fourkites/elemental-atoms";
import { Tooltip } from "@fourkites/elemental-tooltip";

import "./IntegrationStatus.scss";

import IntegrationStatusProps from "./IntegrationStatus.types";

const IntegrationStatus = ({
  type,
  status,
  showStatus = true,
}: IntegrationStatusProps) => {
  const Icon = getIntegrationIcon(type);

  return (
    <span
      data-test-id="integration-container"
      className="integration-container"
    >
      <label data-test-id="integration-label" className="integration-label">
        {getIntegrationName(type)}
      </label>
      <span data-test-id="integration-icon" className="integration-icon">
        <Icon size={"18px"} />
      </span>
      {showStatus && status && <IntegrationStatusTag status={status} />}
    </span>
  );
};

const IntegrationStatusTag = ({ status }: any) => {
  const allIntegrationStatus = {
    connected: {
      icon: <CheckIcon fill="#24A148" />,
      description: "Integration is working",
    },
    pending: {
      icon: <AlertTriangleIcon fill="#FD8B32" />,
      description: "Integration setup in progress",
    },
    error: {
      icon: <XCircleInvertedIcon fill="#FE633D" />,
      description: "Integration has error",
    },
  };

  //@ts-ignore
  const integrationStatus = allIntegrationStatus[status];

  return (
    <Tooltip placement="bottom" text={integrationStatus.description}>
      <span>{integrationStatus.icon}</span>
    </Tooltip>
  );
};

const getIntegrationIcon = (type: string) => {
  let Icon;

  switch (type) {
    case "api":
      Icon = MonitorIcon;
      break;
    case "eld_gps":
      Icon = LocateIcon;
      break;
    case "file":
      Icon = FileTextIcon;
      break;
    case "email":
      Icon = MailIcon;
      break;
    case "mobile":
      Icon = SmartphoneIcon;
      break;
    case "automated":
      Icon = CommandIcon;
      break;
    case "manual":
      Icon = ClipboardIcon;
      break;
    case "other":
      Icon = FileEmptyIcon;
      break;
    default:
      Icon = MonitorIcon;
      break;
  }

  return Icon;
};

const getIntegrationName = (type: string) => {
  let name;

  switch (type) {
    case "api":
      name = "API";
      break;
    case "eld_gps":
      name = "GPS";
      break;
    case "file":
      name = "File";
      break;
    case "email":
      name = "Email";
      break;
    case "mobile":
      name = "Mobile";
      break;
    case "automated":
      name = "Automated";
      break;
    case "manual":
      name = "Manual";
      break;
    case "other":
      name = "Other";
      break;
    default:
      name = "--";
      break;
  }

  return name;
};

export default IntegrationStatus;
