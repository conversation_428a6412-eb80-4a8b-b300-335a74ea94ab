@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.elemental-vinput-container {
  display: flex;
  flex-direction: column;
  align-items: left;
  align-content: left;
  justify-items: left;
}

.elemental-multi-input-input-wrapper {
  z-index: 2;
  background-color: white;
  border-radius: 4px;
}

.elemental-multi-input-content {
  display: flex;
  align-content: center;
  flex-direction: column;
}

.elemental-multi-input-input {
  display: flex;
  align-items: center;

  &.required {
    border: 1px solid $color-accent-cherry-500;
    border-radius: 4px;
    width: fit-content;

    .elemental-Input-border {
      border: none;
      border-right: 1px solid $color-neutral-500;
    }

    .elemental-multi-input-button {
      border: none;
    }

    .elemental-multi-input-button.checkable:disabled {
      border: none;
    }
  }
}

.elemental-multi-input-button {
  height: 30px;
  margin-left: 16px;

  &.checkable {
    z-index: 3;
    cursor: pointer;
    width: 36px;
    background-color: white;
    margin-left: -2px;
    border: 1px solid #0e65e5;
    border-radius: 0px 4px 4px 0px;

    &:disabled {
      z-index: 1;
      border: 1px solid #adb5bd;
    }
  }
}

.elemental-multi-input-values-container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 8px;

  &.small {
    margin-top: 0;
  }
}

.elemental-multi-input-value {
  display: flex;
  padding: 6px 12px 6px 12px;
  margin-top: 8px;
  margin-right: 8px;
  border-radius: 16px;
  background-color: $color-neutral-200;
  color: $color-neutral-900;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0;
  line-height: 17px;

  &.default {
    background-color: $color-neutral-600;
  }
}

.elemental-multi-input-remove-icon {
  margin-left: 16px;
  height: 15px;
  width: 15px;
  fill: $color-neutral-700;
  cursor: pointer;

  &:hover {
    color: $color-neutral-400;
  }
}

.buttonIcon {
  margin-right: 8px;
  position: relative;
  top: 2px;
}

.elemental-input-label {
  font-size: 14px;
}

.elemental-multi-input-requiredIndicator {
  color: $color-accent-cherry-500;
}

.elemental-multi-input-errorLabel {
  display: flex;
  align-items: center;
  color: $color-accent-cherry-500;
  font-size: 14px;
}

.elemental-multi-input-value-wrapper {
  display: flex;
  flex-wrap: wrap;
}

.noActionIndicator {
  pointer-events: none;
}

.elemental-multi-input-value-tooltip-message {
  background-color: $color-neutral-900;
  box-shadow: 0 5px 10px 5px rgba(0, 0, 0, 0.509);
}

.elemental-multi-input-value-tooltip-content {
  display: flex;
  align-content: center;
  align-items: center;
}
