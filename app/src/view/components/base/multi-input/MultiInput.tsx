import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import classNames from "classnames";

import {
  Button,
  XCircleInvertedIcon,
  CheckIcon,
  PlusIcon,
} from "@fourkites/elemental-atoms";
import { Input } from "@fourkites/elemental-input";
import { Tooltip } from "@fourkites/elemental-tooltip";

import "./MultiInput.scss";
import MultiInputProps, { MultiInputValidationProps } from "./MultiInput.types";

const MultiInput = ({
  values,
  onAddValue,
  onRemoveValue,
  defaultValues,
  label,
  placeholder,
  disabled,
  checkable = false,
  size = "large",
  required = false,
  errorLabel = "",
  validation,
}: MultiInputProps) => {
  const valuesContainerClass = classNames(
    "elemental-multi-input-values-container",
    {
      small: size === "small",
    }
  );

  const multiInputContainerClass = classNames("elemental-multi-input-input", {
    required: required,
  });

  const [currentValue, setCurrentValue] = useState<string>("");

  const onChangeCurrentValue = (e: any) => {
    setCurrentValue(e.target.value);
  };

  const onRemoveAnyValue = (value: string) => {
    if (disabled) {
      return;
    }
    onRemoveValue(value);
  };

  return (
    <div className={"elemental-multi-input-container"}>
      {label && (
        <span
          className={"elemental-input-label"}
          data-testid="elemental-multi-input-label"
        >
          {label}
        </span>
      )}
      {required && (
        <span className="elemental-multi-input-requiredIndicator"> *</span>
      )}
      <span className={"elemental-multi-input-content"}>
        <div className={multiInputContainerClass}>
          <div className={"elemental-multi-input-input-wrapper"}>
            <Input
              label=""
              size={size}
              value={currentValue}
              onChange={onChangeCurrentValue}
              placeholder={placeholder}
              disabled={disabled}
            />
          </div>

          <MultiInputAdditions
            checkable={checkable}
            values={values}
            disabled={disabled}
            defaultValues={defaultValues}
            onAddValue={onAddValue}
            size={size}
            validation={validation}
            currentValue={currentValue}
            setCurrentValue={setCurrentValue}
          />
        </div>
        {required && (
          <label className="elemental-multi-input-errorLabel">
            {errorLabel ? errorLabel : "This field is required"}
          </label>
        )}

        <div data-testid="multi-input-list" className={valuesContainerClass}>
          {defaultValues!.map((v, idx) => (
            <span
              key={idx}
              data-test-id="elemental-multi-input-default-value"
              className={"elemental-multi-input-value default"}
            >
              {v}
            </span>
          ))}
          <MultiInputValues
            values={values}
            onRemoveAnyValue={onRemoveAnyValue}
            disabled={disabled}
            validation={validation}
            defaultValues={defaultValues}
          />
        </div>
      </span>
    </div>
  );
};

export default MultiInput;

const MultiInputAdditions = ({
  checkable,
  values,
  disabled,
  defaultValues,
  onAddValue,
  size,
  validation,
  currentValue,
  setCurrentValue,
}: any) => {
  const { t } = useTranslation();

  const addButtonClass = classNames("elemental-multi-input-button", {
    checkable: checkable,
  });

  const {
    maxNumberOfValues,
    maxValueLength,
    maxNumberOfValuesMessageComponent,
    allowRepeated = false,
  }: MultiInputValidationProps = validation;

  const canAddValue = () => {
    const passAllUnique =
      allowRepeated ||
      values
        .concat(defaultValues || [])
        .find((v: any) => v.toLowerCase() === currentValue.toLowerCase()) ==
        null;

    return (
      currentValue &&
      currentValue?.length <= maxValueLength! &&
      values?.length < maxNumberOfValues! &&
      passAllUnique
    );
  };

  const hasReachedMaxValue = values?.length === maxNumberOfValues;

  const multiInputAddition = (
    <span>
      {checkable ? (
        <button
          className={addButtonClass}
          onClick={() => {
            onAddValue(currentValue);
            setCurrentValue("");
          }}
          disabled={disabled || !canAddValue()}
        >
          <CheckIcon
            fill={disabled || !canAddValue() ? "grey" : "#0e65e5"}
            iconClass="elemental-multi-input-add-icon"
          />
        </button>
      ) : (
        <Button
          className={addButtonClass}
          size={size}
          onClick={() => {
            onAddValue(currentValue);
            setCurrentValue("");
          }}
          disabled={disabled || !canAddValue()}
        >
          <PlusIcon
            className="buttonIcon"
            fill="white"
            iconClass={"button-icon-left"}
          />
          {t("Add")}
        </Button>
      )}
    </span>
  );
  return (
    <>
      {hasReachedMaxValue && maxNumberOfValuesMessageComponent ? (
        <Tooltip
          placement="bottom"
          className={"elemental-multi-input-value-tooltip-message"}
          contentComponent={maxNumberOfValuesMessageComponent}
          theme="dark"
        >
          {multiInputAddition}
        </Tooltip>
      ) : (
        <>{multiInputAddition}</>
      )}
    </>
  );
};

const MultiInputValues = ({
  values,
  onRemoveAnyValue,
  disabled,
  validation,
  defaultValues,
}: any) => {
  const { t } = useTranslation();
  const {
    minNumberOfValues,
    minNumberOfValuesMessageComponent,
  }: MultiInputValidationProps = validation;

  const hasOnlyOneValue = values?.length === minNumberOfValues;

  const multiInputValueClass = classNames("elemental-multi-input-value", {
    noActionIndicator: hasOnlyOneValue,
  });

  const multiInputValues = (
    <span className={"elemental-multi-input-value-wrapper"}>
      {defaultValues!.map((v: any, idx: any) => (
        <span
          key={idx}
          data-test-id="elemental-multi-input-default-value"
          className={"elemental-multi-input-value default"}
        >
          {v}
        </span>
      ))}
      {values.map((v: any, idx: any) => (
        <span
          key={idx}
          data-test-id="elemental-multi-input-value"
          className={multiInputValueClass}
        >
          {v}
          <span
            data-test-id="remove-item-button"
            onClick={() => onRemoveAnyValue(v)}
          >
            <XCircleInvertedIcon
              fill={disabled || hasOnlyOneValue ? "#cfd4da" : "#868e96"}
              iconClass="elemental-multi-input-remove-icon"
            />
          </span>
        </span>
      ))}
    </span>
  );

  return (
    <>
      {hasOnlyOneValue && minNumberOfValuesMessageComponent ? (
        <Tooltip
          placement="bottom"
          className={"elemental-multi-input-value-tooltip-message"}
          contentComponent={minNumberOfValuesMessageComponent}
          theme="dark"
        >
          {multiInputValues}
        </Tooltip>
      ) : (
        <>{multiInputValues}</>
      )}
    </>
  );
};
