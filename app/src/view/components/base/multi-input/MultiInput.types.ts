export interface MultiInputValidationProps {
  maxNumberOfValues?: number;
  minNumberOfValues?: number;
  allowRepeated?: boolean;
  maxValueLength?: number;
  maxNumberOfValuesMessageComponent?: any;
  minNumberOfValuesMessageComponent?: any;
}

interface MultiInputProps {
  values: string[];
  onAddValue: Function;
  onRemoveValue: Function;
  defaultValues?: string[];
  checkable?: boolean;
  label?: string;
  placeholder?: string;
  disabled?: boolean;
  size?: "small" | "medium" | "large";
  required?: boolean;
  errorLabel?: string;
  validation?: MultiInputValidationProps;
}

export default MultiInputProps;
