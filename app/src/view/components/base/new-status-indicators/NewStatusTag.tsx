import React from "react";
import classNames from "classnames";

import { Tooltip } from "@fourkites/elemental-tooltip";

import "./NewStatusTag.scss";

import NewStatusLabelProps from "./NewStatusTag.types";

const NewStatusTag = ({
  variant,
  statusCode,
  statusIcon,
  tooltipText,
  statusText,
}: NewStatusLabelProps) => {
  const className = classNames("elemental-status-tag", {
    "status-ok": variant === "ok",
    "status-error": variant === "error",
    "status-alert": variant === "alert",
  });

  const statusDisplay = statusCode !== 0 && statusCode !== 1
    ? `${statusCode} ${statusText}`
    : `${statusText}`;

  const statusContent = (
    <span data-test-id="new-status-tag" className={className}>
      <span className="status-icon">{statusIcon}</span>
      <span>{statusDisplay}</span>
    </span>
  );

  const component = tooltipText ? (
    <Tooltip text={tooltipText} placement={"bottom"}>
      {statusContent}
    </Tooltip>
  ) : (
    statusContent
  );
  return component;
};

export default NewStatusTag;
