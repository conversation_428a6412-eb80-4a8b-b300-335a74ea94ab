import React from "react";

import ProgressBarProps from "./ProgressBar.types";
import "./ProgressBar.scss";

const ProgressBar = ({ progress }: ProgressBarProps) => {
  let coarsedProgress = progress > 100 ? 100 : progress;
  coarsedProgress = Math.round(coarsedProgress);

  return (
    <div className="elemental-ProgressBar-container">
      <div className={"elemental-ProgressBar"}>
        <div
          className={"elemental-ProgressBar progress"}
          style={{ width: 4 * coarsedProgress }}
        />
      </div>
      <label className="elemental-ProgressBar-text">{`${coarsedProgress}%`}</label>
    </div>
  );
};

export default ProgressBar;
