import React, { useRef, useCallback } from "react";

import SignatureCanvas from "react-signature-canvas";

import { XCircleIcon } from "@fourkites/elemental-atoms";

import "./SignatureForm.scss";

const SignatureForm = ({
  onSaveSignature,
  signatureResult,
  setSignatureResult,
}: any) => {
  const sigCanvas = useRef();
  const sigPad = useRef();

  const clearInput = () => {
    //@ts-ignore
    sigPad.current?.clear();
    setSignatureResult("");
  };

  const setSignatureOnChange = () => {
    //@ts-ignore
    const dataURL = sigCanvas.current?.toDataURL();
    setSignatureResult(dataURL);
  };

  const saveInput = () => {
    onSaveSignature(signatureResult);
  };

  const measuredRef = useCallback((node: any) => {
    const resizeCanvas = (signaturePad: any, canvas: any) => {
      canvas.width = canvas.parentElement.clientWidth;
      canvas.height = 200; //canvas.parentElement.clientWidth;
      signaturePad.clear();
    };

    if (node !== null) {
      sigCanvas.current = node.getCanvas();
      sigPad.current = node.getSignaturePad();
      resizeCanvas(node.getSignaturePad(), node.getCanvas());
    }
  }, []);

  return (
    <div className={"elemental-SignatureForm-container"}>
      <button className={"iconContainer"} onClick={clearInput}>
        <XCircleIcon />
      </button>
      <div className={"elemental-SignatureForm-content"}>
        <SignatureCanvas
          ref={measuredRef}
          penColor="black"
          canvasProps={{ width: 500, height: 200, className: "sigCanvas" }}
          onEnd={setSignatureOnChange}
        />
      </div>
    </div>
  );
};

export default SignatureForm;
