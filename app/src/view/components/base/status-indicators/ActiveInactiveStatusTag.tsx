import React from "react";
import classNames from "classnames";

import "./ActiveInactiveStatusTag.scss";

import ActiveInactiveStatusTagProps from "./ActiveInactiveStatusTag.types";

const ActiveInactiveStatusTag = ({
  variant,
  status,
}: ActiveInactiveStatusTagProps) => {
  const wrapperClasses = classNames("active-inactive-status-tag", {
    "status-active": variant === "active",
    "status-inactive": variant === "inactive",
  });

  const statusIndicatorClasses = classNames("status-indicator", {
    "status-indicator-active": variant === "active",
    "status-indicator-inactive": variant === "inactive",
  });

  return (
    <span data-test-id="active-inactive-status-tag" className={wrapperClasses}>
      <span className={statusIndicatorClasses}></span>
      <span>{status}</span>
    </span>
  );
};

export default ActiveInactiveStatusTag;
