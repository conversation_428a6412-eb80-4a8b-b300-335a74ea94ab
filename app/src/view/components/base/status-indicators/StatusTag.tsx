import React from "react";
import classNames from "classnames";

import "./StatusTag.scss";

import StatusLabelProps from "./StatusTag.types";

const StatusTag = ({ variant, label }: StatusLabelProps) => {
  const className = classNames("elemental-status-tag", {
    "status-ok": variant === "ok",
    "status-alert": variant === "alert",
    "status-error": variant === "error",
    "status-info": variant === "info",
  });

  return (
    <span data-testid="status-tag" className={className}>
      {label}
    </span>
  );
};

export default StatusTag;
