@import "@fourkites/elemental-atoms/build/scss/colors/index";


.react-tabs__tab {
  color: $color-neutral-900;
  border: none;

  &:hover {
    background-color: $color-neutral-200;
    border-radius: 4px;
    border-bottom: none;
  }

  > div > span > svg > g > g {
    height: 24px;
    width: 24px;
    fill: $color-neutral-900;
  }
}

.react-tabs__tab.react-tabs__tab--selected {
  color: $color-primary-500;
  border: 1px solid #CFD4DA;
  border-bottom: none;

  &:hover {
    background-color: white;
  }

  > div > span > svg > g > g {
    height: 24px;
    width: 24px;
    fill: $color-primary-500;
  }
}

.tabbed-container {
  height: 100%;
  width: 100%;
}

.tab-container {
  display: flex;
  font-weight: bold;
  justify-content: center;
  padding: 6px 24px 3px 24px;
  cursor: pointer;

  > label {
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 19px;
    cursor: pointer;
  }

  > span {
    margin-right: 8px;
  }
}

.tab-list-container {
  background-color: $color-neutral-50;
  border-bottom: 1px solid #CFD4DA;
  margin: 0 0 10px;
  padding-left: 7.5px;
}
