import React from "react";
import { Tab, Tabs, Tab<PERSON><PERSON>, TabPanel } from "react-tabs";

import "react-tabs/style/react-tabs.css";

import "./TabbedContainer.scss";

interface TabbedContainerProps {
  tabs: any;
  selectedTab: string;
  onSelectTab: Function;
}
const TabbedContainer = ({ tabs, onSelectTab }: TabbedContainerProps) => {
  return (
    <div className={"tabbed-container"}>
      <Tabs>
        <TabList className={"tab-list-container"}>
          {tabs?.map((tab: any) => (
            <Tab key={tab.id} onClick={() => onSelectTab(tab.id)}>
              <div data-testid={tab.id} className={"tab-container"}>
                {tab.icon && <span>{tab.icon}</span>}
                <label data-test-id="tabbed-container-title">{tab.title}</label>
              </div>
            </Tab>
          ))}
        </TabList>

        {tabs?.map((tab: any) => (
          <TabPanel key={tab.id}>{tab.tabComponent}</TabPanel>
        ))}
      </Tabs>
    </div>
  );
};

export default TabbedContainer;
