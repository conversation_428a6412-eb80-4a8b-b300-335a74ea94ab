@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.elemental-toast-container {
  position: absolute;
  top: 16px;
  right: 16px;
  padding: 16px;
  box-sizing: border-box;
  width: 400px;
  border-radius: 4px;
  background-color: $color-neutral-00;
  box-shadow: 0 8px 16px 5px rgba(0, 0, 0, 0.18);
  display: flex;
  align-content: center;
  z-index: 100000000;

  &-ok {
    @extend .elemental-toast-container;
  }

  &-error {
    @extend .elemental-toast-container;
  }
}

.elemental-toast-icon {
  width: 27px;
  height: 27px;
}

.elemental-toast-message {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 300px;
  align-items: left;
  align-content: center;
  margin-left: 16px;
  color: $color-neutral-900;
  font: $typography-standard-regular;
  line-height: 24px;
}

.elemental-toast-close {
  cursor: pointer;
  align-content: "flex-end";
  height: 24px;
  width: 24px;
}

.elemental-toast-title {
  color: #21252a;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0;
  line-height: 24px;
}

.elemental-toast-timestamp {
  color: #868e96;
  font-size: 14px;
  letter-spacing: 0;
  line-height: 21px;
}
