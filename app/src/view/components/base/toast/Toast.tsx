import React, { useState, useEffect } from "react";
import {
  XIcon,
  AlertTriangleIcon,
  CheckCircleIcon,
} from "@fourkites/elemental-atoms";

import { toast } from "react-toastify";

import "./Toast.scss";
import ToastProps from "./Toast.types";

let closeTimeout: number;

const Toast = ({ type, title, message, closeAfter, nonce }: ToastProps) => {
  const [show, setShow] = useState<boolean>(false);

  useEffect(() => {
    if (nonce === 0) {
      return;
    }

    setShow(true);
  }, [nonce]);

  const typeIconSize = "26.67px";
  const xIconSize = "24px";
  const icons = {
    ok: <CheckCircleIcon fill="#24A148" size={typeIconSize} />,
    error: <AlertTriangleIcon fill="#da1e28" size={typeIconSize} />,
  };

  const onClose = () => {
    setShow(false);
    if (closeTimeout) clearTimeout(closeTimeout);
  };

  if (closeTimeout) clearTimeout(closeTimeout);
  if (closeAfter) closeTimeout = window.setTimeout(onClose, closeAfter * 1000);

  return show ? (
    <div className={`elemental-toast-container-${type}`}>
      <div className={"elemental-toast-icon"}>{icons[type]}</div>
      <div data-test-id="toast-message" className={"elemental-toast-message"}>
        {title && (
          <label
            data-test-id="toast-message-title"
            className={"elemental-toast-title"}
          >
            {title}
          </label>
        )}
        <label data-test-id="toast-message-message">{message}</label>
        <label className={"elemental-toast-timestamp"}>Just now</label>
      </div>
      <div onClick={onClose} className={"elemental-toast-close"}>
        <XIcon size={xIconSize} />
      </div>
    </div>
  ) : (
    <></>
  );
};

export const showToast = (
  title: string,
  message: string,
  type: "ok" | "error"
) => {
  toast(
    <Toast
      type={type}
      title={title}
      message={message}
      nonce={1}
      closeAfter={3}
    />,
    {
      closeButton: false,
      autoClose: 3000,
    }
  );
};

export default Toast;
