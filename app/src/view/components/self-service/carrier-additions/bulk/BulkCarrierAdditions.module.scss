@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.header {
  display: flex;
  justify-content: space-between;
  padding-left: 10%;
  padding-right: 10%;
  padding-top: 24px;

  > div[id="title-container"] {
    display: flex;
    flex-direction: column;
    padding: 0;

    > h5 {
      margin: 0;
      font-size: 24px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 36px;
      margin-block-start: 0;
      margin-block-end: 0;
      color: color-neutral-900;
    }

    > span {
      font-size: 14px;
      letter-spacing: 0;
      color: $color-neutral-900;
      font-size: 18px;
      line-height: 27px;
    }
  }

  > div[id="right"] {
    display: flex;
    align-content: center;
    align-items: center;

    > button {
      margin-right: 16px;
    }

    > button[id="close"] {
      margin-left: 0;
      cursor: pointer;
      display: flex;
      align-items: center;
      background: none;
      border: none;

      &:hover svg {
        border-radius: 5px;
        background: $color-neutral-200;
      }
    }
  }
}

.closeIcon {
  width: 40px;
  height: 40px;
}

.subHeader {
  margin-top: 16px;
  padding-left: 10%;
  padding-right: 10%;
  margin-top: 24px;

  > div {
    display: flex;
    align-content: center;
    align-items: center;
    justify-content: space-between;
    width: 1200px;

    > div[id="content"] {
      display: flex;
      align-content: center;
      align-items: center;

      > div {
        display: flex;
        align-content: center;
        align-items: center;
      }

      > div[id="content-divider"] {
        height: 2px;
        width: 80px;
        background-color: #dee2e6;
        margin-left: 16px;
        margin-right: 16px;
      }
    }

    > div[id="file"] {
      > label[id="file-label"] {
        color: $color-neutral-600;
        font-size: 18px;
        font-weight: 900;
        letter-spacing: 0;
        line-height: 22px;
      }

      > label[id="file-name"] {
        border-radius: 4px;
        background-color: $color-neutral-100;
        color: $color-neutral-900;
        font-size: 20px;
        font-weight: 500;
        letter-spacing: 0;
        line-height: 24px;
        text-decoration: underline;
        padding: 8px;
        padding-left: 16px;
        padding-right: 16px;
        margin-right: 16px;
        margin-left: 16px;
      }
    }
  }
}

.oval {
  display: flex;
  align-content: center;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  height: 32px;
  width: 32px;
  margin-right: 8px;
  color: $color-neutral-600;
  border: 2px solid $color-neutral-400;
  border-radius: 16px;
  font-size: 16px;
  font-weight: bold;
  letter-spacing: 0;
  line-height: 19px;
}

.ovalSelected {
  composes: oval;
  background-color: $color-primary-100;
  border: 2px solid $color-primary-500;
  color: $color-primary-500;
}

.headerDescription {
  color: $color-neutral-600;
  font-size: 14px;
  font-weight: bold;
  letter-spacing: 0;
  line-height: 21px;
}

.headerDescriptionSelected {
  composes: headerDescription;
  color: $color-primary-500;
}

.divider {
  display: block;
  height: 1px;
  width: 1200px;
  background-color: $color-neutral-300;
  margin-top: 16px;
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  align-content: center;
  width: 100%;
}
