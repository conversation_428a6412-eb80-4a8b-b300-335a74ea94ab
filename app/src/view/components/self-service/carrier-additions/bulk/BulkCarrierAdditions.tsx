import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

import { Button, XIcon, RefreshCcwIcon } from "@fourkites/elemental-atoms";
import { Modal } from "@fourkites/elemental-modal";

import { useAppSelector, useAppDispatch } from "state/hooks";
import { CarrierAdditionsState } from "state/modules/shipper/CarrierAdditions";

import BulkFileSelection from "./file-selection/BulkFileSelection";
import BulkResultsValidation from "./results-validation/BulkResultsValidation";

import AdditionsHandler from "../handler/AdditionsHandler";
import CarrierAdditionsFooter from "../footer/CarrierAdditionsFooter";

import styles from "./BulkCarrierAdditions.module.scss";
import BulkCarrierAdditionsProps from "./BulkCarrierAdditions.types";

const BulkCarrierAdditions = ({
  managerCompanyId,
  mode,
  onDownloadBulkTemplate,
  show,
  onClose,
}: BulkCarrierAdditionsProps) => {
  const { t } = useTranslation();

  const dispatch = useAppDispatch();
  const results = useAppSelector(
    CarrierAdditionsState.selectors.carrierBulkResultsByMode(mode)
  );

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [isAddingCarriers, setIsAddingCarriers] = useState<boolean>(false);
  const [isDataProcessed, setIsDataProcessed] = useState<boolean>(false);
  const [fileName, setFileName] = useState<string>("");
  const [localCarrierResults, setLocalCarrierResults] = useState<any>({});

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  /*
   * Updates the state to work with the results we got, and edit them
   */
  useEffect(() => {
    setLocalCarrierResults({
      carriersOnPlatform: results.carriersOnPlatform,
      newCarriers: results.newCarriers,
      carriersWithErrors: results.carriersWithErrors,
    });
  }, [results]);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  /*
   * Clear state when closing component
   */
  const onCloseBulkCarrierAdditions = () => {
    // Clear redux state
    dispatch(CarrierAdditionsState.actions.clearBulk(mode));
    dispatch(CarrierAdditionsState.actions.clearAdditions(mode));

    // Clear local state
    setIsDataProcessed(false);
    setIsAddingCarriers(false);
    setFileName("");

    onClose();
  };

  /*
   * Goes to second step when list is processed
   */
  const onBulkListProcessed = (bulkListName: string) => {
    setFileName(bulkListName);
    setIsDataProcessed(true);
  };

  /*
   * Goes to step of carrier additions
   */
  const onFooterConnect = () => {
    setIsAddingCarriers(true);
  };

  const onUpdateCarrierResults = (updatedCarrierResults: any) => {
    setLocalCarrierResults(updatedCarrierResults);
  };

  const onChangeFile = () => {
    setIsDataProcessed(false);
    setIsAddingCarriers(false);
    setFileName("");
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  // TODO: Will we really invite error carriers?
  const carriersToAdd = localCarrierResults?.carriersOnPlatform || [];
  const carriersToInvite = [
    ...(localCarrierResults?.newCarriers || []),
    // TODO: ignoring errors for now
    /*...(localCarrierResults?.carriersWithErrors?.filter(
      (carrier: any) => !carrier?.error?.message?.includes("Duplicate")
    ) || []),*/
  ];

  return (
    <Modal
      size="full"
      title={t("Connect to your FTL carriers")}
      subtitle={t("Find your full truckload carriers and get connected.")}
      show={show}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: onCloseBulkCarrierAdditions,
      }}
      customHeader={
        <CustomHeader
          fileName={fileName}
          isDataProcessed={isDataProcessed}
          isAddingCarriers={isAddingCarriers}
          onChangeFile={onChangeFile}
          onCloseBulkCarrierAdditions={onCloseBulkCarrierAdditions}
        />
      }
      customFooter={
        isAddingCarriers || isDataProcessed ? (
          <CarrierAdditionsFooter
            additionsNumber={carriersToAdd.length}
            invitationsNumber={carriersToInvite.length}
            isConnecting={isAddingCarriers}
            onConnect={onFooterConnect}
          />
        ) : (
          <></>
        )
      }
    >
      <div className={styles.container}>
        {!isAddingCarriers ? (
          <>
            {isDataProcessed ? (
              <BulkResultsValidation
                carrierResults={results}
                onUpdateCarrierResults={onUpdateCarrierResults}
              />
            ) : (
              <BulkFileSelection
                managerCompanyId={managerCompanyId}
                mode={mode}
                onDownloadBulkTemplate={onDownloadBulkTemplate}
                onBulkListProcessed={onBulkListProcessed}
              />
            )}
          </>
        ) : (
          <AdditionsHandler
            managerCompanyId={managerCompanyId}
            mode={mode}
            carriersToAdd={carriersToAdd}
            carriersToInvite={carriersToInvite}
            onHandlerComplete={onCloseBulkCarrierAdditions}
            source={"bulk-csv"}
          />
        )}
      </div>
    </Modal>
  );
};

const CustomHeader = ({
  fileName,
  onChangeFile,
  isDataProcessed,
  isAddingCarriers,
  onCloseBulkCarrierAdditions,
}: any) => {
  const { t } = useTranslation();

  return (
    <div data-test-id="carrier-additions-bulk-upload-handler-header">
      <div className={styles.header}>
        <div
          data-test-id="carrier-additions-bulk-upload-title-container"
          id="title-container"
        >
          <h5 data-test-id="carrier-additions-bulk-upload-title">
            {t("Connect to your FTL carriers")}
          </h5>
          <span data-test-id="carrier-additions-bulk-upload-meassage">
            {t("Upload a list of your full truckload carriers and get connected.")}
          </span>
        </div>

        <div id="right">
          <button
            id="close"
            data-test-id="carrier-additions-bulk-upload-cancel-button"
            onClick={onCloseBulkCarrierAdditions}
          >
            <XIcon iconClass={styles.closeIcon} />
          </button>
        </div>
      </div>

      <div className={styles.subHeader}>
        <div>
          <div id="content">
            <div>
              <span
                className={isDataProcessed ? styles.oval : styles.ovalSelected}
              >
                1
              </span>
              <label
                className={
                  isDataProcessed
                    ? styles.headerDescription
                    : styles.headerDescriptionSelected
                }
              >
                {t("CHOOSE FILE")}
              </label>
            </div>
            <div id="content-divider" />
            <span
              className={!isDataProcessed ? styles.oval : styles.ovalSelected}
            >
              2
            </span>
            <label
              className={
                !isDataProcessed
                  ? styles.headerDescription
                  : styles.headerDescriptionSelected
              }
            >
              {t("VERIFY")}
            </label>
          </div>

          {isDataProcessed && (
            <div id="file">
              <label id="file-label">{t("CSV source file")}</label>

              <label id="file-name">{fileName}</label>

              <Button
                theme="secondary"
                variant="flat"
                onClick={onChangeFile}
                disabled={isAddingCarriers}
              >
                <span className={"button-content"}>
                  <RefreshCcwIcon
                    fill="#21252a"
                    iconClass={"button-icon-left"}
                  />
                  {t("Change File")}
                </span>
              </Button>
            </div>
          )}
        </div>
        <div className={styles.divider} />
      </div>
    </div>
  );
};

export default BulkCarrierAdditions;
