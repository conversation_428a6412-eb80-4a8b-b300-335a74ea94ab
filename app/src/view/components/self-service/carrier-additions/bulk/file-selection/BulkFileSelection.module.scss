@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.fileSelection {
  display: flex;
  flex-direction: column;

  > button {
    display: flex;
    align-items: center;
    width: 264px;
    margin-top: 44px;
    margin-bottom: 28px;
  }
}

.fileProcessing {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  align-items: center;
  align-content: center;
  justify-content: center;

  > label {
    margin-top: 32px;
  }
}

.title {
  font-weight: 600;
  font-size: 18px;
  line-height: 27px;
  margin-bottom: 16px;
}

.description {
  display: flex;
  margin-bottom: 12px;

  > label {
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    margin-right: 16px;
  }
}

.fileProcessingImg {
  height: 154px;
  width: 180px;
  margin-bottom: 32px;
  object-fit: contain;
}

.tableTitle {
  font-weight: 600;
  font-size: 18px;
  line-height: 27px;
}

.tableContainer {
  display: flex;
  flex-direction: column;

  > label {
    margin-top: 32px;
    margin-bottom: 12px;
  }

  > div > div {
    overflow-x: auto;
    overflow-y: auto;

    > table > tbody > tr {
      vertical-align: baseline;
    }
  }
}

.fieldDescription {
  min-width: 130px;

  .required {
    font-weight: 400;
    font-size: 14px;
    line-height: 17px;
    color: $color-accent-cherry-500;
  }

  .optional {
    font-weight: 400;
    font-size: 14px;
    line-height: 17px;
    color: $color-neutral-700;
  }

  > label[id="description"] {
    font-weight: 400;
    font-size: 14px;
    line-height: 17px;
  }
}
