import { useState } from "react";

import BulkFileProcessingHandler from "./handler/BulkFileProcessingHandler";
import FileSelection from "./FileSelection";

import styles from "./BulkFileSelection.module.scss";
import BulkFileSelectionProps from "./BulkFileSelection.types";

const BulkFileSelection = ({
  managerCompanyId,
  mode,
  onBulkListProcessed,
  onDownloadBulkTemplate,
}: BulkFileSelectionProps) => {
  const [bulkList, setBulkList] = useState<any>(null); // TODO
  const [isProcessingFile, setIsProcessingFile] = useState<boolean>(false); // TODO

  const onConfirmFile = (files: File[], data: any) => {
    setBulkList(files[0]);
    setIsProcessingFile(true);
  };

  return (
    <div className={styles.container}>
      {isProcessingFile ? (
        <BulkFileProcessingHandler
          managerCompanyId={managerCompanyId}
          mode={mode}
          bulkList={bulkList}
          onHandlerComplete={() => onBulkListProcessed(bulkList?.name)}
        />
      ) : (
        <FileSelection
          onDownloadBulkTemplate={onDownloadBulkTemplate}
          onConfirmFile={onConfirmFile}
        />
      )}
    </div>
  );
};

export default BulkFileSelection;
