import { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { Button, DownloadIcon } from "@fourkites/elemental-atoms";
import { Table } from "@fourkites/elemental-table";

import FileUpload from "view/components/base/file-upload/FileUpload";

import styles from "./BulkFileSelection.module.scss";

const FileSelection = ({ onDownloadBulkTemplate, onConfirmFile }: any) => {
  const { t } = useTranslation();

  return (
    <div className={styles.fileSelection}>
      <span className={styles.description}>
        <label className={styles.title}>
          {t("Steps to invite your FTL carriers :")}
        </label>
      </span>

      <span className={styles.description}>
        <label>
          {t(
            "STEP 1: Download the provided template file and open in a spreadsheet editor"
          )}
        </label>

        <Button
          theme="primary"
          variant="outline"
          size="medium"
          onClick={onDownloadBulkTemplate}
          data-testid="download-bulk-upload-carriers-template-button"
        >
          <DownloadIcon iconClass={"button-icon-left"} fill="#0e65e5" />
          {t("Download CSV Template")}
        </Button>
      </span>

      <span className={styles.description}>
        <label className={styles.description}>
          {t("STEP 2: Add the required fields, save it as a ")}
          <b>&nbsp;{t(".CSV")}&nbsp;</b>
          {t("file")}
        </label>
      </span>

      <span className={styles.description}>
        <label className={styles.description}>
          {t("STEP 3: Browse for your file and upload")}
        </label>

        <FileUpload type="input" onConfirmFile={onConfirmFile} />
      </span>

      <FileDescriptionTable />
    </div>
  );
};

const FileDescriptionTable = () => {
  const { t } = useTranslation();

  const tableData = useMemo(() => {
    return [
      {
        carrier_name: {
          description: t("The name or DBA name of your FTL carrier."),
          required: true,
        },
        usdot: {
          description: t(
            "The US DOT number of your carrier. We validate this number with " +
              "the FMCSA database to prevent errors."
          ),
          required: false,
        },
        mc_number: {
          description: t(
            "The MC Number of your carrier. While not required, adding the MC " +
              "number will help you search for this carrier later."
          ),
          required: false,
        },
        nmfta_scac: {
          description: t(
            "The SCAC of your carrier. While not required, adding the SCAC " +
              "will help you search for this carrier later. We validate " + 
              "this with the NMFTA database to prevent errors."
          ),
          required: false
        },
        vat_number: {
          description: t("Not used in North America."),
          required: false,
        },
        identifier: {
          description: t("Not used at this time."),
          required: false,
        },
        contact_name: {
          description: t(
            "The name of your contact at the carrier. The person " +
              "listed here will receive an invitation to join FourKites, or " +
              "approve the connection to your company."
          ),
          required: true,
        },
        contact_email: {
          description: t(
            "The email address we will use to reach the contact " +
              "person to create a FourKites account, or approve the connection " +
              "to your company."
          ),
          required: true,
        },
        contact_phone: {
          description: t(
            "The phone number of your carrier. While optional, the " +
              "phone number provided will be available for you in FourKites " +
              "to quickly contact your carrier."
          ),
          required: false,
        },
        carrier_reference_codes: {
          description: t(
            "Carrier reference codes, aka 'Carrier Codes' are the " +
              "unique codes for your carriers from your TMS or load data source. " +
              "FourKites uses these codes to assign carriers to loads. They are " +
              "often SCAC codes, Carrier IDs, or sometime the DOT numbers."
          ),
          required: true,
        },
      },
    ];
  }, []);

  const columns = useMemo(() => {
    const getFieldDescriptionCell = (c: any) => {
      return (
        <FieldDescriptionCell
          description={c?.value?.description}
          required={c?.value?.required}
        />
      );
    };

    return [
      {
        Header: "carrier_name",
        accessor: "carrier_name",
        Cell: getFieldDescriptionCell,
      },
      {
        Header: "usdot",
        accessor: "usdot",
        Cell: getFieldDescriptionCell,
      },
      {
        Header: "mc_number",
        accessor: "mc_number",
        Cell: getFieldDescriptionCell,
      },
      {
        Header: "nmfta_scac",
        accessor: "nmfta_scac",
        Cell: getFieldDescriptionCell,
      },
      {
        Header: "vat_number",
        accessor: "vat_number",
        Cell: getFieldDescriptionCell,
      },
      {
        Header: "identifier",
        accessor: "identifier",
        Cell: getFieldDescriptionCell,
      },
      {
        Header: "contact_name",
        accessor: "contact_name",
        Cell: getFieldDescriptionCell,
      },
      {
        Header: "contact_email",
        accessor: "contact_email",
        Cell: getFieldDescriptionCell,
      },
      {
        Header: "contact_phone",
        accessor: "contact_phone",
        Cell: getFieldDescriptionCell,
      },
      {
        Header: "carrier_reference_codes",
        accessor: "carrier_reference_codes",
        Cell: getFieldDescriptionCell,
      },
    ];
  }, []);

  const paginationParams = {
    paginated: false,
  };

  return (
    <div className={styles.tableContainer}>
      <label className={styles.tableTitle}>
        {t("Template column definitions")}
      </label>
      <label>
        {t("At least one of USDOT, MC or SCAC is required.")}
      </label>
      <Table
        variant="flat-bordered"
        columns={columns}
        data={tableData}
        striped={false}
        pagination={paginationParams}
      />
    </div>
  );
};

const FieldDescriptionCell = ({ description, required }: any) => {
  const { t } = useTranslation();

  return (
    <div className={styles.fieldDescription}>
      <label className={required ? styles.required : styles.optional}>
        {required ? t("Required") : t("Optional")}
      </label>
      <br />

      <label id="description">{description}</label>
    </div>
  );
};

export default FileSelection;
