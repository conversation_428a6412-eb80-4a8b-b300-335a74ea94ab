import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";

import fileProcessing from "assets/img/file-processing.png";

import { useAppSelector, useAppDispatch, useDebounce } from "state/hooks";
import { CarrierAdditionsState } from "state/modules/shipper/CarrierAdditions";

import ProgressBar from "view/components/base/progress-bar/ProgressBar";

import styles from "./BulkFileProcessingHandler.module.scss";
import BulkFileProcessingHandlerProps from "./BulkFileProcessingHandler.types";

const BulkFileProcessingHandler = ({
  managerCompanyId,
  mode,
  bulkList,
  onHandlerComplete,
}: BulkFileProcessingHandlerProps) => {
  const { t } = useTranslation();

  const dispatch = useAppDispatch();

  // Carriers Data
  const requestId = useAppSelector(
    CarrierAdditionsState.selectors.carrierBulkRequestIdByMode(mode)
  );
  const progress = useAppSelector(
    CarrierAdditionsState.selectors.carrierBulkProgressByMode(mode)
  );
  const error = useAppSelector(
    CarrierAdditionsState.selectors.carrierBulkErrorByMode(mode)
  );

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  /*
   * Start handling carrier additions
   */
  useEffect(() => {
    handleBulkListProcessing();
  }, []);

  /*
   * Repeatedly check for status of additions
   */
  useDebounce(
    () => {
      // We can't check progress if we don't have a request id
      if (!requestId || error) {
        return;
      }

      // If completed, finish handler
      if (progress > 99) {
        onHandlerComplete();
        return;
      }

      // Otherwise, repeatedly check for progess
      dispatch(
        CarrierAdditionsState.actions.getBulkListResults({
          shipperId: managerCompanyId,
          mode,
          requestId,
        })
      ).then((payloadAction) => {
        // TODO: handler error to calls here
      });
    },
    2000,
    [managerCompanyId, mode, requestId, progress]
  );

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  /*
   * Handles processing of the bulk carriers list
   */
  const handleBulkListProcessing = async () => {
    // STEP 1: add carriers to shippers network
    const addCarriersResponse = await dispatch(
      CarrierAdditionsState.actions.sendBulkList({
        shipperId: managerCompanyId,
        mode,
        bulkList,
      })
    );
    if ("error" in addCarriersResponse) {
      // Handler error here
      return;
    }
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div className={styles.container}>
      <img
        alt="Upload file"
        className={styles.fileProcessingImg}
        src={fileProcessing}
      />

      {error ? (
        <label className={styles.description}>
          {t("An error occurred. Please wait a few minutes and try again!")}
        </label>
      ) : (
        <>
          <ProgressBar progress={progress} />

          <label className={styles.description}>
            {t("Please wait while we read and process your carrier list...")}
          </label>
        </>
      )}
    </div>
  );
};

export default BulkFileProcessingHandler;
