@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.container {
  display: flex;
  flex-direction: column;
  width: 100%;

  > div[id=results-header] {
    display: flex;
    width: 100%;
    margin-bottom: 16px;

    > label[id=description] {
      color: $color-neutral-900;
      font-size: 20px;
      font-weight: 500;
      letter-spacing: 0;
      line-height: 30px;
      margin-left: 16px;
    }
  }

  > div[id=tabs-wrapper] {
    > div > div > ul {
      background-color: transparent;
    }
  }
}

.tableContainer {
  height: calc(100vh - 490px);

  > div > div {
    overflow-x: auto;
    overflow-y: auto;

    > table {
      > thead > tr > th {
        padding-top: 6px;
        padding-bottom: 6px;
      }
      > tbody > tr > td {
        padding-top: 6px;
        padding-bottom: 6px;
      }
    }
  }
}

.carrierStatus {
  display: flex;

  > div[id=existing-carriers] {
    display: flex;
    flex-direction: column;

    > span {
      padding: 2px 8px 2px 8px;
      background-color: #06D6A0;
      font-size: 22px;
      font-weight: 600;
      letter-spacing: 0;
      line-height: 24px;
    }

    > label {
      padding-top: 12px;
      padding-right: 8px;
      padding-bottom: 6px;
      padding-left: 8px;
      border-left: 1px solid #06D6A0;
      font-size: 14px;
      font-weight: 600;
      letter-spacing: 0;
      line-height: 11px;
    }
  }

  > div[id=new-carriers] {
    display: flex;
    flex-direction: column;

    > span {
      padding: 2px 8px 2px 8px;
      background-color: #FFD670;
      font-size: 22px;
      font-weight: 600;
      letter-spacing: 0;
      line-height: 24px;
    }

    > label {
      padding-top: 12px;
      padding-right: 8px;
      padding-bottom: 6px;
      padding-left: 8px;
      border-left: 1px solid #FFD670;
      font-size: 14px;
      font-weight: 600;
      letter-spacing: 0;
      line-height: 11px;
    }
  }

  > div[id=error-carriers] {
    display: flex;
    flex-direction: column;

    > span {
      padding: 2px 8px 2px 8px;
      background-color: #D9D9D9;
      font-size: 22px;
      font-weight: 600;
      letter-spacing: 0;
      line-height: 24px;
    }

    > label {
      padding-top: 12px;
      padding-right: 8px;
      padding-bottom: 6px;
      padding-left: 8px;
      border-left: 1px solid #D9D9D9;
      font-size: 14px;
      font-weight: 600;
      letter-spacing: 0;
      line-height: 11px;
    }
  }
}


.errorCarriersLabel {
  display: flex;
  align-items: center;
  align-content: center;

  > label[id=error] {
    font-size: 16px;
    font-weight: 600;
    letter-spacing: 0;
    line-height: 21px;
    color: $color-accent-cherry-500;
    margin-left: 8px;
  }

  > label[id=alert] {
    font-size: 16px;
    font-weight: 600;
    letter-spacing: 0;
    line-height: 21px;
    color: $color-accent-tiger-500;
    margin-left: 8px;
  }
}

.errorCarriersAction {
  display: flex;
  max-width: 900px;
  flex-wrap: wrap;
  align-content: center;

  > div {
    margin-right: 16px;
  }
}
