import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

import TabbedContainer from "view/components/base/tabbed-container/TabbedContainer";

import CarriersOnPlatform from "./CarriersOnPlatform";
import NewCarriers from "./NewCarriers";
import CarriersWithErrors from "./CarriersWithErrors";
import CarriersStatus from "./CarriersStatus";

import styles from "./BulkResultsValidation.module.scss";
import BulkResultsValidationProps from "./BulkResultsValidation.types";

const BulkResultsValidation = ({
  carrierResults,
  onUpdateCarrierResults,
}: BulkResultsValidationProps) => {
  const { t } = useTranslation();

  const [localErrorsData, setLocalErrorsData] = useState<any[]>([]);

  const carriersOnPlatform = carrierResults?.carriersOnPlatform || [];
  const newCarriers = carrierResults?.newCarriers || [];
  const carriersWithErrors = carrierResults?.carriersWithErrors || [];

  useEffect(() => {
    if (localErrorsData.length) {
      return;
    }

    setLocalErrorsData(carriersWithErrors);
  }, [carriersWithErrors]);

  const onUpdateErrorsData = (errorsData: any[]) => {
    setLocalErrorsData(errorsData);

    onUpdateCarrierResults({
      carriersOnPlatform: carriersOnPlatform,
      newCarriers: newCarriers,
      carriersWithErrors: localErrorsData,
    });
  };

  const getStatusPercentage = (carriers: any[]) => {
    const totalCarriers =
      carriersOnPlatform.length +
      newCarriers.length +
      carriersWithErrors.length;
    return totalCarriers > 0
      ? Math.round((carriers.length * 100) / totalCarriers)
      : 0;
  };

  const percentageOnPlatform = getStatusPercentage(carriersOnPlatform);
  const percentageNew = getStatusPercentage(newCarriers);
  const percentageWithErrors = getStatusPercentage(carriersWithErrors);

  const tabs = [
    {
      title: `Carriers on FourKites (${carriersOnPlatform?.length})`,
      id: "carriersOnPlatform",
      tabComponent: <CarriersOnPlatform data={carriersOnPlatform} />,
    },
    {
      title: `New Carriers (${newCarriers?.length})`,
      id: "newCarriers",
      tabComponent: <NewCarriers data={newCarriers} />,
    },
    {
      title: `Errors (${carriersWithErrors?.length})`,
      id: "carriersWithErrors",
      tabComponent: (
        <CarriersWithErrors
          data={carriersWithErrors}
          localData={localErrorsData}
          setLocalData={onUpdateErrorsData}
        />
      ),
    },
  ];

  return (
    <div className={styles.container}>
      <div id="results-header">
        <CarriersStatus
          percentageOnPlatform={percentageOnPlatform}
          percentageNew={percentageNew}
          percentageWithErrors={percentageWithErrors}
        />

        <label id={"description"}>
          {percentageOnPlatform > 80
            ? t(
                `Congratulations! ${percentageOnPlatform}% of your carriers are already on FourKites.`
              )
            : t(
                `${percentageOnPlatform}% of your carriers are already on FourKites.`
              )}
        </label>
      </div>

      <div id="tabs-wrapper">
        <TabbedContainer
          tabs={tabs}
          selectedTab={"carriersOnPlatform"}
          onSelectTab={(tabId: string) => {
            //TODO
          }}
        />
      </div>
    </div>
  );
};

export default BulkResultsValidation;
