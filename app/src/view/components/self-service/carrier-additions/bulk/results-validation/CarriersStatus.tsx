import React from "react";
import { useTranslation } from "react-i18next";

import styles from "./BulkResultsValidation.module.scss";

const CarriersStatus = ({
  percentageOnPlatform,
  percentageNew,
  percentageWithErrors,
}: any) => {
  const { t } = useTranslation();

  const getWidth = (status: number) => {
    const scaledWidth = status > 0 ? Math.round((status * 800) / 100) : 88;
    return scaledWidth > 88 ? scaledWidth : 88;
  };

  return (
    <div className={styles.carrierStatus}>
      <div
        id="existing-carriers"
        style={{ width: getWidth(percentageOnPlatform) }}
      >
        <span>{percentageOnPlatform}%</span>
        <label>{t("Carriers on FourKites")}</label>
      </div>
      <div id="new-carriers" style={{ width: getWidth(percentageNew) }}>
        <span>{percentageNew}%</span>
        <label>{t("New Carriers")}</label>
      </div>
      <div
        id="error-carriers"
        style={{ width: getWidth(percentageWithErrors) }}
      >
        <span>{percentageWithErrors}%</span>
        <label>{t("Errors")}</label>
      </div>
    </div>
  );
};

export default CarriersStatus;
