import { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { InfoIcon, AlertTriangleIcon } from "@fourkites/elemental-atoms";
import { Table } from "@fourkites/elemental-table";

import EditableCell from "view/components/base/editable-cell/EditableCell";
import MultiInput from "view/components/base/multi-input/MultiInput";
import IdentificationsIndicator from "view/components/base/company-identifications/IdentificationsIndicator";

import {
  MAX_CARRIER_CODES,
  MAX_CARRIER_CODE_LENGTH,
} from "view/components/self-service/company-management/CompanyManagementUtils";

import styles from "./BulkResultsValidation.module.scss";

const CarriersWithErrors = ({ data, localData }: any) => {
  const { t } = useTranslation();

  const columns = useMemo(() => {
    return [
      {
        Header: t("Carrier Name"),
        accessor: "name",
      },
      {
        Header: "USDOT#",
        id: "usdot",
        accessor: "identifications",
        Cell: (c: any) => {
          return (
            <IdentificationsIndicator
              type={"usdot"}
              label={"DOT"}
              identifications={c.cell.value}
            />
          );
        },
      },
      {
        Header: "MC#",
        id: "mc",
        accessor: "identifications",
        Cell: (c: any) => {
          return (
            <IdentificationsIndicator
              type={"mc"}
              label={"MC"}
              identifications={c.cell.value}
            />
          );
        },
      },
      {
        Header: "NMFTA SCAC",
        id: "scac",
        accessor: "identifications",
        Cell: (c: any) => {
          return (
            <IdentificationsIndicator
              type={"scac"}
              label={"NMFTA SCAC"}
              identifications={c.cell.value}
            />
          );
        },
      },
      {
        Header: t("Error"),
        accessor: "errors",
        Cell: (cellData: any) => (
          <div>
            {cellData?.value?.map((data: any) => (
              <ErrorMessageCell data={data} />
            ))}
          </div>
        ),
      },
      // TODO: not showing action items for now
      /*{
        Header: t("Action"),
        id: "action",
        accessor: "error",
        Cell: (cellData: any) => (
          <ErrorActionCell
            cellData={cellData}
            localData={localData}
            setLocalData={setLocalData}
          />
        ),
      },*/
    ];
  }, [data, localData]);

  const tableData = useMemo(() => {
    return data;
  }, [data]);

  const paginationParams = {
    paginated: false,
  };

  return (
    <div className={styles.tableContainer}>
      <Table
        columns={columns}
        data={tableData}
        striped
        pagination={paginationParams}
      />
    </div>
  );
};

const ErrorMessageCell = ({ data }: any) => {
  const message = data?.message;
  // TODO
  const isError = message?.includes("Missing") || message?.includes("Invalid");

  return (
    <div className={styles.errorCarriersLabel}>
      {isError ? (
        <>
          <InfoIcon fill={"#da1e28"} />
          <label id="error">{message}</label>
        </>
      ) : (
        <>
          <AlertTriangleIcon fill={"#ff8932"} />
          <label id="alert">{message}</label>
        </>
      )}
    </div>
  );
};

const ErrorActionCell = ({ cellData, localData, setLocalData }: any) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const isFieldValid = (fieldValue: string) => {
    return fieldValue != null && fieldValue?.replace(/\s/g, "") != "";
  };

  const getValueForField = (data: any, fieldName: string) => {
    const fieldValues: any = {
      contactName: `${data?.contact?.first_name || ""} ${
        data?.contact?.last_name || ""
      }`,
      email: data?.contact?.email,
      carrierName: data?.name,
      usdot: data?.identifications?.find((id: any) => id.type === "usdot")
        ?.value,
      mc: data?.identifications?.find((id: any) => id.type === "mc")?.value,
    };

    return fieldValues[fieldName];
  };

  const setLocalDataValueForField = (
    index: number,
    fieldName: string,
    fieldValue: string
  ) => {
    const localDataCopy = [...localData];
    const data = localDataCopy[index];

    switch (fieldName) {
      case "carrierName":
        localDataCopy[index] = {
          ...data,
          name: fieldValue,
        };
        break;
      case "email":
        localDataCopy[index] = {
          ...data,
          contact: { ...data.contact, email: fieldValue },
        };
        break;
      case "contactName":
        const splittedName = fieldValue?.split(" ");
        localDataCopy[index] = {
          ...data,
          contact: {
            ...data.contact,
            first_name: splittedName[0] || null,
            last_name: splittedName[1] || null,
          },
        };
        break;
      case "usdot":
        localDataCopy[index] = {
          ...data,
          identifications: data.identifications.map((id: any) =>
            id.type === "usdot" ? { type: "usdot", value: fieldValue } : id
          ),
        };
        break;
      case "mc":
        localDataCopy[index] = {
          ...data,
          identifications: data.identifications.map((id: any) =>
            id.type === "mc" ? { type: "mc", value: fieldValue } : id
          ),
        };
        break;
      default:
        break;
    }

    setLocalData(localDataCopy);
  };

  const onAddCarrierCode = (index: number, code: string) => {
    const localDataCopy = [...localData];
    const data = localDataCopy[index];
    localDataCopy[index] = {
      ...data,
      carrier_codes: [...data.carrier_codes, code],
    };
    setLocalData(localDataCopy);
  };

  const onRemoveCarrierCode = () => {};

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const rowIndex = cellData.row.index;
  const errorData = cellData.row.original;
  const message = errorData?.error?.message;

  //TODO
  const isError = message?.includes("Missing") || message?.includes("Invalid");

  if (!isError) {
    return null;
  }

  const hasContactEmail = isFieldValid(getValueForField(errorData, "email"));
  const hasCarrierName = isFieldValid(
    getValueForField(errorData, "carrierName")
  );
  const hasContactName = isFieldValid(
    getValueForField(errorData, "contactName")
  );
  const hasUsdot = isFieldValid(getValueForField(errorData, "usdot"));
  const hasMcNumber = isFieldValid(getValueForField(errorData, "mc"));
  const hasMissingId = !(hasUsdot || hasMcNumber);

  const multiInputValidation = {
    maxNumberOfValues: MAX_CARRIER_CODES,
    maxValueLength: MAX_CARRIER_CODE_LENGTH,
  };

  return (
    <div className={styles.errorCarriersAction}>
      {!hasCarrierName && (
        <EditableCell
          label={t("Carrier Name")}
          initialValue={getValueForField(localData[rowIndex], "carrierName")}
          onUpdate={(value: string) =>
            setLocalDataValueForField(rowIndex, "carrierName", value)
          }
        />
      )}

      {!hasContactName && (
        <EditableCell
          label={t("Contact Name")}
          initialValue={getValueForField(localData[rowIndex], "contactName")}
          onUpdate={(value: string) => {
            setLocalDataValueForField(rowIndex, "contactName", value);
          }}
        />
      )}

      {!hasContactEmail && (
        <EditableCell
          label={t("Contact Email")}
          initialValue={getValueForField(localData[rowIndex], "email")}
          onUpdate={(value: string) => {
            setLocalDataValueForField(rowIndex, "email", value);
          }}
        />
      )}

      {hasMissingId && (
        <EditableCell
          label={t("USDOT#")}
          initialValue={getValueForField(localData[rowIndex], "usdot")}
          onUpdate={(value: string) =>
            setLocalDataValueForField(rowIndex, "usdot", value)
          }
        />
      )}

      {hasMissingId && (
        <EditableCell
          label={t("MC#")}
          initialValue={getValueForField(localData[rowIndex], "mc")}
          onUpdate={(value: string) =>
            setLocalDataValueForField(rowIndex, "mc", value)
          }
        />
      )}

      <MultiInput
        label={t("Carrier Codes")}
        placeholder="Enter carrier code"
        values={localData[rowIndex]?.carrier_codes || []}
        defaultValues={[]}
        onAddValue={(code: string) => onAddCarrierCode(rowIndex, code)}
        onRemoveValue={onRemoveCarrierCode}
        disabled={false}
        size="small"
        validation={multiInputValidation}
      />
    </div>
  );
};

export default CarriersWithErrors;
