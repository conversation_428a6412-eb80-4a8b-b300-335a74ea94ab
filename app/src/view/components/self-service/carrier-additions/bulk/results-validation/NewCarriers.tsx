import React, { useMemo } from "react";

import { AlertTriangleIcon } from "@fourkites/elemental-atoms";
import { Tooltip } from "@fourkites/elemental-tooltip";
import { Table } from "@fourkites/elemental-table";

import Contact from "view/components/base/contact/Contact";

import IdentificationsIndicator from "view/components/base/company-identifications/IdentificationsIndicator";

import { getCompanyAddress } from "view/components/self-service/company-management/CompanyManagementUtils";

import styles from "./BulkResultsValidation.module.scss";

const NewCarriers = ({ data }: any) => {
  const getCarrierCodes = (codes: string[]) => {
    if (!codes.length) {
      return (
        <Tooltip
          placement="bottom"
          text="You can add carrier codes later after you connect."
          theme="dark"
        >
          <span>
            <AlertTriangleIcon fill={"#ff8932"} />
          </span>
        </Tooltip>
      );
    } else if (codes.length <= 3) {
      return <span>{`${codes.slice(0, 3).join(", ")}`}</span>;
    } else {
      return (
        <span>{`${codes.slice(0, 3).join(", ")} +${codes.length - 3}`}</span>
      );
    }
  };

  const columns = useMemo(() => {
    return [
      {
        Header: "Carrier Name",
        accessor: "name",
      },
      {
        Header: "USDOT#",
        id: "usdot",
        accessor: "identifications",
        Cell: (c: any) => {
          return (
            <IdentificationsIndicator
              type={"usdot"}
              label={"DOT"}
              identifications={c.cell.value}
            />
          );
        },
      },
      {
        Header: "MC#",
        id: "mc",
        accessor: "identifications",
        Cell: (c: any) => {
          return (
            <IdentificationsIndicator
              type={"mc"}
              label={"MC"}
              identifications={c.cell.value}
            />
          );
        },
      },
      {
        Header: "NMFTA SCAC",
        id: "scac",
        accessor: "identifications",
        Cell: (c: any) => {
          return (
            <IdentificationsIndicator
              type={"scac"}
              label={"NMFTA SCAC"}
              identifications={c.cell.value}
            />
          );
        },
      },
      {
        Header: "Carrier Codes",
        accessor: "carrier_codes",
        Cell: (c: any) => getCarrierCodes(c?.cell?.value || []),
      },
      {
        Header: "Contact",
        accessor: "contact",
        Cell: (cell: any) => {
          const c = cell.cell.value;
          return (
            <Contact
              contact={{
                avatar: c.avatar,
                firstName: c.first_name,
                lastName: c.last_name,
                position: c.position,
                email: c.email,
                secondaryEmails: c.secondary_emails || [],
                phones: c.phones || [],
                messaging: c.messaging,
              }}
              contactInline
            />
          );
        },
      },
      {
        Header: "Address",
        accessor: "address",
        Cell: (c: any) => getCompanyAddress({ address: c.cell.value }),
      },
    ];
  }, []);

  const tableData = useMemo(() => {
    return data;
  }, [data]);

  const paginationParams = {
    paginated: false,
  };

  return (
    <div className={styles.tableContainer}>
      <Table
        columns={columns}
        data={tableData}
        striped
        pagination={paginationParams}
      />
    </div>
  );
};

export default NewCarriers;
