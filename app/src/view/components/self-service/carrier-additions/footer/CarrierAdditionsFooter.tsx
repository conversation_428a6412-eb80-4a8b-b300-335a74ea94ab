import React from "react";
import { useTranslation } from "react-i18next";

import { <PERSON>ton, ArrowRightIcon } from "@fourkites/elemental-atoms";

import { fourkitesUrls } from "api/http/apiUtils";

import styles from "./CarrierAdditionsFooter.module.scss";
import CarrierAdditionsFooterProps from "./CarrierAdditionsFooter.types";

const CarrierAdditionsFooter = ({
  additionsNumber,
  invitationsNumber,
  disabled = false,
  isConnecting,
  onConnect,
}: CarrierAdditionsFooterProps) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const disableConnect =
    disabled ||
    isConnecting ||
    (additionsNumber === 0 && invitationsNumber === 0);

  return (
    <div className={styles.footer}>
      <div id="left">
        {/*
      //TODO: removing help links while they are not ready
        <div>
          <LinkButton
            theme="primary"
            variant="flat"
            target="_blank"
            href={fourkitesUrls.help}
          >
            <span className={"button-content"}>
              <BookIcon fill="#0e65e5" iconClass={"button-icon-left"} />
              {t("Help Documentation")}
            </span>
          </LinkButton>
        </div>

        <div>
          <LinkButton
            theme="primary"
            variant="flat"
            target="_blank"
            href={fourkitesUrls.help}
          >
            <span className={"button-content"}>
              <MailIcon fill="#0e65e5" iconClass={"button-icon-left"} />
              {t("Contact FourKites Support")}
            </span>
          </LinkButton>
        </div>
        */}
      </div>

      <div id="right">
        <label data-test-id="carrier-additions-bulk-upload-footer-message">
          {t(
            `${additionsNumber > 0 ? additionsNumber : "No"} ${
              additionsNumber > 1 ? "Carriers" : "Carrier"
            } ${
              additionsNumber > 1 ? "connections" : "connection"
            } will be live. ${
              invitationsNumber > 0 ? invitationsNumber : "No"
            } ${
              invitationsNumber > 1 ? "Carriers" : "Carrier"
            } will be onboarded.`
          )}
        </label>
        <Button disabled={disableConnect} onClick={onConnect}>
          {t("Connect")}
          <ArrowRightIcon fill="white" iconClass={"button-icon-right"} />
        </Button>
      </div>
    </div>
  );
};

export default CarrierAdditionsFooter;
