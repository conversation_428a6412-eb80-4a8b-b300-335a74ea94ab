@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  align-content: center;
  width: 100%;
  margin-top: 28px;

  .greetInfoBanner {
    display: flex;
    align-items: center;
    margin-bottom: 2%;

    > div[id="connected-panel-img"] {
      display: flex;
      margin-right: 15px;
      align-items: center;
      justify-content: center;

      > img {
        width: 80px;
      }
    }

    > div[id="greet-user-info"] {
      display: flex;
      align-items: flex-start;
      justify-content: center;
      flex-direction: column;

      > label[id="greet-msg"] {
        font-size: 24px;
        font-weight: 100;
        flex-direction: column;
        align-items: flex-start;
        line-height: 40px;

        > img {
          width: 20px;
        }
      }

      > label[id="associated-company"] {
        display: flex;
        font-size: 18px;
        font-weight: 400;
        color: $color-neutral-900;
      }
    }
  }
}

.content {
  display: flex;
  justify-content: center;
  align-content: center;
  padding-left: 3%;
  padding-right: 3%;
  margin-top: 4%;

  .bulkConnectionContainer {
    display: flex;
    width: 100%;
    flex-direction: column;
    flex: 5;
    align-items: center;

    > label[id=description] {
      color: $color-neutral-900;
      font-size: 18px;
      font-weight: 500;
      letter-spacing: 0;
      line-height: 28px;
    }

    > div {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      height: 135px;

      > button {
        display: flex;
        align-items: center;
        margin-right: 16px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .manualConnectionContainer {
    display: flex;
    flex-direction: column;
    width: 100%;
    align-items: center;
    flex: 5;

    > img {
      height: 224px;
      width: 224px;
    }
  }

  .inviteCarriersContainer {
    display: flex;
    width: 100%;
    flex-direction: column;
    flex: 5;
    align-items: center;

    > label[id=description] {
      color: $color-neutral-900;
      font-size: 18px;
      font-weight: 500;
      letter-spacing: 0;
      line-height: 28px;
    }

    > div {
      display: flex;
      margin-bottom: 30px;

      > button {
        display: flex;
        align-items: center;
        margin-right: 16px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}

.divider {
  display: flex;
  flex-direction: column;
  align-content: center;
  align-items: center;
  justify-content: center;
  flex: 1;

  > div[id=vertical-bar] {
    border-left: 1px solid $color-neutral-400;
    height: 230px;
  }

  > div[id=oval] {
    display: flex;
    align-content: center;
    align-items: center;
    justify-content: center;
    color: $color-neutral-900;
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 0;
    line-height: 17px;
    box-sizing: border-box;
    height: 36px;
    width: 36px;
    border: 1px solid $color-neutral-400;
    border-radius: 18px;
  }
}

.subtitle {
  color: $color-neutral-900;
  font-size: 24px;
  font-weight: bold;
  letter-spacing: 0;
  line-height: 28px;
  text-align: center;
  margin-bottom: 24px;
}

.searchContainer {
  display: inline-block;
  align-items: center;
  align-content: center;
  justify-content: center;
  width: 612px;
}
