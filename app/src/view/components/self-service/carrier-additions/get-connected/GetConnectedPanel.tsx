import { useTranslation } from "react-i18next";
import copyToClipboard from "copy-to-clipboard";
import {
  Button,
  UploadIcon,
  ExcelIcon,
  CopyIcon,
} from "@fourkites/elemental-atoms";

import GetConnectedPanelImg from "assets/img/add-carriers.png";
import GetGreetImg from "assets/img/waving-hand.png";
import { FOURKITES_APP_URL } from "api/http/apiUtils";

import { useAppSelector } from "state/hooks";
import UsersState from "state/modules/Users";
import { CarrierInvitationsState } from "state/modules/shipper/CarrierInvitations";

import CompanySuggestions from "view/components/self-service/company-suggestions/CompanySuggestions";
import { showToast } from "view/components/base/toast/Toast";
import FloatingChip from "view/components/base/floating-chip/FloatingChip";

import styles from "./GetConnectedPanel.module.scss";
import GetConnectedPanelProps from "./GetConnectedPanel.types";
import GreetInfoBannerProps from "./GreetInfoBanner.types";
import BulkConnectionBannerProps from "./BulkConnectionBanner.types";
import ManualConnectionBannerProps from "./ManualConnectionBanner.types";

const GetConnectedPanel = ({
  mode,
  managerCompanyName,
  managerCompanyId,
  managedCompanyType,
  onShowBulkCarrierAdditions,
  onDownloadBulkTemplate,
  onShowManualCarrierAdditions,
}: GetConnectedPanelProps) => {
  return (
    <div className={styles.container}>
      <GreetInfoBanner managerCompanyName={managerCompanyName} />

      <FloatingChip content={"Choose an option to connect"} size={"medium"} />

      <div className={styles.content}>
        <BulkConnectionBanner
          onShowBulkCarrierAdditions={onShowBulkCarrierAdditions}
          onDownloadBulkTemplate={onDownloadBulkTemplate}
        />

        <div className={styles.divider}>
          <div id="vertical-bar" />
          <div id="oval">OR</div>
          <div id="vertical-bar" />
        </div>

        <ManualConnectionBanner
          mode={mode}
          managerCompanyId={managerCompanyId}
          managedCompanyType={managedCompanyType}
          onShowManualCarrierAdditions={onShowManualCarrierAdditions}
        />

        <div className={styles.divider}>
          <div id="vertical-bar" />
          <div id="oval">OR</div>
          <div id="vertical-bar" />
        </div>

        <InviteCarriersBanner />
      </div>
    </div>
  );
};

export default GetConnectedPanel;

const GreetInfoBanner = ({ managerCompanyName }: GreetInfoBannerProps) => {
  const { t } = useTranslation();
  const currentUser = useAppSelector(UsersState.selectors.getCurrentUser);

  return (
    <div className={styles.greetInfoBanner}>
      <div id="connected-panel-img">
        <img alt="Add Carriers" src={GetConnectedPanelImg} />
      </div>
      <div id="greet-user-info">
        <label id="greet-msg" data-testid="greet-msg">
          {t(`Hello ${currentUser?.name}! `)}
          <img alt="hello" src={GetGreetImg} />
        </label>
        <label id="associated-company" data-testid="user-associated-company">
          {t(
            `Welcome The ${managerCompanyName}! Let's get started & connect with your Carriers.`
          )}
        </label>
      </div>
    </div>
  );
};

const BulkConnectionBanner = ({
  onShowBulkCarrierAdditions,
  onDownloadBulkTemplate,
}: BulkConnectionBannerProps) => {
  const { t } = useTranslation();

  return (
    <div className={styles.bulkConnectionContainer}>
      <label className={styles.subtitle}>{t("Upload all your Carriers")}</label>

      <div>
        <Button
          size="large"
          onClick={onShowBulkCarrierAdditions}
          data-testid="upload-carrier-list-button"
        >
          <UploadIcon fill="white" iconClass={"button-icon-left"} />
          {t("Upload Carrier List")}
        </Button>

        <Button
          size="large"
          theme="secondary"
          variant="outline"
          onClick={onDownloadBulkTemplate}
          data-testid="download-csv-template-button"
        >
          <ExcelIcon iconClass={"button-icon-left"} />
          {t("Download CSV Template")}
        </Button>
      </div>

      <label id="description">
        {t(
          "Complete our template with your carrier information and upload it " +
            "here. We will need the below information to process:"
        )}
        <br />
        <br />• {t("Carrier name")}
        <br />• {t("USDOT# or MC#")}
        <br />• {t("Carrier contact person name & Email")}
      </label>
    </div>
  );
};

const ManualConnectionBanner = ({
  mode,
  managerCompanyId,
  managedCompanyType,
  onShowManualCarrierAdditions,
}: ManualConnectionBannerProps) => {
  const { t } = useTranslation();

  return (
    <div className={styles.manualConnectionContainer}>
      <label className={styles.subtitle}>{t("Search & Add Manually")}</label>

      <div className={styles.searchContainer}>
        <CompanySuggestions
          mode={mode}
          managerCompanyId={managerCompanyId}
          suggestedCompanyType={managedCompanyType}
          onSelectSuggestion={onShowManualCarrierAdditions}
          // TODO: for now, we are disabling adding not found carriers
          //onSelectNotFound={onShowManualCarrierAdditions}
        />
      </div>
    </div>
  );
};

const InviteCarriersBanner = () => {
  const { t } = useTranslation();
  const inviterToken = useAppSelector(
    CarrierInvitationsState.selectors.inviterToken()
  );

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/
  const onCopyInviterCode = async () => {
    const inviterTokenUrl = `${FOURKITES_APP_URL}self-service/signup?inviter=${inviterToken}`;
    await copyToClipboard(inviterTokenUrl);
    showToast(t("Invite link copied to clipboard."), t(""), "ok");
  };

  return (
    <div className={styles.inviteCarriersContainer}>
      <label className={styles.subtitle}>
        {t("Invite your Carriers at Scale")}
      </label>

      <div>
        {inviterToken && (
          <Button
            size="large"
            onClick={onCopyInviterCode}
            data-testid="copy-invitation-code-button"
          >
            <CopyIcon fill="white" iconClass={"button-icon-left"} />
            {t("Copy Invite Link")}
          </Button>
        )}
      </div>

      <label id="description">
        {t(
          "FourKites Invite link is an extensible and flexible URL that is " +
            "unique and dedicated for your organization."
        )}
        <br />
        <br />•{" "}
        {t(
          "This URL can be embedded within your websites, apps, email " +
            "signatures, custom email campaigns, IMs, published as a QR " +
            "code at your facilities or anywhere across your workflow, to " +
            "be shared with your carriers."
        )}
        <br />•{" "}
        {t(
          "Your carriers can use this URL to instantly connect with you on " +
            "FourKites and share tracking data with you."
        )}
      </label>
    </div>
  );
};
