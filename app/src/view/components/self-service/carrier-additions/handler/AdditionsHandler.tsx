import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";

import fileProcessing from "assets/img/file-processing.png";

import { useAppSelector, useAppDispatch, useDebounce } from "state/hooks";
import { CarriersNetworkState } from "state/modules/shipper/CarriersNetwork";
import { CarrierAdditionsState } from "state/modules/shipper/CarrierAdditions";
import { CarrierInvitationsState } from "state/modules/shipper/CarrierInvitations";
import { ShipperIndicatorsState } from "state/modules/shipper/ShipperIndicators";

import UsersState from "state/modules/Users";

import ProgressBar from "view/components/base/progress-bar/ProgressBar";
import { showToast } from "view/components/base/toast/Toast";

import styles from "./AdditionsHandler.module.scss";
import AdditionsHandlerProps from "./AdditionsHandler.types";
import { sleep } from "api/http/mockServiceCall";

const AdditionsHandler = ({
  managerCompanyId,
  mode,
  carriersToAdd,
  carriersToInvite,
  onHandlerComplete,
  source,
}: AdditionsHandlerProps) => {
  const { t } = useTranslation();

  const dispatch = useAppDispatch();

  // User Data
  const currentUser = useAppSelector(UsersState.selectors.getCurrentUser);

  // Carriers Data
  const requestId = useAppSelector(
    CarrierAdditionsState.selectors.carrierAdditionRequestIdByMode(mode)
  );
  const progress = useAppSelector(
    CarrierAdditionsState.selectors.carrierAdditionProgressByMode(mode)
  );
  const additionsError = useAppSelector(
    CarrierAdditionsState.selectors.carrierAdditionErrorByMode(mode)
  );
  const invitationsError = useAppSelector(
    CarrierInvitationsState.selectors.carrierInvitationsErrorByMode(mode)
  );

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  const showToastAndCompleteHandler = () => {
    const addedLabel =
      carriersToAdd?.length > 0
        ? `${carriersToAdd?.length} added`
        : "None added";
    const invitedLabel =
      carriersToInvite?.length > 0
        ? `${carriersToInvite?.length} invited`
        : "none invited";

    showToast(t("Carriers added"), t(`${addedLabel}, ${invitedLabel}.`), "ok");

    onHandlerComplete();
  };

  /*
   * Start handling carrier additions
   */
  useEffect(() => {
    handleCarrierAdditions();

    handleCarrierInvitations();
  }, []);

  /*
   * Repeatedly check for status of additions
   */
  useDebounce(
    async () => {
      // We can't check progress if we don't have a request id or if an error
      // occurred
      if (!requestId || additionsError) {
        return;
      }

      // If completed, fetches list of carriers again and finish handler
      if (progress > 99) {
        await updateCarriersList();
        showToastAndCompleteHandler();
        return;
      }

      // Otherwise, repeatedly check for progess
      dispatch(
        CarrierAdditionsState.actions.getCarrierAdditionsResults({
          shipperId: managerCompanyId,
          mode,
          requestId,
        })
      ).then((payloadAction) => {
        // TODO: handler error to calls here
      });
    },
    1000,
    [managerCompanyId, mode, requestId, progress, additionsError]
  );

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const updateCarriersList = async () => {
    // We need to retrieve the indicators again, as we've just update the count
    // and we rely on indicators to display GetConnected panel
    await dispatch(
      ShipperIndicatorsState.actions.getShipperIndicators({
        shipperId: managerCompanyId,
        mode,
      })
    );

    await dispatch(
      CarriersNetworkState.actions.getCarriers({
        shipperId: managerCompanyId,
        mode,
      })
    );
  };

  const getCarrierAdditionsData = (carriers: any[]) =>
    carriers?.map((c: any) => ({
      permalink: c.permalink,
      carrier_codes: c.carrier_codes,
    }));

  const getCarrierInvitationsData = (carriers: any[]) =>
    carriers?.map((c: any) => ({
      name: c.name,
      identifications: c.identifications,
      carrier_codes: c.carrier_codes,
      contact: c.contact,
    }));

  const additionsData = getCarrierAdditionsData(carriersToAdd);
  const invitationsData = getCarrierInvitationsData(carriersToInvite);
  const hasAdditions = additionsData != null && additionsData.length > 0;

  /*
   * Handles additions of carrier already on platform to the shipper's network
   */
  const handleCarrierAdditions = async () => {
    if (!additionsData || additionsData.length === 0) {
      return;
    }

    const addCarriersResponse = await dispatch(
      CarrierAdditionsState.actions.addCarriersToNetwork({
        shipperId: managerCompanyId,
        mode,
        carriers: additionsData,
        user: {
          id: currentUser?.userId,
          first_name: currentUser?.name,
          last_name: currentUser?.lastName,
          email: currentUser?.emailAddress,
        },
        source,
        tpl: currentUser?.defaultCompanyType?.includes('3pl') ? currentUser?.defaultCompanyID : ''
      })
    );

    if ("error" in addCarriersResponse) {
      // Handler error here
      return;
    }
  };

  /*
   * Handles carrier ivitations of carriers outside of the platform to the
   * shipper's network
   */
  const handleCarrierInvitations = async () => {
    if (!invitationsData || invitationsData.length === 0) {
      return;
    }

    const inviteCarriersResponse = await dispatch(
      CarrierInvitationsState.actions.inviteCarriersToNetwork({
        shipperId: managerCompanyId,
        mode,
        carriers: invitationsData,
        user: {
          id: currentUser?.userId,
          first_name: currentUser?.name,
          last_name: currentUser?.lastName,
          email: currentUser?.emailAddress,
        },
        source,
        tpl: currentUser?.defaultCompanyType?.includes('3pl') ? currentUser?.defaultCompanyID : ''
      })
    );

    if ("error" in inviteCarriersResponse) {
      return;
    }

    // If we don't have any additions to do, invitations are complete, update list
    if (!hasAdditions) {
      await updateCarriersList();
      showToastAndCompleteHandler();
    }
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/
  return (
    <div className={styles.container}>
      <img
        alt="Upload file"
        className={styles.fileProcessingImg}
        src={fileProcessing}
      />

      {hasAdditions && additionsError && (
        <label className={styles.description}>
          {t("An error occurred while adding the carriers.")}
          <br />
          {t("Please wait a few minutes and try again!")}
        </label>
      )}

      {hasAdditions && !additionsError && (
        <>
          <ProgressBar progress={progress} />

          <label className={styles.description}>
            {t("Please wait while we add the carriers...")}
          </label>
        </>
      )}

      {invitationsError && (
        <label className={styles.description}>
          {t("An error occurred while inviting the carriers.")}
          <br />
          {t("Please wait a few minutes and try again!")}
        </label>
      )}
    </div>
  );
};

export default AdditionsHandler;
