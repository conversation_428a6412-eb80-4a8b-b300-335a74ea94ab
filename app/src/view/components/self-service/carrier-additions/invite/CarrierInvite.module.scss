@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

// CSV Preview Styles
.csvPreviewContainer {
  margin-top: 16px;
}

.csvPreviewTitle {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #374151;
}

.csvPreviewTable {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  background-color: #f9fafb;
}

.csvTableWrapper {
  max-height: 300px;
  overflow-y: auto;
  overflow-x: auto;
}

.csvTable {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
}

.csvTableHeader {
  background-color: #f3f4f6;
}

.csvTableHeaderCell {
  padding: 8px 12px;
  text-align: left;
  font-weight: 600;
  border-bottom: 1px solid #e5e7eb;
  color: #374151;
  white-space: nowrap;
}

.csvTableRow {
  &:nth-child(even) {
    background-color: #ffffff;
  }
  &:nth-child(odd) {
    background-color: #f9fafb;
  }
}

.csvTableCell {
  padding: 8px 12px;
  border-bottom: 1px solid #e5e7eb;
  color: #6b7280;
  white-space: nowrap;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.csvPreviewFooter {
  padding: 8px 12px;
  background-color: #f3f4f6;
  border-top: 1px solid #e5e7eb;
  font-size: 11px;
  color: #6b7280;
  text-align: center;
}


.yellowWarningBox {
  background-color: #fff9e6;
  border: 1px solid #ffbf0a;
  border-radius: 10px;
  padding: 16px;
  margin-top: 8px;
  font-weight: 500;
  width: 100%;
  color: #d4a200;
  box-sizing: border-box; /* This ensures padding is included in width calculation */
}
.yellowWarningBox .bold {
  font-weight: 700;
}


.bccContainer {
  border: 1px solid #cfe2ff;
  
  border-radius: 10px;
  width: 100%;
  box-sizing: border-box;
  padding: 16px 20px;
  margin-top: 16px;
}


.bccHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.bccEmail {
  color: #2563eb;
  font-weight: 500;
  margin-left: 8px;
}

.copyButton {
  background-color: transparent;
  border: none;
  padding: 0;
  cursor: pointer;
  color: #2563eb;
  font-weight: 500;
  font-size: 16px;
}

.copyIcon {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 4px;
  background-size: contain;
  background-repeat: no-repeat;
}

.bccEmail {
  color: #2563eb;
  font-weight: 500;
  margin-left: 8px;
}

.bccContent {
  padding: 16px;
  color: #2563eb;
  font-weight: 500;
}




.wrapper {
  display: block;
  width: 100%;
  padding: 0 24px;
  box-sizing: border-box;
}




.bccContainer1 {
  border: 1px solid #e2e8f0;
  border-radius: 10px;
  width: 100%;
  box-sizing: border-box;
  margin-top: 16px;
  overflow: hidden;
}

.headerContainer1 {
  background-color: #2563eb;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
}

.headerText1 {
  font-weight: bold;
  font-size: 16px;
}

.copyButton1 {
  background-color: transparent;
  border: none;
  cursor: pointer;
  color: white;
  font-weight: 500;
  font-size: 16px;
  display: flex;
  align-items: center;
}

.emailContainer1 {
  background-color: white;
  padding: 16px 20px;
}

.emailText1 {
  font-weight: 500;
  color: #2563eb;
}













.bccContainer2 {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  font-family: sans-serif;
}

.headerContainer2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f3f7ff; /* Light blue background */
  border-bottom: 1px solid #e5e7eb;
}

.headerText2 {
  font-weight: 600;
  color: #3563EB; /* Blue text color */
  font-size: 16px;
}

.copyButton2 {
  display: flex;
  align-items: center;
  gap: 6px;
  background: transparent;
  border: none;
  color: #2563eb;
  cursor: pointer;
  font-weight: 500;
  font-size: 14px;
}

.emailContainer2 {
  background-color: white;
  padding: 16px;
}

.emailText2 {
  color: #2563eb; /* Blue text for email */
  font-size: 16px;
}



.bold {
  font-weight: bold;
}


















