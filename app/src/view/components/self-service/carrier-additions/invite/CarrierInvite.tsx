import React, { useRef, useState } from "react";
import { useTranslation } from "react-i18next";

import { Button, UploadIcon, ExcelIcon, AlertCircleIcon, CheckIcon, MailIcon, Link1Icon, CopyIcon, DownloadIcon, ChevronDownIcon, ChevronUpIcon } from "@fourkites/elemental-atoms";
import { Modal } from "@fourkites/elemental-modal";
import { Spinner } from "@fourkites/elemental-loading-indicator";

import { useAppSelector, useAppDispatch } from "state/hooks";
import { UsersState } from "state/modules/Users";
import CarrierSurveyApi from "api/shipper/CarrierSurveyApi";
import { csvToArray } from "view/components/base/file-upload/FileUtils";

import styles from "./CarrierInvite.module.scss";
import CarrierInviteProps from "./CarrierInvite.types";

interface FileUploadState {
  file: File | null;
  fileName: string;
  isUploading: boolean;
  success: boolean;
  error: string | null;
  preview: any[] | null;
}

const CarrierInvite = ({
  show,
  onClose,
  surveyLink
}: CarrierInviteProps) => {
  const { t } = useTranslation();
  const shipperId = useAppSelector(UsersState.selectors.getCompanyId);
  const dispatch = useAppDispatch();

  /*****************************************************************************
   * STATE
   ****************************************************************************/
  const [copiedSection, setCopiedSection] = useState<string | null>(null);
  const [showTemplate, setShowTemplate] = useState<boolean>(false);
  const [isPreviewExpanded, setIsPreviewExpanded] = useState<boolean>(false);
  const [fileUploadState, setFileUploadState] = useState<FileUploadState>({
    file: null,
    fileName: "",
    isUploading: false,
    success: false,
    error: null,
    preview: null
  });
  const fileInputRef = useRef<HTMLInputElement>(null);

  const COPY_TEXTS = {
    ccEmail: "<EMAIL>",
    subject: "Action Required: Please Complete FourKites Onboarding Survey",
    surveyLink: surveyLink,
    emailBody: `Dear Valued Carrier Partner,

We are excited to enhance our logistics partnership through FourKites Connect, which will provide real-time visibility to our shipments.

To complete your onboarding, please click the personalized survey link below:

SURVEY_LINK

This survey will take approximately 5 minutes to complete and will help us set up your account properly. Please complete this survey within 3 business days.`,
  };

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  /*
   * Clear state when closing component
   */
  const onCloseCarrierInvite = () => {
    setShowTemplate(false);
    setIsPreviewExpanded(false);
    setFileUploadState({
      file: null,
      fileName: "",
      isUploading: false,
      success: false,
      error: null,
      preview: null
    });
    onClose();
  };

  const handleCopy = (text: string, section: string) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopiedSection(section);
      setTimeout(() => setCopiedSection(null), 2000); // Reset after 2s
    });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      // Check if it's a CSV file
      if (selectedFile.type === 'text/csv' || selectedFile.name.endsWith('.csv')) {
        // Read the file to generate preview
        const reader = new FileReader();
        reader.onload = (event) => {
          const csvText = event.target?.result as string;
          if (csvText) {
            try {
              const previewData = csvToArray(csvText);
              setFileUploadState({
                ...fileUploadState,
                file: selectedFile,
                fileName: selectedFile.name,
                success: false,
                error: null,
                preview: previewData
              });
              // Reset preview expansion for new file
              setIsPreviewExpanded(false);
            } catch (error) {
              console.error('Error parsing CSV:', error);
              setFileUploadState({
                ...fileUploadState,
                file: selectedFile,
                fileName: selectedFile.name,
                success: false,
                error: 'Error parsing CSV file',
                preview: null
              });
            }
          }
        };
        reader.readAsText(selectedFile);
      } else {
        // For non-CSV files, just set the file without preview
        setFileUploadState({
          ...fileUploadState,
          file: selectedFile,
          fileName: selectedFile.name,
          success: false,
          error: null,
          preview: null
        });
      }
    }
  };

  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleUpload = async () => {
    if (!fileUploadState.file || !shipperId) return;

    setFileUploadState({
      ...fileUploadState,
      isUploading: true,
      success: false,
      error: null
    });

    try {
      await CarrierSurveyApi.uploadCarrierSurvey(shipperId, fileUploadState.file);

      setFileUploadState({
        ...fileUploadState,
        isUploading: false,
        success: true,
        error: null
      });
    } catch (error: any) {
      console.error('Error uploading file:', error);
      setFileUploadState({
        ...fileUploadState,
        isUploading: false,
        success: false,
        error: error.message || t('An error occurred during upload')
      });
    }
  };

  const downloadTemplate = () => {
    // Create CSV template content
    const csvContent = [
      'name,identifier_type,identifier_value,carrier_codes',
      'Example Carrier Inc,usdot,<usdot_value>,SCAC',
      'Sample Transport LLC,usdot,789012,SMPL'
    ].join('\n');

    // Create blob and download
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    link.setAttribute('href', url);
    link.setAttribute('download', 'carrier_survey_template.csv');
    link.style.visibility = 'hidden';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    URL.revokeObjectURL(url);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const renderCopySection = (label: string, text: string, sectionKey: string, IconComponent = CopyIcon, displayText?: string, isPlaceholder?: boolean) => (
    <div className={styles.bccContainer}>
      <div style={{ display: "flex", justifyContent: "space-between", marginBottom: "8px" }}>
        <span className={styles.bold}>{label}</span>
        <button className={styles.copyButton} onClick={() => handleCopy(text, sectionKey)}>
          <IconComponent fill="#2563eb" iconClass="button-icon-left" />
          {copiedSection === sectionKey ? t("Copied") : t("Copy")}
        </button>
      </div>
      <div style={{ fontWeight: 500, whiteSpace: "pre-line" }}>
        {isPlaceholder ? (
          <strong 
            style={{ 
              color: "#2563eb", 
              cursor: "pointer",
              textDecoration: "underline"
            }}
            onClick={() => {
              handleCopy(text, sectionKey);
              // Optionally open the link in a new tab
              if (text && text.startsWith('http')) {
                window.open(text, '_blank');
              }
            }}
          >
            {displayText}
          </strong>
        ) : (
          displayText || text
        )}
      </div>
    </div>
  );

  return (
    <Modal
      size="large"
      title={t("Carrier Invitation Email")}
      show={show}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: onCloseCarrierInvite,
      }}
    >
      <div style={{ display: "flex", flexDirection: "column", width: "100%", padding: "0 24px", boxSizing: "border-box" }}>
        {/* File Upload Section - Now at the top */}
        <div style={{ marginBottom: "24px", padding: "20px", border: "1px solid #e5e7eb", borderRadius: "12px", backgroundColor: "#fafbfc" }}>
          <h3 style={{ fontSize: "18px", fontWeight: "600", marginBottom: "8px", display: "flex", alignItems: "center", color: "#1f2937" }}>
            <div style={{ marginRight: "10px" }}>
              <ExcelIcon fill="#2563eb" />
            </div>
            {t("Bulk Carrier Survey Upload")}
          </h3>

          <p style={{ fontSize: "14px", color: "#6b7280", marginBottom: "20px" }}>
            {t("Upload a CSV file with carrier information to send surveys to multiple carriers at once.")}
          </p>

          {/* File Selection */}
          <div style={{ marginBottom: "20px" }}>
            <div style={{ display: "flex", alignItems: "center", marginBottom: "8px" }}>
              <div style={{
                width: "24px",
                height: "24px",
                borderRadius: "50%",
                backgroundColor: fileUploadState.fileName ? "#10b981" : "#2563eb",
                color: "white",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontSize: "12px",
                fontWeight: "600",
                marginRight: "8px"
              }}>
                {fileUploadState.fileName ? "✓" : "1"}
              </div>
              <span style={{ fontSize: "14px", fontWeight: "500", color: "#374151" }}>
                {t("Select your CSV file")}
              </span>
            </div>

            <div style={{ marginLeft: "32px", marginBottom: "8px" }}>
              <span style={{ fontSize: "12px", color: "#6b7280" }}>
                {t("Don't have a file ready? ")}
                <button
                  onClick={downloadTemplate}
                  style={{
                    background: "none",
                    border: "none",
                    color: "#2563eb",
                    textDecoration: "underline",
                    cursor: "pointer",
                    fontSize: "12px",
                    padding: "0"
                  }}
                >
                  {t("Download the CSV template")}
                </button>
                {t(" to get started.")}
              </span>
            </div>

            {/* CSV Format Requirements */}
            <div style={{
              marginLeft: "32px",
              marginBottom: "12px",
              padding: "12px",
              backgroundColor: "#f0f9ff",
              border: "1px solid #0ea5e9",
              borderRadius: "6px"
            }}>
              <div style={{ fontSize: "13px", fontWeight: "600", color: "#0c4a6e", marginBottom: "8px" }}>
                {t("CSV Format Requirements:")}
              </div>
              <div style={{ fontSize: "12px", color: "#0369a1", lineHeight: "1.4" }}>
                <div style={{ marginBottom: "4px" }}>
                  • <strong>name</strong>: Carrier company name (required)
                </div>
                <div style={{ marginBottom: "4px" }}>
                  • <strong>identifier_type</strong>: Must be "usdot" (only USDOT is supported for now)
                </div>
                <div style={{ marginBottom: "4px" }}>
                  • <strong>identifier_value</strong>: USDOT number (required)
                </div>
                <div>
                  • <strong>carrier_codes</strong>: SCAC or other carrier codes (required)
                </div>
              </div>
              <div style={{
                fontSize: "11px",
                color: "#0369a1",
                marginTop: "8px",
                fontStyle: "italic"
              }}>
                {t("All fields are required. Currently, only USDOT identifier type is supported.")}
              </div>
            </div>

            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              accept=".csv,.xlsx,.xls"
              style={{ display: 'none' }}
            />

            <div style={{ marginLeft: "32px" }}>
              <Button
                theme="tertiary"
                variant="flat"
                onClick={triggerFileInput}
                disabled={fileUploadState.isUploading}
              >
                <UploadIcon fill="#2563eb" iconClass="button-icon-left" />
                {fileUploadState.fileName ? t("Choose Different File") : t("Choose File")}
              </Button>
            </div>
          </div>

          {/* Selected File Display */}
          {fileUploadState.fileName && (
            <div style={{
              marginTop: "16px",
              padding: "16px",
              backgroundColor: "#f0f9ff",
              border: "1px solid #0ea5e9",
              borderRadius: "8px"
            }}>
              <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                <div style={{ display: "flex", alignItems: "center" }}>
                  <div style={{ marginRight: "10px" }}>
                    <ExcelIcon fill="#0ea5e9" size="20px" />
                  </div>
                  <div>
                    <div style={{ fontSize: "14px", fontWeight: "500", color: "#0c4a6e" }}>
                      {t("Selected File:")} {fileUploadState.fileName}
                    </div>
                    <div style={{ fontSize: "12px", color: "#0369a1", marginTop: "2px" }}>
                      {t("Ready to upload to system")}
                    </div>
                  </div>
                </div>

                {/* Step 2: Process File */}
                <div style={{ display: "flex", alignItems: "center" }}>
                  <div style={{
                    width: "24px",
                    height: "24px",
                    borderRadius: "50%",
                    backgroundColor: fileUploadState.success ? "#10b981" : "#2563eb",
                    color: "white",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    fontSize: "12px",
                    fontWeight: "600",
                    marginRight: "12px"
                  }}>
                    {fileUploadState.success ? "✓" : "2"}
                  </div>
                  <Button
                    onClick={handleUpload}
                    disabled={fileUploadState.isUploading || fileUploadState.success}
                    theme="primary"
                    variant="solid"
                  >
                    {fileUploadState.isUploading
                      ? t("Uploading...")
                      : fileUploadState.success
                        ? t("File Uploaded!")
                        : t("Upload Carrier List")
                    }
                  </Button>
                </div>
              </div>
            </div>
          )}
          
          {/* Status Messages */}
          {fileUploadState.isUploading && (
            <div style={{
              display: "flex",
              alignItems: "center",
              marginTop: "16px",
              padding: "12px",
              backgroundColor: "#fef3c7",
              border: "1px solid #f59e0b",
              borderRadius: "8px"
            }}>
              <Spinner isLoading size="small" />
              <span style={{ marginLeft: "12px", fontSize: "14px", color: "#92400e" }}>
                {t("Processing carrier file...")}
              </span>
            </div>
          )}

          {fileUploadState.success && (
            <div style={{
              display: "flex",
              alignItems: "center",
              marginTop: "16px",
              padding: "12px",
              backgroundColor: "#d1fae5",
              border: "1px solid #10b981",
              borderRadius: "8px"
            }}>
              <div style={{ marginRight: "8px" }}>
                <CheckIcon fill="#10b981" />
              </div>
              <div>
                <div style={{ fontSize: "14px", fontWeight: "500", color: "#065f46" }}>
                  {t("File processed successfully!")}
                </div>
                <div style={{ fontSize: "12px", color: "#047857", marginTop: "2px" }}>
                  {t("Carrier list has been uploaded and is ready for survey invitations")}
                </div>
              </div>
            </div>
          )}

          {fileUploadState.error && (
            <div style={{
              display: "flex",
              alignItems: "center",
              marginTop: "16px",
              padding: "12px",
              backgroundColor: "#fee2e2",
              border: "1px solid #ef4444",
              borderRadius: "8px"
            }}>
              <div style={{ marginRight: "8px" }}>
                <AlertCircleIcon fill="#ef4444" />
              </div>
              <div>
                <div style={{ fontSize: "14px", fontWeight: "500", color: "#991b1b" }}>
                  {t("Upload failed")}
                </div>
                <div style={{ fontSize: "12px", color: "#dc2626", marginTop: "2px" }}>
                  {fileUploadState.error}
                </div>
              </div>
            </div>
          )}

          {/* CSV Preview Section - Now appears right after file selection */}
          {fileUploadState.fileName && (
            <div style={{ marginTop: "16px" }}>
              {!fileUploadState.preview && fileUploadState.fileName.endsWith('.csv') && (
                <div style={{
                  padding: "12px",
                  backgroundColor: "#f3f4f6",
                  borderRadius: "8px",
                  fontSize: "13px",
                  color: "#6b7280",
                  textAlign: "center"
                }}>
                  {t("Processing CSV file for preview...")}
                </div>
              )}

              {!fileUploadState.preview && !fileUploadState.fileName.endsWith('.csv') && (
                <div style={{
                  padding: "12px",
                  backgroundColor: "#fef3c7",
                  border: "1px solid #f59e0b",
                  borderRadius: "8px",
                  fontSize: "13px",
                  color: "#92400e",
                  textAlign: "center"
                }}>
                  {t("File preview is only available for CSV files")}
                </div>
              )}

              {fileUploadState.preview && fileUploadState.preview.length > 0 && (
                <div>
                  <div style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    marginBottom: "12px",
                    padding: "8px 12px",
                    backgroundColor: "#ecfdf5",
                    border: "1px solid #10b981",
                    borderRadius: "6px"
                  }}>
                    <div style={{ display: "flex", alignItems: "center" }}>
                      <CheckIcon fill="#10b981" size="16px" />
                      <span style={{
                        marginLeft: "8px",
                        fontSize: "13px",
                        fontWeight: "500",
                        color: "#065f46"
                      }}>
                        {t("File validated successfully - {{count}} carriers found", {
                          count: fileUploadState.preview.length
                        })}
                      </span>
                    </div>

                    <button
                      onClick={() => setIsPreviewExpanded(!isPreviewExpanded)}
                      style={{
                        background: "none",
                        border: "none",
                        cursor: "pointer",
                        display: "flex",
                        alignItems: "center",
                        color: "#065f46",
                        fontSize: "12px",
                        fontWeight: "500"
                      }}
                    >
                      <span style={{ marginRight: "4px" }}>
                        {isPreviewExpanded ? t("Hide Preview") : t("Show Preview")}
                      </span>
                      {isPreviewExpanded ? (
                        <ChevronUpIcon fill="#065f46" size="16px" />
                      ) : (
                        <ChevronDownIcon fill="#065f46" size="16px" />
                      )}
                    </button>
                  </div>

                  {isPreviewExpanded && (
                    <div className={styles.csvPreviewTable}>
                      <div style={{
                        fontSize: "14px",
                        fontWeight: "500",
                        marginBottom: "8px",
                        color: "#374151"
                      }}>
                        {t("Preview (First 10 rows)")}
                      </div>
                      <div className={styles.csvTableWrapper}>
                        <table className={styles.csvTable}>
                          <thead className={styles.csvTableHeader}>
                            <tr>
                              {fileUploadState.preview.length > 0 && Object.keys(fileUploadState.preview[0]).map((header, index) => (
                                <th key={index} className={styles.csvTableHeaderCell} style={{
                                  borderRight: index < Object.keys(fileUploadState.preview![0]).length - 1 ? "1px solid #e5e7eb" : "none"
                                }}>
                                  {header}
                                </th>
                              ))}
                            </tr>
                          </thead>
                          <tbody>
                            {fileUploadState.preview.map((row, rowIndex) => (
                              <tr key={rowIndex} className={styles.csvTableRow}>
                                {Object.values(row).map((cell: any, cellIndex) => (
                                  <td key={cellIndex} className={styles.csvTableCell} style={{
                                    borderBottom: rowIndex < fileUploadState.preview!.length - 1 ? "1px solid #e5e7eb" : "none",
                                    borderRight: cellIndex < Object.values(row).length - 1 ? "1px solid #e5e7eb" : "none"
                                  }}>
                                    {cell || '-'}
                                  </td>
                                ))}
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                      <div className={styles.csvPreviewFooter}>
                        {t("{{fileName}} contains {{count}} carriers ready to upload", {
                          count: fileUploadState.preview.length,
                          fileName: fileUploadState.fileName
                        })}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Divider between bulk upload and email template */}
        <div style={{
          margin: "32px 0",
          borderTop: "1px solid #e5e7eb",
          paddingTop: "24px"
        }}>
          <h3 style={{
            fontSize: "16px",
            fontWeight: "600",
            marginBottom: "8px",
            color: "#1f2937"
          }}>
            {t("Email Template for Carrier Outreach")}
          </h3>
          <p style={{
            fontSize: "14px",
            color: "#6b7280",
            marginBottom: "16px"
          }}>
            {t("After uploading your carrier list, use this email template to personally reach out to carriers.")}
          </p>
        </div>

        <div className={styles.yellowWarningBox}>
          <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <MailIcon fill="#ffbf0a" />
            <div className={styles.bold}>{t("How to Use This Email Template")}</div>
          </div>
          <div>{t("1. Copy each section using the dedicated Copy buttons below")}</div>
          <div>{t("2. Open your email client and compose a new email to your carrier")}</div>
          <div>{t("3. Paste the copied content and replace SURVEY_LINK with the actual survey link")}</div>
          <div>
            <span className={styles.bold}>{t("4. Important")}</span>
            {t(": <NAME_EMAIL> to the CC field")}
          </div>
        </div>

        {renderCopySection(t("CC Email Address"), COPY_TEXTS.ccEmail, "cc")}
        {renderCopySection(t("Subject Line"), COPY_TEXTS.subject, "subject")}
        {renderCopySection(t("Survey Link"), COPY_TEXTS.surveyLink, "link", Link1Icon, "SURVEY_LINK", true)}
        {renderCopySection(t("Email Body"), COPY_TEXTS.emailBody, "body")}
      </div>
    </Modal>
  );
};

export default CarrierInvite;
