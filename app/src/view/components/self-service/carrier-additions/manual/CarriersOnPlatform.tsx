import { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { XIcon } from "@fourkites/elemental-atoms";
import { Table } from "@fourkites/elemental-table";

import MultiInput from "view/components/base/multi-input/MultiInput";
import IdentificationsIndicator from "view/components/base/company-identifications/IdentificationsIndicator";

import {
  getCompanyAddress,
  MAX_CARRIER_CODES,
  MAX_CARRIER_CODE_LENGTH,
} from "view/components/self-service/company-management/CompanyManagementUtils";

import styles from "./ManualCarrierAdditions.module.scss";

const CarriersOnPlatform = ({
  data,
  onRemoveSelectedAddition,
  onUpdateSeletedAddition,
}: any) => {
  const { t } = useTranslation();

  const onAddCarrierCode = (index: number, code: string) => {
    const addition = data[index];
    const updatedAddition = {
      ...addition,
      carrier_codes: [...(addition?.carrier_codes || []), code],
    };

    onUpdateSeletedAddition(index, updatedAddition);
  };

  const onRemoveCarrierCode = (index: number, code: string) => {
    const addition = data[index];
    const updatedAddition = {
      ...addition,
      carrier_codes:
        addition?.carrier_codes?.filter((c: any) => c !== code) || [],
    };

    onUpdateSeletedAddition(index, updatedAddition);
  };

  const multiInputValidation = {
    maxNumberOfValues: MAX_CARRIER_CODES,
    maxValueLength: MAX_CARRIER_CODE_LENGTH,
  };

  const columns = useMemo(() => {
    return [
      {
        Header: t("Carrier Name"),
        accessor: "name",
      },
      {
        Header: t("USDOT#"),
        id: "usdot",
        accessor: "identifications",
        Cell: (c: any) => {
          return (
            <IdentificationsIndicator
              type={"usdot"}
              label={"DOT"}
              identifications={c?.cell?.value}
            />
          );
        },
      },
      {
        Header: t("MC#"),
        id: "mc",
        accessor: "identifications",
        Cell: (c: any) => {
          return (
            <IdentificationsIndicator
              type={"mc"}
              label={"MC"}
              identifications={c?.cell?.value}
            />
          );
        },
      },
      {
        Header: t("NMFTA SCAC"),
        id: "scac",
        accessor: "identifications",
        Cell: (c: any) => {
          return (
            <IdentificationsIndicator
              type={"scac"}
              label={"NMFTA SCAC"}
              identifications={c?.cell?.value}
            />
          );
        },
      },
      {
        Header: t("Carrier Codes"),
        accessor: "carrier_codes",
        Cell: (c: any) => {
          const rowIndex = c.row.index;
          const carrierCodesData = data[rowIndex]?.carrier_codes
            ? data[rowIndex]?.carrier_codes
            : [];
          const hasNoData = carrierCodesData?.length === 0;

          return (
            <div className={styles.carrierCodesWrapper}>
              <MultiInput
                size="small"
                placeholder={t("Enter carrier code")}
                errorLabel={t("Atleast one carrier code is required")}
                values={data[rowIndex]?.carrier_codes || []}
                defaultValues={[]}
                disabled={false}
                checkable
                required={hasNoData}
                onAddValue={(code: string) => onAddCarrierCode(rowIndex, code)}
                onRemoveValue={(code: string) =>
                  onRemoveCarrierCode(rowIndex, code)
                }
                validation={multiInputValidation}
              />
            </div>
          );
        },
      },
      {
        Header: t("Address"),
        accessor: "address",
        Cell: (c: any) => getCompanyAddress({ address: c.cell.value }),
      },
      {
        Header: "",
        accessor: "remove",
        Cell: (c: any) => (
          <span
            id="remove"
            onClick={() => {
              onRemoveSelectedAddition(c.row.original);
            }}
          >
            <XIcon fill="#adb5bd" iconClass={styles.removeIcon} />
          </span>
        ),
      },
    ];
  }, [onRemoveSelectedAddition, onUpdateSeletedAddition]);

  const tableData = useMemo(() => {
    return data;
  }, [data]);
  const paginationParams = {
    paginated: false,
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div className={styles.tableContainer}>
      <Table
        columns={columns}
        data={tableData}
        rowHeight="small"
        striped
        pagination={paginationParams}
      />
    </div>
  );
};

export default CarriersOnPlatform;
