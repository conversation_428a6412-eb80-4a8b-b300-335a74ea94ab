@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.header {
  display: flex;
  justify-content: space-between;
  padding-left: 10%;
  padding-right: 10%;
  padding-top: 24px;

  > div[id="title-container"] {
    display: flex;
    flex-direction: column;
    padding: 0;

    > h5 {
      margin: 0;
      font-size: 24px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 36px;
      margin-block-start: 0;
      margin-block-end: 0;
      color: color-neutral-900;
    }

    > span {
      font-size: 14px;
      letter-spacing: 0;
      color: $color-neutral-900;
      font-size: 18px;
      line-height: 27px;
    }
  }

  > div[id="right"] {
    display: flex;
    align-content: center;
    align-items: center;

    > label {
      color: $color-neutral-900;
      font-size: 16px;
      letter-spacing: 0;
      line-height: 24px;
    }

    > button {
      margin-right: 16px;
      margin-left: 16px;
    }

    > button[id="close"] {
      margin-left: 0;
      cursor: pointer;
      display: flex;
      align-items: center;
      background: none;
      border: none;

      &:hover svg {
        border-radius: 5px;
        background: $color-neutral-200;
      }
    }
  }
}

.closeIcon {
  width: 40px;
  height: 40px;
}

.container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.content {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.tabsWrapper {
  max-height: calc(90vh + -284px);
  padding-top: 100px;
  width: 80%;
  position: absolute;
  overflow-y: auto;

  > div > div > ul {
    background-color: transparent;
  }
}

.companySuggestionsWrapper {
  z-index: 100;
  position: absolute;
  padding-top: 16px;
  margin-left: -5px;
  background-color: white;
  width: 100%;
  height: 72px;
}

.tableContainer {
  height: calc(100vh - 433px);

  > div > div {
    overflow-x: auto;
    overflow-y: auto;

    > table {
      > thead > tr > th {
        padding-top: 6px;
        padding-bottom: 6px;
      }
      > tbody > tr > td {
        padding-top: 6px;
        padding-bottom: 6px;
      }
    }
  }
}

.carrierCodesWrapper {
  max-width: 500px;
}

.removeIcon {
  height: 28px;
  width: 28px;
}

.suggestionsContainer {
  height: 800px;
  overflow-y: auto;
}

.invitationsContainer {
  height: 800px;
  overflow-y: auto;
}

.selectedSuggestionContainer {
  display: flex;
  width: 1010px;
  margin-bottom: 16px;
  padding: 8px 16px 8px 16px;
  background-color: white;
  box-sizing: border-box;
  border: 1px solid #cfd4da;
  border-radius: 4px;

  > span[id="avalability-status"] {
    display: flex;
    align-content: center;
    align-items: center;
    height: fit-content;
    width: 35%;
    font: $typography-standard-bold;
    font-size: 14px;
    line-height: 21px;
    color: $color-accent-mint-500;
    margin-top: 4px;

    > label {
      margin-left: 8px;
    }
  }

  > span[id="remove"] {
    display: flex;
    align-content: center;
    align-items: center;
    cursor: pointer;
  }
}
