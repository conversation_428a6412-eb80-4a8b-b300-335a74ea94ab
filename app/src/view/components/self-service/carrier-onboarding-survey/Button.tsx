import React from 'react';
import styles from './WelcomeScreen.module.css';

interface ButtonProps {
  variant: 'outline' | 'solid';
  children: React.ReactNode;
  className?: string;
  icon?: string;
  onClick?: () => void;
}

const Button: React.FC<ButtonProps> = ({ variant, children, className, icon, onClick }) => {
  const buttonClass = variant === 'outline' ? styles.outlineButton : styles.solidButton;

  return (
    <button className={`${buttonClass} ${className || ''}`} onClick={onClick}>
      <span className={styles.buttonContent}>
        <span className={styles.buttonLabel}>{children}</span>
        {icon && <img src={icon} alt="" className={styles.buttonIcon} />}
      </span>
    </button>
  );
};

export default Button;