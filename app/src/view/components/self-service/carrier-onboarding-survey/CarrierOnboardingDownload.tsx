// ========================================
// CARRIER ONBOARDING EXCEL DOWNLOAD COMPONENT
// ========================================
import React, { useState } from 'react';
import { Button, DownloadIcon } from "@fourkites/elemental-atoms";
import carriersNetworkApi from "api/shipper/CarriersNetworkApi";

// ========================================
// TYPES
// ========================================
interface CarrierOnboardingData {
  date: string;
  invited: number;
  surveyCompleted: number;
  surveyViaIvr: number;
  surveyViaForm: number;
  registrationCompleted: number;
  salesforceSuccess: number;
}

interface ApiResponse {
  Success: boolean;
  data: CarrierOnboardingData[];
  metadata?: {
    shipperPermalink: string;
    generatedAt: string;
    totalRecords: number;
  };
  error?: string;
}

// ========================================
// DOWNLOAD SERVICE
// ========================================
class CarrierOnboardingDownloadService {
  async fetchData(shipperPermalink: string): Promise<CarrierOnboardingData[]> {
    try {
      const data = await carriersNetworkApi.getCarrierOnboardingData(shipperPermalink);
      return data || [];
    } catch (error) {
      console.error('Error fetching carrier onboarding data:', error);
      return [];
    }
  }

  generateSimpleExcel(data: CarrierOnboardingData[]): string {
    const content = [
      // UTF-8 BOM for proper Excel encoding
      '\uFEFF',
      
      // Headers (tab-separated for Excel)
      'Date\tInvited\tSurvey_Completed\tSurvey_via_IVR\tSurvey_via_Form\tRegistration_Completed\tSalesforce_Created',
      
      // Data rows
      ...data.map(row => [
        row.date,
        row.invited,
        row.surveyCompleted,
        row.surveyViaIvr,
        row.surveyViaForm,
        row.registrationCompleted,
        row.salesforceSuccess
      ].join('\t'))
    ];
    
    return content.join('\n');
  }

  downloadFile(content: string, filename: string): void {
    const blob = new Blob([content], { 
      type: 'application/vnd.ms-excel;charset=utf-8;' 
    });
    
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.style.display = 'none';
    
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  }
}

// ========================================
// REACT COMPONENT
// ========================================
interface CarrierOnboardingDownloadProps {
  shipperPermalink: string;
  buttonText?: string;
  className?: string;
  disabled?: boolean;
}

const CarrierOnboardingDownload: React.FC<CarrierOnboardingDownloadProps> = ({
  shipperPermalink,
  buttonText = "Download Excel",
  className = "",
  disabled = false
}) => {
  const [isDownloading, setIsDownloading] = useState(false);
  const downloadService = new CarrierOnboardingDownloadService();

  const handleDownload = async () => {
    if (isDownloading || disabled) return;

    setIsDownloading(true);
    
    try {
      const data = await downloadService.fetchData(shipperPermalink);
      const excelContent = downloadService.generateSimpleExcel(data);
      const filename = `carrier_onboarding_${shipperPermalink}_${new Date().toISOString().split('T')[0]}.xls`;
      downloadService.downloadFile(excelContent, filename);
    } catch (error) {
      console.error('Download failed:', error);
      alert('Download failed. Please try again.');
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <Button
      theme="secondary"
      size="medium"
      onClick={handleDownload}
      disabled={disabled || isDownloading}
      className={className}
      data-testid="download-carrier-onboarding-button"
    >
      <DownloadIcon fill="#000" iconClass="button-icon-left" />
      {isDownloading ? 'Downloading...' : buttonText}
    </Button>
  );
};

// ========================================
// HOOK VERSION (Alternative)
// ========================================
export const useCarrierOnboardingDownload = (shipperPermalink: string) => {
  const [isDownloading, setIsDownloading] = useState(false);
  const downloadService = new CarrierOnboardingDownloadService();

  const downloadExcel = async () => {
    if (isDownloading) return;

    setIsDownloading(true);
    
    try {
      const data = await downloadService.fetchData(shipperPermalink);
      const excelContent = downloadService.generateSimpleExcel(data);
      const filename = `carrier_onboarding_${shipperPermalink}_${new Date().toISOString().split('T')[0]}.xls`;
      downloadService.downloadFile(excelContent, filename);
    } catch (error) {
      console.error('Download failed:', error);
      throw error;
    } finally {
      setIsDownloading(false);
    }
  };

  return { downloadExcel, isDownloading };
};

// ========================================
// EXPORT
// ========================================
export default CarrierOnboardingDownload;