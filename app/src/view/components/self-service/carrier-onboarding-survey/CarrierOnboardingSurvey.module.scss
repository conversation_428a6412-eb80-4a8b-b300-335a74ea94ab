/* WelcomeScreen.module.css - Fixed Version */

/* Import your existing color variables */
@import "@fourkites/elemental-atoms/build/scss/colors/index";

/* Page Container - Fixed positioning issues */
.pageContainer {
  display: flex;
  min-height: 100vh;
  flex-direction: column;
}

.container {
  display: flex;
  width: 100%;
  flex-direction: column;
  background-color: $color-neutral-50;
  min-height: 100vh; /* Ensure full height */
}

.content {
  width: 100%;
  background-color: $color-neutral-00;
  flex: 1; /* Take remaining space */
}

.exportButton {
  position: absolute;
  right: 24px;
  top: 128px;
  z-index: 1;
}

.loading {
  display: flex;
  width: 100%;
  margin-top: 32px;
  justify-content: center;
}

/* Header - Fixed spacing */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center; /* Better alignment */
  padding: 16px 24px;
  background-color: $color-neutral-00;
  gap: 24px; /* Reduced from 200px for better responsiveness */
  flex-wrap: wrap; /* Allow wrapping on smaller screens */
}

.onboardingText {
  display: flex;
  flex-direction: column;
  gap: 8px; /* Better spacing */

  .title {
    font-size: 24px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 36px;
    color: $color-neutral-900;
    margin: 0;
  }

  .subtitle {
    font-size: 16px;
    color: $color-neutral-700;
    margin: 0;
  }
}

/* Stepper - Improved responsive design */
.stepper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
  max-width: 600px; /* Set reasonable max-width */
  min-width: 300px; /* Prevent too narrow on small screens */

  .stepContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    position: relative;

    &:not(:last-child)::after {
      content: "";
      position: absolute;
      right: -50%;
      width: 100%;
      height: 1px;
      background-color: $color-neutral-300;
      z-index: 0;
    }

    .oval {
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      height: 32px;
      width: 32px;
      color: $color-neutral-600;
      border: 2px solid $color-neutral-400;
      border-radius: 50%;
      font-size: 16px;
      font-weight: bold;
      background-color: $color-neutral-00;
      z-index: 1;
      position: relative;
    }
  }

  .currentOvalSelected {
    background-color: $color-primary-500;
    border: 2px solid $color-primary-500;
    color: $color-neutral-00;
  }

  .ovalCompleted {
    background-color: $color-primary-100;
    border: 2px solid $color-primary-500;
    color: $color-primary-500;
  }
}

/* Form Container - Better centering and spacing */
.formContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start; /* Changed from center */
  margin: 32px auto 120px; /* Added bottom margin for footer space */
  padding: 24px;
  background-color: $color-neutral-00;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  max-width: 800px;
  width: 100%;
  min-height: 600px; /* Minimum height instead of fixed */
  
  .additionalQuestions {
    display: flex;
    gap: 16px;
    width: 100%;
    margin-top: 24px;

    > div {
      flex: 1;
    }
  }
}

.question {
  margin-bottom: 24px;
  width: 100%;

  .questionText {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 12px;
    color: $color-neutral-900;
  }

  .contextText {
    font-size: 14px;
    margin-bottom: 16px;
    color: $color-neutral-700;
    line-height: 1.5;
  }

  .options {
    display: flex;
    flex-direction: column;
    gap: 8px;

    input[type="radio"] {
      margin-right: 8px;
    }

    label {
      display: flex;
      align-items: center;
      font-size: 16px;
      color: $color-neutral-700;
      cursor: pointer;
      padding: 8px;
      border-radius: 4px;
      transition: background-color 0.2s;

      &:hover {
        background-color: $color-neutral-50;
      }
    }
  }
}

.mainContent {
  flex: 1;
  padding-bottom: 80px; /* Space for footer */
}

/* Footer - FIXED: Removed position fixed */
.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: $color-neutral-00;
  border-top: 1px solid $color-neutral-200;
  margin-top: auto; /* Push to bottom */
  position: relative; /* Changed from fixed */
  width: 100%;
  box-sizing: border-box;

  .footerLink {
    color: $color-primary-500;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.2s;

    &:hover {
      color: $color-primary-600;
    }
  }

  .footerButtons {
    display: flex;
    gap: 16px;

    .footerButton {
      min-width: 100px;
    }
  }
}

/* Welcome Screen Specific Styles - Completely rewritten */
.welcomeContainer {
  min-height: 100vh;
  background: linear-gradient(135deg, #eff6ff 0%, #e0e7ff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.welcomeCard {
  max-width: 64rem;
  width: 100%;
  background: white;
  border-radius: 16px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

/* Header Section */
.header {
  text-align: center;
  padding: 48px 32px 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.iconContainer {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #2563eb 0%, #4338ca 100%);
  border-radius: 50%;
  margin-bottom: 24px;
  box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
}

.iconInner {
  width: 48px;
  height: 48px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon {
  width: 24px;
  height: 24px;
  color: #2563eb;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
  line-height: 1.2;
}

.subtitle {
  font-size: 1.25rem;
  color: #6b7280;
  margin: 0;
}

/* Content Section */
.content {
  padding: 32px;
}

.welcomeTitle {
  text-align: center;
  margin-bottom: 32px;
}

.welcomeHeading {
  font-size: 2rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
}

.welcomeDescription {
  color: #4b5563;
  font-size: 1.125rem;
  line-height: 1.7;
  max-width: 48rem;
  margin: 0 auto;
}

.brandName {
  font-weight: 600;
  color: #2563eb;
}

/* Benefits Grid */
.benefitsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

.benefitCard {
  background: #f9fafb;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  border: 1px solid #f3f4f6;
  transition: all 0.2s;
}

.benefitCard:hover {
  background: #f3f4f6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.benefitIcon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 12px;
}

.benefitIcon.green {
  background: #dcfce7;
}

.benefitIcon.blue {
  background: #dbeafe;
}

.benefitIcon.purple {
  background: #f3e8ff;
}

.benefitIcon.orange {
  background: #fed7aa;
}

.benefitIconSvg {
  width: 20px;
  height: 20px;
}

.benefitIconSvg.green {
  color: #16a34a;
}

.benefitIconSvg.blue {
  color: #2563eb;
}

.benefitIconSvg.purple {
  color: #9333ea;
}

.benefitIconSvg.orange {
  color: #ea580c;
}

.benefitText {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin: 0;
}

/* Privacy Notice */
.privacyNotice {
  background: #eff6ff;
  border-left: 4px solid #60a5fa;
  border-radius: 0 8px 8px 0;
  padding: 20px;
  margin-bottom: 32px;
  display: flex;
  align-items: flex-start;
}

.privacyIcon {
  flex-shrink: 0;
  margin-right: 12px;
  margin-top: 2px;
}

.privacyIcon {
  width: 20px;
  height: 20px;
  color: #60a5fa;
}

.privacyContent {
  flex: 1;
}

.privacyTitle {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1e40af;
  margin-bottom: 6px;
}

.privacyText {
  font-size: 0.875rem;
  color: #1d4ed8;
  margin: 0;
  line-height: 1.5;
}

.privacyLink {
  color: #1d4ed8;
  text-decoration: underline;
  margin-left: 4px;
  transition: color 0.2s;
}

.privacyLink:hover {
  color: #1e40af;
}

/* CTA Section */
.ctaSection {
  text-align: center;
}

.startButton {
  background: linear-gradient(135deg, #2563eb 0%, #4338ca 100%);
  color: white;
  font-weight: 600;
  font-size: 1.125rem;
  padding: 16px 32px;
  border-radius: 12px;
  border: none;
  cursor: pointer;
  box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.startButton:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #3730a3 100%);
  box-shadow: 0 15px 30px rgba(37, 99, 235, 0.4);
  transform: translateY(-2px);
}

.startButton:focus {
  outline: none;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3);
}

.buttonIcon {
  width: 20px;
  height: 20px;
  margin-left: 8px;
}

.timeIndicator {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 12px;
}

/* Footer */
.footer {
  text-align: center;
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.footerText {
  font-size: 0.75rem;
  color: #9ca3af;
  margin: 0;
}

/* Thank You Page */
.thankYouPage {
  text-align: center;
  padding: 64px 32px;
}

.thankYouPage h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #10b981;
  margin-bottom: 16px;
}

.thankYouPage p {
  font-size: 1.125rem;
  color: #6b7280;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .stepper {
    max-width: 100%;
    margin-top: 16px;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .welcomeHeading {
    font-size: 1.75rem;
  }
}

@media (max-width: 768px) {
  .welcomeContainer {
    padding: 16px;
  }
  
  .welcomeCard {
    border-radius: 12px;
  }
  
  .header {
    padding: 32px 24px 16px;
  }
  
  .content {
    padding: 24px;
  }
  
  .title {
    font-size: 1.75rem;
  }
  
  .welcomeHeading {
    font-size: 1.5rem;
  }
  
  .welcomeDescription {
    font-size: 1rem;
  }
  
  .benefitsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .benefitCard {
    padding: 16px;
  }
  
  .formContainer {
    margin: 16px;
    padding: 20px;
  }
  
  .footer {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .iconContainer {
    width: 64px;
    height: 64px;
    margin-bottom: 16px;
  }
  
  .iconInner {
    width: 36px;
    height: 36px;
  }
  
  .icon {
    width: 18px;
    height: 18px;
  }
  
  .title {
    font-size: 1.5rem;
  }
  
  .subtitle {
    font-size: 1rem;
  }
  
  .welcomeHeading {
    font-size: 1.25rem;
  }
  
  .benefitsGrid {
    grid-template-columns: 1fr;
  }
  
  .startButton {
    width: 100%;
    font-size: 1rem;
    padding: 14px 24px;
  }
  
  .formContainer {
    margin: 8px;
    padding: 16px;
  }
}
