import React from "react";
import { useTranslation } from "react-i18next";

import interrogationModalIcon from "assets/img/interrogationModalIcon.png";

import { Modal } from "@fourkites/elemental-modal";

import styles from "./ProviderCredentials.module.scss";

const ConfirmHelpRequestModal = ({
  show,
  onClose,
  onConfirm,
  helpRequestMessage,
  isMandatoryFieldsFilled,
  onTextChange,
}: any) => {
  const { t } = useTranslation();

  const title = t("Are you sure you want FourKites to reach out for support?");

  const textAreaClass = !isMandatoryFieldsFilled
    ? styles.mandatoryModalTextArea
    : styles.modalTextArea;

  return (
    <Modal
      size="medium"
      title={title}
      show={show}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: onClose,
      }}
      saveButtonProps={{
        label: t("I'm sure"),
        onClick: onConfirm,
        disabled: !isMandatoryFieldsFilled,
      }}
    >
      <div className={styles.modalWrapper}>
        <img
          className={styles.logo}
          src={interrogationModalIcon}
          alt="Confirm Icon"
        />
        <div>
          <p>
            {t(
              "Please describe the issue you are experiencing, or the help " +
                "you would like to receive."
            )}
          </p>

          <p>
            {t(
              "Please ensure you have administrator-level access to your Telematics / GPS provider's " +
                "website, and that you have followed the instructions provided."
            )}
          </p>

          <p>
            {t(
              "A FourKites support representative will contact you via email."
            )}
          </p>

          <textarea
            id="provider-help-message-textarea"
            className={textAreaClass}
            placeholder={t(
              "For Example: I need help connecting my Telematics / GPS provider."
            )}
            value={helpRequestMessage}
            rows={5}
            onChange={(e: any) => onTextChange(e.target.value)}
          />
        </div>
      </div>
    </Modal>
  );
};

export default ConfirmHelpRequestModal;
