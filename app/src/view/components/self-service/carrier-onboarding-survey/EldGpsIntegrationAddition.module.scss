@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.container {
  display: flex;
  flex-direction: column;

  > h1 {
    display: flex;
    width: fit-content;
    align-items: center;
    font-size: 34px;
    letter-spacing: 0;
    line-height: 42px;
    text-align: center;
    margin-top: 12px;
    margin-bottom: 16px;

    > button {
      display: flex;
      align-items: center;
      cursor: pointer;
      margin-right: 16px;
      background-color: transparent;
      border: none;
      border-radius: 24px;

      &:hover {
        background-color: $color-neutral-100;
      }
    }
  }

  > label {
    width: 960px;
    color: #495057;
    font-size: 18px;
    letter-spacing: 0;
    line-height: 27px;
  }
}

.providerCredentialsWrapper {
  display: flex;
  gap: 16px;
  margin-top: 24px;
  width: 350px;
  .title {
    font-size: 20px;
    font-weight: 700;
  }
  .stepsInfo {
  font-size: 14px;
  font-weight: 400;
  color: #495057;
  }
  .divider {
    border-right: 1px solid #CFD4DA;
  }
}

.providersTabs {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  flex-direction: column;
  margin-top: 24px;
}

.providerTab {
  cursor: pointer;
  width: 320px;
  top: 295px;
  left: 80px;
  padding: 12px 16px;
  border-radius: 6px;
  border: 1px solid #CFD4DA;
  display: flex;
  justify-content: space-between;

  .status {
    font-size: 14px;
    display: inline-flex;
    gap: 4px;
    align-items: center;
  }
}

.providerTab.inProgress {
  border: 1px solid #0E65E5;
  background: #F3F7FE;
  color: #0B51B7;
  .status {
    color: #0E65E5;
  }
}

.providerTab.completed {
  border: 1px solid #33C54F;
  background: #F4FFF6;
  .status {
    color: #197132;
  }
}