import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

import { ArrowLeftIcon } from "@fourkites/elemental-atoms";

import ProviderSelection from "./ProviderSelection";
import ProviderCredentials from "./ProviderCredentials";
import {
  LockFillIcon,
  MenuMeatballFillIcon,
  SuccessInvertedColoredIcon
} from "@fourkites/elemental-atoms";

import stylesELD from "./EldGpsIntegrationAddition.module.scss";



import styles from "./CarrierOnboardingSurvey.module.scss";

interface EldGpsIntegrationAdditionProps {
  isModal: boolean;
  isExternallyUsed: boolean;
  onBack: () => void;
  handleTechnicalTeamContact:() => void;
  onNextStep: () => void;
  credentials: Credential[];
  setCredentials: (credentials: Credential[]) => void;
}

interface Credential {
  id: string; 
  value: any; 
}

// We need to create a page which will have three sections: header, content and footer. The header will have a title and a subtitle. The content will be dynamic. We will render three different components in the component section.
const EldGpsIntegrationAddition = ({
  isModal,
  isExternallyUsed,
  onBack,
  handleTechnicalTeamContact,
  onNextStep,
  credentials,
  setCredentials,
}: EldGpsIntegrationAdditionProps) => {
  const [selectedProviders, setselectedProviders] = useState<any>(null);
  const [provider, setProvider] = useState<any>(null);
  const [credential, setCredential] = useState<any>(null);
  const [providerType, setProviderType] = useState<"truck" | "trailer">(
    "truck"
  );
  const [assetNumber, setAssetNumber] = useState(undefined);
  const [providerCredentials, setProviderCredentials] = useState<any[]>([]);
  const [dsaFieldValues, setDsaFieldValues] = useState<any>({
    cid: "",
    name: "",
    position: "",
  });
  const { t } = useTranslation();


  useEffect(() => {
    if (provider == null) return;
    const providerKey = provider.id;  // assuming provider.id is the identifier you're using
    const indexToUpdate = credentials.findIndex((cred: Credential) => cred.hasOwnProperty(providerKey));
    setCredential(credentials[indexToUpdate]);
  }, [provider]);

  
  const onSelectProviders = (providers: any) => {
    setselectedProviders(providers);
    // Create an array of empty JSON objects, one for each provider
    if(credentials==null) {
      const credentials = providers.map((provider: any) => ({ [provider.id]: {} }));
      setCredentials(credentials);
    }
    setProvider(providers[0]);
  };


  const onClickBack = () => {
    //TODO: close tutorial
    if (!provider) {
      if (onBack) onBack();
    } else {
      setProvider(null);
    }
  };
  const showBackArrow = (onBack != null && !isModal) || provider != null;
  return (
    <div className={styles.pageContainer}>
      <div className={styles.container}>
        <div className={styles.header}>
          <div className={styles.onboardingText}>
            <span className={styles.title}>Carrier Onboarding</span>
            <span className={styles.subtitle}>
              Add Location Providers
            </span>
          </div>
          <Stepper
            steps={["Step One", "Step Two", "Step Three", "Step Four", "Step Five"]}
            currentStep={3}
          />
        </div>
        <div className={styles.content}>
          
    <div
      className={stylesELD.container}
      id="telematics-gps-integration-addition-container"
      data-test-id="telematics-gps-integration-additions-modal"
    >
      <h1>
        {showBackArrow && (
          <button onClick={onClickBack}>
            <ArrowLeftIcon />
          </button>
        )}

        {provider
          ? t("Authenticate")
          : t("Search & choose your location provider")}
      </h1>

      {/* */}

      {provider ? (
        <div className={stylesELD.providerCredentialsWrapper}>
          <div>
          <p className={stylesELD.title}>Authenticate your selected Telematics / GPS service providers</p>
          <div className={stylesELD.stepsInfo}></div>
          <EldProviderTabs providers={selectedProviders} onSelectProvider={setProvider}/>
          </div>
          <div className={stylesELD.divider}></div>
          <ProviderCredentials
            isCreating={true}
            isModal={isModal}
            isExternallyUsed={isExternallyUsed}
            provider={provider}
            credentials={credentials}
            setProvider={setProvider}
            setCredentials={setCredentials}
            handleTechnicalTeamContact={handleTechnicalTeamContact}
            onNextStep={onNextStep}
            providerType={providerType}
            setProviderType={setProviderType}
            assetNumber={assetNumber}
            setAssetNumber={setAssetNumber}
            dsaFieldValues={dsaFieldValues}
            setDsaFieldValues={setDsaFieldValues}
            providerCredentials={providerCredentials}
            setProviderCredentials={setProviderCredentials}
          />
        </div>
      ) : (
        <ProviderSelection
          isModal={isModal}
          onSelectProviders={onSelectProviders}
          isExternallyUsed={isExternallyUsed}
          onBack={onBack}
          handleTechnicalTeamContact={handleTechnicalTeamContact}
        />
      )}
    </div>
</div>
        

        
      </div>
    </div>
  );
};

//Create a stepper component containing n steps. It should be configurable to show the current step and the steps that have been completed. The component should be able to receive a list of steps and the current step as props.
// The component should have a prop to receive the current step and a prop to receive the list of steps.
// The completed steps should also have styles, i.e. should be highlighted.
export const Stepper = ({
  steps,
  currentStep,
}: {
  steps: string[];
  currentStep: number;
}) => {
  return (
    <div className={styles.stepper}>
      {steps.map((step, index) => (
        <div className={styles.stepContainer}>
          <div
            key={index}
            className={`${styles.step} ${
              index === currentStep ? styles.currentStep : ""
            }`}
          >
            <div
              className={`${styles.oval} ${
                index === currentStep ? styles.currentOvalSelected : ""
              } ${index < currentStep ? styles.ovalCompleted : ""}
              `}
            >
              {index + 1}
            </div>
            <div
              className={`${styles.stepLabel} ${
                index === currentStep ? styles.currentStepLabel : ""
              }`}
            >
              {step}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};




const STATUS_VS_ICON = {
  pending: <LockFillIcon size="14"/>,
  completed: <SuccessInvertedColoredIcon size="14" />,
  error: <MenuMeatballFillIcon size="14" />,
};

const EldProviderTabs = ({providers, onSelectProvider}: {providers: any[], onSelectProvider: (provider: any) => void}) => {
  return (
    <div className={stylesELD.providersTabs}>
      {providers?.map((provider: any) => (
        <button className={`${stylesELD.providerTab} ${stylesELD.completed}`} key={provider.id} onClick={() => onSelectProvider(provider)}>
          <span>{provider.name}</span>
          <span className={stylesELD.status}>
            
            
          </span>
        </button>
      ))}
    </div>
  );
}


export default EldGpsIntegrationAddition;
