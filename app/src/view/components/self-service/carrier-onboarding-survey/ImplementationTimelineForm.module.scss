// ImplementationTimelineForm.module.scss
// Working version without mixin errors

// Variables
$primary-color: #2563eb;
$secondary-color: #64748b;
$success-color: #10b981;
$error-color: #ef4444;
$warning-color: #f59e0b;
$background-color: #f8fafc;
$white: #ffffff;
$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;

$border-radius: 8px;
$border-radius-lg: 12px;
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

// Page Container
.pageContainer {
  min-height: 100vh;
  background-color: $background-color;
  padding: 2rem 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;

  @media (max-width: 768px) {
    padding: 1rem 0.5rem;
  }
}

// Main Container - Card design with proper height
.container {
  background-color: $white;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-lg;
  width: 100%;
  max-width: 800px;
  height: 85vh;
  max-height: 800px;
  min-height: 600px;
  display: grid;
  grid-template-rows: auto 1fr auto;
  overflow: hidden;

  @media (max-width: 768px) {
    max-width: 100%;
    height: 100vh;
    max-height: 100vh;
    border-radius: $border-radius;
    box-shadow: $shadow-md;
  }

  @media (max-width: 640px) {
    border-radius: 0;
    box-shadow: none;
    height: 100vh;
  }
}

// Header Section - Blue gradient
.header {
  background: linear-gradient(135deg, $primary-color 0%, #1d4ed8 100%);
  color: $white;
  padding: 1.5rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 1rem;

  @media (max-width: 768px) {
    padding: 1rem 1.5rem;
    flex-direction: column;
    align-items: flex-start;
  }
}

.onboardingText {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.title {
  font-size: 1.875rem;
  font-weight: 700;
  margin: 0;
  line-height: 1.2;

  @media (max-width: 768px) {
    font-size: 1.5rem;
  }
}

.subtitle {
  font-size: 1rem;
  font-weight: 400;
  opacity: 0.9;
  margin: 0;

  @media (max-width: 768px) {
    font-size: 0.875rem;
  }
}

// Stepper Component
.stepper {
  display: flex;
  align-items: center;
  gap: 1rem;

  @media (max-width: 768px) {
    gap: 0.5rem;
    flex-wrap: wrap;
  }
}

.stepContainer {
  display: flex;
  align-items: center;
  position: relative;

  &:not(:last-child)::after {
    content: '';
    position: absolute;
    right: -0.75rem;
    top: 50%;
    transform: translateY(-50%);
    width: 0.5rem;
    height: 2px;
    background-color: rgba(255, 255, 255, 0.3);

    @media (max-width: 768px) {
      display: none;
    }
  }
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.oval {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  color: $white;
  font-size: 0.875rem;
  font-weight: 600;
  transition: all 0.2s ease-in-out;

  @media (max-width: 768px) {
    width: 1.75rem;
    height: 1.75rem;
    font-size: 0.75rem;
  }
}

.currentOvalSelected {
  background-color: $white;
  color: $primary-color;
  box-shadow: $shadow-sm;
}

.ovalCompleted {
  background-color: $success-color;
  color: $white;
}

.stepLabel {
  font-size: 0.75rem;
  font-weight: 500;
  opacity: 0.8;
  text-align: center;
  white-space: nowrap;

  @media (max-width: 768px) {
    font-size: 0.625rem;
  }
}

.currentStepLabel {
  opacity: 1;
  font-weight: 600;
}

// Content Section with proper scrolling
.content {
  grid-row: 2;
  padding: 2rem;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0; // Critical for grid scrolling

  // Enhanced scrollbar styling
  &::-webkit-scrollbar {
    width: 10px;
  }

  &::-webkit-scrollbar-track {
    background: $gray-100;
    border-radius: 5px;
  }

  &::-webkit-scrollbar-thumb {
    background: $gray-400;
    border-radius: 5px;
    
    &:hover {
      background: $gray-500;
    }
  }

  // Firefox scrollbar
  scrollbar-width: thin;
  scrollbar-color: $gray-400 $gray-100;

  @media (max-width: 768px) {
    padding: 1.5rem;
    
    &::-webkit-scrollbar {
      width: 8px;
    }
  }

  @media (max-width: 640px) {
    padding: 1rem;
    
    &::-webkit-scrollbar {
      width: 6px;
    }
  }
}

// Footer Section - Fixed at bottom
.footer {
  background-color: $gray-50;
  border-top: 1px solid $gray-200;
  padding: 1.25rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 1rem;
  grid-row: 3;
  flex-shrink: 0;

  @media (max-width: 768px) {
    padding: 1rem 1.5rem;
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  @media (max-width: 640px) {
    padding: 0.75rem 1rem;
  }
}

.footerLink {
  color: $primary-color;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.2s ease-in-out;

  &:hover {
    color: #1d4ed8;
    text-decoration: underline;
  }

  @media (max-width: 768px) {
    text-align: center;
  }
}

.footerButtons {
  display: flex;
  gap: 1rem;
  align-items: center;

  @media (max-width: 768px) {
    width: 100%;
    justify-content: space-between;
  }
}

.footerButton {
  min-width: 100px;
  font-weight: 500;
  transition: all 0.2s ease-in-out;

  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: $shadow-md;
  }

  @media (max-width: 768px) {
    flex: 1;
    min-width: auto;
  }
}

// Form Container with proper spacing
.formContainer {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding-bottom: 2rem; // Extra space for last elements
}

.question {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.questionText {
  font-size: 1.5rem;
  font-weight: 600;
  color: $gray-800;
  margin: 0 0 1rem 0;
  line-height: 1.3;

  @media (max-width: 768px) {
    font-size: 1.25rem;
  }
}

.formFields {
  display: flex;
  flex-direction: column;
  gap: 2rem;

  @media (max-width: 768px) {
    gap: 1.5rem;
  }
}

.options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;

  @media (max-width: 768px) {
    gap: 0.5rem;
  }
}

// Notice Card Styling
.noticeCard {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 2px solid #f59e0b;
  border-radius: $border-radius-lg;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease-in-out;

  &:hover {
    transform: translateY(-2px);
    box-shadow: $shadow-lg;
  }

  @media (max-width: 768px) {
    padding: 1.25rem;
    gap: 0.75rem;
  }
}

.noticeIcon {
  font-size: 1.5rem;
  flex-shrink: 0;
  margin-top: 0.25rem;

  @media (max-width: 768px) {
    font-size: 1.25rem;
  }
}

.noticeContent {
  flex: 1;
}

.noticeTitle {
  font-size: 1.125rem;
  font-weight: 700;
  color: #92400e;
  margin: 0 0 0.5rem 0;
  line-height: 1.3;

  @media (max-width: 768px) {
    font-size: 1rem;
  }
}

.noticeText {
  font-size: 0.9rem;
  color: #78350f;
  margin: 0;
  line-height: 1.5;
  font-weight: 500;

  @media (max-width: 768px) {
    font-size: 0.85rem;
  }
}

// Radio Button Styling
.radioGroupLabel {
  font-size: 1rem;
  font-weight: 600;
  color: $gray-800;
  margin-bottom: 1rem;
  display: block;
  line-height: 1.4;

  @media (max-width: 768px) {
    font-size: 0.9rem;
  }
}

.requiredAsterisk {
  color: $error-color;
  font-weight: 700;
  margin-left: 0.25rem;
}

.radioContainer {
  display: flex;
  flex-direction: column;
  gap: 0.875rem;
  padding: 1.25rem;
  background-color: $gray-50;
  border-radius: $border-radius;
  border: 1px solid $gray-200;
  transition: border-color 0.2s ease-in-out;

  &:hover {
    border-color: $gray-300;
  }

  @media (max-width: 768px) {
    padding: 1rem;
    gap: 0.75rem;
  }
}

.radioOption {
  display: flex;
  align-items: flex-start;
  gap: 0.875rem;
  cursor: pointer;
  padding: 0.75rem;
  border-radius: 8px;
  transition: background-color 0.2s ease-in-out;

  &:hover {
    background-color: rgba(37, 99, 235, 0.06);
  }

  &:active {
    background-color: rgba(37, 99, 235, 0.1);
  }

  @media (max-width: 768px) {
    gap: 0.75rem;
    padding: 0.625rem;
  }
}

.radioInput {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid $gray-400;
  border-radius: 50%;
  cursor: pointer;
  margin-top: 0.125rem;
  flex-shrink: 0;
  appearance: none;
  transition: all 0.2s ease-in-out;
  position: relative;
  background-color: $white;

  &:checked {
    border-color: $primary-color;
    background-color: $primary-color;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 0.5rem;
      height: 0.5rem;
      background-color: $white;
      border-radius: 50%;
    }
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
    border-color: $primary-color;
  }

  &:hover:not(:checked) {
    border-color: $primary-color;
    background-color: rgba(37, 99, 235, 0.05);
  }

  @media (max-width: 768px) {
    width: 1.125rem;
    height: 1.125rem;

    &:checked::after {
      width: 0.4rem;
      height: 0.4rem;
    }
  }
}

.radioLabel {
  font-size: 0.875rem;
  color: $gray-700;
  line-height: 1.5;
  cursor: pointer;
  flex: 1;
  font-weight: 400;

  &:hover {
    color: $gray-800;
  }

  @media (max-width: 768px) {
    font-size: 0.8rem;
  }
}

// Textarea Styling - Aligned with other form elements
.textareaLabel {
  font-size: 1rem;
  font-weight: 600;
  color: $gray-800;
  margin-bottom: 0.75rem;
  display: block;
  line-height: 1.4;

  @media (max-width: 768px) {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }
}

.textarea {
  width: 100%;
  min-height: 120px;
  padding: 1rem;
  border: 2px solid $gray-300;
  border-radius: $border-radius;
  font-size: 0.875rem;
  font-family: inherit;
  line-height: 1.6;
  resize: vertical;
  background-color: $white;
  transition: all 0.2s ease-in-out;
  box-sizing: border-box;

  &:focus {
    outline: none;
    border-color: $primary-color;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }

  &:hover:not(:focus) {
    border-color: $gray-400;
  }

  &::placeholder {
    color: $gray-400;
    font-style: italic;
  }

  @media (max-width: 768px) {
    min-height: 100px;
    padding: 0.875rem;
    font-size: 0.8rem;
  }
}

// Terms Section Styling
.termsSection {
  background-color: $gray-50;
  border: 2px solid $gray-200;
  border-radius: $border-radius-lg;
  padding: 1.75rem;
  transition: border-color 0.2s ease-in-out;

  &:hover {
    border-color: $gray-300;
  }

  @media (max-width: 768px) {
    padding: 1.25rem;
  }
}

.termsSectionTitle {
  font-size: 1.125rem;
  font-weight: 700;
  color: $gray-800;
  margin: 0 0 1rem 0;
  line-height: 1.3;
  display: flex;
  align-items: center;
  gap: 0.25rem;

  @media (max-width: 768px) {
    font-size: 1rem;
  }
}

.termsContent {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.termsDescription {
  font-size: 0.875rem;
  color: $gray-700;
  line-height: 1.6;
  margin: 0;
  padding: 1rem;
  background-color: $white;
  border-radius: $border-radius;
  border-left: 4px solid $primary-color;

  @media (max-width: 768px) {
    font-size: 0.8rem;
    padding: 0.875rem;
  }
}

// Checkbox Styling
.termsCheckbox {
  display: flex;
  align-items: flex-start;
  gap: 0.875rem;
  padding: 1rem;
  background-color: $white;
  border-radius: $border-radius;
  border: 1px solid $gray-300;
  transition: all 0.2s ease-in-out;

  &:hover {
    border-color: $primary-color;
    background-color: rgba(37, 99, 235, 0.02);
  }

  @media (max-width: 768px) {
    gap: 0.75rem;
    padding: 0.875rem;
  }
}

.checkboxInput {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid $gray-400;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 0.125rem;
  flex-shrink: 0;
  appearance: none;
  transition: all 0.2s ease-in-out;
  position: relative;
  background-color: $white;

  &:checked {
    background-color: $primary-color;
    border-color: $primary-color;

    &::after {
      content: '✓';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: $white;
      font-size: 0.875rem;
      font-weight: 700;
    }
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
    border-color: $primary-color;
  }

  &:hover:not(:checked) {
    border-color: $primary-color;
    background-color: rgba(37, 99, 235, 0.05);
  }

  @media (max-width: 768px) {
    width: 1.125rem;
    height: 1.125rem;

    &:checked::after {
      font-size: 0.75rem;
    }
  }
}

.checkboxLabel {
  font-size: 0.875rem;
  color: $gray-800;
  line-height: 1.5;
  cursor: pointer;
  flex: 1;
  font-weight: 500;

  &:hover {
    color: $primary-color;
  }

  @media (max-width: 768px) {
    font-size: 0.8rem;
  }
}

// Error Text Styling
.errorText {
  color: $error-color;
  font-size: 0.75rem;
  margin-top: 0.5rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.25rem;

  &::before {
    content: '⚠';
    font-size: 0.875rem;
  }

  @media (max-width: 768px) {
    font-size: 0.7rem;
  }
}

// Animations
.noticeCard,
.termsSection {
  animation: slideInUp 0.4s ease-out;
}

.termsCheckbox {
  animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}