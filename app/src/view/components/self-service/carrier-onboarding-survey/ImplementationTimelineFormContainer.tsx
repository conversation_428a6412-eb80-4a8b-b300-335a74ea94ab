import { useTranslation } from "react-i18next";
import React, { useState, useEffect } from "react";
import styles from "./ImplementationTimelineForm.module.scss";
import { Link } from "react-router-dom";
import { Button } from "@fourkites/elemental-atoms";
import { Input } from "@fourkites/elemental-input";
import { isFieldInvalid } from "view/components/base/FormUtils";
import { calculateDeadlineDate, calculateDeadlineFromNow, isValidDate } from "utils/dateUtils";

export const ImplementationTimelineForm = ({
  implementationTimeline,
  setImplementationTimeline,
  implementationChallenges,
  setImplementationChallenges,
  termsAccepted,
  setTermsAccepted,
  onBack,
  submitSurvey,
  handleTechnicalTeamContact,
  shipperName,
  surveyCreatedAt
}: {
  implementationTimeline: string;
  setImplementationTimeline: React.Dispatch<React.SetStateAction<string>>;
  implementationChallenges: string;
  setImplementationChallenges: React.Dispatch<React.SetStateAction<string>>;
  termsAccepted: boolean;
  setTermsAccepted: React.Dispatch<React.SetStateAction<boolean>>;
  onBack: () => void;
  submitSurvey: () => void;
  handleTechnicalTeamContact: () => void;
  shipperName: string;
  surveyCreatedAt?: string;
}) => {
  const { t } = useTranslation();

  // Check if all required fields are valid
  const isFormValid = () => {
    return !isFieldInvalid(implementationTimeline) && termsAccepted;
  };

  const steps = ["Step One", "Step Two", "Step Three", "Step Four", "Step Five"];

  return (
    <div className={styles.pageContainer}>
      <div className={styles.container}>
        {/* Header Section */}
        <header className={styles.header}>
          <div className={styles.onboardingText}>
            <h1 className={styles.title}>Carrier Onboarding</h1>
            <p className={styles.subtitle}>Implementation Timeline</p>
          </div>
          <Stepper steps={steps} currentStep={4} />
        </header>

        {/* Main Content */}
        <main className={styles.content}>
          <ImplementationTimelineFormContent
            implementationTimeline={implementationTimeline}
            setImplementationTimeline={setImplementationTimeline}
            implementationChallenges={implementationChallenges}
            setImplementationChallenges={setImplementationChallenges}
            termsAccepted={termsAccepted}
            setTermsAccepted={setTermsAccepted}
            shipperName={shipperName}
            surveyCreatedAt={surveyCreatedAt}
          />
        </main>

        {/* Footer Section */}
        <footer className={styles.footer}>
          <Link
            to="#"
            className={styles.footerLink}
            onClick={(e) => {
              e.preventDefault();
              handleTechnicalTeamContact();
            }}
          >
            {t("Get it done by a call")}
          </Link>
          
          <div className={styles.footerButtons}>
            <Button
              className={styles.footerButton}
              theme="secondary"
              size="medium"
              onClick={onBack}
            >
              {t("Back")}
            </Button>
            <Button
              className={styles.footerButton}
              theme="primary"
              size="medium"
              disabled={!isFormValid()}
              onClick={submitSurvey}
            >
              {t("Complete Survey")}
            </Button>
          </div>
        </footer>
      </div>
    </div>
  );
};

export const Stepper = ({
  steps,
  currentStep,
}: {
  steps: string[];
  currentStep: number;
}) => {
  return (
    <div className={styles.stepper}>
      {steps.map((step, index) => (
        <div key={index} className={styles.stepContainer}>
          <div className={`${styles.step} ${index === currentStep ? styles.currentStep : ""}`}>
            <div
              className={`${styles.oval} ${
                index === currentStep 
                  ? styles.currentOvalSelected 
                  : index < currentStep 
                  ? styles.ovalCompleted 
                  : ""
              }`}
            >
              {index + 1}
            </div>
            <div
              className={`${styles.stepLabel} ${
                index === currentStep ? styles.currentStepLabel : ""
              }`}
            >
              {step}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export const ImplementationTimelineFormContent = ({
  implementationTimeline,
  setImplementationTimeline,
  implementationChallenges,
  setImplementationChallenges,
  termsAccepted,
  setTermsAccepted,
  shipperName,
  surveyCreatedAt
}: {
  implementationTimeline: string;
  setImplementationTimeline: React.Dispatch<React.SetStateAction<string>>;
  implementationChallenges: string;
  setImplementationChallenges: React.Dispatch<React.SetStateAction<string>>;
  termsAccepted: boolean;
  setTermsAccepted: React.Dispatch<React.SetStateAction<boolean>>;
  shipperName: string;
  surveyCreatedAt?: string;
}) => {
  const { t } = useTranslation();
  const [deadlineDate, setDeadlineDate] = useState<string>("");

  // Calculate dynamic deadline based on survey creation date
  useEffect(() => {
    const calculateDeadline = () => {
      if (surveyCreatedAt && isValidDate(surveyCreatedAt)) {
        // Use the surveyCreatedAt from the validate_survey_request API response
        setDeadlineDate(calculateDeadlineDate(surveyCreatedAt));
      } else {
        // Fallback: Use current date + 14 days if no valid creation date
        setDeadlineDate(calculateDeadlineFromNow());
      }
    };

    calculateDeadline();
  }, [surveyCreatedAt]);

  // Timeline options configuration
  const timelineOptions = [
    { value: "already_have_capabilities", label: t("Already have visibility capabilities") },
    { value: "within_1_month", label: t("Within 1 month") },
    { value: "within_3_months", label: t("Within 3 months") },
    { value: "within_6_months", label: t("Within 6 months") },
    { value: "need_assistance", label: t("Need assistance to meet deadline") }
  ];

  return (
    <div className={styles.formContainer}>
      <div className={styles.question}>
        <h2 className={styles.questionText}>
          {t("Implementation Timeline")}
        </h2>
        
        <div className={styles.formFields}>
          {/* Important Notice */}
          <div className={styles.options}>
            <div className={styles.noticeCard}>
              <div className={styles.noticeIcon}>📅</div>
              <div className={styles.noticeContent}>
                <h3 className={styles.noticeTitle}>{t("Important Deadline")}</h3>
                <p className={styles.noticeText}>
                  {deadlineDate
                    ? t(`${shipperName} requires all carriers to have Real Time Visibility capabilities by ${deadlineDate}.`)
                    : t(`${shipperName} requires all carriers to have Real Time Visibility capabilities.`)
                  }
                </p>
              </div>
            </div>
          </div>

          {/* Implementation Timeline Radio Group */}
          <div className={styles.options}>
            <span className={styles.radioGroupLabel}>
              {t("When will you be able to implement visibility capabilities (real time GPS tracking)?")}
              <span className={styles.requiredAsterisk}> *</span>
            </span>
            <div className={styles.radioContainer}>
              {timelineOptions.map((option, index) => (
                <div key={option.value} className={styles.radioOption}>
                  <input
                    type="radio"
                    id={`timeline-${index}`}
                    name="implementationTimeline"
                    value={option.value}
                    checked={implementationTimeline === option.value}
                    onChange={() => setImplementationTimeline(option.value)}
                    className={styles.radioInput}
                    required
                  />
                  <label 
                    htmlFor={`timeline-${index}`} 
                    className={styles.radioLabel}
                  >
                    {option.label}
                  </label>
                </div>
              ))}
            </div>
            {isFieldInvalid(implementationTimeline) && (
              <div className={styles.errorText}>
                {t("Field is required")}
              </div>
            )}
          </div>

          {/* Implementation Challenges Textarea */}
          <div className={styles.options}>
            <label className={styles.textareaLabel}>
              {t("Please share any concerns or implementation challenges regarding this requirement:")}
            </label>
            <textarea
              className={styles.textarea}
              value={implementationChallenges}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => 
                setImplementationChallenges(e.target.value)
              }
              rows={4}
              placeholder={t("Enter your concerns or challenges here...")}
            />
          </div>

          {/* Terms and Conditions */}
          <div className={styles.options}>
            <div className={styles.termsSection}>
              <h3 className={styles.termsSectionTitle}>
                <a 
                  href="https://www.fourkites.com/legal/general-terms-and-conditions-for-data-providers/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className={styles.termsLink}
                >
                  {t("General Terms & Conditions for Data Providers")}
                </a>
                <span className={styles.requiredAsterisk}> *</span>
              </h3>
              
              <div className={styles.termsContent}>
                <p className={styles.termsDescription}>
                  {t("By checking the box below, you acknowledge, on behalf of the entity you are representing in this survey, that the information provided will be used to facilitate an integration with the FourKites Platform for the purpose of sharing data of our mutual customer(s) and that sharing of such data and access to the FourKites Platform once the integration has been completed will be subject to the above terms.")}
                </p>
                
                <div className={styles.termsCheckbox}>
                  <input
                    type="checkbox"
                    id="terms-accepted"
                    checked={termsAccepted}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => 
                      setTermsAccepted(e.target.checked)
                    }
                    className={styles.checkboxInput}
                    required
                  />
                  <label htmlFor="terms-accepted" className={styles.checkboxLabel}>
                    {t("I confirm and accept the FourKites General Terms & Conditions for Data Providers")}
                  </label>
                </div>
                
                {!termsAccepted && (
                  <div className={styles.errorText}>
                    {t("You must accept the terms and conditions to proceed")}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImplementationTimelineForm;