import React from 'react';
import styles from './WelcomeScreen.module.css';
import { Button } from "@fourkites/elemental-atoms";

interface ModalProps {
  title: string;
  description: React.ReactNode;
  onAccept: () => void;
  onDecline: () => void;
}

const Modal: React.FC<ModalProps> = ({ title, description, onAccept, onDecline }) => {
  return (
    <div className={styles.modal}>
      <div className={styles.modalContent}>
        <h2 className={styles.modalTitle}>{title}</h2>
        <p className={styles.modalDescription}>{description}</p>
        <div className={styles.modalButtons}>
          <Button variant="outline" onClick={onDecline} className={styles.modalButton}>
            Decline
          </Button>
          <Button variant="solid" onClick={onAccept} className={styles.modalButton}>
            Accept
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Modal;