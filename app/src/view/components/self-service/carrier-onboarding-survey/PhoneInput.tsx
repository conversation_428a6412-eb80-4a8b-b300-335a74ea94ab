import React, { useState, useEffect, useRef } from 'react';
import { isFieldInvalid } from 'view/components/base/FormUtils';
import { COMMON_COUNTRIES, ALL_COUNTRIES, getCountryByCode, getFlagEmoji } from './countryData'

// Define props interface
interface PhoneInputProps {
  label: string;
  placeholder: string;
  errorLabel: string;
  value: string;
  onChange: (value: string) => void;
  country: string;
  onChangeCountry: (code: string) => void;
  required?: boolean;
  disabled?: boolean;
  invalid?: boolean;
}

const PhoneInput: React.FC<PhoneInputProps> = (props) => {
  const {
    label,
    placeholder,
    errorLabel,
    value,
    onChange,
    country,
    onChangeCountry,
    required = false,
    disabled = false,
    invalid
  } = props;
  
  const [searchQuery, setSearchQuery] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [filteredCountries, setFilteredCountries] = useState<string[]>([]);
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  // Handle phone number input change
  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const phoneValue = e.target.value;
    // Just store the phone number value directly, without adding country code
    // This matches the original PhoneInput behavior
    onChange(phoneValue);
  };
  
  // Filter countries based on search query
  useEffect(() => {
    if (!searchQuery) {
      // Show common countries first, then all others
      const commonCountriesSet = new Set(COMMON_COUNTRIES);
      const prioritizedCodes = [
        ...COMMON_COUNTRIES,
        ...ALL_COUNTRIES
          .filter(c => !commonCountriesSet.has(c.code))
          .map(c => c.code)
      ];
      setFilteredCountries(prioritizedCodes);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = ALL_COUNTRIES
        .filter(c =>
          c.name.toLowerCase().includes(query) ||
          c.dialCode.includes(query) ||
          c.code.toLowerCase().includes(query)
        )
        .map(c => c.code);
      setFilteredCountries(filtered);
    }
  }, [searchQuery]);
  
  // Handle clicking outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // Styles for component
  const styles = {
    container: {
      position: 'relative' as const,
      width: '100%'
    },
    inputContainer: {
      display: 'flex' as const,
      alignItems: 'center' as const,
      width: '100%'
    },
    countrySelector: {
      display: 'flex' as const,
      alignItems: 'center' as const,
      padding: '8px 12px',
      border: '1px solid #cfd4da',
      borderRadius: '4px 0 0 4px',
      cursor: 'pointer' as const,
      backgroundColor: '#f8f9fa',
      minWidth: '90px'
    },
    phoneInput: {
      flex: 1,
      borderRadius: '0 4px 4px 0',
      borderLeft: 'none'
    },
    dropdown: {
      position: 'absolute' as const,
      top: '100%',
      left: 0,
      zIndex: 1000,
      width: '300px',
      maxHeight: '300px',
      overflowY: 'auto' as const,
      backgroundColor: 'white',
      border: '1px solid #cfd4da',
      borderRadius: '4px',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
      marginTop: '4px'
    },
    searchInput: {
      padding: '8px 12px',
      borderBottom: '1px solid #eee',
      position: 'sticky' as const,
      top: 0,
      backgroundColor: 'white',
      zIndex: 1
    },
    countryList: {
      listStyle: 'none',
      padding: 0,
      margin: 0
    },
    countryItem: {
      padding: '8px 12px',
      cursor: 'pointer' as const,
      display: 'flex' as const,
      alignItems: 'center' as const,
      gap: '8px'
    },
    countryItemHover: {
      backgroundColor: '#f3f4f6'
    },
    commonCountriesHeading: {
      padding: '4px 12px',
      backgroundColor: '#f3f4f6',
      fontWeight: 'bold' as const,
      fontSize: '12px',
      color: '#6b7280'
    },
    flag: {
      width: '24px',
      height: '16px',
      objectFit: 'cover' as const,
      borderRadius: '2px'
    },
    sectionHeader: {
      padding: '8px 12px',
      fontWeight: 'bold' as const,
      backgroundColor: '#f3f4f6',
      fontSize: '12px',
      color: '#6b7280'
    },
    label: {
      display: 'block',
      marginBottom: '8px',
      fontWeight: 500
    },
    required: {
      color: '#ef4444',
      marginLeft: '4px'
    },
    input: {
      flex: 1,
      padding: '8px 12px',
      border: '1px solid #cfd4da',
      borderRadius: '0 4px 4px 0',
      borderLeft: 'none',
      fontSize: '16px'
    },
    error: {
      color: '#ef4444',
      fontSize: '12px',
      marginTop: '4px'
    }
  };
  
  // Get country name and dial code from country code
  const getCountryDetails = (code: string) => {
    const country = getCountryByCode(code);
    return country || { name: code, dialCode: '', code };
  };
  
  const currentCountry = getCountryDetails(country);
  
  // Handle key events in dropdown
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsDropdownOpen(false);
    }
  };
  
  return (
    <div style={styles.container}>
      <label style={styles.label}>
        {label}
        {required && <span style={styles.required}>*</span>}
      </label>
      
      <div style={styles.inputContainer}>
        {/* Country selector button */}
        <div
          style={styles.countrySelector}
          onClick={() => !disabled && setIsDropdownOpen(!isDropdownOpen)}
        >
          <span role="img" aria-label={currentCountry.name} style={{ marginRight: '8px' }}>
            {getFlagEmoji(country)}
          </span>
          <span>{currentCountry.dialCode}</span>
          <span style={{ marginLeft: '4px' }}>▼</span>
        </div>
        
        {/* Phone number input */}
        <input
          type="tel"
          placeholder={placeholder}
          value={value}
          onChange={handlePhoneChange}
          disabled={disabled}
          style={styles.input}
          required={required}
        />
      </div>
      
      {/* Error message */}
      {(invalid || (isFieldInvalid(value) && required)) && (
        <div style={styles.error}>
          {errorLabel}
        </div>
      )}
      
      {/* Country selection dropdown */}
      {isDropdownOpen && (
        <div
          style={styles.dropdown}
          ref={dropdownRef}
          onKeyDown={handleKeyDown}
          tabIndex={0}
        >
          <div style={styles.searchInput}>
            <input
              type="text"
              placeholder="Search countries..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              style={{ width: '100%', border: 'none', outline: 'none' }}
            />
          </div>
          
          <ul style={styles.countryList}>
            {/* Show "Common Countries" section if not searching */}
            {!searchQuery && (
              <>
                <li style={styles.sectionHeader}>Common Countries</li>
                {filteredCountries
                  .filter(code => COMMON_COUNTRIES.includes(code))
                  .map(code => {
                    const details = getCountryDetails(code);
                    return (
                      <li
                        key={code}
                        style={styles.countryItem}
                        onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f3f4f6'}
                        onMouseOut={(e) => e.currentTarget.style.backgroundColor = ''}
                        onClick={() => {
                          onChangeCountry(code);
                          // Don't modify the phone number when changing country
                          // This matches the original PhoneInput behavior
                          setIsDropdownOpen(false);
                        }}
                      >
                        <span role="img" aria-label={details.name}>
                          {getFlagEmoji(code)}
                        </span>
                        <span>{details.name}</span>
                        <span style={{ marginLeft: 'auto', color: '#6b7280' }}>
                          {details.dialCode}
                        </span>
                      </li>
                    );
                  })}
                <li style={styles.sectionHeader}>All Countries</li>
              </>
            )}
            
            {/* Filtered country list */}
            {filteredCountries
              .filter(code => searchQuery || !COMMON_COUNTRIES.includes(code))
              .map(code => {
                const details = getCountryDetails(code);
                return (
                  <li
                    key={code}
                    style={styles.countryItem}
                    onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f3f4f6'}
                    onMouseOut={(e) => e.currentTarget.style.backgroundColor = ''}
                    onClick={() => {
                      onChangeCountry(code);
                      // Don't modify the phone number when changing country
                      // This matches the original PhoneInput behavior
                      setIsDropdownOpen(false);
                    }}
                  >
                    <span role="img" aria-label={details.name}>
                      {getFlagEmoji(code)}
                    </span>
                    <span>{details.name}</span>
                    <span style={{ marginLeft: 'auto', color: '#6b7280' }}>
                      {details.dialCode}
                    </span>
                  </li>
                );
              })}
            
            {filteredCountries.length === 0 && (
              <li style={{ padding: '12px', textAlign: 'center', color: '#6b7280' }}>
                No countries found
              </li>
            )}
          </ul>
        </div>
      )}
    </div>
  );
};

export default PhoneInput;