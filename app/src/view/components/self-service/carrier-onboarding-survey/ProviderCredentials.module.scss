@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.container {
  display: flex;
  padding-bottom: 50px;
}

.formContainer {
  display: flex;
  flex: 1;
  flex-direction: column;
  margin-right: 16px;
  max-height: calc(113vh + -179px);
  overflow-y: auto;
  padding-bottom: 50px;

  > div[id="security-alert"] {
    display: flex;
    align-items: center;
    border-radius: 4px;
    background-color: #fff8e6;
    padding: 16px;
    width: 426px;
    font-size: 16px;
    letter-spacing: 0;
    line-height: 30px;
    margin-top: 16px;
    margin-bottom: 16px;

    > label {
      margin-left: 16px;
    }
  }

  > div[id="location-provider-type"] {
    > span {
      margin-top: 16px;
      display: flex;

      > div {
        margin-right: 32px;
      }
    }
  }

  > div > button {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 24px;
    margin-bottom: 24px;
    width: 460px;
  }
}

.formLink {
  color: $color-primary-500;
  font-family: Lato;
  font-size: 16px;
  letter-spacing: 0;
  text-decoration: underline;
  cursor: pointer;

  &:visited {
    color: $color-primary-500;
  }
}

.formLabel {
  font-size: 18px;
  letter-spacing: 0;
  line-height: 27px;
}

.radioButtonWrapper {
  > div > input[type="radio"] {
    opacity: 1;
  }
}

.assetNumberInput {
  flex: 1;
  margin-top: 8px;
  margin-bottom: 8px;

  > div {
    width: 460px;

    > div {
      display: flex;

      > input {
        width: 100%;
      }
    }
  }
}

.providerCredentialInput {
  flex: 1;
  margin-top: 8px;

  > div {
    width: 460px;

    > div {
      display: flex;

      > input {
        width: 100%;
      }
    }
  }
}

.loader {
  display: flex;
  align-items: left;
  align-content: center;
  justify-content: center;
  margin-top: 32px;
  margin-bottom: 32px;
}

.modalWrapper {
  display: flex;
  width: 100%;
  align-items: center;
  align-content: center;

  > img {
    height: 80px;
    width: 80px;
    margin-right: 16px;
  }

  .modalTextArea {
    width: 100%;
    border-radius: 8px;
    padding: 8px;
  }

  .mandatoryModalTextArea {
    width: 100%;
    border-radius: 8px;
    padding: 8px;
    border: 2px solid red;
  }
}

.footer {
  position: fixed; /* Freezes the footer at the bottom */
  bottom: 0; /* Aligns it to the bottom of the viewport */
  left: 0;
  width: 90%; /* Ensures it spans the full width */
  z-index: 1000; /* Brings it above other elements */
  
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: $color-neutral-00;
  border-top: 1px solid $color-neutral-200;

  .footerLink {
    color: $color-primary-500;
    text-decoration: none;
    font-size: 14px;
  }

  .footerButtons {
    display: flex;
    gap: 16px;

    .footerButton {
      min-width: 100px;
    }
  }
}
