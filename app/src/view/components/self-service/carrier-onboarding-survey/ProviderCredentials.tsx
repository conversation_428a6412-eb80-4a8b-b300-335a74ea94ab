import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

import { useAppSelector, useAppDispatch } from "state/hooks";
import { Link } from "react-router-dom";
import { Button } from "@fourkites/elemental-atoms";
import { EldGpsIntegrationsState } from "state/modules/carrier/EldGpsIntegrations";
import { UsersState } from "state/modules/Users";
import { ExternalParametersState } from "state/modules/external/ExternalParameters";

import { showToast } from "view/components/base/toast/Toast";

import ProviderInstructions from "./ProviderInstructions";
import ProviderCredentialsForm from "./ProviderCredentialsForm";

import styles from "./ProviderCredentials.module.scss";
import ProviderCredentialsProps from "./ProviderCredentials.types";

const ProviderCredentials = ({
  isModal,
  isExternallyUsed,
  isCreating,
  provider,
  credentials,
  integrationId,
  setProvider,
  setCredentials,
  handleTechnicalTeamContact,
  onNextStep,
  providerType,
  setProviderType,
  assetNumber,
  setAssetNumber,
  dsaFieldValues,
  setDsaFieldValues,
  providerCredentials,
  setProviderCredentials,
}: ProviderCredentialsProps) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  /*****************************************************************************
   * REDUX
   ****************************************************************************/
  const carrierId: string = useAppSelector(UsersState.selectors.getCompanyId);
  const user = useAppSelector(UsersState.selectors.getCurrentUser);
  const externalUser = useAppSelector(
    ExternalParametersState.selectors.externalUser()
  );

  // If used externally, we use external user
  const currentUser = isExternallyUsed ? externalUser : user;

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  /*
   * Populates credentials form when we receive a new provider
   */

  useEffect(() => {
    updateCurrentCredential(
      false,
    providerType,
    assetNumber,
    providerCredentials,
  null);
  
  }, [providerType,assetNumber,providerCredentials]);


  useEffect(() => {
    updateCurrentCredential(
      true,
    providerType,
    assetNumber,
    providerCredentials,
    dsaFieldValues);
  }, [dsaFieldValues]);

  useEffect(() => {
    
    fetchAndSetProviderCredentials();
  }, [provider]);

  /*****************************************************************************
   * INTERNAL_METHODS
   ****************************************************************************/

  /*
   * Parses credentials of provider to create new integration
   */
  const fetchAndSetProviderCredentials = () => {
    
    if (!credentials || !provider?.id) {
      console.error("Credentials or provider data is missing.");
      return;
    }
  
    const providerKey = provider.id;
  
    // Find the index of the provider based on provider.id
    const indexToFetch = credentials.findIndex((cred:any) => cred.hasOwnProperty(providerKey));
  
    if (indexToFetch === -1) {
      console.error("Provider not found in credentials array.");
      return;
    }
  
    // Safely access the provider data
    const providerData = credentials[indexToFetch][providerKey];

    if (Object.keys(providerData).length == 0){

      const providerCredentials = provider?.credentials?.map((cred: any) => ({
        [cred?.id]: cred?.value ? cred?.value : "",
      }));
      setProviderCredentials(providerCredentials);

    }
    else{
  
    if (providerData) {
      setProviderType(providerData.providerType || ""); // Default to an empty string if not found
      setAssetNumber(providerData.assetNumber || null); // Default to null if not found
      if(providerData.providerCredentials){
        setProviderCredentials(providerData.providerCredentials || []); // Default to empty array if not found
      }
      if(providerData.dsaFieldValues){
        setDsaFieldValues(providerData.dsaFieldValues || []); // Default to empty array if not found
      }
    } else {
      console.error("Provider data is not properly structured.");
    }
  }
  };
  


  const updateCurrentCredential = (
    peoplenetProvider:boolean,
    providerType:any,
    assetNumber:any,
    providerCredentials:any,
    dsaFieldValues:any
  ) => {
    if (credentials == null) return;
  
    // Find the index of the provider based on provider.id
    const providerKey = provider.id;  
    const indexToUpdate = credentials.findIndex((cred: Credential) => cred.hasOwnProperty(providerKey));
    // If the provider is found, update the corresponding credential at the found index
    if (indexToUpdate !== -1) {
      // Create a copy of the credentials array
      const updatedCredentials = [...credentials];
  
      // Update the specific index with the new credential value
      if(!peoplenetProvider){
      updatedCredentials[indexToUpdate] = {
        ...updatedCredentials[indexToUpdate],
        [providerKey]: {

          providerType: providerType,
          assetNumber: assetNumber,
          providerCredentials: providerCredentials
        },
      };
    }
    else{

      updatedCredentials[indexToUpdate] = {
        ...updatedCredentials[indexToUpdate],
        [providerKey]: {

          providerType: providerType,
          assetNumber: assetNumber,
          dsaFieldValues: dsaFieldValues
        },
      };
    
    }
  
      // Update the state with the new array
      setCredentials(updatedCredentials);
      } else {
      console.log("Provider not found in credentials array");
    }
  };


  const getParsedCredentials = () => {
    let newCredentials = {};

    // Parses through list of required credentials to create a key:value dict of
    // credentials filled by the user
    for (let cred of providerCredentials) {
      newCredentials = { ...newCredentials, ...cred };
    }
    return newCredentials;
  };

  /*
   * Handler to create a new credential
   */
  const onCreateCredentials = async () => {
    const response = await dispatch(
      EldGpsIntegrationsState.actions.createIntegration({
        carrierId: carrierId,
        userId: currentUser?.userId,
        modes: ["ltl", "ftl"],
        assetNumber: assetNumber,
        provider: {
          id: provider?.id,
          type: providerType,
          alias: "", //TODO,
          credentials: getParsedCredentials(),
        },
      })
    );

    if ("error" in response) {
      //TODO: handler error
    }

  };

  /*
   * Handler update credentials
   */
  const onUpdateCredentials = async () => {
    const response = await dispatch(
      EldGpsIntegrationsState.actions.updateIntegration({
        carrierId: carrierId,
        userId: currentUser?.userId,
        modes: ["ltl", "ftl"],
        integrationId: integrationId!,
        assetNumber: assetNumber,
        provider: {
          id: provider?.id,
          type: providerType,
          alias: "", //TODO,
          credentials: getParsedCredentials(),
        },
      })
    );

    if ("error" in response) {
      showToast(
        t("Error"),
        t("We could not update your credentials. Please try again shortly."),
        "error"
      );
    }

    // Finish adding integration
    showToast(
      t("Success"),
      t("Your integrations were updated successfully."),
      "ok"
    );
  };

  /*
   * Handler to create new help requests for adding credentials
   */
  const onHelpRequest = async (helpRequestMessage: string) => {
    const response = await dispatch(
      EldGpsIntegrationsState.actions.createIntegrationHelpRequest({
        carrierId: carrierId,
        modes: ["ltl", "ftl"],
        provider: {
          id: provider?.id,
          name: provider?.name,
          type: providerType,
        },
        userMessage: helpRequestMessage,
      })
    );

    // Show error if error
    if ("error" in response) {
      showToast(
        t("Error"),
        t("We could not process your request. Please try again shortly."),
        "error"
      );
    }

    // Confirm success
    showToast(
      t("Support contacted"),
      t("An email was sent to support. We will get back to you soon."),
      "ok"
    );
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  // XXX: ideally instructions_url should not exist anymore, but it was so far
  // not fixed on BE
  let instructions_urls = provider?.instructions_url?.split(",");
  let instructions_url = instructions_urls?.length ? instructions_urls[0] : "";
  const pdfInstructions = provider?.instructions_url_pdf
    ? provider?.instructions_url_pdf
    : instructions_url;

  return (
    <div
      className={styles.container}
      id="provider-container"
      data-test-id="provider-credentials-container"
    >
      <ProviderCredentialsForm
        provider={provider}
        providerType={providerType}
        setProviderType={setProviderType}
        assetNumber={assetNumber}
        setAssetNumber={setAssetNumber}
        credentials={providerCredentials}
        setCredentials={setProviderCredentials}
        onComplete={isCreating ? onCreateCredentials : onUpdateCredentials}
        onHelpRequest={onHelpRequest}
        dsaFieldValues={dsaFieldValues}
        setDsaFieldValues={setDsaFieldValues}
      />

      <ProviderInstructions
        isModal={isModal}
        isCreating={isCreating}
        isExternallyUsed={isExternallyUsed}
        pdfInstructions={pdfInstructions}
        htmlInstructions={provider?.instructions_url_html}
      />

      <div className={styles.footer}>
              <Link to="#" className={styles.footerLink} onClick={() => handleTechnicalTeamContact()}>
                {t("I'm not sure, complete this setup with my technical team")}
              </Link>
                <div className={styles.footerButtons}>
                  <Button
                    className={styles.footerButton}
                    theme="secondary"
                    size="medium"
                    onClick={() => setProvider(null)}
                  >
                    {t("Back")}
                  </Button>
                  <Button
                    className={styles.footerButton}
                    theme="primary"
                    size="medium"
                    onClick={() => onNextStep()}
                  >
                    {t("Proceed")}
                  </Button>
                </div>
              </div>
          </div>
  );
};

export default ProviderCredentials;
