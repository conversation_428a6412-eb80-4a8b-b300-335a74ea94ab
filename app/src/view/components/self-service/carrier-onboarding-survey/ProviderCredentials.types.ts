interface ProviderCredentialsProps {
  isCreating: boolean;
  isModal: boolean;
  isExternallyUsed: boolean;
  provider: any;
  credentials: any;
  integrationId?: string | number;
  setProvider: (provider: any) => void;
  setCredentials: (provider: any) => void;
  handleTechnicalTeamContact: () => void;
  onNextStep: () => void;
  providerType:any;
  setProviderType:any;
  assetNumber:any;
  setAssetNumber:any;
  dsaFieldValues:any;
  setDsaFieldValues:any;
  providerCredentials:any;
  setProviderCredentials:any;
}

export default ProviderCredentialsProps;
