@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-top: 24px;
  padding-bottom: 24px;

  > img {
    object-fit: contain;
    height: 120px;
    width: 120px;
  }

  > h1 {
    display: flex;
    align-items: center;
    font-size: 24px;
    letter-spacing: 0;
    line-height: 36px;
    text-align: center;
  }

  > label {
    font-size: 20px;
    letter-spacing: 0;
    line-height: 30px;
  }

  > label[id="other-options"] {
    font-size: 24px;
    letter-spacing: 0;
    line-height: 36px;
    text-align: center;
  }

  > div[id="form"] {
    display: flex;
    width: 100%;
    max-width: 100vw;
    flex-wrap: wrap;
    align-items: left;
    align-content: left;
    text-align: left;
    padding: 16px;
    box-sizing: border-box;
    border: 1px solid #c4c4c4;
    border-radius: 6px;
    background-color: #f8f8f8;
    margin-top: 16px;
    margin-bottom: 32px;

    > button {
      margin-top: 24px;
      height: 35px;
    }
  }
}

.inputWrapper {
  display: flex;
  width: 100%;
  margin-right: 16px;
  max-width: 220px;

  > div {
    width: 100%;

    > div {
      background-color: white;
      border-radius: 4px;
      display: flex;

      > input {
        width: 100%;
      }
    }
  }
}

.loader {
  display: flex;
  align-items: center;
  align-content: center;
  justify-content: center;
  width: 100%;
  margin-top: 32px;
  margin-bottom: 32px;
}
