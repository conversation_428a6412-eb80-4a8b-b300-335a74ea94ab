import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import providerNotFound from "assets/img/providerNotFound.png";

import { Button } from "@fourkites/elemental-atoms";
import { Input } from "@fourkites/elemental-input";
import { Spinner } from "@fourkites/elemental-loading-indicator";

import { showToast } from "view/components/base/toast/Toast";

import { useAppSelector, useAppDispatch } from "state/hooks";
import { LocationProvidersState } from "state/modules/carrier/LocationProviders";
import { UsersState } from "state/modules/Users";

import {
  isFieldInvalid,
  isEmailInvalid,
  getFirstName,
  getLastName,
} from "view/components/base/FormUtils";

import styles from "./ProviderNotFound.module.scss";
import ProviderNotFoundProps from "./ProviderNotFound.types";

const ProviderNotFound = () => {
  const { t } = useTranslation();

  /*****************************************************************************
   * REDUX
   ****************************************************************************/

  const dispatch = useAppDispatch();

  const isCreatingLocationProviders = useAppSelector(
    LocationProvidersState.selectors.isCreatingLocationProviders()
  );

  // Carrier ID
  const carrierId: string = useAppSelector(UsersState.selectors.getCompanyId);

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [form, setForm] = useState<any>({});
  const [confirmed, setConfirmed] = useState<boolean>(false);

  const areInputsInvalid =
    isFieldInvalid(form?.providerName || "") ||
    isFieldInvalid(form?.contactName || "") ||
    isEmailInvalid(form?.contactEmail || "");

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  const onAddNewProvider = async () => {
    setConfirmed(true);
    if (areInputsInvalid) {
      return;
    }

    //Get popular providers
    const response = await dispatch(
      LocationProvidersState.actions.addLocationProvider({
        carrierId: carrierId,
        locationProvider: {
          name: form?.providerName,
          contact: {
            first_name: getFirstName(form?.contactName || ""),
            last_name: getLastName(form?.contactName || "", ""),
            email: form?.contactEmail,
          },
        },
      })
    );

    if ("error" in response) {
      // TODO: Handler error here and display on page
      return;
    }

    showToast(
      t("Provider received"),
      t("Your GPS provider info is received and we will get back to you soon."),
      "ok"
    );
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div id="provider-not-found" className={styles.container}>
      <img src={providerNotFound} alt="Provider not found" />

      <h1>{t("Oops! We couldn't find your location provider")}</h1>

      <label>
        {t(
          "Please share some information about your provider and FourKites will integrate them for you."
        )}
      </label>

      {isCreatingLocationProviders ? (
        <div className={styles.loader}>
          <Spinner isLoading size="medium" />
        </div>
      ) : (
        <div id="form">
          <div className={styles.inputWrapper}>
            <Input
              label={`${t("GPS Provider name")}`}
              errorLabel={t("Field is required")}
              value={form?.providerName || ""}
              onChange={(e: any) =>
                setForm({ ...form, providerName: e.target.value })
              }
              invalid={confirmed && isFieldInvalid(form?.providerName || "")}
              required
            />
          </div>

          <div className={styles.inputWrapper}>
            <Input
              label={`${t("GPS Provider's contact name")}`}
              errorLabel={t("Field is required")}
              value={form?.contactName || ""}
              onChange={(e: any) =>
                setForm({ ...form, contactName: e.target.value })
              }
              invalid={confirmed && isFieldInvalid(form?.contactName || "")}
              required
            />
          </div>

          <div className={styles.inputWrapper}>
            <Input
              label={`${t("GPS Provider's contact email")}`}
              errorLabel={t("Please provide a valid email")}
              value={form?.contactEmail || ""}
              onChange={(e: any) =>
                setForm({ ...form, contactEmail: e.target.value })
              }
              invalid={confirmed && isEmailInvalid(form?.contactEmail || "")}
              required
            />
          </div>

          <Button size="medium" onClick={onAddNewProvider}>
            {t("Submit")}
          </Button>
        </div>
      )}

      {/*
      <label id="other-options">
        {t(
          "alternatively you can use our Mobile App as your Tracking solution!"
        )}
      </label>

      <CarrierLink />
      */}
    </div>
  );
};

export default ProviderNotFound;
