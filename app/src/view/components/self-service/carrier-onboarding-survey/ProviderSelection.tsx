import React, { useState, useEffect } from "react";
import { useMediaQuery } from "react-responsive";
import { useTranslation } from "react-i18next";

import classNames from "classnames";
import { Checkbox } from "@fourkites/elemental-checkbox";
import genericGpsProvider from "assets/img/genericGpsProvider.png";

import { Button, SearchIcon, XIcon } from "@fourkites/elemental-atoms";
import { Input } from "@fourkites/elemental-input";
import { Spinner } from "@fourkites/elemental-loading-indicator";

import { useAppSelector, useAppDispatch, useDebounce } from "state/hooks";
import { LocationProvidersState, setSelectedLocationProviders} from "state/modules/carrier/LocationProviders";

import ProviderNotFound from "./ProviderNotFound";
import SetupExplanation from "./SetupExplanation";

import styles from "./ProviderSelection.module.scss";
import ProviderSelectionProps from "./ProviderSelection.types";
import { useDispatch } from "react-redux";

import { Link } from "react-router-dom";

const ProviderSelection = ({
  isModal,
  isExternallyUsed,
  onSelectProviders,
  onBack,
  handleTechnicalTeamContact,
}: ProviderSelectionProps) => {
  const { t } = useTranslation();
  const [forceRender, setForceRender] = useState(false);
  const isMobile = useMediaQuery({ maxWidth: 720 });

  /*****************************************************************************
   * REDUX
   ****************************************************************************/

  const dispatch = useAppDispatch();

  const locationProviders = useAppSelector(
    LocationProvidersState.selectors.locationProviders()
  );
  
  const selectedLocationProviders = useAppSelector(
    LocationProvidersState.selectors.selectedLocationProviders()
  );
  const combinedLocationProviders = useAppSelector(
    LocationProvidersState.selectors.combinedLocationProviders()
  );
  const isLoading = useAppSelector(
    LocationProvidersState.selectors.isLoadingLocationProviders()
  );

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [query, setQuery] = useState<string>("");

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  useEffect(() => {
    setForceRender(!forceRender);
  },[locationProviders]);

  useDebounce(
    () => {
      // Only search after 3 chars
      if (!query || query?.length < 3) {
        //TODO: avoid duplicate calls
        dispatch(
          LocationProvidersState.actions.getLocationProviders({
            showPopular: true,
            query: "",
          })
        );
        return;
      }

      dispatch(
        LocationProvidersState.actions.getLocationProviders({
          showPopular: false,
          query,
        })
      );
    },
    300, // timeout
    [query]
  );

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div
      className={styles.container}
      id="provider-selection"
      data-test-id="provider-selection-page"
    >
      <ProvidersSearch
        isMobile={isMobile}
        isExternallyUsed={isExternallyUsed}
        isLoading={isLoading}
        isModal={isModal}
        selectedLocationProviders={selectedLocationProviders}
        combinedLocationProviders={combinedLocationProviders}
        query={query}
        setQuery={setQuery}
        onSelectProviders={onSelectProviders}
        onBack={onBack}
        handleTechnicalTeamContact={handleTechnicalTeamContact}
      />
      <div className ={styles.setupExplainationBoxTelematics}>
      <SetupExplanation
        isMobile={isMobile}
        isExternallyUsed={isExternallyUsed}
      />
      </div>
    </div>
  );
};

const ProvidersSearch = ({
  isMobile,
  isExternallyUsed,
  isLoading,
  isModal,
  selectedLocationProviders,
  combinedLocationProviders,
  query,
  setQuery,
  onSelectProviders,
  onBack,
  handleTechnicalTeamContact,
}: any) => {
  const { t } = useTranslation();

  const handleSubmitSelection = () => {
    onSelectProviders(selectedLocationProviders);
  };
  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const inputWrapperClass = classNames(styles.inputWrapper, {
    [styles.inputWrapperMobile]: isMobile && isExternallyUsed,
  });

  const isProviderNotFound = combinedLocationProviders?.length === 0;

  const providersWrapperMaxHeight = isModal
    ? "calc(100vh + -520px)"
    : "calc(100vh + -390px)";

    const dispatch = useDispatch();
    const handleSelection = (provider: any) => {
      const isAlreadySelected = selectedLocationProviders.some(
        (p: any) => p.id === provider.id
      );
    
      const updatedProviders = isAlreadySelected
        ? selectedLocationProviders.filter((p: any) => p.id !== provider.id) // Remove if already selected
        : [...selectedLocationProviders, provider]; // Add if not selected
    
      dispatch(setSelectedLocationProviders(updatedProviders));
    };
    const isProviderSelected = (provider: any) => {
      return selectedLocationProviders.some((p: any) => p.id === provider.id);
    }
    return (
    <div className={styles.providerSearch} id="providers-search">
      <div className={inputWrapperClass}>
        <Input
          label={""}
          placeholder={t("Select location Provider")}
          value={query}
          onChange={(e: any) => setQuery(e.target.value)}
          icon={query ? <XIcon /> : <SearchIcon />}
          onIconClick={() => setQuery("")}
          size="large"
        />
      </div>

      <label className={styles.subtitle} id="subtitle">
        {t("Or choose one from the list of popular location providers")}
      </label>

      {isLoading && (
        <div className={styles.loader}>
          <Spinner isLoading size="medium" />
        </div>
      )}

      {!isLoading && isProviderNotFound && (
        <ProviderNotFound />
      )}

      {!isLoading && !isProviderNotFound && (
        <>
          <h3 id="provider-selection-list-header">
            {t("Popular location providers")}
          </h3>
          <div
            id="providers-wrapper"
            className={styles.providersWrapper}
            style={{ maxHeight: providersWrapperMaxHeight }}
          > 
            {(combinedLocationProviders || []).map((provider: any, index: number) => (
              <LocationProvider
                key={index}
                provider={provider}
                onProviderSelection={handleSelection}
                isProviderSelected={() => isProviderSelected(provider)}
              />
            ))}
          </div>
        </>
      )}

<div className={styles.footer}>
        <Link to="#" className={styles.footerLink} onClick={() => handleTechnicalTeamContact()}>
          {t("I'm not sure, complete this setup with my technical team")}
        </Link>
          <div className={styles.footerButtons}>
            <Button
              className={styles.footerButton}
              theme="secondary"
              size="medium"
              onClick={() => onBack()}
            >
              {t("Back")}
            </Button>
            <Button
              className={styles.footerButton}
              theme="primary"
              size="medium"
              onClick={() => handleSubmitSelection()}
            >
              {t("Proceed")}
            </Button>
          </div>
        </div>
    </div>

    

    
  );
};

const LocationProvider = ({ provider, onProviderSelection, isProviderSelected }: any) => {
  const { t } = useTranslation();

  // Provider name should be display_name or name
  const getProviderName = (provider: any) =>
    provider?.display_name ? provider?.display_name : provider?.name;
  
  return (
    <div 
    className={styles.providerContainer} 
    id="provider-selection-provider-container">
      <img
        className={styles.logo}
        src={provider?.logo ? provider?.logo : genericGpsProvider}
        alt="Provider logo"
      />
      <label>{getProviderName(provider)}</label>
    
      <Checkbox
        size="medium"
        className={styles.providerCheckbox}
        label={""}
        checked={isProviderSelected(provider)}
        onChange={() => onProviderSelection(provider)}
      />
    </div>
  );
};

export default ProviderSelection;
