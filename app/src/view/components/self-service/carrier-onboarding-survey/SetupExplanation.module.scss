@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.container {
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
  align-items: center;
  flex: 2;
  border-left: 1px solid #dee2e6;
  padding-left: 24px;
  margin-top: -120px;
}

.containerExternal {
  composes: container;
  border-left: none;
  margin-top: 0;
  padding-left: 0;
}

.descriptionWrapper {
  display: flex;
  flex-direction: column;
}

.title {
  font-weight: 400;
  font-size: 24px;
  line-height: 36px;
  margin-block-start: 24px;
  margin-block-end: 12px;
}

.subTitle {
  composes: title;
  font-size: 20px;
  line-height: 30px;
  margin-block-start: -12px;
  margin-block-end: 12px;
}

.label {
  font-style: normal;
  font-weight: 400;
  font-size: 15px;
  line-height: 24px;
  text-align: justify;
}

.privacyMessage {
  font-style: normal;
  font-weight: 700;
  font-size: 15px;
  line-height: 24px;
  text-align: justify;
  margin-top: 12px;
  margin-bottom: 12px;
}

.privacyLink {
  cursor: pointer;
  width: fit-content;
  margin-bottom: 12px;
}

.slider {
  border: 1px solid #cfd4da;
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.24);
  border-radius: 6px;
}

.logos {
  width: 100%;
  object-fit: cover;
}
