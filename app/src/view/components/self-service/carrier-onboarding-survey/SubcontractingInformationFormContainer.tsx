import { useTranslation } from "react-i18next";
import React from "react";
import styles from "./SubcontractingInformationForm.module.scss";
import { Link } from "react-router-dom";
import { Button } from "@fourkites/elemental-atoms";
import { Input } from "@fourkites/elemental-input";
import { isFieldInvalid } from "view/components/base/FormUtils";

export const SubcontractingInformationForm = ({
  doesSubcontract,
  setDoesSubcontract,
  subcontractorCount,
  setSubcontractorCount,
  canProvideTracking,
  setCanProvideTracking,
  subcontractorTelematics,
  setSubcontractorTelematics,
  onNextStep,
  onBack,
  handleTechnicalTeamContact,
  shipperName = "shipper"
}: {
  doesSubcontract: string;
  setDoesSubcontract: React.Dispatch<React.SetStateAction<string>>;
  subcontractorCount: string;
  setSubcontractorCount: React.Dispatch<React.SetStateAction<string>>;
  canProvideTracking: string;
  setCanProvideTracking: React.Dispatch<React.SetStateAction<string>>;
  subcontractorTelematics: string;
  setSubcontractorTelematics: React.Dispatch<React.SetStateAction<string>>;
  onNextStep: () => void;
  onBack: () => void;
  handleTechnicalTeamContact: () => void;
  shipperName?: string;
}) => {
  const { t } = useTranslation();

  // Form is always valid since no fields are required
  const isFormValid = () => {
    return true;
  };

  const steps = ["Step One", "Step Two", "Step Three", "Step Four", "Step Five"];

  return (
    <div className={styles.pageContainer}>
      <div className={styles.container}>
        {/* Header Section */}
        <header className={styles.header}>
          <div className={styles.onboardingText}>
            <h1 className={styles.title}>Carrier Onboarding</h1>
            <p className={styles.subtitle}>Subcontracting Information</p>
          </div>
          <Stepper steps={steps} currentStep={3} />
        </header>

        {/* Main Content */}
        <main className={styles.content}>
          <SubcontractingFormContent
            doesSubcontract={doesSubcontract}
            setDoesSubcontract={setDoesSubcontract}
            subcontractorCount={subcontractorCount}
            setSubcontractorCount={setSubcontractorCount}
            canProvideTracking={canProvideTracking}
            setCanProvideTracking={setCanProvideTracking}
            subcontractorTelematics={subcontractorTelematics}
            setSubcontractorTelematics={setSubcontractorTelematics}
            shipperName={shipperName}
          />
        </main>

        {/* Footer Section */}
        <footer className={styles.footer}>
          <Link
            to="#"
            className={styles.footerLink}
            onClick={(e) => {
              e.preventDefault();
              handleTechnicalTeamContact();
            }}
          >
            {t("Get it done by a call")}
          </Link>
          
          <div className={styles.footerButtons}>
            <Button
              className={styles.footerButton}
              theme="secondary"
              size="medium"
              onClick={onBack}
            >
              {t("Back")}
            </Button>
            <Button
              className={styles.footerButton}
              theme="primary"
              size="medium"
              disabled={!isFormValid()}
              onClick={onNextStep}
            >
              {t("Proceed")}
            </Button>
          </div>
        </footer>
      </div>
    </div>
  );
};

export const Stepper = ({
  steps,
  currentStep,
}: {
  steps: string[];
  currentStep: number;
}) => {
  return (
    <div className={styles.stepper}>
      {steps.map((step, index) => (
        <div key={index} className={styles.stepContainer}>
          <div className={`${styles.step} ${index === currentStep ? styles.currentStep : ""}`}>
            <div
              className={`${styles.oval} ${
                index === currentStep 
                  ? styles.currentOvalSelected 
                  : index < currentStep 
                  ? styles.ovalCompleted 
                  : ""
              }`}
            >
              {index + 1}
            </div>
            <div
              className={`${styles.stepLabel} ${
                index === currentStep ? styles.currentStepLabel : ""
              }`}
            >
              {step}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export const SubcontractingFormContent = ({
  doesSubcontract,
  setDoesSubcontract,
  subcontractorCount,
  setSubcontractorCount,
  canProvideTracking,
  setCanProvideTracking,
  subcontractorTelematics,
  setSubcontractorTelematics,
  shipperName = "shipper"
}: {
  doesSubcontract: string;
  setDoesSubcontract: React.Dispatch<React.SetStateAction<string>>;
  subcontractorCount: string;
  setSubcontractorCount: React.Dispatch<React.SetStateAction<string>>;
  canProvideTracking: string;
  setCanProvideTracking: React.Dispatch<React.SetStateAction<string>>;
  subcontractorTelematics: string;
  setSubcontractorTelematics: React.Dispatch<React.SetStateAction<string>>;
  shipperName?: string;
}) => {
  const { t } = useTranslation();

  // Configuration for radio button groups - removed required checks
  const radioGroups = [
    {
      id: 'doesSubcontract',
      label: t(`Do you subcontract ${shipperName} loads to other carriers?`),
      value: doesSubcontract,
      setValue: setDoesSubcontract,
      required: false,
      options: [
        { value: "frequently", label: t("Yes, frequently") },
        { value: "occasionally", label: t("Yes, occasionally") },
        { value: "no", label: t("No") }
      ]
    },
    {
      id: 'subcontractorCount',
      label: t("Approximately how many subcontractors do you work with?"),
      value: subcontractorCount,
      setValue: setSubcontractorCount,
      required: false,
      options: [
        { value: "0", label: t("0") },
        { value: "1-5", label: t("1-5") },
        { value: "6-10", label: t("6-10") },
        { value: "11-20", label: t("11-20") },
        { value: "21-50", label: t("21-50") },
        { value: "more_than_50", label: t("more than 50") }
      ]
    },
    {
      id: 'canProvideTracking',
      label: t(`Are you able to ensure your subcontractors provide real-time GPS tracking for ${shipperName} loads?`),
      value: canProvideTracking,
      setValue: setCanProvideTracking,
      required: false,
      options: [
        { value: "yes_can_ensure_compliance", label: t("Yes, we can ensure compliance") },
        { value: "possibly_with_support", label: t("Possibly, but would need support") },
        { value: "no_difficult", label: t("No, this would be difficult") },
        { value: "dont_use_subcontractors", label: t(`I do not use subcontractors for ${shipperName} loads`) }
      ]
    }
  ];

  return (
    <div className={styles.formContainer}>
      <div className={styles.question}>
        <h2 className={styles.questionText}>
          {t("Subcontracting Information")}
        </h2>
        
        <div className={styles.formFields}>
          {/* Radio button groups */}
          {radioGroups.map((group, groupIndex) => (
            <div key={group.id} className={styles.options}>
              <span className={styles.radioGroupLabel}>
                {group.label}
              </span>
              <div className={styles.radioContainer}>
                {group.options.map((option, optionIndex) => (
                  <div key={option.value} className={styles.radioOption}>
                    <input
                      type="radio"
                      id={`${group.id}-${optionIndex}`}
                      name={group.id}
                      value={option.value}
                      checked={group.value === option.value}
                      onChange={() => group.setValue(option.value)}
                      className={styles.radioInput}
                      required={false} // Removed required
                    />
                    <label 
                      htmlFor={`${group.id}-${optionIndex}`} 
                      className={styles.radioLabel}
                    >
                      {option.label}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          ))}

          {/* Textarea field */}
          <div className={styles.options}>
            <label className={styles.textareaLabel}>
              {t("Do you know which telematics providers are used by your subcontractors? If yes, please list them:")}
            </label>
            <textarea
              className={styles.textarea}
              value={subcontractorTelematics}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => 
                setSubcontractorTelematics(e.target.value)
              }
              rows={4}
              placeholder={t("Enter your response here...")}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubcontractingInformationForm;