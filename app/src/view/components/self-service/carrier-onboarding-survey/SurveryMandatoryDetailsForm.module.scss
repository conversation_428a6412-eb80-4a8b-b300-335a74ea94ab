// CarrierOnboardingSurvey.module.scss - Complete Styles

// Variables
$primary-color: #2563eb;
$secondary-color: #64748b;
$success-color: #10b981;
$error-color: #ef4444;
$warning-color: #f59e0b;
$background-color: #f8fafc;
$white: #ffffff;
$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;

$border-radius: 8px;
$border-radius-lg: 12px;
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

// Mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin transition($property: all, $duration: 0.2s, $timing: ease-in-out) {
  transition: $property $duration $timing;
}

// Page Container
.pageContainer {
  min-height: 100vh;
  background-color: $background-color;
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;

  @media (max-width: 768px) {
    padding: 0;
    height: 100vh;
  }
}

// Main Container
.container {
  background-color: $white;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-lg;
  width: 100%;
  max-width: 800px;
  height: 100%;
  max-height: 90vh;
  display: grid;
  grid-template-rows: auto 1fr auto; // Header, Content, Footer
  overflow: hidden;

  @media (max-width: 768px) {
    max-width: 100%;
    height: 100vh;
    max-height: 100vh;
    border-radius: 0;
    box-shadow: none;
    grid-template-rows: auto 1fr auto;
  }
}

// Header Section
.header {
  background: linear-gradient(135deg, $primary-color 0%, #1d4ed8 100%);
  color: $white;
  padding: 1.5rem 2rem;
  @include flex-between;
  flex-wrap: wrap;
  gap: 1rem;
  grid-row: 1; // First row in grid

  @media (max-width: 768px) {
    padding: 1rem 1.5rem;
    flex-direction: column;
    align-items: flex-start;
  }
}

.onboardingText {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.title {
  font-size: 1.875rem;
  font-weight: 700;
  margin: 0;
  line-height: 1.2;

  @media (max-width: 768px) {
    font-size: 1.5rem;
  }
}

.subtitle {
  font-size: 1rem;
  font-weight: 400;
  opacity: 0.9;
  margin: 0;

  @media (max-width: 768px) {
    font-size: 0.875rem;
  }
}

// Stepper Component
.stepper {
  display: flex;
  align-items: center;
  gap: 1rem;

  @media (max-width: 768px) {
    gap: 0.5rem;
    flex-wrap: wrap;
  }
}

.stepContainer {
  display: flex;
  align-items: center;
  position: relative;

  &:not(:last-child)::after {
    content: '';
    position: absolute;
    right: -0.75rem;
    top: 50%;
    transform: translateY(-50%);
    width: 0.5rem;
    height: 2px;
    background-color: rgba(255, 255, 255, 0.3);

    @media (max-width: 768px) {
      display: none;
    }
  }
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.oval {
  @include flex-center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  color: $white;
  font-size: 0.875rem;
  font-weight: 600;
  @include transition();

  @media (max-width: 768px) {
    width: 1.75rem;
    height: 1.75rem;
    font-size: 0.75rem;
  }
}

.currentOvalSelected {
  background-color: $white;
  color: $primary-color;
  box-shadow: $shadow-sm;
}

.ovalCompleted {
  background-color: $success-color;
  color: $white;
}

.stepLabel {
  font-size: 0.75rem;
  font-weight: 500;
  opacity: 0.8;
  text-align: center;
  white-space: nowrap;

  @media (max-width: 768px) {
    font-size: 0.625rem;
  }
}

.currentStepLabel {
  opacity: 1;
  font-weight: 600;
}

// Content Section
.content {
  grid-row: 2; // Second row in grid
  padding: 2rem;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;

  // Custom scrollbar styling
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: $gray-100;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: $gray-300;
    border-radius: 4px;
    
    &:hover {
      background: $gray-400;
    }
  }

  // Firefox scrollbar
  scrollbar-width: thin;
  scrollbar-color: $gray-300 $gray-100;

  @media (max-width: 768px) {
    padding: 1.5rem;
    
    &::-webkit-scrollbar {
      width: 6px;
    }
  }

  @media (max-width: 640px) {
    padding: 1rem;
    
    &::-webkit-scrollbar {
      width: 4px;
    }
  }
}

// Form Container
.formContainer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.question {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  height: 100%;
}

.questionText {
  font-size: 1.5rem;
  font-weight: 600;
  color: $gray-800;
  margin: 0 0 1rem 0;
  line-height: 1.3;
  flex-shrink: 0;

  @media (max-width: 768px) {
    font-size: 1.25rem;
    margin-bottom: 0.75rem;
  }
}

.formFields {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  flex: 1;
  
  @media (max-width: 768px) {
    gap: 1.25rem;
  }
}

.options {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

// Checkbox Group Styling
.checkboxGroupLabel {
  font-size: 1rem;
  font-weight: 500;
  color: $gray-700;
  margin-bottom: 0.75rem;
  display: block;
}

.checkboxGroup {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1rem;
  background-color: $gray-50;
  border-radius: $border-radius;
  border: 1px solid $gray-200;
}

.otherOption {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid $gray-200;

  // Style the input that appears when "Other" is selected
  :global(.input-container) {
    margin-top: 0.5rem;
  }
}

// Radio Button Styling
.radioGroupLabel {
  font-size: 1rem;
  font-weight: 500;
  color: $gray-700;
  margin-bottom: 0.75rem;
  display: block;
  line-height: 1.4;

  &.required {
    font-weight: 600;
  }
}

.requiredAsterisk {
  color: $error-color;
  font-weight: 600;
}

.radioContainer {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1rem;
  background-color: $gray-50;
  border-radius: $border-radius;
  border: 1px solid $gray-200;
}

.radioOption {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
  @include transition();
  padding: 0.5rem;
  border-radius: 6px;

  &:hover {
    background-color: rgba(37, 99, 235, 0.05);
  }
}

.radioInput {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid $gray-300;
  border-radius: 50%;
  cursor: pointer;
  margin-top: 0.125rem;
  flex-shrink: 0;
  appearance: none;
  @include transition();
  position: relative;

  &:checked {
    border-color: $primary-color;
    background-color: $primary-color;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 0.5rem;
      height: 0.5rem;
      background-color: $white;
      border-radius: 50%;
    }
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }
}

.radioLabel {
  font-size: 0.875rem;
  color: $gray-700;
  line-height: 1.4;
  cursor: pointer;
  flex: 1;
  user-select: none;
}

// Textarea Styling
.textareaLabel {
  font-size: 1rem;
  font-weight: 500;
  color: $gray-700;
  margin-bottom: 0.5rem;
  display: block;
  line-height: 1.4;
}

.textarea {
  width: 100%;
  min-height: 120px;
  padding: 0.75rem;
  border: 1px solid $gray-300;
  border-radius: $border-radius;
  font-size: 0.875rem;
  font-family: inherit;
  line-height: 1.5;
  resize: vertical;
  @include transition(border-color);
  box-sizing: border-box;

  &:focus {
    outline: none;
    border-color: $primary-color;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }

  &::placeholder {
    color: $gray-400;
  }
}

// Error Text Styling
.errorText {
  color: $error-color;
  font-size: 0.75rem;
  margin-top: 0.5rem;
  font-weight: 500;
}

// Footer Section
.footer {
  background-color: $gray-50;
  border-top: 1px solid $gray-200;
  padding: 1.25rem 2rem;
  @include flex-between;
  flex-wrap: wrap;
  gap: 1rem;
  grid-row: 3; // Third row in grid
  position: relative;
  z-index: 10;

  @media (max-width: 768px) {
    padding: 1rem 1.5rem;
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  @media (max-width: 640px) {
    padding: 0.75rem 1rem;
  }
}

.footerLink {
  color: $primary-color;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  @include transition();

  &:hover {
    color: #1d4ed8;
    text-decoration: underline;
  }

  @media (max-width: 768px) {
    text-align: center;
  }
}

.footerButtons {
  display: flex;
  gap: 1rem;
  align-items: center;

  @media (max-width: 768px) {
    width: 100%;
    justify-content: space-between;
  }
}

.footerButton {
  min-width: 100px;
  font-weight: 500;
  @include transition();

  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: $shadow-md;
  }

  @media (max-width: 768px) {
    flex: 1;
    min-width: auto;
  }
}

// Form Input Overrides
:global {
  .input-container {
    .input-label {
      font-weight: 500;
      color: $gray-700;
      margin-bottom: 0.5rem;
    }

    .input-field {
      border-radius: $border-radius;
      border: 1px solid $gray-300;
      padding: 0.75rem;
      font-size: 0.875rem;
      @include transition(border-color);

      &:focus {
        outline: none;
        border-color: $primary-color;
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
      }

      &.invalid {
        border-color: $error-color;
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
      }
    }

    .error-message {
      color: $error-color;
      font-size: 0.75rem;
      margin-top: 0.25rem;
    }
  }

  // Checkbox Styling
  .checkbox-container {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    
    .checkbox-input {
      margin-top: 0.125rem;
    }

    .checkbox-label {
      font-size: 0.875rem;
      color: $gray-700;
      line-height: 1.4;
    }
  }

  // Button Styling
  button {
    border-radius: $border-radius;
    font-weight: 500;
    @include transition();
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
}

// Loading States
.loading {
  opacity: 0.7;
  pointer-events: none;
}

// Animation Classes
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fadeIn {
  animation: fadeIn 0.3s ease-out;
}

// Responsive Design
@media (max-width: 640px) {
  .pageContainer {
    padding: 0;
    height: 100vh;
  }

  .container {
    height: 100vh;
    max-height: 100vh;
    border-radius: 0;
    box-shadow: none;
    grid-template-rows: auto 1fr auto;
  }

  .header {
    padding: 1rem;
  }

  .content {
    padding: 1rem;
  }

  .footer {
    padding: 0.75rem 1rem;
  }

  .questionText {
    font-size: 1.125rem;
    margin-bottom: 0.5rem;
  }

  .formFields {
    gap: 1rem;
  }

  .checkboxGroup {
    padding: 0.75rem;
  }

  .radioContainer {
    padding: 0.75rem;
  }
}

// Additional mobile optimizations
@media (max-width: 480px) {
  .container {
    height: 100vh;
    grid-template-rows: auto 1fr auto;
  }

  .content {
    padding: 0.75rem;
  }

  .footer {
    padding: 0.5rem 0.75rem;
  }

  .footerButtons {
    gap: 0.5rem;
  }

  .checkboxGroup {
    padding: 0.5rem;
    gap: 0.5rem;
  }

  .radioContainer {
    padding: 0.5rem;
    gap: 0.5rem;
  }

  .radioOption {
    padding: 0.25rem;
  }

  .questionText {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }
}

// Ensure proper height on very small screens
@media (max-height: 600px) {
  .pageContainer {
    padding: 0;
  }
  
  .container {
    height: 100vh;
    max-height: 100vh;
  }
  
  .header {
    padding: 0.75rem 1rem;
  }
  
  .content {
    padding: 1rem;
  }
  
  .footer {
    padding: 0.75rem 1rem;
  }
}

// High Contrast Mode Support
@media (prefers-contrast: high) {
  .container {
    border: 2px solid $gray-800;
  }

  .oval {
    border: 2px solid currentColor;
  }

  .footerLink {
    text-decoration: underline;
  }

  .radioInput {
    border-width: 3px;
  }
}

// Reduced Motion Support
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// Print Styles
@media print {
  .pageContainer {
    height: auto;
    background: white;
    padding: 0;
  }

  .container {
    box-shadow: none;
    border: 1px solid #ccc;
    height: auto;
    max-height: none;
    display: block;
  }

  .header {
    background: white !important;
    color: black !important;
    border-bottom: 2px solid #ccc;
  }

  .footer {
    display: none;
  }

  .content {
    padding: 1rem;
    overflow: visible;
  }
}

.fieldDescription {
  font-size: 0.75rem;
  color: $gray-500;
  margin-top: 0.375rem;
  margin-bottom: 0;
  line-height: 1.4;
  font-style: italic;
  
  @media (max-width: 768px) {
    font-size: 0.6875rem;
    margin-top: 0.25rem;
  }
}