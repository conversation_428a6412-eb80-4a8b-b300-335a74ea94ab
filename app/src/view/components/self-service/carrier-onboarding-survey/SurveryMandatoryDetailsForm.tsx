import { useTranslation } from "react-i18next";
import styles from "./SurveryMandatoryDetailsForm.module.scss";
import { Link } from "react-router-dom";
import { Button } from "@fourkites/elemental-atoms";
import { Input } from "@fourkites/elemental-input";
import { Select } from "@fourkites/elemental-select"; // Add Select component import
import { isFieldInvalid, isEmailInvalid } from "view/components/base/FormUtils";

export const SurveryMandatoryDetailsForm = ({
  email,
  setEmail,
  carrierName,
  setCarrierName,
  vatNumber,
  setVatNumber,
  vatType, // Add vatType prop
  setVatType, // Add setVatType prop
  contactName,
  setContactName,
  contactPhoneNumber,
  setContactPhoneNumber,
  contactEmail,
  setContactEmail,
  onNextStep,
  onBack,
  handleTechnicalTeamContact,
  shipperName = "shipper"
}: {
  email: string;
  setEmail: React.Dispatch<React.SetStateAction<string>>;
  carrierName: string;
  setCarrierName: React.Dispatch<React.SetStateAction<string>>;
  vatNumber: string;
  setVatNumber: React.Dispatch<React.SetStateAction<string>>;
  vatType: string; // Add vatType type
  setVatType: React.Dispatch<React.SetStateAction<string>>; // Add setVatType type
  contactName: string;
  setContactName: React.Dispatch<React.SetStateAction<string>>;
  contactPhoneNumber: string;
  setContactPhoneNumber: React.Dispatch<React.SetStateAction<string>>;
  contactEmail: string;
  setContactEmail: React.Dispatch<React.SetStateAction<string>>;
  onNextStep: () => void;
  onBack: () => void;
  handleTechnicalTeamContact: () => void;
  shipperName?: string;
}) => {
  const { t } = useTranslation();

  // Check if all required fields are valid
  const isFormValid = () => {
    return !isFieldInvalid(contactName) &&
           !isFieldInvalid(contactPhoneNumber) &&
           !isEmailInvalid(contactEmail);
  };

  const steps = ["Step One", "Step Two", "Step Three", "Step Four", "Step Five"];

  return (
    <div className={styles.pageContainer}>
      <div className={styles.container}>
        {/* Header Section */}
        <header className={styles.header}>
          <div className={styles.onboardingText}>
            <h1 className={styles.title}>Carrier Onboarding</h1>
            <p className={styles.subtitle}>Gathering Company and User Info</p>
          </div>
          <Stepper steps={steps} currentStep={0} />
        </header>

        {/* Main Content */}
        <main className={styles.content}>
          <CarrierOnboardingSurveyForm
            email={email}
            setEmail={setEmail}
            carrierName={carrierName}
            setCarrierName={setCarrierName}
            vatNumber={vatNumber}
            setVatNumber={setVatNumber}
            vatType={vatType}
            setVatType={setVatType}
            contactName={contactName}
            setContactName={setContactName}
            contactPhoneNumber={contactPhoneNumber}
            setContactPhoneNumber={setContactPhoneNumber}
            contactEmail={contactEmail}
            setContactEmail={setContactEmail}
            shipperName={shipperName}
          />
        </main>

        {/* Footer Section */}
        <footer className={styles.footer}>
          <Link
            to="#"
            className={styles.footerLink}
            onClick={(e) => {
              e.preventDefault();
              handleTechnicalTeamContact();
            }}
          >
            {t("Get it done by a call")}
          </Link>
          
          <div className={styles.footerButtons}>
            <Button
              className={styles.footerButton}
              theme="secondary"
              size="medium"
              onClick={onBack}
            >
              {t("Back")}
            </Button>
            <Button
              className={styles.footerButton}
              theme="primary"
              size="medium"
              disabled={!isFormValid()}
              onClick={onNextStep}
            >
              {t("Proceed")}
            </Button>
          </div>
        </footer>
      </div>
    </div>
  );
};

export const Stepper = ({
  steps,
  currentStep,
}: {
  steps: string[];
  currentStep: number;
}) => {
  return (
    <div className={styles.stepper}>
      {steps.map((step, index) => (
        <div key={index} className={styles.stepContainer}>
          <div className={`${styles.step} ${index === currentStep ? styles.currentStep : ""}`}>
            <div
              className={`${styles.oval} ${
                index === currentStep 
                  ? styles.currentOvalSelected 
                  : index < currentStep 
                  ? styles.ovalCompleted 
                  : ""
              }`}
            >
              {index + 1}
            </div>
            <div
              className={`${styles.stepLabel} ${
                index === currentStep ? styles.currentStepLabel : ""
              }`}
            >
              {step}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export const CarrierOnboardingSurveyForm = ({
  email,
  setEmail,
  carrierName,
  setCarrierName,
  vatNumber,
  setVatNumber,
  vatType,
  setVatType,
  contactName,
  setContactName,
  contactPhoneNumber,
  setContactPhoneNumber,
  contactEmail,
  setContactEmail,
  shipperName = "shipper"
}: {
  email: string;
  setEmail: React.Dispatch<React.SetStateAction<string>>;
  carrierName: string;
  setCarrierName: React.Dispatch<React.SetStateAction<string>>;
  vatNumber: string;
  setVatNumber: React.Dispatch<React.SetStateAction<string>>;
  vatType: string;
  setVatType: React.Dispatch<React.SetStateAction<string>>;
  contactName: string;
  setContactName: React.Dispatch<React.SetStateAction<string>>;
  contactPhoneNumber: string;
  setContactPhoneNumber: React.Dispatch<React.SetStateAction<string>>;
  contactEmail: string;
  setContactEmail: React.Dispatch<React.SetStateAction<string>>;
  shipperName?: string;
}) => {
  const { t } = useTranslation();

  // ID type options for the dropdown
  const idTypeOptions = [
    t("USDOT"),
    t("MC"), 
    t("VAT")
  ];

  // Form field configuration for cleaner rendering
  const formFields = [
    {
      label: t("Technical Point of Contact Name"),
      value: contactName,
      setValue: setContactName,
      validator: isFieldInvalid,
      errorLabel: t("Field is required"),
      description: null,
      type: "input"
    },
    {
      label: t("Technical Contact Phone Number"),
      value: contactPhoneNumber,
      setValue: setContactPhoneNumber,
      validator: isFieldInvalid,
      errorLabel: t("Field is required"),
      description: null,
      type: "input"
    },
    {
      label: t("Technical Contact Email"),
      value: contactEmail,
      setValue: setContactEmail,
      validator: isEmailInvalid,
      errorLabel: t("A valid email is required"),
      description: null,
      type: "input"
    }
  ];

  return (
    <div className={styles.formContainer}>
      <div className={styles.question}>
        <h2 className={styles.questionText}>
          {t("Enter Technical contact details")}
        </h2>
        
        <div className={styles.formFields}>
          {formFields.map((field, index) => (
            <div key={index} className={styles.options}>
              {(
                <Input
                  label={field.label}
                  errorLabel={field.errorLabel}
                  value={field.value}
                  invalid={field.validator(field.value)}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => 
                    field.setValue(e.target.value)
                  }
                  required
                />
              )}
              {field.description && (
                <p className={styles.fieldDescription}>
                  {field.description}
                </p>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SurveryMandatoryDetailsForm;