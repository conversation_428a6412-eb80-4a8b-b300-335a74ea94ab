import React from 'react';
import { useTranslation } from 'react-i18next';
import styles from './SurveyCompletionPage.module.scss';
import fourkitesCircleLogo from "assets/img/fourkitesCircleLogo.png";
import { Button } from "@fourkites/elemental-atoms";
import { onNav } from "router/navigationUtils";
import { fourkitesUrls, FOURKITES_APP_URL } from "api/http/apiUtils";

interface SurveyCompletionPageProps {
  shipperName: string;
  companyAlreadyExists: boolean;
  onboardingToken?: string;
  carrierName?: string;
  contactEmail?: string;
}

const SurveyCompletionPage: React.FC<SurveyCompletionPageProps> = ({
  shipperName,
  companyAlreadyExists,
  onboardingToken,
  carrierName = "Carrier",
  contactEmail
}) => {
  const { t } = useTranslation();

  const handlePrimaryAction = () => {
    if (companyAlreadyExists) {
      // Company already exists, redirect to login page
      onNav(fourkitesUrls.login);
    } else {
      // New company, redirect to onboarding URL with token
      if (onboardingToken) {
        onNav(`${FOURKITES_APP_URL}self-service/onboarding/${onboardingToken}`);
      } else {
        // Fallback to login if no token
        onNav(fourkitesUrls.login);
      }
    }
  };



  const getNextStepInfo = () => {
    if (companyAlreadyExists) {
      return {
        title: t("Ready to Access Your Account"),
        description: t("Your company already has a FourKites account. You can now sign in to access your dashboard and manage your shipments."),
        buttonText: t("Login to FourKites"),
        buttonIcon: "🔐"
      };
    } else {
      return {
        title: t("Your next step is to complete your registration"),
        description: t("This is required to create your FourKites account, which you'll need to log in and continue the onboarding process."),
        buttonText: t("Complete Registration"),
        buttonIcon: "🚀"
      };
    }
  };

  const nextStepInfo = getNextStepInfo();

  return (
    <div className={styles.container}>
      <div className={styles.completionCard}>
        {/* Header Section */}
        <div className={styles.header}>
          <div className={styles.logoContainer}>
            <img 
              src={fourkitesCircleLogo} 
              alt="FourKites Logo" 
              className={styles.fourkitesLogo}
            />
          </div>
          
          {/* Success Icon */}
          <div className={styles.successIcon}>
            <div className={styles.checkmarkContainer}>
              <svg className={styles.checkmark} viewBox="0 0 24 24" fill="none">
                <path 
                  d="M9 12l2 2 4-4" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                />
              </svg>
            </div>
          </div>

          <h1 className={styles.title}>
            {t("Survey Complete!")}
          </h1>
          
          <p className={styles.subtitle}>
            {t("Thank you for completing the carrier onboarding survey")}
          </p>
        </div>

        {/* Content Section */}
        <div className={styles.content}>
          {/* Thank You Message */}
          <div className={styles.thankYouSection}>
            <p className={styles.description}>
              {t("Thank you for completing the survey! We'll be in touch soon with next steps for your integration with {{shipperName}}.", { shipperName })}
            </p>
            {!companyAlreadyExists && (
              <div className={styles.nextStepNotice}>
                <p className={styles.noticeText}>
                  {t("Since your company is new to FourKites, you must complete the registration process to create your account and access the platform.")}
                </p>
              </div>
            )}
          </div>

          {/* Next Step Action */}
          <div className={styles.actionSection}>
            {/* Action Buttons */}
            <div className={styles.buttonContainer}>
              <Button
                size="large"
                theme="primary"
                onClick={handlePrimaryAction}
                className={styles.primaryButton}
              >
                {nextStepInfo.buttonText}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SurveyCompletionPage;
