import { useTranslation } from "react-i18next";
import React, { useState } from 'react';
import styles from "./TMSInformationForm.module.scss";
import { Link } from "react-router-dom";
import { Button } from "@fourkites/elemental-atoms";
import { Input } from "@fourkites/elemental-input";
import { isFieldInvalid } from "view/components/base/FormUtils";
import { Checkbox } from "@fourkites/elemental-checkbox";

// TMS Information Form - Page 1: TMS Provider & Data Types
export const TMSInformationForm = ({
  tmsProvider,
  setTmsProvider,
  tmsType,
  setTmsType,
  tmsDataTypes,
  setTmsDataTypes,
  integrationTypes,
  setIntegrationTypes,
  onNextStep,
  onBack,
  handleTechnicalTeamContact
}: {
  tmsProvider: string;
  setTmsProvider: React.Dispatch<React.SetStateAction<string>>;
  tmsType: string;
  setTmsType: React.Dispatch<React.SetStateAction<string>>;
  tmsDataTypes: string[];
  setTmsDataTypes: React.Dispatch<React.SetStateAction<string[]>>;
  integrationTypes: string[];
  setIntegrationTypes: React.Dispatch<React.SetStateAction<string[]>>;
  onNextStep: () => void;
  onBack: () => void;
  handleTechnicalTeamContact: () => void;
}) => {
  const { t } = useTranslation();

  const handleDataTypeChange = (type: string, checked: boolean) => {
    if (checked) {
      setTmsDataTypes([...tmsDataTypes, type]);
    } else {
      setTmsDataTypes(tmsDataTypes.filter(item => item !== type));
    }
  };

  // Check if required field is valid
  const isFormValid = () => {
    return !isFieldInvalid(tmsType) && !isFieldInvalid(tmsProvider);
  };

  const steps = ["Step One", "Step Two", "Step Three", "Step Four", "Step Five"];

  return (
    <div className={styles.pageContainer}>
      <div className={styles.container}>
        {/* Header Section */}
        <header className={styles.header}>
          <div className={styles.onboardingText}>
            <h1 className={styles.title}>Carrier Onboarding</h1>
            <p className={styles.subtitle}>Transportation Management System</p>
          </div>
          <Stepper steps={steps} currentStep={1} />
        </header>

        {/* Main Content */}
        <main className={styles.content}>
          <TMSProviderFormContent
            tmsProvider={tmsProvider}
            setTmsProvider={setTmsProvider}
            tmsType={tmsType}
            setTmsType={setTmsType}
            tmsDataTypes={tmsDataTypes}
            handleDataTypeChange={handleDataTypeChange}
            integrationTypes={integrationTypes}
            setIntegrationTypes={setIntegrationTypes}
          />
        </main>

        {/* Footer Section */}
        <footer className={styles.footer}>
          <Link
            to="#"
            className={styles.footerLink}
            onClick={(e) => {
              e.preventDefault();
              handleTechnicalTeamContact();
            }}
          >
            {t("Get it done by a call")}
          </Link>
          
          <div className={styles.footerButtons}>
            <Button
              className={styles.footerButton}
              theme="secondary"
              size="medium"
              onClick={onBack}
            >
              {t("Back")}
            </Button>
            <Button
              className={styles.footerButton}
              theme="primary"
              size="medium"
              disabled={!isFormValid()}
              onClick={onNextStep}
            >
              {t("Proceed")}
            </Button>
          </div>
        </footer>
      </div>
    </div>
  );
};

export const Stepper = ({
  steps,
  currentStep,
}: {
  steps: string[];
  currentStep: number;
}) => {
  return (
    <div className={styles.stepper}>
      {steps.map((step, index) => (
        <div key={index} className={styles.stepContainer}>
          <div className={`${styles.step} ${index === currentStep ? styles.currentStep : ""}`}>
            <div
              className={`${styles.oval} ${
                index === currentStep 
                  ? styles.currentOvalSelected 
                  : index < currentStep 
                  ? styles.ovalCompleted 
                  : ""
              }`}
            >
              {index + 1}
            </div>
            <div
              className={`${styles.stepLabel} ${
                index === currentStep ? styles.currentStepLabel : ""
              }`}
            >
              {step}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export const TMSProviderFormContent = ({
  tmsProvider,
  setTmsProvider,
  tmsType,
  setTmsType,
  tmsDataTypes,
  handleDataTypeChange,
  integrationTypes,
  setIntegrationTypes
}: {
  tmsProvider: string;
  setTmsProvider: React.Dispatch<React.SetStateAction<string>>;
  tmsType: string;
  setTmsType: React.Dispatch<React.SetStateAction<string>>;
  tmsDataTypes: string[];
  handleDataTypeChange: (type: string, checked: boolean) => void;
  integrationTypes: string[];
  setIntegrationTypes: React.Dispatch<React.SetStateAction<string[]>>;
}) => {
  const { t } = useTranslation();
  const [otherDataType, setOtherDataType] = useState("");
  const [otherIntegrationType, setOtherIntegrationType] = useState("");

  const dataTypeOptions = [
    { value: "truck_trailer_numbers_or_license_plates", label: t("Truck/Trailer numbers or license plates") },
    { value: "shipment_in_transit_location_updates", label: t("Shipment in transit location updates") },
    { value: "shipment_status_updates", label: t("Shipment status updates") },
    { value: "route_planning", label: t("Route planning (Pickup & Delivery stops, Intermodal terminal stops)") },
    { value: "appointment_times", label: t("Appointment times") },
    { value: "shipment_eta", label: t("Shipment ETA (Estimated Time of Arrival at the final destination)") },
    { value: "rail_wagon_numbers", label: t("Rail wagon numbers") },
    { value: "container_numbers", label: t("Container numbers") },
    { value: "reason_codes", label: t("Reason codes") },
    { value: "edocuments_such_as_pod", label: t("eDocuments such as PODs") }
  ];

  const integrationTypeOptions = [
    { value: "sftp", label: t("SFTP") },
    { value: "api", label: t("API") },
    { value: "email", label: t("Email") },
  ];

  const handleOtherDataTypeChange = (value: string) => {
    setOtherDataType(value);
    if (value && !tmsDataTypes.includes("other")) {
      handleDataTypeChange("other", true);
    } else if (!value && tmsDataTypes.includes("other")) {
      handleDataTypeChange("other", false);
    }
  };

  const handleIntegrationTypeChange = (type: string, checked: boolean) => {
    if (checked) {
      setIntegrationTypes([...integrationTypes, type]);
    } else {
      setIntegrationTypes(integrationTypes.filter(item => item !== type));
    }
  };

  const handleOtherIntegrationTypeChange = (value: string) => {
    setOtherIntegrationType(value);
    if (value && !integrationTypes.includes("other")) {
      handleIntegrationTypeChange("other", true);
    } else if (!value && integrationTypes.includes("other")) {
      handleIntegrationTypeChange("other", false);
    }
  };

  return (
    <div className={styles.formContainer}>
      <div className={styles.question}>
        <h2 className={styles.questionText}>
          {t("Transportation Management System (TMS)")}
        </h2>
        
        <div className={styles.formFields}>
          {/* TMS Type Selection */}
          <div className={styles.options}>
            <span className={styles.checkboxGroupLabel}>
              {t("How do you manage your transportation operations?")}
            </span>
            <div className={styles.radioGroup}>
              <label className={styles.radioOption}>
                <input
                  type="radio"
                  name="tmsType"
                  value="tms"
                  checked={tmsType === 'tms'}
                  onChange={(e) => {
                    setTmsType(e.target.value);
                    if (e.target.value === 'tms') {
                      setTmsProvider(''); // Reset provider when switching types
                    }
                  }}
                />
                <span className={styles.radioLabel}>
                  {t("Transportation Management System (TMS)")}
                </span>
                <p className={styles.radioDescription}>
                  {t("I use a dedicated TMS software/platform (e.g., McLeod, TMW, MercuryGate, etc.)")}
                </p>
              </label>

              <label className={styles.radioOption}>
                <input
                  type="radio"
                  name="tmsType"
                  value="manual"
                  checked={tmsType === 'manual'}
                  onChange={(e) => {
                    setTmsType(e.target.value);
                  }}
                />
                <span className={styles.radioLabel}>
                  {t("Manual Process")}
                </span>
                <p className={styles.radioDescription}>
                  {t("I use spreadsheets, emails, phone calls, or other manual methods")}
                </p>
              </label>
            </div>
          </div>

          {/* TMS Provider Field - Only show if TMS is selected */}
          {tmsType === 'tms' && (
            <div className={styles.options}>
              <Input
                label={t("Which TMS provider/system do you use?")}
                errorLabel={t("Field is required")}
                value={tmsProvider}
                invalid={isFieldInvalid(tmsProvider)}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setTmsProvider(e.target.value)
                }
                required={true}
                placeholder={t("e.g., McLeod, TMW, MercuryGate, Oracle TMS, etc.")}
              />
              <p className={styles.fieldDescription}>
                {t("Enter the name of your TMS software or platform")}
              </p>
            </div>
          )}

          {/* Manual Process Details - Only show if Manual is selected */}
          {tmsType === 'manual' && (
            <div className={styles.options}>
              <Input
                label={t("Please describe your current process")}
                errorLabel={t("Field is required")}
                value={tmsProvider}
                invalid={isFieldInvalid(tmsProvider)}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setTmsProvider(e.target.value)
                }
                required={true}
                placeholder={t("e.g., Excel spreadsheets, email coordination, phone calls, etc.")}
              />
              <p className={styles.fieldDescription}>
                {t("Briefly describe how you currently manage shipments and track loads")}
              </p>
            </div>
          )}

          {/* TMS Data Types Checkbox Section */}
          <div className={styles.options}>
            <span className={styles.checkboxGroupLabel}>
              {t("What transportation data do you currently track or manage? (Select all that apply)")}
            </span>
            <div className={styles.checkboxGroup}>
              {dataTypeOptions.map((option, index) => (
                <Checkbox
                  key={index}
                  label={option.label}
                  checked={tmsDataTypes.includes(option.value)}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    handleDataTypeChange(option.value, e.target.checked)
                  }
                />
              ))}
              
              {/* Other option with input */}
              <div className={styles.otherOption}>
                <Checkbox
                  label="Other"
                  checked={tmsDataTypes.includes("other")}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    handleDataTypeChange("other", e.target.checked);
                    if (!e.target.checked) {
                      setOtherDataType("");
                    }
                  }}
                />
                {tmsDataTypes.includes("other") && (
                  <Input
                    label=""
                    value={otherDataType}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      handleOtherDataTypeChange(e.target.value);
                    }}
                    placeholder="Please specify"
                  />
                )}
              </div>
            </div>
          </div>

          {/* Integration Types Checkbox Section */}
          <div className={styles.options}>
            <span className={styles.checkboxGroupLabel}>
              {t("How can you integrate and share data with Fourkites? (Select all that apply)")}
            </span>
            <div className={styles.checkboxGroup}>
              {integrationTypeOptions.map((option, index) => (
                <Checkbox
                  key={index}
                  label={option.label}
                  checked={integrationTypes.includes(option.value)}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    handleIntegrationTypeChange(option.value, e.target.checked)
                  }
                />
              ))}

              {/* Other option with input */}
              <div className={styles.otherOption}>
                <Checkbox
                  label="Other"
                  checked={integrationTypes.includes("other")}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    handleIntegrationTypeChange("other", e.target.checked);
                    if (!e.target.checked) {
                      setOtherIntegrationType("");
                    }
                  }}
                />
                {integrationTypes.includes("other") && (
                  <Input
                    label=""
                    value={otherIntegrationType}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      handleOtherIntegrationTypeChange(e.target.value);
                    }}
                    placeholder="Please specify"
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TMSInformationForm;
