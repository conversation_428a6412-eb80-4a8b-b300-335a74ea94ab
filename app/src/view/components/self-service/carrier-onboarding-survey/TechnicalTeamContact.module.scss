@import "@fourkites/elemental-atoms/build/scss/colors/index";
.container {
    display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 32px auto;
  padding: 24px;
  background-color: $color-neutral-100;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  }
  
  @media (max-width: 991px) {
    .container {
      padding: 0 20px;
    }
  }
  
  .title {
    color: var(--Text-Colors-Default, #21252a);
    font-size: 18px;
    font-weight: 700;
    align-self: start;
  }
  
  .description {
    color: var(--Text-Colors-Secondary, #495057);
    font-size: 16px;
    align-self: start;
  }
  
  @media (max-width: 991px) {
    .description {
      max-width: 100%;
    }
  }
  
  .formGroup {
    display: flex;
    margin-top: 24px;
    width: 100%;
    gap: 20px;
    flex-wrap: wrap;
    justify-content: space-between;
  }
  
  @media (max-width: 991px) {
    .formGroup {
      max-width: 100%;
      margin-top: 20px;
    }
  }
  
  .labelWrapper {
    display: flex;
    align-items: center;
    font-size: 16px;
    color: var(--Text-Colors-Secondary, #495057);
    justify-content: start;
    margin: auto 0;
  }
  
  .label {
    align-self: stretch;
    margin: auto 0;
  }
  
  .requiredIcon {
    aspect-ratio: 0.83;
    object-fit: contain;
    object-position: center;
    width: 10px;
    align-self: stretch;
    margin: auto 0;
  }
  
  .input {
    align-self: stretch;
    border-radius: 4px;
    border: 1px solid var(--Components-Input-Field-Borders-Default, #adb5bd);
    background-color: #fff;
    gap: 10px;
    overflow: hidden;
    font-size: 14px;
    color: var(--Text-Colors-Default, #21252a);
    line-height: 24px;
    padding: 4px 8px;
    width: 100%;
  }
  
  @media (max-width: 991px) {
    .input {
      white-space: initial;
    }
  }
  
  .contactList {
    list-style-type: none;
    padding: 0;
    margin: 0;
    width: 100%;
  }
  
  .contactItem {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
  }
  
  .deleteButton {
    background-color: transparent;
    color: var(--Text-Colors-Secondary, #495057);
    border: none;
    padding: 5px 10px;
    margin-left: 10px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
  }
  
  .deleteIcon {
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }
  
  .addButton {
    background-color: transparent;
    color: var(--Primary-500, #0e65e5);
    border: none;
    padding: 5px 10px;
    margin-top: 10px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
  }
  
  .buttonGroup {
    align-self: end;
    display: flex;
    margin-top: 48px;
    width: 256px;
    max-width: 100%;
    gap: 16px;
    font-size: 14px;
    font-weight: 600;
    line-height: 32px;
  }
  
  @media (max-width: 991px) {
    .buttonGroup {
      margin-top: 40px;
    }
  }
  
  .outlineButton {
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    border: 2px solid var(--Primary-500, #0e65e5);
    display: flex;
    min-height: 32px;
    gap: 16px;
    color: var(--Primary-500, #0e65e5);
    flex: 1;
    padding: 0 16px;
    background-color: transparent;
    cursor: pointer;
  }
  
  .buttonContent {
    align-self: stretch;
    display: flex;
    align-items: center;
    gap: 8px;
    justify-content: center;
    margin: auto 0;
  }
  
  .buttonIcon {
    aspect-ratio: 1;
    object-fit: contain;
    object-position: center;
    width: 16px;
    align-self: stretch;
    margin: auto 0;
  }
  
  .solidButton {
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    background: var(--components-solid-buttons-primary-default, #0e65e5);
    display: flex;
    min-height: 32px;
    gap: 16px;
    color: rgba(255, 255, 255, 1);
    flex: 1;
    padding: 0 16px;
    border: none;
    cursor: pointer;
  }
  
  .visually-hidden {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
  
  .toggleWrapper {
    display: flex;
    align-items: center;
    margin-top: 20px;
  }
  
  .toggleLabel {
    margin-right: 10px;
    font-size: 16px;
    color: var(--Text-Colors-Secondary, #495057);
  }
  
  .toggleSwitch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
  }
  
  .toggleSwitch input {
    opacity: 0;
    width: 0;
    height: 0;
  }
  
  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
  }
  
  .slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
  }
  
  input:checked + .slider {
    background-color: var(--Primary-500, #0e65e5);
  }
  
  input:checked + .slider:before {
    transform: translateX(26px);
  }
  
  .toggleDescription {
    margin-top: 8px;
    font-size: 14px;
    color: var(--Text-Colors-Secondary, #495057);
  }