import React, { useState } from 'react';
import styles from './TechnicalTeamContact.module.scss';
import ContactProps  from "./Contact.types"
import { PhoneInput } from '@fourkites/elemental-phone-input';

interface TechnicalTeamContactProps {
  contacts: ContactProps[];
  setContacts: (contacts: ContactProps[]) => void;
  isTechnicalContact: boolean;
  setIsTechnicalContact: (isTechnicalContact: boolean) => void;
}

const TechnicalTeamContact: React.FC<TechnicalTeamContactProps> = ({
  contacts,
  setContacts,
  isTechnicalContact,
  setIsTechnicalContact,
}) => {

  const handleContactChange = (id: number, field: string, value: string) => {
    setContacts(contacts.map(contact =>
        contact.id === id ? { ...contact, [field]: value } : contact
      )
    );
  };

  const addContact = () => {
    if (contacts.length < 2) {
      const newId = Math.max(...contacts.map(c => c.id)) + 1;
      setContacts([...contacts, { id: newId, email: '', phone: '', countryCode: 'US' }]);
    }
  };

  const removeContact = (id: number) => {
    if (contacts.length > 1) {
      setContacts(contacts.filter(contact => contact.id !== id));
    }
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    console.log('Submitted:', { contacts, isTechnicalContact });
  };

  return (
    <section className={styles.container}>
      <h1 className={styles.title}>Technical (or IT) Team's Contact Information</h1>
      <p className={styles.description}>
        Provide your technical team's contact information to complete the setup
      </p>
      <form onSubmit={handleSubmit}>
        <ul className={styles.contactList}>
          {contacts.map((contact) => (
            <li key={contact.id} className={styles.contactItem}>
              <div className={styles.formGroup}>
                <label htmlFor={`email-${contact.id}`} className={styles.labelWrapper}>
                  <span className={styles.label}>Email ID</span>
                  </label>
                <input
                  type="email"
                  id={`email-${contact.id}`}
                  className={styles.input}
                  placeholder="<EMAIL>"
                  aria-label="Email ID"
                  required
                  value={contact.email}
                  onChange={(e) => handleContactChange(contact.id, 'email', e.target.value)}
                />
              </div>
              <div className={styles.formGroup}>
                <label htmlFor={`phone-${contact.id}`} className={styles.labelWrapper}>
                  <span className={styles.label}>Phone Number</span>
                  </label>

                  <PhoneInput
                    label=""
                    placeholder="Phone number"
                    value={contact.phone}
                    onChange={(phoneNumber) => handleContactChange(contact.id, 'phone', phoneNumber)}
                    country={contact.countryCode}
                    onChangeCountry={(newCountryCode) => handleContactChange(contact.id, 'countryCode', newCountryCode)}
                  />
              </div>
              {contacts.length > 1 && (
                <button
                  type="button"
                  className={styles.deleteButton}
                  onClick={() => removeContact(contact.id)}
                  aria-label="Remove contact"
                >
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M2 4H3.33333H14" stroke="#495057" strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M5.33331 4.00004V2.66671C5.33331 2.31309 5.47379 1.97395 5.72384 1.7239C5.97389 1.47385 6.31302 1.33337 6.66665 1.33337H9.33331C9.68693 1.33337 10.0261 1.47385 10.2761 1.7239C10.5262 1.97395 10.6666 2.31309 10.6666 2.66671V4.00004M12.6666 4.00004V13.3334C12.6666 13.687 12.5262 14.0261 12.2761 14.2762C12.0261 14.5262 11.6869 14.6667 11.3333 14.6667H4.66665C4.31302 14.6667 3.97389 14.5262 3.72384 14.2762C3.47379 14.0261 3.33331 13.687 3.33331 13.3334V4.00004H12.6666Z" stroke="#495057" strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M6.66669 7.33337V11.3334" stroke="#495057" strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M9.33331 7.33337V11.3334" stroke="#495057" strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  
                </button>
              )}
            </li>
          ))}
        </ul>
        {contacts.length < 2 && (
          <button type="button" className={styles.addButton} onClick={addContact}>
            + Add More
          </button>
        )}
        <div className={styles.toggleWrapper}>
          <label htmlFor="technicalContactToggle" className={styles.toggleLabel}>
            I am the technical contact
          </label>
          <label className={styles.toggleSwitch}>
            <input
              type="checkbox"
              id="technicalContactToggle"
              checked={isTechnicalContact}
              onChange={(e) => setIsTechnicalContact(e.target.checked)}
            />
            <span className={styles.slider}></span>
          </label>
        </div>
        <p className={styles.toggleDescription}>
        Enabling this will allow us to contact you for more details if needed. Please provide your phone number for faster service. Providing additional contacts are helpful.
        </p>
      </form>
    </section>
  );
};

export default TechnicalTeamContact;