import { useTranslation } from "react-i18next";
import React, { useState, useEffect, useRef } from "react";
import styles from "./SubcontractingInformationForm.module.scss";

import { Button } from "@fourkites/elemental-atoms";
import { isFieldInvalid } from "view/components/base/FormUtils";
import { Spinner } from "@fourkites/elemental-loading-indicator";
import VoiceOnboardingApi from "api/carrier/VoiceOnboardingApi";
import { showToast } from "view/components/base/toast/Toast";
import { FOURKITES_APP_URL } from "api/http/apiUtils";
import { onNav } from "router/navigationUtils";
import { useLocation } from 'react-router-dom';
import { fourkitesUrls } from "api/http/apiUtils";
import PhoneInput from "./PhoneInput";
import { ALL_COUNTRIES } from "./countryData";

export const ContactInformationForm = ({
  phoneNumber,
  setPhoneNumber,
  country,
  setCountry,
  onNextStep,
  onBack,
  shipperName = "shipper",
}: {
  phoneNumber: string;
  setPhoneNumber: React.Dispatch<React.SetStateAction<string>>;
  country: string;
  setCountry: React.Dispatch<React.SetStateAction<string>>;
  onNextStep: () => void;
  onBack: () => void;
  shipperName?: string;
}) => {
  const { t } = useTranslation();
  
  // Get onboardingToken and companyAlreadyExists from URL
  const location = useLocation();
  const urlParams = new URLSearchParams(location.search);
  const onboardingToken = urlParams.get('onboardingToken');
  const companyAlreadyExists = urlParams.get('companyAlreadyExists') === 'true';
  
  // Call states
  const [isCallLoading, setIsCallLoading] = useState<boolean>(false);
  const [callSid, setCallSid] = useState<string>("");
  const [callStatus, setCallStatus] = useState<string>("");
  const [callErrorMessage, setCallErrorMessage] = useState<string>("");

  // Language selection state
  const [selectedLanguage, setSelectedLanguage] = useState<string>("en");

  // Language options
  const languageOptions = [
    { value: "en", label: "English" },
    { value: "es", label: "Español" },
    { value: "fr", label: "Français" },
    { value: "tr", label: "Türkçe" }
  ];

  // Polling interval reference for cleanup
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Check if phone number is valid
  const isFormValid = () => {
    return !isFieldInvalid(phoneNumber);
  };

  // Handle cleanup on component unmount
  useEffect(() => {
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
    };
  }, []);

  // Reset all call-related states
  const resetCallStates = () => {
    // Stop any active polling
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
    
    // Reset all call-related states
    setIsCallLoading(false);
    setCallSid("");
    setCallErrorMessage("");
    // Note: We don't reset callStatus if it's "completed" to keep showing success message
  };

  // Poll for call status when callSid is available
  useEffect(() => {
    if (callSid) {
      // Clear any existing interval
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
      
      // Set up polling
      pollingIntervalRef.current = setInterval(async () => {
        try {
          const response = await VoiceOnboardingApi.getCallStatus(callSid);
          
          if (response && response.data) {
            const currentStatus = response.data.status;
            setCallStatus(currentStatus);
            
            // Handle different statuses
            switch (currentStatus) {
              case "completed":
                // Stop polling
                if (pollingIntervalRef.current) {
                  clearInterval(pollingIntervalRef.current);
                  pollingIntervalRef.current = null;
                }

                // Reset loading state and call SID
                setIsCallLoading(false);
                setCallSid("");

                // Show success message
                showToast(t("Call completed"), t("Thank you for completing the call"), "ok");

                // Redirect to thank you page instead of directly to login/onboarding
                setTimeout(() => {
                  onNextStep(); // This will navigate to SurveyCompletionPage
                }, 1000); // Short delay to allow toast to show
                break;
                
              case "no-answer":
              case "failed":
              case "busy":
              case "canceled":
                // Stop polling on final error statuses
                if (pollingIntervalRef.current) {
                  clearInterval(pollingIntervalRef.current);
                  pollingIntervalRef.current = null;
                }
                setIsCallLoading(false);
                setCallSid(""); // Reset call SID
                setCallErrorMessage(
                  t(`Call could not be completed (${currentStatus}). Please try again later.`)
                );
                break;
                
              // For in-progress, queued, ringing statuses - continue polling
              default:
                break;
            }
          }
        } catch (error) {
          console.error("Error polling call status:", error);
          // On error, stop polling
          if (pollingIntervalRef.current) {
            clearInterval(pollingIntervalRef.current);
            pollingIntervalRef.current = null;
          }
          setIsCallLoading(false);
          setCallSid(""); // Reset call SID
          setCallErrorMessage(t("Failed to get call status. Please try again."));
        }
      }, 3000); // Poll every 3 seconds
    }
  }, [callSid, onboardingToken, t]);

  // Function to initiate the call
  const initiateCall = async () => {
    if (!isFormValid()) {
      return;
    }
    
    // Reset all states before starting a new call
    resetCallStates();
    
    setIsCallLoading(true);
    
    try {
      // Get the country dial code from our selected country
      const countryDetails = ALL_COUNTRIES.find(c => c.code === country);
      const dialCode = countryDetails ? countryDetails.dialCode : '';
      
      // Format the phone number with the country code but without spaces
      // e.g., "+918909675785" instead of "+91 8909675785"
      const formattedPhoneNumber = `${dialCode}${phoneNumber}`.replace(/\s+/g, '');
      
      const response = await VoiceOnboardingApi.initiateVoiceCall(formattedPhoneNumber, onboardingToken || "", selectedLanguage);
      
      if (response && response.data && response.data.call_sid) {
        setCallSid(response.data.call_sid);
        setCallStatus(response.data.status || "queued");
        showToast(t("Call initiated"), t("Please answer your phone when it rings"), "ok");
      } else {
        setIsCallLoading(false);
        setCallErrorMessage(t("Failed to initiate call. Please try again."));
      }
    } catch (error) {
      console.error("Error initiating voice call:", error);
      setIsCallLoading(false);
      setCallErrorMessage(t("Something went wrong. Please try again later."));
    }
  };

  const steps = ["Step One", "Step Two", "Step Three", "Step Four", "Step Five"];

  return (
    <div className={styles.pageContainer}>
      <div className={styles.container}>
        {/* Header Section */}
        <header className={styles.header}>
          <div className={styles.onboardingText}>
            <h1 className={styles.title}>Carrier Onboarding</h1>
            <p className={styles.subtitle}>Contact Information</p>
          </div>
          <Stepper steps={steps} currentStep={4} />
        </header>

        {/* Main Content */}
        <main className={styles.content}>
          <div className={styles.formContainer}>
            <div className={styles.question}>
              <h2 className={styles.questionText}>
                {t("Contact Information")}
              </h2>
              
              {callSid && callStatus && callStatus !== "completed" && (
                <div className={styles.noticeCard}>
                  <div className={styles.noticeIcon}>📞</div>
                  <div className={styles.noticeContent}>
                    <h3 className={styles.noticeTitle}>
                      {callStatus === "initiated" && t("Call is being initiated")}
                      {callStatus === "queued" && t("Call is being initiated")}
                      {callStatus === "ringing" && t("Phone is ringing")}
                      {callStatus === "in-progress" && t("Call in progress")}
                      {!["initiated", "queued", "ringing", "in-progress"].includes(callStatus) && t("Call Status: ") + callStatus}
                    </h3>
                    <p className={styles.noticeText}>
                      {callStatus === "initiated" && t("We're setting up a call to your phone number. Please wait.")}
                      {callStatus === "queued" && t("We're setting up a call to your phone number. Please wait.")}
                      {callStatus === "ringing" && t("Your phone is ringing. Please answer the call.")}
                      {callStatus === "in-progress" && t("You're connected! Please complete the call to finish your onboarding.")}
                    </p>
                  </div>
                </div>
              )}
              
              {/* Successful call completion message */}
              {!callSid && callStatus === "completed" && (
                <div className={styles.noticeCard} style={{ backgroundColor: "#f0fdf4", borderColor: "#22c55e" }}>
                  <div className={styles.noticeIcon}>✅</div>
                  <div className={styles.noticeContent}>
                    <h3 className={styles.noticeTitle} style={{ color: "#15803d" }}>
                      {t("Call Completed Successfully")}
                    </h3>
                    <p className={styles.noticeText} style={{ color: "#15803d" }}>
                      {t("Thank you for completing the call. Our team will help you with the onboarding process.")}
                    </p>
                  </div>
                </div>
              )}
              
              {/* Error message - Improved styling */}
              {callErrorMessage && (
                <div className={styles.noticeCard} style={{ 
                  backgroundColor: "#fee2e2", 
                  borderColor: "#ef4444",
                  borderWidth: "1px",
                  borderStyle: "solid",
                  borderRadius: "8px",
                  padding: "16px 20px",
                  marginBottom: "24px",
                  display: "flex",
                  alignItems: "flex-start",
                  gap: "12px"
                }}>
                  <div style={{ 
                    backgroundColor: "#fecaca",
                    borderRadius: "50%",
                    width: "32px",
                    height: "32px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    flexShrink: 0
                  }}>
                    <span role="img" aria-label="Error" style={{ fontSize: "16px" }}>⚠️</span>
                  </div>
                  <div>
                    <h3 style={{ 
                      margin: "0 0 8px 0", 
                      color: "#b91c1c",
                      fontSize: "18px",
                      fontWeight: 600
                    }}>
                      {t("Call Error")}
                    </h3>
                    <p style={{ 
                      margin: "0", 
                      color: "#b91c1c",
                      fontSize: "15px",
                      lineHeight: "1.5"
                    }}>
                      {callErrorMessage}
                    </p>
                    <div style={{ 
                      marginTop: "12px",
                      fontSize: "14px"
                    }}>
                      <button 
                        onClick={() => setCallErrorMessage("")}
                        style={{
                          background: "none",
                          border: "none",
                          color: "#b91c1c",
                          textDecoration: "underline",
                          cursor: "pointer",
                          padding: "0",
                          fontWeight: 500
                        }}
                      >
                        {t("Dismiss")}
                      </button>
                    </div>
                  </div>
                </div>
              )}
              
              <p className={styles.questionDescription}>
                {t(`Please provide your phone number so we can reach you regarding ${shipperName} shipments`)}
              </p>
              
              <div className={styles.formFields}>
                {/* Phone Number Input - REPLACED WITH IMPROVED VERSION */}
                <div className={styles.options}>
                  <PhoneInput
                    label={t("Phone Number")}
                    placeholder={t("Enter your phone number")}
                    errorLabel={t("Phone number is required")}
                    value={phoneNumber}
                    onChange={(value) => setPhoneNumber(value)}
                    country={country}
                    onChangeCountry={(countryCode) => setCountry(countryCode)}
                    required={true}
                    disabled={isCallLoading}
                    invalid={isFieldInvalid(phoneNumber)}
                  />
                  <p className={styles.fieldDescription}>
                    {t("Select your country and enter your phone number. The country code will be added automatically.")}
                  </p>
                </div>

                {/* Language Selection */}
                <div className={styles.options}>
                  <label className={styles.radioGroupLabel}>
                    {t("Preferred Language for Call")}
                  </label>
                  <select
                    className="input-field"
                    value={selectedLanguage}
                    onChange={(e) => setSelectedLanguage(e.target.value)}
                    disabled={isCallLoading}
                    style={{
                      borderRadius: "8px",
                      border: "1px solid #d1d5db",
                      padding: "0.75rem",
                      fontSize: "0.875rem",
                      backgroundColor: isCallLoading ? "#f3f4f6" : "#ffffff",
                      cursor: isCallLoading ? "not-allowed" : "pointer"
                    }}
                  >
                    {languageOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                  <p className={styles.fieldDescription}>
                    {t("Select your preferred language for the onboarding call.")}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </main>

        {/* Footer Section */}
        <footer className={styles.footer}>
          <div className={styles.footerButtons}>
            <Button
              className={styles.footerButton}
              theme="secondary"
              size="medium"
              onClick={onBack}
              disabled={isCallLoading}
            >
              {t("Back")}
            </Button>
            <Button
              className={styles.footerButton}
              theme="primary"
              size="medium"
              disabled={!isFormValid() || isCallLoading}
              onClick={initiateCall}
            >
              {isCallLoading ? (
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <Spinner isLoading size="small" />
                  {t("Call in progress...")}
                </div>
              ) : (
                t("Call now")
              )}
            </Button>
          </div>
        </footer>
      </div>
    </div>
  );
};

export const Stepper = ({
  steps,
  currentStep,
}: {
  steps: string[];
  currentStep: number;
}) => {
  return (
    <div className={styles.stepper}>
      {steps.map((step, index) => (
        <div key={index} className={styles.stepContainer}>
          <div className={`${styles.step} ${index === currentStep ? styles.currentStep : ""}`}>
            <div
              className={`${styles.oval} ${
                index === currentStep 
                  ? styles.currentOvalSelected 
                  : index < currentStep 
                  ? styles.ovalCompleted 
                  : ""
              }`}
            >
              {index + 1}
            </div>
            <div
              className={`${styles.stepLabel} ${
                index === currentStep ? styles.currentStepLabel : ""
              }`}
            >
              {step}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ContactInformationForm;