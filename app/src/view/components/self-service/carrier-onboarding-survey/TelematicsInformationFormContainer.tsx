import { useTranslation } from "react-i18next";
import React from 'react';
import styles from "./TMSInformationForm.module.scss";
import { Link } from "react-router-dom";
import { Button } from "@fourkites/elemental-atoms";
import { Input } from "@fourkites/elemental-input";
import { isFieldInvalid } from "view/components/base/FormUtils";

// Telematics Information Form - Page 2: Telematics Providers & Fleet Percentage
export const TelematicsInformationForm = ({
  truckTelematicsProvider,
  setTruckTelematicsProvider,
  trailerTelematicsProvider,
  setTrailerTelematicsProvider,
  telematicsPercentage,
  setTelematicsPercentage,
  hasGpsProvider,
  setHasGpsProvider,
  alternativeTrackingMethod,
  setAlternativeTrackingMethod,
  onNextStep,
  onBack,
  handleTechnicalTeamContact
}: {
  truckTelematicsProvider: string;
  setTruckTelematicsProvider: React.Dispatch<React.SetStateAction<string>>;
  trailerTelematicsProvider: string;
  setTrailerTelematicsProvider: React.Dispatch<React.SetStateAction<string>>;
  telematicsPercentage: string;
  setTelematicsPercentage: React.Dispatch<React.SetStateAction<string>>;
  hasGpsProvider: string;
  setHasGpsProvider: React.Dispatch<React.SetStateAction<string>>;
  alternativeTrackingMethod: string;
  setAlternativeTrackingMethod: React.Dispatch<React.SetStateAction<string>>;
  onNextStep: () => void;
  onBack: () => void;
  handleTechnicalTeamContact: () => void;
}) => {
  const { t } = useTranslation();

  // Check if all required fields are valid
  const isFormValid = () => {
    // GPS provider question is required
    if (isFieldInvalid(hasGpsProvider)) {
      return false;
    }

    // If they have GPS providers, telematics fields are required
    if (hasGpsProvider === 'yes') {
      return !isFieldInvalid(truckTelematicsProvider) &&
             !isFieldInvalid(trailerTelematicsProvider);
    }

    // If they don't have GPS providers, alternative tracking method is required
    if (hasGpsProvider === 'no') {
      return !isFieldInvalid(alternativeTrackingMethod);
    }

    return false;
  };

  const steps = ["Step One", "Step Two", "Step Three", "Step Four", "Step Five"];

  return (
    <div className={styles.pageContainer}>
      <div className={styles.container}>
        {/* Header Section */}
        <header className={styles.header}>
          <div className={styles.onboardingText}>
            <h1 className={styles.title}>Carrier Onboarding</h1>
            <p className={styles.subtitle}>Telematics / GPS Setup</p>
          </div>
          <Stepper steps={steps} currentStep={2} />
        </header>

        {/* Main Content */}
        <main className={styles.content}>
          <TelematicsFormContent
            truckTelematicsProvider={truckTelematicsProvider}
            setTruckTelematicsProvider={setTruckTelematicsProvider}
            trailerTelematicsProvider={trailerTelematicsProvider}
            setTrailerTelematicsProvider={setTrailerTelematicsProvider}
            telematicsPercentage={telematicsPercentage}
            setTelematicsPercentage={setTelematicsPercentage}
            hasGpsProvider={hasGpsProvider}
            setHasGpsProvider={setHasGpsProvider}
            alternativeTrackingMethod={alternativeTrackingMethod}
            setAlternativeTrackingMethod={setAlternativeTrackingMethod}
          />
        </main>

        {/* Footer Section */}
        <footer className={styles.footer}>
          <Link
            to="#"
            className={styles.footerLink}
            onClick={(e) => {
              e.preventDefault();
              handleTechnicalTeamContact();
            }}
          >
            {t("Get it done by a call")}
          </Link>
          
          <div className={styles.footerButtons}>
            <Button
              className={styles.footerButton}
              theme="secondary"
              size="medium"
              onClick={onBack}
            >
              {t("Back")}
            </Button>
            <Button
              className={styles.footerButton}
              theme="primary"
              size="medium"
              disabled={!isFormValid()}
              onClick={onNextStep}
            >
              {t("Proceed")}
            </Button>
          </div>
        </footer>
      </div>
    </div>
  );
};

export const Stepper = ({
  steps,
  currentStep,
}: {
  steps: string[];
  currentStep: number;
}) => {
  return (
    <div className={styles.stepper}>
      {steps.map((step, index) => (
        <div key={index} className={styles.stepContainer}>
          <div className={`${styles.step} ${index === currentStep ? styles.currentStep : ""}`}>
            <div
              className={`${styles.oval} ${
                index === currentStep 
                  ? styles.currentOvalSelected 
                  : index < currentStep 
                  ? styles.ovalCompleted 
                  : ""
              }`}
            >
              {index + 1}
            </div>
            <div
              className={`${styles.stepLabel} ${
                index === currentStep ? styles.currentStepLabel : ""
              }`}
            >
              {step}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export const TelematicsFormContent = ({
  truckTelematicsProvider,
  setTruckTelematicsProvider,
  trailerTelematicsProvider,
  setTrailerTelematicsProvider,
  telematicsPercentage,
  setTelematicsPercentage,
  hasGpsProvider,
  setHasGpsProvider,
  alternativeTrackingMethod,
  setAlternativeTrackingMethod
}: {
  truckTelematicsProvider: string;
  setTruckTelematicsProvider: React.Dispatch<React.SetStateAction<string>>;
  trailerTelematicsProvider: string;
  setTrailerTelematicsProvider: React.Dispatch<React.SetStateAction<string>>;
  telematicsPercentage: string;
  setTelematicsPercentage: React.Dispatch<React.SetStateAction<string>>;
  hasGpsProvider: string;
  setHasGpsProvider: React.Dispatch<React.SetStateAction<string>>;
  alternativeTrackingMethod: string;
  setAlternativeTrackingMethod: React.Dispatch<React.SetStateAction<string>>;
}) => {
  const { t } = useTranslation();

  // Form field configuration for telematics/GPS providers
  const formFields = [
    {
      label: t("Truck Telematics / GPS Provider(s)"),
      value: truckTelematicsProvider,
      setValue: setTruckTelematicsProvider,
      validator: isFieldInvalid,
      errorLabel: t("Field is required"),
      required: true,
      description: "Enter your Truck Telematics / GPS provider(s). If you don't use any, enter 'None'."
    },
    {
      label: t("Trailer Telematics / GPS Provider(s)"),
      value: trailerTelematicsProvider,
      setValue: setTrailerTelematicsProvider,
      validator: isFieldInvalid,
      errorLabel: t("Field is required"),
      required: true,
      description: "Enter your Trailer Telematics / GPS provider(s). If you don't use any, enter 'None'."
    }
  ];

  const radioGroups = [
    {
      id: 'telematicsPercentage',
      label: t("Approximately what percentage of your fleet is equipped with Telematics / GPS devices?"),
      value: telematicsPercentage,
      setValue: setTelematicsPercentage,
      required: false,
      options: [
        { value: "1-25", label: t("1-25 %") },
        { value: "26-50", label: t("26-50 %") },
        { value: "51-75", label: t("51-75 %") },
        { value: "76-99", label: t("76-99 %") },
        { value: "100", label: t("100 %") }
      ]
    }
  ];

  return (
    <div className={styles.formContainer}>
      <div className={styles.question}>
        <h2 className={styles.questionText}>
          {t("Telematics / GPS Setup")}
        </h2>

        <div className={styles.formFields}>
          {/* GPS Provider Question */}
          <div className={styles.options}>
            <span className={styles.radioGroupLabel}>
              {t("Do you have Telematics / GPS providers for tracking your trucks and trailers?")}
            </span>
            <div className={styles.radioContainer}>
              <div className={styles.radioOption}>
                <input
                  type="radio"
                  id="hasGps-yes"
                  name="hasGpsProvider"
                  value="yes"
                  checked={hasGpsProvider === 'yes'}
                  onChange={() => {
                    setHasGpsProvider('yes');
                    // Clear alternative tracking method when GPS is available
                    setAlternativeTrackingMethod('');
                  }}
                  className={styles.radioInput}
                />
                <label htmlFor="hasGps-yes" className={styles.radioLabel}>
                  {t("Yes")}
                </label>
              </div>
              <div className={styles.radioOption}>
                <input
                  type="radio"
                  id="hasGps-no"
                  name="hasGpsProvider"
                  value="no"
                  checked={hasGpsProvider === 'no'}
                  onChange={() => {
                    setHasGpsProvider('no');
                    // Clear telematics fields when no GPS
                    setTruckTelematicsProvider('');
                    setTrailerTelematicsProvider('');
                    setTelematicsPercentage('');
                  }}
                  className={styles.radioInput}
                />
                <label htmlFor="hasGps-no" className={styles.radioLabel}>
                  {t("No")}
                </label>
              </div>
            </div>
          </div>

          {/* Conditional Content Based on GPS Provider Answer */}
          {hasGpsProvider === 'yes' && (
            <>
              {/* Telematics Provider Fields */}
              {formFields.map((field, index) => (
                <div key={index} className={styles.options}>
                  <Input
                    label={field.label}
                    errorLabel={field.errorLabel}
                    value={field.value}
                    invalid={field.validator(field.value)}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      field.setValue(e.target.value)
                    }
                    required={field.required}
                  />
                  {field.description && (
                    <p className={styles.fieldDescription}>
                      {field.description}
                    </p>
                  )}
                </div>
              ))}

              {/* Fleet Percentage Radio Group */}
              {radioGroups.map((group, groupIndex) => (
                <div key={group.id} className={styles.options}>
                  <span className={styles.radioGroupLabel}>
                    {group.label}
                  </span>
                  <div className={styles.radioContainer}>
                    {group.options.map((option, optionIndex) => (
                      <div key={option.value} className={styles.radioOption}>
                        <input
                          type="radio"
                          id={`${group.id}-${optionIndex}`}
                          name={group.id}
                          value={option.value}
                          checked={group.value === option.value}
                          onChange={() => group.setValue(option.value)}
                          className={styles.radioInput}
                        />
                        <label
                          htmlFor={`${group.id}-${optionIndex}`}
                          className={styles.radioLabel}
                        >
                          {option.label}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </>
          )}

          {/* Alternative Tracking Method for carriers without GPS */}
          {hasGpsProvider === 'no' && (
            <div className={styles.options}>
              <Input
                label={t("How do you actively track your trucks/trailers while in transit?")}
                errorLabel={t("Field is required")}
                value={alternativeTrackingMethod}
                invalid={isFieldInvalid(alternativeTrackingMethod)}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setAlternativeTrackingMethod(e.target.value)
                }
                required={true}
                placeholder={t("E.g., manual check calls, driver phone tracking")}
              />
              <p className={styles.fieldDescription}>
                {t("Please describe your current method for tracking vehicle locations and shipment status")}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TelematicsInformationForm;