.container {
    text-align: center;
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
  }
  
  .title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
  }
  
  .searchBar {
    width: 100%;
    max-width: 400px;
    padding: 10px;
    margin-bottom: 20px;
    font-size: 16px;
    border: 1px solid #ccc;
    border-radius: 5px;
  }
  
  .providerGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
  }
  
  .providerCard {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 8px;
    transition: transform 0.2s ease;
  }
  
  .providerCard:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
  
  .providerLogo {
    max-width: 100px;
    max-height: 100px;
    object-fit: contain;
    margin-bottom: 10px;
  }
  
  .providerName {
    font-size: 16px;
    font-weight: 500;
  }
  
  .noResults {
    grid-column: 1 / -1;
    font-size: 18px;
    color: #999;
  }
  