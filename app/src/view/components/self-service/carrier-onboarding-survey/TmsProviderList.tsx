import React, { useState } from "react";
import { useMediaQuery } from "react-responsive";
import { useTranslation } from "react-i18next";

import classNames from "classnames";
import genericGpsProvider from "assets/img/genericGpsProvider.png";

import { Button, SearchIcon, XIcon } from "@fourkites/elemental-atoms";
import { Input } from "@fourkites/elemental-input";

import ProviderNotFound from "./ProviderNotFound";
import SetupExplanation from "./SetupExplanation";
import tmsProviders from "./tmsProviders";
import stylesStepper from "./CarrierOnboardingSurvey.module.scss";
import styles from "./ProviderSelection.module.scss";
import { Link } from "react-router-dom";

  
  
interface TmsProviderSelectionProps {
  isModal: boolean;
  isExternallyUsed: boolean;
  onBack: () => void;
  handleTechnicalTeamContact:() => void;
  onNextStep: () => void;
  selectedTmsProvider: any;
  setSelectedTmsProvider: any;
}

const TmsProviderList = ({
  isModal,
  isExternallyUsed,
  onBack,
  handleTechnicalTeamContact,
  onNextStep,
  selectedTmsProvider,
  setSelectedTmsProvider,
}: TmsProviderSelectionProps) => {
  const { t } = useTranslation();
  return (
    <div className={stylesStepper.pageContainer}>
      <div className={stylesStepper.container}>
        <div className={stylesStepper.header}>
          <div className={stylesStepper.onboardingText}>
            <span className={stylesStepper.title}>Carrier Onboarding</span>
            <span className={stylesStepper.subtitle}>
              Select TMS Provider
            </span>
          </div>
          <Stepper
            steps={["Step One", "Step Two", "Step Three", "Step Four", "Step Five"]}
            currentStep={3}
          />
        </div>
        <div className={stylesStepper.content}>
          <TmsProviderSearch
          isModal={isModal}
          isExternallyUsed={isExternallyUsed}
          onBack={onBack}
          handleTechnicalTeamContact={handleTechnicalTeamContact}
          onNextStep={onNextStep}
          selectedTmsProvider={selectedTmsProvider}
          setSelectedTmsProvider={setSelectedTmsProvider}
          />
        </div>
        <div className={stylesStepper.footer}>
        <Link to="#" className={stylesStepper.footerLink} onClick={() => handleTechnicalTeamContact()}>
          {t("I'm not sure, complete this setup with my technical team")}
        </Link>
          <div className={stylesStepper.footerButtons}>
            <Button
              className={stylesStepper.footerButton}
              theme="secondary"
              size="medium"
              onClick={() => onBack()}
            >
              {t("Back")}
            </Button>

            <Button
              className={styles.footerButton}
              theme="primary"
              size="medium"
              onClick={() => handleTechnicalTeamContact()}
            >
              {t("Proceed")}
            </Button>
            
          </div>
        </div>
      </div>
    </div>
  );
};

//Create a stepper component containing n steps. It should be configurable to show the current step and the steps that have been completed. The component should be able to receive a list of steps and the current step as props.
// The component should have a prop to receive the current step and a prop to receive the list of steps.
// The completed steps should also have stylesStepper, i.e. should be highlighted.
export const Stepper = ({
  steps,
  currentStep,
}: {
  steps: string[];
  currentStep: number;
}) => {
  return (
    <div className={stylesStepper.stepper}>
      {steps.map((step, index) => (
        <div className={stylesStepper.stepContainer}>
          <div
            key={index}
            className={`${stylesStepper.step} ${
              index === currentStep ? stylesStepper.currentStep : ""
            }`}
          >
            <div
              className={`${stylesStepper.oval} ${
                index === currentStep ? stylesStepper.currentOvalSelected : ""
              } ${index < currentStep ? stylesStepper.ovalCompleted : ""}
              `}
            >
              {index + 1}
            </div>
            <div
              className={`${stylesStepper.stepLabel} ${
                index === currentStep ? stylesStepper.currentStepLabel : ""
              }`}
            >
              {step}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}; 
  
  
const TmsProviderSearch = ({
  isModal,
  isExternallyUsed,
  onBack,
  handleTechnicalTeamContact,
  onNextStep,
  selectedTmsProvider,
  setSelectedTmsProvider,
}: TmsProviderSelectionProps) => {
  const { t } = useTranslation();
  const isMobile = useMediaQuery({ maxWidth: 720 });

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [query, setQuery] = useState<string>("");

  /*****************************************************************************
   * FILTERED PROVIDERS
   ****************************************************************************/

  const filteredProviders = tmsProviders.filter((provider) =>
    provider.name.toLowerCase().includes(query.toLowerCase())
  );

  /*****************************************************************************
   * HANDLERS
   ****************************************************************************/

  const handleSelection = (provider: any) => {
    // If the same provider is selected again, deselect it
    if (selectedTmsProvider?.id === provider.id) {
      setSelectedTmsProvider(null);
    } else {
      setSelectedTmsProvider(provider); // Update the selected provider
    }
  };

  const isProviderSelected = (provider: any) => {
    return selectedTmsProvider?.id === provider.id;
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div
      className={styles.container}
      id="provider-selection"
      data-test-id="provider-selection-page"
    >
      <ProvidersSearch
        isMobile={isMobile}
        isExternallyUsed={isExternallyUsed}
        isModal={isModal}
        selectedTmsProvider={selectedTmsProvider}
        query={query}
        setQuery={setQuery}
        filteredProviders={filteredProviders}
        onProviderSelection={handleSelection}
        isProviderSelected={isProviderSelected}
      />
      <div className ={styles.setupExplainationBox}>
      <SetupExplanation
        isMobile={isMobile}
        isExternallyUsed={isExternallyUsed}
      />
      </div>
    </div>
  );
};

const ProvidersSearch = ({
  isMobile,
  isExternallyUsed,
  isModal,
  selectedTmsProvider,
  query,
  setQuery,
  filteredProviders,
  onProviderSelection,
  isProviderSelected,
}: any) => {
  const { t } = useTranslation();

  const inputWrapperClass = classNames(styles.inputWrapper, {
    [styles.inputWrapperMobile]: isMobile && isExternallyUsed,
  });

  const isProviderNotFound = filteredProviders.length === 0;

  const providersWrapperMaxHeight = isModal
    ? "calc(100vh + -320px)"
    : "calc(100vh + -390px)";

  return (
    <div className={styles.providerSearch} id="providers-search">
      <div className={inputWrapperClass}>
        <Input
          label={""}
          placeholder={t("Search TMS Provider")}
          value={query}
          onChange={(e: any) => setQuery(e.target.value)}
          icon={query ? <XIcon /> : <SearchIcon />}
          onIconClick={() => setQuery("")}
          size="large"
        />
      </div>

      <label className={styles.subtitle} id="subtitle">
        {t("Or choose one from the list of TMS providers")}
      </label>

      {isProviderNotFound && (
        <ProviderNotFound />
      )}

      {!isProviderNotFound && (
        <>
          <h3 id="provider-selection-list-header">
            {t("TMS Providers")}
          </h3>
          <div
            id="providers-wrapper"
            className={styles.providersWrapper}
            style={{ maxHeight: providersWrapperMaxHeight }}
          >
            {filteredProviders.map((provider: any) => (
              <LocationProvider
                key={provider.id}
                provider={provider}
                onProviderSelection={onProviderSelection}
                isProviderSelected={isProviderSelected(provider)}
              />
            ))}
          </div>
        </>
      )}
    </div>
  );
};

const LocationProvider = ({ provider, onProviderSelection, isProviderSelected }: any) => {
  const { t } = useTranslation();

  return (
    <div
    className={
      isProviderSelected
        ? styles.providerContainerAfterSelection
        : styles.providerContainer
    }
      id="provider-selection-provider-container"
    >
      <img
        className={styles.logo}
        src={genericGpsProvider}
        alt="Provider logo"
      />
      <label>{provider.name}</label>
      <button onClick={() => onProviderSelection(provider)}>
        {isProviderSelected ? t("Selected") : t("Select")}
      </button>
    </div>
  );
};


export default TmsProviderList;
