/* CRITICAL OVERRIDE FOR REACT COMPONENTS - Place this at the very top */
* {
  box-sizing: border-box;
}

/* Override any React Router or parent component height constraints */
body, html {
  margin: 0;
  padding: 0;
  height: auto !important;
  min-height: 100vh;
  overflow-y: auto !important;
}

#root {
  height: auto !important;
  min-height: 100vh !important;
}

.App {
  height: auto !important;
  min-height: 100vh !important;
}

/* FORCE SCROLLABLE LAYOUT - COMPREHENSIVE FIX */

/* Reset all containers to allow natural scrolling */
html {
  margin: 0;
  padding: 0;
  height: auto !important;
  overflow-y: scroll !important;
}

body {
  margin: 0;
  padding: 0;
  height: auto !important;
  min-height: 100vh;
  overflow-y: scroll !important;
}

/* Force React root containers to be scrollable */
#root {
  height: auto !important;
  min-height: 100vh !important;
  overflow-y: visible !important;
}

.App {
  height: auto !important;
  min-height: 100vh !important;
  overflow-y: visible !important;
}

/* Force any router containers to be scrollable */
.router-container,
.route-container,
[class*="router"],
[class*="Route"] {
  height: auto !important;
  min-height: auto !important;
  overflow-y: visible !important;
}

/* Add this critical CSS to the top of your styles to override any parent constraints */
.welcomeScreen {
  background-color: #fff;
  /* Remove flexbox that might be constraining height */
  display: block !important;
  /* Remove any overflow constraints */
  overflow: visible !important;
  font-family: Lato, sans-serif;
  /* Remove the large bottom padding that might be causing issues */
  padding: 20px 80px 50px;
  /* No height constraints */
  height: auto !important;
  min-height: auto !important;
}
@media (max-width: 991px) {
  .welcomeScreen {
    padding: 0 20px 50px;
  }
}
.contentWrapper {
  border-radius: 8px;
  border: 1px solid var(--Neutral-300, #dee2e6);
  background: var(--Neutral-50, #f8f9fa);
  align-self: center;
  display: flex;
  margin-top: 30px;
  width: 800px;
  max-width: 100%;
  flex-direction: column;
  overflow: hidden;
  padding: 24px;
}
@media (max-width: 991px) {
  .contentWrapper {
    margin-top: 40px;
    padding: 0 20px;
  }
}
.headerWrapper {
  align-self: flex-start;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  color: var(--Text-Colors-Default, #21252a);
  font-weight: 700;
  line-height: 1;
  flex-wrap: wrap;
}
.headerText {
  flex: 1;
  margin: auto 0;
}
@media (max-width: 991px) {
  .headerText {
    max-width: 100%;
  }
}
.description {
  color: var(--Text-Colors-Default, #21252a);
  font-feature-settings: "liga" off, "clig" off;
  font-size: 19px;
  font-weight: 400;
  line-height: 24px;
  margin-top: 12px;
}
@media (max-width: 991px) {
  .description {
    max-width: 100%;
  }
}
.buttonContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 36px;
  width: 100%;
  max-width: 100%;
  gap: 20px;
  font-size: 16px;
  font-weight: 600;
  line-height: 48px;
  flex-wrap: wrap;
}

.outlineButton {
  align-self: stretch;
  border-radius: 4px;
  border: 2px solid var(--Primary-500, #0e65e5);
  min-height: 32px;
  color: var(--Primary-500, #0e65e5);
  text-align: center;
  flex: 1;
  padding: 0 16px;
}
.solidButton {
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  background: var(--components-solid-buttons-primary-default, #0e65e5);
  display: flex;
  min-height: 32px;
  gap: 16px;
  color: #fff;
  padding: 0 16px;
}
.buttonContent {
  align-self: stretch;
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
  margin: auto 0;
}
.buttonLabel {
  align-self: stretch;
  margin: auto 0;
}
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}
.modalContent {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  max-width: 500px;
  width: 100%;
}
.modalTitle {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 16px;
}
.modalDescription {
  font-size: 16px;
  margin-bottom: 24px;
}
.modalButtons {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}
.modalButton {
  flex: 1;
  max-width: 120px;
}
.termsContent {
  max-height: 300px; 
  overflow-y: auto; 
  padding: 10px;
  border: 1px solid #ccc; 
  background-color: #f9f9f9;
}
.loader {
  display: flex;
  align-items: left;
  align-content: center;
  justify-content: center;
  margin-top: 32px;
  margin-bottom: 32px;
}
.thankYouPage {
  text-align: center;
  margin-top: 50px;
}
.thankYouPage h1 {
  color: #4caf50;
  font-size: 2.5rem;
}
.thankYouPage p {
  font-size: 1.2rem;
  margin-top: 20px;
}
.highlight {
  background-color: #fff3cd;
  padding: 10px;
  border-left: 4px solid #ffc107;
  margin-top: 20px;
  margin-bottom: 20px;
}
.footer {
  font-size: 0.9em;
  color: #666;
  margin-top: 30px;
}

/* MAIN CONTAINER - FORCE SCROLLABLE */
.container {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  /* Remove the min-height constraint that prevents scrolling */
  padding: 20px;
  /* Key fix: No height constraints at all */
}

.welcomeCard {
  max-width: 600px;
  width: 100%;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: visible !important;
  /* Center using margin */
  margin: 20px auto 40px auto;
  /* Block display for natural flow */
  display: block;
  /* Allow card to be as tall as needed */
  height: auto !important;
  min-height: auto !important;
  position: relative;
}

/* Header Section */
.header {
  background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
  padding: 30px 20px;
  text-align: center;
  color: white;
}

.logoContainer {
  margin-bottom: 16px;
}

.fourkitesLogo {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.iconContainer {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.iconInner {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon {
  width: 20px;
  height: 20px;
  color: white;
}

.title {
  font-size: 22px;
  font-weight: 700;
  margin: 0;
  line-height: 1.2;
}

/* Content Section */
.content {
  padding: 24px 20px 30px 20px;
}

.welcomeTitle {
  margin-bottom: 20px;
}

.welcomeDescription {
  font-size: 16px;
  color: #4b5563;
  line-height: 1.5;
  margin: 0;
  text-align: center;
}

.brandName {
  font-weight: 600;
  color: #1f2937;
}

/* Benefits Section */
.benefitsSection {
  margin: 20px 0;
}

.benefitsHeading {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12px;
  text-align: center;
}

.benefitsGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.benefitCard {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.benefitIcon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.benefitIcon.green {
  background: #dcfce7;
}

.benefitIcon.blue {
  background: #dbeafe;
}

.benefitIcon.purple {
  background: #f3e8ff;
}

.benefitIcon.orange {
  background: #fed7aa;
}

.benefitIconSvg {
  width: 16px;
  height: 16px;
}

.benefitIconSvg.green {
  color: #16a34a;
}

.benefitIconSvg.blue {
  color: #2563eb;
}

.benefitIconSvg.purple {
  color: #9333ea;
}

.benefitIconSvg.orange {
  color: #ea580c;
}

.benefitText {
  font-size: 13px;
  font-weight: 500;
  color: #374151;
  margin: 0;
  line-height: 1.2;
}

/* Notice Sections */
.dataUseNotice, .privacyNotice {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 12px;
  border-radius: 6px;
  margin: 12px 0;
}

.dataUseNotice {
  background: #f1f5f9;
  border-left: 3px solid #64748b;
}

.privacyNotice {
  background: #eff6ff;
  border-left: 3px solid #3b82f6;
}

.dataIcon, .privacyIcon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  margin-top: 1px;
}

.dataIcon {
  color: #64748b;
}

.privacyIcon {
  color: #3b82f6;
}

.dataContent, .privacyContent {
  flex: 1;
}

.dataTitle, .privacyTitle {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 2px 0;
}

.dataText, .privacyText {
  font-size: 13px;
  color: #6b7280;
  margin: 0;
  line-height: 1.3;
}

.privacyLink {
  color: #3b82f6;
  text-decoration: underline;
}

.privacyLink:hover {
  color: #2563eb;
}

/* CTA Section */
.ctaSection {
  text-align: center;
  margin: 20px 0 16px;
}

.startButton {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  min-height: 48px;
  min-width: 180px;
}

.startButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

.buttonIcon {
  width: 18px;
  height: 18px;
}

.timeIndicator {
  font-size: 12px;
  color: #6b7280;
  margin: 8px 0 0 0;
}

/* Footer */
.footer {
  text-align: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.footerText {
  font-size: 10px;
  color: #9ca3af;
  margin: 0;
}

/* Thank You Page */
.thankYouPage {
  text-align: center;
  padding: 60px 40px;
  max-width: 600px;
  margin: 0 auto;
}

.thankYouPage h1 {
  color: #059669;
  font-size: 36px;
  margin-bottom: 16px;
}

.thankYouPage p {
  color: #6b7280;
  font-size: 18px;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }
  
  .welcomeCard {
    max-width: 100%;
    margin: 10px auto 20px auto;
  }
  
  .header {
    padding: 24px 16px;
  }
  
  .content {
    padding: 20px 16px 24px 16px;
  }
  
  .title {
    font-size: 20px;
  }
  
  .benefitsGrid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.welcomeCard {
  animation: fadeIn 0.6s ease-out;
}

/* OVERRIDE ANY FRAMEWORK CONSTRAINTS */
* {
  box-sizing: border-box;
}

/* Force any parent containers to allow scrolling */
div[class*="welcome"],
div[class*="screen"],
div[class*="container"],
div[class*="wrapper"],
div[class*="layout"] {
  height: auto !important;
  max-height: none !important;
  overflow: visible !important;
}


