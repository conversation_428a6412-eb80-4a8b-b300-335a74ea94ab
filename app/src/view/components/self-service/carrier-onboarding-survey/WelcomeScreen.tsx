import React, { useState, useEffect } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import { serviceUrls } from "api/http/apiUtils";
import styles from './WelcomeScreen.module.css';
import { Spinner } from "@fourkites/elemental-loading-indicator";
import { Button } from "@fourkites/elemental-atoms";
import fourkitesCircleLogo from "assets/img/fourkitesCircleLogo.png";
import TMSInformationForm from "view/components/self-service/carrier-onboarding-survey/TMSInformationFormContainer";
import { TelematicsInformationForm } from "view/components/self-service/carrier-onboarding-survey/TelematicsInformationFormContainer";
import SurveryMandatoryDetailsForm from "view/components/self-service/carrier-onboarding-survey/SurveryMandatoryDetailsForm";
import SubcontractingInformationForm from "view/components/self-service/carrier-onboarding-survey/SubcontractingInformationFormContainer";
import Modal from './Modal';
import ImplementationTimelineForm from "view/components/self-service/carrier-onboarding-survey/ImplementationTimelineFormContainer";
import ContactProps from "./Contact.types"
import EldGpsIntegrationAddition from "./EldGpsIntegrationAddition"
import TmsProviderList from './TmsProviderList';
import { useLocation } from 'react-router-dom';
import { onNav } from "router/navigationUtils";
import { fourkitesUrls } from "api/http/apiUtils";
import ContactInformationForm from "view/components/self-service/carrier-onboarding-survey/TechnicalTeamContactContainer";
import SurveyCompletionPage from "view/components/self-service/carrier-onboarding-survey/SurveyCompletionPage";
import { FOURKITES_APP_URL } from "api/http/apiUtils";


interface WelcomeScreenProps {
  shipperName: string;
}

const WelcomeScreen: React.FC = () => {
  const location = useLocation();
  const urlParams = new URLSearchParams(location.search);
  const shipperName = urlParams.get('shipperName') || 'Shipper';
  const onboardingToken = urlParams.get('onboardingToken'); // Extract onboardingToken from URL
  const companyAlreadyExists = urlParams.get('companyAlreadyExists') === 'true'; // Extract companyAlreadyExists flag from URL
  const surveyCreatedAt = urlParams.get('surveyCreatedAt'); // Extract surveyCreatedAt from URL



  const [currentStep, setCurrentStep] = useState<string>("Welcomepage");
  const [email, setEmail] = useState("");
  const [carrierName, setCarrierName] = useState("");
  const [vatNumber, setVatNumber] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [country, setCountry] = useState("US");
  
  const [contactName, setContactName] = useState("");
  const [contactPhoneNumber, setContactPhoneNumber] = useState("");
  const [contactEmail, setContactEmail] = useState("");
  const [tmsProvider, setTmsProvider] = useState("");
  const [tmsType, setTmsType] = useState(""); // 'tms' or 'manual'
  const [truckTelematicsProvider, setTruckTelematicsProvider] = useState("");
  const [trailerTelematicsProvider, setTrailerTelematicsProvider] = useState("");
  const [telematicsPercentage, setTelematicsPercentage] = useState("");
  const [hasGpsProvider, setHasGpsProvider] = useState("");
  const [alternativeTrackingMethod, setAlternativeTrackingMethod] = useState("");
  const [tmsDataTypes, setTmsDataTypes] = useState<string[]>([]);
  const [integrationTypes, setIntegrationTypes] = useState<string[]>([]);
  const [doesSubcontract, setDoesSubcontract] = useState("");
  const [subcontractorCount, setSubcontractorCount] = useState("");
  const [canProvideTracking, setCanProvideTracking] = useState("");
  const [subcontractorTelematics, setSubcontractorTelematics] = useState("");
  const [stepTrackerStack, setStepTrackerStack] = useState<string[]>([]);
  const [implementationTimeline, setImplementationTimeline] = useState("");
  const [vatType, setVatType] = useState("");
  const [implementationChallenges, setImplementationChallenges] = useState("");
  const [termsAccepted, setTermsAccepted] = useState(false);

  const history = useHistory();

  const ThankYouPage: React.FC<{  }> = ({  }) => {
    return (
      <div className={styles.thankYouPage}>
        <h1>Thank You!</h1>
        <p>Survey response has been successfully submitted.</p>
      </div>
    );
  };

  // Push step onto the stack
  const push = (...elements: string[]) => {
    setStepTrackerStack((prevStack) => [...prevStack, ...elements]);
    return elements;
  };
  // Pop step from the stack
  const pop = (): string | undefined => {
    if (stepTrackerStack.length === 0) {
      console.error('Stack is empty. Cannot pop.');
      return undefined; // Return undefined if the stack is empty
    }
    const deletedElement = stepTrackerStack[stepTrackerStack.length - 1];
    setStepTrackerStack((prevStack) => prevStack.slice(0, -1)); // Remove the last element
    return deletedElement; // Return the deleted element
  };


  const updateCurrentStep = (newStep: string) => {
    push(currentStep); // Push the current step before changing
    setCurrentStep(newStep); // Update the step
  };

  // Cleanup function for component unmounting
  useEffect(() => {
    // Cleanup function
    return () => {
      // Clear any pending timeouts/intervals
      // Reset states to prevent memory leaks
      setStepTrackerStack([]);
      //setTmsDataTypes([]);
      //setIsSubmitting(false);
    };
  }, []);

  // Memory management for large state objects
  useEffect(() => {
    // Clear unused data when moving between steps
    if (currentStep === "ThankYouPage") {
      // Clear form data after successful submission
      const clearFormData = setTimeout(() => {
        setEmail("");
        setCarrierName("");
        setVatNumber("");
        setContactName("");
        setContactPhoneNumber("");
        setContactEmail("");
        setTmsProvider("");
        setTruckTelematicsProvider("");
        setTrailerTelematicsProvider("");
        setTelematicsPercentage("");
        setHasGpsProvider("");
        setAlternativeTrackingMethod("");
        setTmsDataTypes([]);
        setIntegrationTypes([]);
        setDoesSubcontract("");
        setSubcontractorCount("");
        setCanProvideTracking("");
        setSubcontractorTelematics("");
        setImplementationTimeline("");
        setImplementationChallenges("");
        setTermsAccepted(false);
      }, 5000); // Clear after 5 seconds

      return () => clearTimeout(clearFormData);
    }
  }, [currentStep]);

  // Prevent memory leaks from navigation stack
  useEffect(() => {
    // Limit stack size to prevent memory buildup
    if (stepTrackerStack.length > 10) {
      setStepTrackerStack(prev => prev.slice(-5)); // Keep only last 5 steps
    }
  }, [stepTrackerStack]);

  const handleStartOnboarding = () => {
    updateCurrentStep("BasicDetailsPage");
  };

  const handleIvrCall = () => {
    updateCurrentStep("ContactCustomer")
  };

  const submitSurvey = async () => {

    const url = serviceUrls.selfOnboarding + `/survey/${email}`;
    
    const requestBody = {
      onboarding_token: onboardingToken,
      survey_info: [
        {
          contact_name: contactName,
          contact_phone_number: contactPhoneNumber,
          contact_email: contactEmail,
          tms_provider: tmsProvider,
          tms_type: tmsType, // NEW: 'tms' or 'manual'
          tms_data_types: tmsDataTypes,
          integration_types: integrationTypes,
          truck_telematics_provider: truckTelematicsProvider,
          trailer_telematics_provider: trailerTelematicsProvider,
          telematics_percentage: telematicsPercentage,
          has_gps_provider: hasGpsProvider,
          alternative_tracking_method: alternativeTrackingMethod,
          does_subcontract: doesSubcontract,
          subcontractor_count: subcontractorCount,
          can_provide_tracking: canProvideTracking,
          subcontractor_telematics: subcontractorTelematics,
          implementation_timeline: implementationTimeline,
          implementation_challenges: implementationChallenges,
          terms_accepted: termsAccepted,
        },
      ],
    };

    console.log("Request body:", requestBody);
  
    try {
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      if (response.ok) {
        try {
          const responseData = await response.json();
          console.log("Survey submitted successfully:", responseData);
        } catch (jsonError) {
          console.log("Survey submitted successfully (empty response)");
        }

        // Redirect to completion page instead of directly to onboarding/login
        updateCurrentStep("SurveyCompletionPage");
      } else {
        try {
          const errorData = await response.json();
          console.error("Error submitting survey:", errorData);
          alert("Failed to submit survey. Please try again.");
        } catch (jsonError) {
          console.error(`Survey submission failed with status: ${response.status}`);
          alert(`Failed to submit survey. Status: ${response.status}. Please try again.`);
        }
      }
    } catch (error) {
      console.error("Error occurred while submitting survey:", error);
      alert("An error occurred while submitting the survey. Please check your connection and try again.");
    }
  };

  const onBack = () => {
    const previousStep = pop();
    if (previousStep) {
      setCurrentStep(previousStep);
    } else {
      console.error("Step tracker stack is empty. No previous step to navigate to.");
      setCurrentStep("Welcomepage");
    }
  };
  const handleTechnicalTeamContact = () => {
    updateCurrentStep("ContactCustomer");
  };

  let content;
  
  if (currentStep === "BasicDetailsPage") {
    content = (
      <SurveryMandatoryDetailsForm
          email={email}
          setEmail={setEmail}
          carrierName={carrierName}
          setCarrierName={setCarrierName}
          vatNumber={vatNumber}
          setVatNumber={setVatNumber}
          vatType={vatType}
          setVatType={setVatType}
          contactName={contactName}
          setContactName={setContactName}
          contactPhoneNumber={contactPhoneNumber}
          setContactPhoneNumber={setContactPhoneNumber}
          contactEmail={contactEmail}
          setContactEmail={setContactEmail}
          onNextStep={() => updateCurrentStep("TMSInformation")}
          onBack={onBack}
          handleTechnicalTeamContact={handleTechnicalTeamContact}
          shipperName={shipperName}
        />    
    );
  } else if (currentStep === "Welcomepage") {
    content = (
      <>
      <div className={styles.container}>
        <div className={styles.welcomeCard}>
          {/* Header Section */}
          <div className={styles.header}>
            <div className={styles.logoContainer}>
              <img 
                src={fourkitesCircleLogo} 
                alt="FourKites Logo" 
                className={styles.fourkitesLogo}
              />
            </div>
            <h1 className={styles.title}>
              Welcome to the {shipperName} Carrier Network!
            </h1>
          </div>

          {/* Content Section */}
          <div className={styles.content}>
            <div className={styles.welcomeTitle}>
              <p className={styles.welcomeDescription}>
                <span className={styles.brandName}>{shipperName}</span> requires real-time visibility
                for all shipments and works with{' '}
                <span className={styles.brandName}>FourKites</span>, our Real Time Visibility provider,
                to achieve this. This brief survey helps us understand your technical capabilities in order
                to set up an appropriate integration between your systems and the FourKites Platform
                enabling effective tracking of {shipperName} shipments.
              </p>
            </div>

            {/* Benefits Section */}
            <div className={styles.benefitsSection}>
              <h3 className={styles.benefitsHeading}>Benefits for carriers:</h3>
              <div className={styles.benefitsGrid}>
                <div className={styles.benefitCard}>
                  <div className={`${styles.benefitIcon} ${styles.green}`}>
                    <svg className={`${styles.benefitIconSvg} ${styles.green}`} fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                    </svg>
                  </div>
                  <p className={styles.benefitText}>Reduced check calls</p>
                </div>
                
                <div className={styles.benefitCard}>
                  <div className={`${styles.benefitIcon} ${styles.blue}`}>
                    <svg className={`${styles.benefitIconSvg} ${styles.blue}`} fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"/>
                    </svg>
                  </div>
                  <p className={styles.benefitText}>Faster detention resolution</p>
                </div>
                
                <div className={styles.benefitCard}>
                  <div className={`${styles.benefitIcon} ${styles.purple}`}>
                    <svg className={`${styles.benefitIconSvg} ${styles.purple}`} fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"/>
                    </svg>
                  </div>
                  <p className={styles.benefitText}>Improved appointment management</p>
                </div>
                
                <div className={styles.benefitCard}>
                  <div className={`${styles.benefitIcon} ${styles.orange}`}>
                    <svg className={`${styles.benefitIconSvg} ${styles.orange}`} fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
                    </svg>
                  </div>
                  <p className={styles.benefitText}>Enhanced business relationship with {shipperName}</p>
                </div>
              </div>
            </div>

            {/* Data Use Notice */}
            <div className={styles.dataUseNotice}>
              <svg className={styles.dataIcon} fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"/>
              </svg>
              <div className={styles.dataContent}>
                <h4 className={styles.dataTitle}>Limited Data Use</h4>
                <p className={styles.dataText}>
                  Information submitted under this survey will only be used for {shipperName} shipment tracking purposes.
                </p>
              </div>
            </div>

            {/* Privacy Notice */}
            <div className={styles.privacyNotice}>
              <svg className={styles.privacyIcon} fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"/>
              </svg>
              <div className={styles.privacyContent}>
                <h4 className={styles.privacyTitle}>Data Protection</h4>
                <p className={styles.privacyText}>
                  Any personal data provided in responses to this survey will be processed in accordance with the{' '}
                  <a 
                    href="https://www.fourkites.com/legal/privacy-policy/" 
                    target="_blank" 
                    rel="noopener noreferrer" 
                    className={styles.privacyLink}
                  >
                    FourKites Privacy Notice
                  </a>{' '}
                  and solely for the purposes described herein.
                </p>
              </div>
            </div>

            {/* CTA Section */}
            <div className={styles.ctaSection}>
              <div className={styles.buttonContainer}>
                <button
                  onClick={handleStartOnboarding}
                  className={styles.startButton}
                >
                  Start Survey
                  <svg className={styles.buttonIcon} fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 111.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"/>
                  </svg>
                </button>
                
                <button
                  onClick={handleIvrCall}
                  className={styles.startButton}
                >
                  Get it done by a call
                  <svg className={styles.buttonIcon} fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 111.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"/>
                  </svg>
                </button>
              </div>
              <p className={styles.timeIndicator}>Takes only 3-5 minutes to complete</p>
            </div>

            {/* Footer */}
            <div className={styles.footer}>
              <p className={styles.footerText}>
                © 2025 {shipperName} & FourKites. All rights reserved.
              </p>
            </div>
          </div>
        </div>
      </div>
      </>
    )
  }else if (currentStep === "TMSInformation") {
    content = (
      <TMSInformationForm
        tmsProvider={tmsProvider}
        setTmsProvider={setTmsProvider}
        tmsType={tmsType}
        setTmsType={setTmsType}
        tmsDataTypes={tmsDataTypes}
        setTmsDataTypes={setTmsDataTypes}
        integrationTypes={integrationTypes}
        setIntegrationTypes={setIntegrationTypes}
        onNextStep={() => updateCurrentStep("TelematicsInformation")} // Go to new Telematics page
        onBack={onBack}
        handleTechnicalTeamContact={handleTechnicalTeamContact}
      />
    );
  } else if (currentStep === "TelematicsInformation") {
    content = (
      <TelematicsInformationForm
        truckTelematicsProvider={truckTelematicsProvider}
        setTruckTelematicsProvider={setTruckTelematicsProvider}
        trailerTelematicsProvider={trailerTelematicsProvider}
        setTrailerTelematicsProvider={setTrailerTelematicsProvider}
        telematicsPercentage={telematicsPercentage}
        setTelematicsPercentage={setTelematicsPercentage}
        hasGpsProvider={hasGpsProvider}
        setHasGpsProvider={setHasGpsProvider}
        alternativeTrackingMethod={alternativeTrackingMethod}
        setAlternativeTrackingMethod={setAlternativeTrackingMethod}
        onNextStep={() => updateCurrentStep("DirectSharePage")} // Go to existing Subcontracting page
        onBack={onBack}
        handleTechnicalTeamContact={handleTechnicalTeamContact}
      />
    );
  } else if (currentStep === "DirectSharePage") {
    content = (
      <SubcontractingInformationForm 
        doesSubcontract={doesSubcontract}
        setDoesSubcontract={setDoesSubcontract}
        subcontractorCount={subcontractorCount}
        setSubcontractorCount={setSubcontractorCount}
        canProvideTracking={canProvideTracking}
        setCanProvideTracking={setCanProvideTracking}
        subcontractorTelematics={subcontractorTelematics}
        setSubcontractorTelematics={setSubcontractorTelematics}
        onNextStep={() => updateCurrentStep("ImplementationTimeline")}
        onBack={onBack}
        handleTechnicalTeamContact={handleTechnicalTeamContact}
        shipperName={shipperName}
      />
    );
  } else if (currentStep === "ImplementationTimeline") {
    content = (
      <ImplementationTimelineForm
        implementationTimeline={implementationTimeline}
        setImplementationTimeline={setImplementationTimeline}
        implementationChallenges={implementationChallenges}
        setImplementationChallenges={setImplementationChallenges}
        termsAccepted={termsAccepted}
        setTermsAccepted={setTermsAccepted}
        onBack={onBack}
        submitSurvey={submitSurvey}
        handleTechnicalTeamContact={handleTechnicalTeamContact}
        shipperName={shipperName}
        surveyCreatedAt={surveyCreatedAt || undefined}
      />
    );
  } else if (currentStep === "ContactCustomer") {
    content = (
      <ContactInformationForm
        phoneNumber={phoneNumber}
        setPhoneNumber={setPhoneNumber}
        country={country}
        setCountry={setCountry}
        onNextStep={() => updateCurrentStep("SurveyCompletionPage")} // Updated to go to SurveyCompletionPage
        onBack={onBack}

        shipperName={shipperName}
      />
    );
  } else if (currentStep === "SurveyCompletionPage") {
    content = (
      <SurveyCompletionPage
        shipperName={shipperName}
        companyAlreadyExists={companyAlreadyExists}
        onboardingToken={onboardingToken || undefined}
        carrierName={carrierName}
        contactEmail={contactEmail}
      />
    );
  } else if (currentStep === "ThankYouPage") {
    content = <ThankYouPage />;
  }

  return <main className={styles.welcomeScreen}>{content}</main>;
};

export default WelcomeScreen;