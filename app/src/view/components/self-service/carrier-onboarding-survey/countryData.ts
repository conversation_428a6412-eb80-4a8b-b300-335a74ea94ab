export interface Country {
  code: string;
  name: string;
  dialCode: string;
}

// Common countries to prioritize in the dropdown
export const COMMON_COUNTRIES: string[] = [
  'US', 'IN', 'GB', 'CA', 'AU',
  // European countries
  'DE', 'FR', 'ES', 'IT', 'NL', 'PT', 'BE', 'CH', 'AT', 'SE', 'NO', 'DK', 'FI',
  'PL', 'CZ', 'HU', 'RO', 'GR', 'IE', 'LU', 'BG', 'HR', 'CY', 'MT', 'SK', 'SI', 'EE', 'LV', 'LT',
  // Other major countries
  'MX', 'BR', 'CN', 'JP', 'RU', 'ZA', 'AE', 'SG', 'MY'
];

// Complete list of all countries with their dial codes
export const ALL_COUNTRIES: Country[] = [
  // Major non-European countries
  { code: 'US', name: 'United States', dialCode: '+1' },
  { code: 'IN', name: 'India', dialCode: '+91' },
  { code: 'GB', name: 'United Kingdom', dialCode: '+44' },
  { code: 'CA', name: 'Canada', dialCode: '+1' },
  { code: 'AU', name: 'Australia', dialCode: '+61' },
  { code: 'MX', name: 'Mexico', dialCode: '+52' },
  { code: 'BR', name: 'Brazil', dialCode: '+55' },
  { code: 'CN', name: 'China', dialCode: '+86' },
  { code: 'JP', name: 'Japan', dialCode: '+81' },
  { code: 'RU', name: 'Russia', dialCode: '+7' },
  { code: 'ZA', name: 'South Africa', dialCode: '+27' },
  { code: 'AE', name: 'United Arab Emirates', dialCode: '+971' },
  { code: 'SG', name: 'Singapore', dialCode: '+65' },
  { code: 'MY', name: 'Malaysia', dialCode: '+60' },
  
  // European Union countries
  { code: 'DE', name: 'Germany', dialCode: '+49' },
  { code: 'FR', name: 'France', dialCode: '+33' },
  { code: 'ES', name: 'Spain', dialCode: '+34' },
  { code: 'IT', name: 'Italy', dialCode: '+39' },
  { code: 'NL', name: 'Netherlands', dialCode: '+31' },
  { code: 'PT', name: 'Portugal', dialCode: '+351' },
  { code: 'BE', name: 'Belgium', dialCode: '+32' },
  { code: 'SE', name: 'Sweden', dialCode: '+46' },
  { code: 'PL', name: 'Poland', dialCode: '+48' },
  { code: 'RO', name: 'Romania', dialCode: '+40' },
  { code: 'GR', name: 'Greece', dialCode: '+30' },
  { code: 'CZ', name: 'Czech Republic', dialCode: '+420' },
  { code: 'HU', name: 'Hungary', dialCode: '+36' },
  { code: 'AT', name: 'Austria', dialCode: '+43' },
  { code: 'IE', name: 'Ireland', dialCode: '+353' },
  { code: 'DK', name: 'Denmark', dialCode: '+45' },
  { code: 'FI', name: 'Finland', dialCode: '+358' },
  { code: 'SK', name: 'Slovakia', dialCode: '+421' },
  { code: 'HR', name: 'Croatia', dialCode: '+385' },
  { code: 'BG', name: 'Bulgaria', dialCode: '+359' },
  { code: 'LT', name: 'Lithuania', dialCode: '+370' },
  { code: 'SI', name: 'Slovenia', dialCode: '+386' },
  { code: 'LV', name: 'Latvia', dialCode: '+371' },
  { code: 'EE', name: 'Estonia', dialCode: '+372' },
  { code: 'CY', name: 'Cyprus', dialCode: '+357' },
  { code: 'LU', name: 'Luxembourg', dialCode: '+352' },
  { code: 'MT', name: 'Malta', dialCode: '+356' },
  
  // Other European countries
  { code: 'CH', name: 'Switzerland', dialCode: '+41' },
  { code: 'NO', name: 'Norway', dialCode: '+47' },
  { code: 'UA', name: 'Ukraine', dialCode: '+380' },
  { code: 'BY', name: 'Belarus', dialCode: '+375' },
  { code: 'RS', name: 'Serbia', dialCode: '+381' },
  { code: 'MD', name: 'Moldova', dialCode: '+373' },
  { code: 'MK', name: 'North Macedonia', dialCode: '+389' },
  { code: 'AL', name: 'Albania', dialCode: '+355' },
  { code: 'IS', name: 'Iceland', dialCode: '+354' },
  { code: 'MC', name: 'Monaco', dialCode: '+377' },
  { code: 'LI', name: 'Liechtenstein', dialCode: '+423' },
  { code: 'ME', name: 'Montenegro', dialCode: '+382' },
  { code: 'BA', name: 'Bosnia and Herzegovina', dialCode: '+387' },
  
  // Asia & Pacific
  { code: 'AF', name: 'Afghanistan', dialCode: '+93' },
  { code: 'AM', name: 'Armenia', dialCode: '+374' },
  { code: 'AZ', name: 'Azerbaijan', dialCode: '+994' },
  { code: 'BH', name: 'Bahrain', dialCode: '+973' },
  { code: 'BD', name: 'Bangladesh', dialCode: '+880' },
  { code: 'BT', name: 'Bhutan', dialCode: '+975' },
  { code: 'BN', name: 'Brunei', dialCode: '+673' },
  { code: 'KH', name: 'Cambodia', dialCode: '+855' },
  { code: 'GE', name: 'Georgia', dialCode: '+995' },
  { code: 'HK', name: 'Hong Kong', dialCode: '+852' },
  { code: 'ID', name: 'Indonesia', dialCode: '+62' },
  { code: 'IR', name: 'Iran', dialCode: '+98' },
  { code: 'IQ', name: 'Iraq', dialCode: '+964' },
  { code: 'IL', name: 'Israel', dialCode: '+972' },
  { code: 'JO', name: 'Jordan', dialCode: '+962' },
  { code: 'KZ', name: 'Kazakhstan', dialCode: '+7' },
  { code: 'KW', name: 'Kuwait', dialCode: '+965' },
  { code: 'KG', name: 'Kyrgyzstan', dialCode: '+996' },
  { code: 'LA', name: 'Laos', dialCode: '+856' },
  { code: 'LB', name: 'Lebanon', dialCode: '+961' },
  { code: 'MO', name: 'Macao', dialCode: '+853' },
  { code: 'MV', name: 'Maldives', dialCode: '+960' },
  { code: 'MN', name: 'Mongolia', dialCode: '+976' },
  { code: 'MM', name: 'Myanmar', dialCode: '+95' },
  { code: 'NP', name: 'Nepal', dialCode: '+977' },
  { code: 'KP', name: 'North Korea', dialCode: '+850' },
  { code: 'OM', name: 'Oman', dialCode: '+968' },
  { code: 'PK', name: 'Pakistan', dialCode: '+92' },
  { code: 'PS', name: 'Palestine', dialCode: '+970' },
  { code: 'PH', name: 'Philippines', dialCode: '+63' },
  { code: 'QA', name: 'Qatar', dialCode: '+974' },
  { code: 'SA', name: 'Saudi Arabia', dialCode: '+966' },
  { code: 'KR', name: 'South Korea', dialCode: '+82' },
  { code: 'LK', name: 'Sri Lanka', dialCode: '+94' },
  { code: 'SY', name: 'Syria', dialCode: '+963' },
  { code: 'TW', name: 'Taiwan', dialCode: '+886' },
  { code: 'TJ', name: 'Tajikistan', dialCode: '+992' },
  { code: 'TH', name: 'Thailand', dialCode: '+66' },
  { code: 'TL', name: 'Timor-Leste', dialCode: '+670' },
  { code: 'TR', name: 'Turkey', dialCode: '+90' },
  { code: 'TM', name: 'Turkmenistan', dialCode: '+993' },
  { code: 'UZ', name: 'Uzbekistan', dialCode: '+998' },
  { code: 'VN', name: 'Vietnam', dialCode: '+84' },
  { code: 'YE', name: 'Yemen', dialCode: '+967' },
  
  // Africa
  { code: 'DZ', name: 'Algeria', dialCode: '+213' },
  { code: 'AO', name: 'Angola', dialCode: '+244' },
  { code: 'BJ', name: 'Benin', dialCode: '+229' },
  { code: 'BW', name: 'Botswana', dialCode: '+267' },
  { code: 'BF', name: 'Burkina Faso', dialCode: '+226' },
  { code: 'BI', name: 'Burundi', dialCode: '+257' },
  { code: 'CM', name: 'Cameroon', dialCode: '+237' },
  { code: 'CV', name: 'Cape Verde', dialCode: '+238' },
  { code: 'CF', name: 'Central African Republic', dialCode: '+236' },
  { code: 'TD', name: 'Chad', dialCode: '+235' },
  { code: 'KM', name: 'Comoros', dialCode: '+269' },
  { code: 'CG', name: 'Congo', dialCode: '+242' },
  { code: 'CD', name: 'Congo (DRC)', dialCode: '+243' },
  { code: 'DJ', name: 'Djibouti', dialCode: '+253' },
  { code: 'EG', name: 'Egypt', dialCode: '+20' },
  { code: 'GQ', name: 'Equatorial Guinea', dialCode: '+240' },
  { code: 'ER', name: 'Eritrea', dialCode: '+291' },
  { code: 'ET', name: 'Ethiopia', dialCode: '+251' },
  { code: 'GA', name: 'Gabon', dialCode: '+241' },
  { code: 'GM', name: 'Gambia', dialCode: '+220' },
  { code: 'GH', name: 'Ghana', dialCode: '+233' },
  { code: 'GN', name: 'Guinea', dialCode: '+224' },
  { code: 'GW', name: 'Guinea-Bissau', dialCode: '+245' },
  { code: 'CI', name: 'Ivory Coast', dialCode: '+225' },
  { code: 'KE', name: 'Kenya', dialCode: '+254' },
  { code: 'LS', name: 'Lesotho', dialCode: '+266' },
  { code: 'LR', name: 'Liberia', dialCode: '+231' },
  { code: 'LY', name: 'Libya', dialCode: '+218' },
  { code: 'MG', name: 'Madagascar', dialCode: '+261' },
  { code: 'MW', name: 'Malawi', dialCode: '+265' },
  { code: 'ML', name: 'Mali', dialCode: '+223' },
  { code: 'MR', name: 'Mauritania', dialCode: '+222' },
  { code: 'MU', name: 'Mauritius', dialCode: '+230' },
  { code: 'MA', name: 'Morocco', dialCode: '+212' },
  { code: 'MZ', name: 'Mozambique', dialCode: '+258' },
  { code: 'NA', name: 'Namibia', dialCode: '+264' },
  { code: 'NE', name: 'Niger', dialCode: '+227' },
  { code: 'NG', name: 'Nigeria', dialCode: '+234' },
  { code: 'RW', name: 'Rwanda', dialCode: '+250' },
  { code: 'ST', name: 'São Tomé and Príncipe', dialCode: '+239' },
  { code: 'SN', name: 'Senegal', dialCode: '+221' },
  { code: 'SC', name: 'Seychelles', dialCode: '+248' },
  { code: 'SL', name: 'Sierra Leone', dialCode: '+232' },
  { code: 'SO', name: 'Somalia', dialCode: '+252' },
  { code: 'SS', name: 'South Sudan', dialCode: '+211' },
  { code: 'SD', name: 'Sudan', dialCode: '+249' },
  { code: 'SZ', name: 'Swaziland', dialCode: '+268' },
  { code: 'TZ', name: 'Tanzania', dialCode: '+255' },
  { code: 'TG', name: 'Togo', dialCode: '+228' },
  { code: 'TN', name: 'Tunisia', dialCode: '+216' },
  { code: 'UG', name: 'Uganda', dialCode: '+256' },
  { code: 'ZM', name: 'Zambia', dialCode: '+260' },
  { code: 'ZW', name: 'Zimbabwe', dialCode: '+263' },
  
  // Americas (North, Central, South)
  { code: 'AR', name: 'Argentina', dialCode: '+54' },
  { code: 'BS', name: 'Bahamas', dialCode: '+1' },
  { code: 'BB', name: 'Barbados', dialCode: '+1' },
  { code: 'BZ', name: 'Belize', dialCode: '+501' },
  { code: 'BO', name: 'Bolivia', dialCode: '+591' },
  { code: 'CL', name: 'Chile', dialCode: '+56' },
  { code: 'CO', name: 'Colombia', dialCode: '+57' },
  { code: 'CR', name: 'Costa Rica', dialCode: '+506' },
  { code: 'CU', name: 'Cuba', dialCode: '+53' },
  { code: 'DM', name: 'Dominica', dialCode: '+1' },
  { code: 'DO', name: 'Dominican Republic', dialCode: '+1' },
  { code: 'EC', name: 'Ecuador', dialCode: '+593' },
  { code: 'SV', name: 'El Salvador', dialCode: '+503' },
  { code: 'GT', name: 'Guatemala', dialCode: '+502' },
  { code: 'GY', name: 'Guyana', dialCode: '+592' },
  { code: 'HT', name: 'Haiti', dialCode: '+509' },
  { code: 'HN', name: 'Honduras', dialCode: '+504' },
  { code: 'JM', name: 'Jamaica', dialCode: '+1' },
  { code: 'NI', name: 'Nicaragua', dialCode: '+505' },
  { code: 'PA', name: 'Panama', dialCode: '+507' },
  { code: 'PY', name: 'Paraguay', dialCode: '+595' },
  { code: 'PE', name: 'Peru', dialCode: '+51' },
  { code: 'PR', name: 'Puerto Rico', dialCode: '+1' },
  { code: 'KN', name: 'Saint Kitts and Nevis', dialCode: '+1' },
  { code: 'LC', name: 'Saint Lucia', dialCode: '+1' },
  { code: 'VC', name: 'Saint Vincent and the Grenadines', dialCode: '+1' },
  { code: 'SR', name: 'Suriname', dialCode: '+597' },
  { code: 'TT', name: 'Trinidad and Tobago', dialCode: '+1' },
  { code: 'UY', name: 'Uruguay', dialCode: '+598' },
  { code: 'VE', name: 'Venezuela', dialCode: '+58' },
  
  // Oceania
  { code: 'AS', name: 'American Samoa', dialCode: '+1' },
  { code: 'CK', name: 'Cook Islands', dialCode: '+682' },
  { code: 'FJ', name: 'Fiji', dialCode: '+679' },
  { code: 'PF', name: 'French Polynesia', dialCode: '+689' },
  { code: 'GU', name: 'Guam', dialCode: '+1' },
  { code: 'KI', name: 'Kiribati', dialCode: '+686' },
  { code: 'MH', name: 'Marshall Islands', dialCode: '+692' },
  { code: 'FM', name: 'Micronesia', dialCode: '+691' },
  { code: 'NR', name: 'Nauru', dialCode: '+674' },
  { code: 'NC', name: 'New Caledonia', dialCode: '+687' },
  { code: 'NZ', name: 'New Zealand', dialCode: '+64' },
  { code: 'NU', name: 'Niue', dialCode: '+683' },
  { code: 'NF', name: 'Norfolk Island', dialCode: '+672' },
  { code: 'MP', name: 'Northern Mariana Islands', dialCode: '+1' },
  { code: 'PW', name: 'Palau', dialCode: '+680' },
  { code: 'PG', name: 'Papua New Guinea', dialCode: '+675' },
  { code: 'WS', name: 'Samoa', dialCode: '+685' },
  { code: 'SB', name: 'Solomon Islands', dialCode: '+677' },
  { code: 'TK', name: 'Tokelau', dialCode: '+690' },
  { code: 'TO', name: 'Tonga', dialCode: '+676' },
  { code: 'TV', name: 'Tuvalu', dialCode: '+688' },
  { code: 'VU', name: 'Vanuatu', dialCode: '+678' },
  
  // Other territories
  { code: 'AX', name: 'Åland Islands', dialCode: '+358' },
  { code: 'AD', name: 'Andorra', dialCode: '+376' },
  { code: 'AI', name: 'Anguilla', dialCode: '+1' },
  { code: 'AQ', name: 'Antarctica', dialCode: '+672' },
  { code: 'AG', name: 'Antigua and Barbuda', dialCode: '+1' },
  { code: 'AW', name: 'Aruba', dialCode: '+297' },
  { code: 'BM', name: 'Bermuda', dialCode: '+1' },
  { code: 'BQ', name: 'Bonaire', dialCode: '+599' },
  { code: 'BV', name: 'Bouvet Island', dialCode: '+47' },
  { code: 'IO', name: 'British Indian Ocean Territory', dialCode: '+246' },
  { code: 'VG', name: 'British Virgin Islands', dialCode: '+1' },
  { code: 'KY', name: 'Cayman Islands', dialCode: '+1' },
  { code: 'CX', name: 'Christmas Island', dialCode: '+61' },
  { code: 'CC', name: 'Cocos Islands', dialCode: '+61' },
  { code: 'CW', name: 'Curaçao', dialCode: '+599' },
  { code: 'FK', name: 'Falkland Islands', dialCode: '+500' },
  { code: 'FO', name: 'Faroe Islands', dialCode: '+298' },
  { code: 'GF', name: 'French Guiana', dialCode: '+594' },
  { code: 'TF', name: 'French Southern Territories', dialCode: '+262' },
  { code: 'GI', name: 'Gibraltar', dialCode: '+350' },
  { code: 'GL', name: 'Greenland', dialCode: '+299' },
  { code: 'GD', name: 'Grenada', dialCode: '+1' },
  { code: 'GP', name: 'Guadeloupe', dialCode: '+590' },
  { code: 'GG', name: 'Guernsey', dialCode: '+44' },
  { code: 'HM', name: 'Heard Island and McDonald Islands', dialCode: '+672' },
  { code: 'VA', name: 'Holy See', dialCode: '+379' },
  { code: 'IM', name: 'Isle of Man', dialCode: '+44' },
  { code: 'JE', name: 'Jersey', dialCode: '+44' },
  { code: 'XK', name: 'Kosovo', dialCode: '+383' },
  { code: 'MQ', name: 'Martinique', dialCode: '+596' },
  { code: 'YT', name: 'Mayotte', dialCode: '+262' },
  { code: 'MS', name: 'Montserrat', dialCode: '+1' },
  { code: 'AN', name: 'Netherlands Antilles', dialCode: '+599' },
  { code: 'PN', name: 'Pitcairn', dialCode: '+64' },
  { code: 'RE', name: 'Réunion', dialCode: '+262' },
  { code: 'BL', name: 'Saint Barthélemy', dialCode: '+590' },
  { code: 'SH', name: 'Saint Helena', dialCode: '+290' },
  { code: 'MF', name: 'Saint Martin', dialCode: '+590' },
  { code: 'PM', name: 'Saint Pierre and Miquelon', dialCode: '+508' },
  { code: 'SM', name: 'San Marino', dialCode: '+378' },
  { code: 'SX', name: 'Sint Maarten', dialCode: '+1' },
  { code: 'GS', name: 'South Georgia and the South Sandwich Islands', dialCode: '+500' },
  { code: 'SJ', name: 'Svalbard and Jan Mayen', dialCode: '+47' },
  { code: 'TC', name: 'Turks and Caicos Islands', dialCode: '+1' },
  { code: 'UM', name: 'U.S. Minor Outlying Islands', dialCode: '+1' },
  { code: 'VI', name: 'U.S. Virgin Islands', dialCode: '+1' },
  { code: 'WF', name: 'Wallis and Futuna', dialCode: '+681' },
  { code: 'EH', name: 'Western Sahara', dialCode: '+212' },
];

// Helper function to get country details by code
export const getCountryByCode = (code: string): Country | undefined => {
  return ALL_COUNTRIES.find(country => country.code === code);
};

// Helper function to get country by dial code
export const getCountryByDialCode = (dialCode: string): Country | undefined => {
  return ALL_COUNTRIES.find(country => country.dialCode === dialCode);
};

// Helper function to convert country code to flag emoji
export const getFlagEmoji = (countryCode: string): string => {
  const codePoints = countryCode
    .toUpperCase()
    .split('')
    .map(char => 127397 + char.charCodeAt(0));
  return String.fromCodePoint(...codePoints);
};

// Helper function to format phone number with country code
export const formatPhoneNumber = (phoneNumber: string, countryCode: string): string => {
  const country = getCountryByCode(countryCode);
  if (!country) return phoneNumber;
  
  // Remove any existing country code or + symbol
  let cleanNumber = phoneNumber.replace(/^\+\d+\s?/, '');
  
  // Add the country code
  return `${country.dialCode}${cleanNumber}`.replace(/\s+/g, '');
};