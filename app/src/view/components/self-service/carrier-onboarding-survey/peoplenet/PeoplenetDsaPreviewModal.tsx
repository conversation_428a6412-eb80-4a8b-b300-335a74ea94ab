import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Document, Page } from "react-pdf";

import { Modal } from "@fourkites/elemental-modal";

//@ts-ignore
import peoplenetDsa from "assets/documents/peoplenetDsa.pdf";

import styles from "./PeoplenetDsaPreviewModal.module.scss";

const PeoplenetDsaPreviewModal = ({ show, onClose, onSign }: any) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * STATE
   ****************************************************************************/
  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  //useEffect(() => {}, [provider]);

  /*****************************************************************************
   * INTERNAL_METHODS
   ****************************************************************************/

  const onDocumentLoadSuccess = ({ numPages }: any) => {
    setNumPages(numPages);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  if (document == null) {
    return null;
  }

  const pageNumbers = Array.apply(null, Array(numPages)).map((x, i) => i + 1);

  return (
    <Modal
      size="medium"
      title={t("Peoplenet Data Sharing Consent")}
      show={show}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: onClose,
      }}
      saveButtonProps={{
        label: t("Sign"),
        onClick: onSign,
      }}
    >
      <div className={styles.container}>
        <Document file={peoplenetDsa} onLoadSuccess={onDocumentLoadSuccess}>
          {pageNumbers?.map((page) => (
            <Page key={page} pageNumber={page} />
          ))}
        </Document>
      </div>
    </Modal>
  );
};

export default PeoplenetDsaPreviewModal;
