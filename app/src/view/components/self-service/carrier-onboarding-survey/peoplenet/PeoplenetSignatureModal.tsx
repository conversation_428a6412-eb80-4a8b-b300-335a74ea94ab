import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import { Modal } from "@fourkites/elemental-modal";

import SignatureForm from "view/components/base/signature-form/SignatureForm";

import styles from "./PeoplenetSignatureModal.module.scss";

const PeoplenetSignatureModal = ({ show, onClose, onSubmitSignature }: any) => {
  const { t } = useTranslation();
  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [signatureResult, setSignatureResult] = useState<File | string>();
  const [showError, setShowError] = useState<boolean>(false);

  const onSave = () => {
    if (signatureResult != null && signatureResult !== "") {
      setShowError(false);
      onSubmitSignature(signatureResult);
    } else {
      setShowError(true);
    }
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <Modal
      size="small"
      title={t("Signature")}
      show={show}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: onClose,
      }}
      saveButtonProps={{
        label: t("Submit"),
        onClick: onSave,
      }}
    >
      <div className={styles.container}>
        {showError && (
          <p className={styles.error}>{t("Please provide your signature")}</p>
        )}
        <SignatureForm
          signatureResult={signatureResult}
          setSignatureResult={setSignatureResult}
        />
      </div>
    </Modal>
  );
};

export default PeoplenetSignatureModal;
