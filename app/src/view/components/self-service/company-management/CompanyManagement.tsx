import React, { useMemo, useState, useEffect, useCallback } from "react";
import { useTranslation } from "react-i18next";

import { Table } from "@fourkites/elemental-table";

import { useAppSelector } from "state/hooks";
import { CarriersNetworkState } from "state/modules/shipper/CarriersNetwork";
import { ShippersNetworkState } from "state/modules/carrier/ShippersNetwork";

import TableWithSidePanel from "view/components/base/containers/TableWithSidePanel";

import CompanyManagementHeader from "./management-header/CompanyManagementHeader";
import CarrierDetails from "./carrier-details/CarrierDetails";
import ShipperDetails from "./shipper-details/ShipperDetails";
import getColumns from "./management-table/CompanyManagementTableColumns";

import CompanyManagementProps from "./CompanyManagement.types";
import styles from "./CompanyManagement.module.scss";

const CompanyManagement = ({
  mode,
  managerCompanyId,
  managedCompanyType,
  // Companies data and fetching
  companiesData,
  fetchDataForPage,
  pageCount,
  totalEntries,
  // Carrier Additions
  onShowManualCarrierAdditions,
  onShowBulkCarrierAdditions,
  onShowCarrierInviteTemplate,
}: CompanyManagementProps) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * REDUX
   ****************************************************************************/

  const carriersNetworkFilters = useAppSelector(
    CarriersNetworkState.selectors.networkFiltersByMode(mode)
  );
  const shippersNetworkFilters = useAppSelector(
    ShippersNetworkState.selectors.networkFiltersByMode(mode)
  );

  const isGettingCarriers = useAppSelector(
    CarriersNetworkState.selectors.isLoadingCarriersByMode(mode)
  );
  const isGettingShippers = useAppSelector(
    ShippersNetworkState.selectors.isLoadingShippersByMode(mode)
  );

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [clickedRowId, setClickedRowId] = useState<any>(null);
  const [clickedRowIndex, setClickedRowIndex] = useState<any>(null);
  const [clickedCompany, setClickedCompany] = useState<any>(null);
  const [pageSize, setPageSize] = useState<number>(25);
  const [isPaginationFetch, setPaginationFetch] = useState<any>(false);

  /*****************************************************************************
   * DATA
   ****************************************************************************/

  const isManagingCarriers = managedCompanyType === "carrier";

  // Paginate list of shippers or FTL for carriers
  const allowPagination =
    (isManagingCarriers && mode === "ftl") || !isManagingCarriers;

  const { query, networkStatus } = isManagingCarriers
    ? carriersNetworkFilters
    : shippersNetworkFilters;
  
  const isLoading = isManagingCarriers ? isGettingCarriers : isGettingShippers;

  const columns = useMemo(() => {
    return getColumns(
      t,
      mode,
      managedCompanyType,
      networkStatus,
      clickedCompany != null
    );

    // eslint-disable-next-line
  }, [managedCompanyType, networkStatus, clickedCompany]);

  // Filter data by search, sorting by name as default
  const data = useMemo(
    () =>
      filterCompaniesData(
        isManagingCarriers,
        companiesData,
        query,
        allowPagination
      ),
    [isManagingCarriers, companiesData, allowPagination, query]
  );

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  // Updates clicked company data when we click on row
  useEffect(() => {
    setClickedCompany(clickedRowIndex !== null ? data[clickedRowIndex] : null);
  }, [clickedRowIndex]);

  // Updates clicked rows when we change query or network status
  useEffect(() => {
    setClickedRowId(null);
    setClickedRowIndex(null);
  }, [query, networkStatus]);

  /*****************************************************************************
   * INTENRAL METHODS
   ****************************************************************************/

  const onRowClicked = (rowInfo: any) => {
    const selectedRowId = rowInfo?.row?.id;
    const selectedRowIndex = rowInfo?.row?.index;

    if (selectedRowId !== clickedRowId) {
      setClickedRowId(selectedRowId);
      setClickedRowIndex(selectedRowIndex);
    } else {
      setClickedRowId(null);
      setClickedRowIndex(null);
    }
  };

  const onClosePanel = () => {
    setClickedRowId(null);
    setClickedRowIndex(null);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const preselectedRowIds =
    clickedRowIndex != null &&
    clickedRowId != null &&
    clickedRowIndex < data?.length
      ? [clickedRowId]
      : [];

  const detailsComponent =
    managedCompanyType === "carrier" ? (
      <CarrierDetails
        mode={mode}
        shipperId={managerCompanyId}
        carrierId={clickedCompany?.permalink}
        networkId={clickedCompany?.id}
        carrierData={clickedCompany}
        onClosePanel={onClosePanel}
      />
    ) : (
      <ShipperDetails
        mode={mode}
        carrierId={managerCompanyId}
        shipperId={clickedCompany?.permalink}
        networkId={clickedCompany?.id}
        shipperData={clickedCompany}
        onClosePanel={onClosePanel}
      />
    );

  const sidePanel = clickedCompany != null ? detailsComponent : null;

  // Fetcher is only useful if it's FTL, if not, its undefined. Also, we need to
  // save the last page size to make sure it is consistent when changing network
  // status
  const pageFetcher = useCallback(
    (obj: { pageIndex: number; pageSize: number; sortBy?: [] | undefined }) => {
      setPaginationFetch(true);
      setPageSize(obj.pageSize);
      fetchDataForPage(obj);
    },
    [setPageSize]
  );
  const fetchDataForPageHandler = allowPagination ? pageFetcher : undefined;

  const paginationParams = {
    defaultPageSize: pageSize,
    fetchDataForPage: fetchDataForPageHandler,
    pageCount: pageCount,
    totalEntries: totalEntries,
    paginated: true,
  };

  const showTable = isLoading ? isPaginationFetch : true

  return (
    <div className={styles.container}>
      <CompanyManagementHeader
        mode={mode}
        managerCompanyId={managerCompanyId}
        managedCompanyType={managedCompanyType}
        pageSize={pageSize}
        onShowManualCarrierAdditions={onShowManualCarrierAdditions}
        onShowBulkCarrierAdditions={onShowBulkCarrierAdditions}
        onShowCarrierInviteTemplate={onShowCarrierInviteTemplate}
      />

      <TableWithSidePanel
        table={
          <div className={styles.tableContainer}>
            {(showTable) && <Table
              variant="flat-bordered"
              data={data}
              columns={columns}
              onRowClicked={onRowClicked}
              preselectedRowIds={preselectedRowIds}
              enableManualSort={allowPagination}
              striped
              pagination={paginationParams}
            />
        }
          </div>
        }
        sidePanel={sidePanel}
      />
    </div>
  );
};

/*
 * Returns filtered data based on user filters and query
 */
const filterCompaniesData = (
  isManagingCarriers: boolean,
  companiesData: any,
  query: string,
  allowPagination: boolean
) => {
  // If pagination is allowed, data is already filtered (done by server)
  if (allowPagination) {
    return companiesData;
  }

  // If not, we need to filter data locally
  const filterData = (d: any) => {
    // Name
    const name = d.name;
    // Codes
    let allCodes = isManagingCarriers
      ? d?.carrier_codes || null
      : d?.customer_codes || null;
    allCodes = allCodes ? [...allCodes?.default, ...allCodes?.custom] : [];
    allCodes = allCodes.join(", ");
    // Identifications
    const usdot =
      d?.identifications?.find((i: any) => i?.type === "usdot")?.value || "";

    const dataToFilter = [name, allCodes, usdot];

    return dataToFilter?.some((value: string) =>
      value ? value?.toLowerCase()?.includes(query?.toLowerCase() || "") : false
    );
  };

  return companiesData
    ?.filter(filterData)
    .sort((a: any, b: any) => a.name.localeCompare(b.name));
};

export default CompanyManagement;