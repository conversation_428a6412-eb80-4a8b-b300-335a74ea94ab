import { LoadsTrackingMode } from "state/BaseTypes";

interface CompanyManagmentProps {
  initialSetup?: boolean;
  managerCompanyId: string;
  managedCompanyType: "carrier" | "shipper";
  mode: LoadsTrackingMode;
  companiesData: any[];
  fetchDataForPage: (obj: {
    pageIndex: number;
    pageSize: number;
    sortBy?: [] | undefined;
  }) => void;
  pageCount: number;
  totalEntries: number;
  onShowBulkCarrierAdditions?: () => void;
  onShowManualCarrierAdditions?: () => void;
  onShowCarrierInviteTemplate?: ()=> void;
}

export default CompanyManagmentProps;
