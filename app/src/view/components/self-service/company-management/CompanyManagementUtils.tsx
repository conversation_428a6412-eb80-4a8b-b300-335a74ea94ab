import {
  getUserLocaleDate,
  timeZoneAbbreviated,
} from "view/components/base/DateUtils";

export const getNetworkStatusVariant = (status: string) => {
  const variants: any = {
    connected: "ok",
    pending: "alert",
    disconnected: "error",
  };

  return variants[status];
};

export const getDataStatusVariant = (status: string) => {
  const variants: any = {
    active: "ok",
    error: "error",
  };

  return status ? variants[status] : "blank";
};

// TODO: replace by chips whenever we use this
export const getCompanyIdentifications = (data: any) => {
  const idents: string[] = data?.identifications?.map((ident: any) => {
    if (!ident?.value) {
      return "";
    }

    return `${ident?.type?.toUpperCase()}#: ${ident?.value || "--"}`;
  }) || ["--"];

  // Don't return null identifications
  return idents.filter((i: string) => i !== "").join(" | ");
};

export const getCompanyAddress = (data: any) => {
  return `${data?.address?.city || "--"}, ${data?.address?.state || "--"}, ${
    data?.address?.country_code || "--"
  }`;
};

export const getFormattedDate = (inputValue: string) => {
  if (inputValue == null) {
    return "--";
  }

  return getUserLocaleDate(inputValue) + " " + timeZoneAbbreviated();
};

/*******************************************************************************
 * COMPANY MANAGEMENT CONSTANTS
 ******************************************************************************/

export const MAX_CARRIER_CODES = 48;
export const MAX_CARRIER_CODE_LENGTH = 30;
export const MIN_CARRIER_CODES_TO_BE_PRESENT = 1;
export const MAX_SCACS = 10;
export const MAX_SCACS_LENGTH = 10;
