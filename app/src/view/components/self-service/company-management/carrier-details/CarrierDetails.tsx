import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { Spinner } from "@fourkites/elemental-loading-indicator";

import { LoadsTrackingMode } from "state/BaseTypes";
import { useAppDispatch, useAppSelector } from "state/hooks";
import { CarrierInvitationsState } from "state/modules/shipper/CarrierInvitations";
import { CarriersNetworkState } from "state/modules/shipper/CarriersNetwork";
import { CarriersNetworkDetailsState } from "state/modules/shipper/CarriersNetworkDetails";
import { EmailLoadsAdditionsIntegrationsState } from "state/modules/shipper/EmailLoadsAdditionsIntegrations";
import UsersState from "state/modules/Users";

import { showToast } from "view/components/base/toast/Toast";

import CompanyDetailsHeader from "../company-details/company-header/CompanyDetailsHeader";

import CompanyContacts from "../company-details/contacts/CompanyContacts";
import CompanyInfo from "../company-details/info/CompanyInfo";
import RecommendedActions from "../company-details/recommended-actions/RecommendedActions";
//import CompanyLogs from "../logs/CompanyLogs";

import CarrierInvitations from "./invitations/CarrierInvitations";

import Capabilities, { findCapabilityIndex } from "./capabilities/Capabilities";
import DataCollection from "./data-collection/DataCollection";
import NetworkHealth from "./network-health/NetworkHealth";
import CarrierCodes from "./codes/CarrierCodes";

import DisconnectCarrierModal from "./modals/DisconnectCarrierModal";
import NetworkErrorModal from "./modals/NetworkErrorModal";

import styles from "./CarrierDetails.module.scss";
import CarrierDetailsProps from "./CarrierDetails.types";
import { CommonCompanyContactsState } from "state/modules/CommonCompanyContacts";

const CarrierDetails = ({
  mode,
  shipperId,
  carrierId,
  networkId,
  carrierData,
  onClosePanel,
}: CarrierDetailsProps) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * REDUX
   ****************************************************************************/

  const dispatch = useAppDispatch();

  const carrierDetails: any = useAppSelector(
    CarriersNetworkDetailsState.selectors.carrierDetails()
  );

  const currentUser = useAppSelector(UsersState.selectors.getCurrentUser);

  const hasError: boolean = useAppSelector(
    CarriersNetworkState.selectors.hasErrorByMode(mode as LoadsTrackingMode)
  );

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [showModal, setShowModal] = useState<boolean>(false);

  const modesWithoutAdditions = ["ltl", "parcel", "ocean"].includes(mode);

  const networkStatus = carrierData?.status;
  const isAddedToNetwork = networkStatus != null;
  const isPendingInvite = networkStatus === "invited";
  const isExpiredInvite = networkStatus === "expired";
  const isNotInNetwork =
    networkStatus === null || networkStatus === "disconnected";

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  useEffect(() => {
    // Only fetch details of carriers in the network
    if (!isAddedToNetwork && !modesWithoutAdditions) {
      return;
    }

    // If still pending, fetches invitations
    if (isPendingInvite || isExpiredInvite) {
      dispatch(
        CarrierInvitationsState.actions.getCarrierInvitations({
          shipperId,
          networkId,
          mode,
        })
      );
      return;
    }

    // Otherwise, get carrier details
    dispatch(
      CarriersNetworkDetailsState.actions.getCarrierDetails({
        shipperId,
        carrierId,
        mode,
      })
    );
    // eslint-disable-next-line
  }, [
    mode,
    shipperId,
    carrierId,
    networkId,
    isAddedToNetwork,
    isPendingInvite,
    isExpiredInvite,
  ]);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const fetchCarrierData = () => {
    // Updates table component
    dispatch(CarriersNetworkState.actions.getCarriers({ shipperId, mode }));

    // Updates details pane component
    dispatch(
      CarriersNetworkDetailsState.actions.getCarrierDetails({
        shipperId,
        carrierId,
        mode,
      })
    );
  };

  const handleCapabilitiesChanges = async (capabilities: any[]) => {
    const connectHandler = isNotInNetwork
      ? CarriersNetworkState.actions.connectCarrier
      : CarriersNetworkState.actions.changeCarrierCapabilities;

    const connectResponse = await dispatch(
      connectHandler({ shipperId, carrierId, capabilities, mode })
    );
    if ("error" in connectResponse) {
      return false;
    }

    return true;
  };

  const handleCredentialChanges = async (capabilities: any[]) => {
    const connectHandler = CarriersNetworkState.actions.storeCredential
    const connectResponse = await dispatch(
      connectHandler({ shipperId, carrierId, capabilities, mode })
    );
    console.log(connectResponse)
    if ("error" in connectResponse) {
      return false;
    }

    return true;
  };



  const handleIntegrations = async (capabilities: any[]) => {
    /*TODO: how to handle each case of loads integrations?
    These are all the edge cases we have:

    1) Let's say we disable the loads toggle. Should we do anything with the
       integration? Delete it? Disable it?
    2) Let's say we enable again. What to do with the integration?
    3) How to uniquely identify which integration refers to that carrier?
       What can we say is unique? The sender email?
    */

    const isLoadsEnabled =
      capabilities[findCapabilityIndex("loads", capabilities)]?.enabled;

    if (!isLoadsEnabled) {
      // TODO: handle disable in future
      return;
    }

    // Get default params for integration. Default integration params vary with
    // each carrier and are returned as part of carrier details. Other fields
    // like default name and email are constant
    const appEnvironment = window?.appConfig?.app_environment
      ? window?.appConfig?.app_environment
      : "dev";
    const defaultName = `${carrierData?.name} Shipments`;
    const defaultEmail =
      appEnvironment === "prod"
        ? `${shipperId}_${carrierId}@loads.fourkites.com`
        : `${shipperId}_${carrierId}@loads${appEnvironment}.fourkites.com`;
    const defaultParams =
      carrierDetails?.default_loads_additions_parameters?.email_integration;

    const defaultIntegration = {
      name: defaultName,
      enabled: true,
      sender_email: defaultEmail,
      file_format: defaultParams?.file_format || null,
      template: {
        preset: defaultParams?.template?.preset || null,
        custom: {
          mapping_configuration_name:
            defaultParams?.template?.custom?.mapping_configuration_name || null,
          edi_schema_source:
            defaultParams?.template?.custom?.edi_schema_source || null,
        },
      },
    };

    const response = await dispatch(
      EmailLoadsAdditionsIntegrationsState.actions.createIntegration({
        shipperId,
        mode: mode,
        userId: currentUser?.userId,
        integration: defaultIntegration,
      })
    );

    if ("error" in response) {
      return false;
    }

    return true;
  };

  const onClickConnect = async (capabilitiesAndCredentials: any) => {
    const capabilitiesUpdated = await handleCapabilitiesChanges(
      capabilitiesAndCredentials
    );

    if (!capabilitiesUpdated) {
      // TODO: handle error
      return;
    }

    const integrationsUpdated = await handleIntegrations(
      capabilitiesAndCredentials?.capabilities || []
    );
    if (!integrationsUpdated) {
      // TODO: handle error
      //return;
    }
    // Update carriers data
    fetchCarrierData();
  };

  const onClickSave = async (capabilitiesAndCredentials: any) => {
    console.log("hsrbgjkbgjke")
    const capabilitiesUpdated = await handleCredentialChanges(
      capabilitiesAndCredentials
    );

    if (!capabilitiesUpdated) {
      // TODO: handle error
      return;
    }
    fetchCarrierData();
  };

  const onClickDisconnect = () => {
    setShowModal(true);
  };

  const onConfirmDisconnect = async () => {
    setShowModal(false);

    const disconnectResponse = await dispatch(
      CarriersNetworkState.actions.disconnectCarrier({
        shipperId,
        carrierId,
        mode,
      })
    );
    if ("error" in disconnectResponse) {
      // TODO: handle error
      return;
    }

    showToast(t("The carrier was disconnected from your network."), "", "ok");

    fetchCarrierData();
  };

  const onAcknowledgeError = () => {
    dispatch(CarriersNetworkState.actions.clearError(mode));
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  if (!carrierData) {
    return null;
  }

  return (
    <div key={carrierId} className={styles.container}>
      <div
        data-test-id="carrier-details-wrapper"
        key={carrierId}
        className={styles.wrapper}
      >
        {isPendingInvite || isExpiredInvite ? (
          <CarrierInvitationsElement
            mode={mode}
            shipperId={shipperId}
            carrierId={carrierId}
            networkId={networkId}
            onClosePanel={onClosePanel}
            isPendingInvite={isPendingInvite}
            isExpiredInvite={isExpiredInvite}
          />
        ) : (
        <CarrierDetailsElement
            mode={mode}
            shipperId={shipperId}
            carrierId={carrierId}
            onClickConnect={onClickConnect}
            onClickDisconnect={onClickDisconnect}
            onClickSave={onClickSave}
            onClosePanel={onClosePanel}
          />
        )}

        <NetworkErrorModal
          showModal={hasError}
          onClose={onAcknowledgeError}
          onOk={onAcknowledgeError}
        />

        <DisconnectCarrierModal
          showModal={showModal}
          onClose={() => setShowModal(false)}
          onDisconnect={onConfirmDisconnect}
        />
      </div>
    </div>
  );
};

const CarrierDetailsElement = ({
  mode,
  shipperId,
  carrierId,
  onClickConnect,
  onClickSave,
  onClickDisconnect,
  onClosePanel,
}: any) => {
  // Carrier details
  const carrierDetails: any = useAppSelector(
    CarriersNetworkDetailsState.selectors.carrierDetails()
  );
  const isLoadingCarrierDetails: boolean = useAppSelector(
    CarriersNetworkDetailsState.selectors.isLoadingCarrierDetails()
  );

  const networkStatus = carrierDetails?.status;
  const isAddedToNetwork = networkStatus != null;
  const isConnectedToNetwork = ["connected", "in_progress"].includes(
    networkStatus
  );

  if (isLoadingCarrierDetails) {
    return (
      <div className={styles.loader}>
        <Spinner isLoading size="medium" />
      </div>
    );
  }

  const onClickCapabilitiesConnect = async (
    capabilitiesAndCredentials: any
  ) => {
    const carrierCodes = carrierDetails?.carrier_codes;
    if (
      carrierCodes?.custom?.length === 0 &&
      carrierCodes?.default.length === 0 &&
      mode === "ftl"
    ) {
      showToast(
        "Carrier Codes",
        "Provide at least one carrier code before connecting.",
        "error"
      );
      return;
    }
    await onClickConnect(capabilitiesAndCredentials);
  };

  const onClickDataCollectionSave = async (
    capabilitiesAndCredentials: any
  ) => {
    
    await onClickSave(capabilitiesAndCredentials);  
  };

  return (
    <>
      <CompanyDetailsHeader
        mode={mode}
        companyType={"carrier"}
        isLoading={isLoadingCarrierDetails}
        companyDetails={carrierDetails}
        onClickDisconnect={onClickDisconnect}
        onClosePanel={onClosePanel}
      />

      <NetworkHealth
        isLoading={isLoadingCarrierDetails}
        trackingQuality={carrierDetails?.tracking_quality}
        loadVolume={carrierDetails?.load_volume?.value}
        locationDataIntegrations={carrierDetails?.location_data_integrations}
        assetAssignmentIntegrations={
          carrierDetails?.asset_assignment_integrations
        }
        managedCompanyType={"carrier"}
      />

      <RecommendedActions
        mode={mode}
        managedCompanyType={"carrier"}
        isLoading={isLoadingCarrierDetails}
        trackedPercentage={
          carrierDetails?.tracking_quality?.mine?.tracked_percentage
        }
        assignedPercentage={
          carrierDetails?.tracking_quality?.mine?.assigned_percentage
        }
        connectivityStatus={carrierDetails?.connectivity?.status}
        specialInstructions={carrierDetails?.special_onboarding_instructions}
        recommendationType={carrierDetails?.recommendation_type}
      />

      {mode !== "air" && (
      <Capabilities
        mode={mode}
        shipperId={shipperId}
        carrierId={carrierId}
        isConnectedToNetwork={isConnectedToNetwork}
        credentials={carrierDetails?.credentials}
        capabilities={carrierDetails?.capabilities?.filter((c: any) => c.id !== "data_collection") || []}
        onClickConnect={onClickCapabilitiesConnect}
        onClickDisconnect={onClickDisconnect}
      />
      )}

      {mode !== "ltl" && mode !=="ocean" && (
        <DataCollection
          mode={mode}
          shipperId={shipperId}
          carrierId={carrierId}
          credentials={carrierDetails?.data_collection_credentials}
          onClickSave={onClickDataCollectionSave}
        />
      )}

      {(isConnectedToNetwork || mode === "ftl") && (mode !== "air") && (
        <CarrierCodes
          mode={mode}
          shipperId={shipperId}
          carrierId={carrierId}
          carrierCodes={carrierDetails?.carrier_codes}
          isLoading={isLoadingCarrierDetails}
        />
      )}

      <CompanyInfo
        type={carrierDetails?.type}
        address={carrierDetails?.address}
        trackingModes={carrierDetails?.tracking_modes || []}
        fleetSize={carrierDetails?.fleet_size}
        isLoading={isLoadingCarrierDetails}
        identifications={carrierDetails?.identifications}
      />

      {(isAddedToNetwork && mode !== "air") && (
        <CompanyContacts
          contacts={carrierDetails?.contacts}
          companyId={shipperId}
          scac={carrierDetails?.carrier_codes?.custom?.join(",") || ""}
          showYourContacts = {false}
        companyName = {carrierDetails?.name}
        />
      )}

      {/*
      // TODO: NOTES COMPONENT
      */}
    </>
  );
};

const CarrierInvitationsElement = ({
  mode,
  shipperId,
  carrierId,
  networkId,
  onClosePanel,
  isPendingInvite,
  isExpiredInvite,
}: any) => {

  const dispatch = useAppDispatch();

  // Carrier invitations
  const carrierInvitations: any = useAppSelector(
    CarrierInvitationsState.selectors.carrierInvitationsByMode(mode)
  );
  const isLoadingCarrierInvitations: boolean = useAppSelector(
    CarrierInvitationsState.selectors.isLoadingCarrierInvitationsByMode(mode)
  );

  if (isLoadingCarrierInvitations) {
    return (
      <div className={styles.loader}>
        <Spinner isLoading size="medium" />
      </div>
    );
  };

  const handleCredentialChanges = async (capabilities: any[]) => {
    const connectHandler = CarriersNetworkState.actions.storeCredential
    const connectResponse = await dispatch(
      connectHandler({ shipperId, carrierId: networkId, capabilities, mode })
    );
    console.log(connectResponse)
    if ("error" in connectResponse) {
      return false;
    }

    return true;
  };

  const fetchCarrierData = () => {
    // Updates table component
    dispatch(CarriersNetworkState.actions.getCarriers({ shipperId, mode }));

    // Updates details pane component
    dispatch(
      CarrierInvitationsState.actions.getCarrierInvitations({
        shipperId,
        networkId,
        mode,
      })
    );
  };

  const onClickSave = async (capabilitiesAndCredentials: any) => {
    const capabilitiesUpdated = await handleCredentialChanges(
      capabilitiesAndCredentials
    );

    if (!capabilitiesUpdated) {
      // TODO: handle error
      return;
    }
    fetchCarrierData();
  };

  const onClickDataCollectionSave = async (
    capabilitiesAndCredentials: any
  ) => {
    
    await onClickSave(capabilitiesAndCredentials);  
  };

  return (
    <>
      <CompanyDetailsHeader
        mode={mode}
        isLoading={isLoadingCarrierInvitations}
        companyDetails={carrierInvitations}
        companyType={"carrier"}
        onClosePanel={onClosePanel}
      />

      <CarrierInvitations
        mode={mode}
        shipperId={shipperId}
        carrierId={carrierId}
        networkId={networkId}
        invitations={carrierInvitations}
        isLoading={isLoadingCarrierInvitations}
        onClosePanel={onClosePanel}
        isPendingInvite={isPendingInvite}
        isExpiredInvite={isExpiredInvite}
      />

      <DataCollection
          mode={mode}
          shipperId={shipperId}
          carrierId={networkId}
          credentials={carrierInvitations?.data_collection_credentials}
          onClickSave={onClickDataCollectionSave}
      />

      <CompanyInfo
        type={carrierInvitations?.type}
        address={carrierInvitations?.address}
        trackingModes={carrierInvitations?.tracking_modes || []}
        fleetSize={carrierInvitations?.fleet_size || "-"}
        isLoading={isLoadingCarrierInvitations}
        identifications={carrierInvitations?.identifications}
      />

      <CompanyContacts
        contacts={carrierInvitations?.contacts}
        companyId={shipperId}
        scac={carrierInvitations?.carrier_codes?.custom?.join(",") || ""}
        showYourContacts = {false}
      companyName = {carrierInvitations?.name}
      />
    </>
  );
};

export default CarrierDetails;
