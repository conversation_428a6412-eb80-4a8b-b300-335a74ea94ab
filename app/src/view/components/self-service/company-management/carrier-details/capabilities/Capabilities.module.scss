@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.containerSection {
  display: flex;
  flex-direction: column;
}

.capabilityWrapper {
  display: flex;
  flex-direction: column;
  margin-bottom: 12px;
}

.subtitle {
  font: $typography-standard-bold;
  color: $color-neutral-600;
  margin-top: 28px;
  margin-bottom: 24px;
}

.capabilityTooltip {
  display: inline-flex;
  align-items: center;
  margin-left: 10px;
}

.loadsCapabilityStatusLabel {
  margin-left: 56px;

  > label[id="loadsCapabilityDefined"] {
    color: $color-accent-mint-500;
  }

  > label[id="loadsCapabilityNotDefined"] {
    color: $color-primary-400;
  }
}

.capabilityToggle {
  display: flex;
  flex-direction: row;
}
