import React, { useState, useEffect, ChangeEvent } from "react";
import { useTranslation } from "react-i18next";

import { Switch } from "@fourkites/elemental-switch";
import { Accordion } from "@fourkites/elemental-accordion";

import { useAppSelector } from "state/hooks";
import { LoadsTrackingMode } from "state/BaseTypes";
import { CarriersNetworkState } from "state/modules/shipper/CarriersNetwork";

import CapabilitiesAuthentication from "./CapabilitiesAuthentication";
import CapabilitiesPrerequisites from "./CapabilitiesPrerequisites";

import styles from "./Capabilities.module.scss";
import { Tooltip } from "@fourkites/elemental-tooltip";
import { InfoIcon } from "@fourkites/elemental-atoms";

const Capabilities = ({
  mode,
  credentials,
  capabilities,
  onClickConnect,
  onClickDisconnect,
}: any) => {
  const { t } = useTranslation();

  const [localCapabilities, setLocalCapabilities] = useState<any[]>([]);

  const hasError: boolean = useAppSelector(
    CarriersNetworkState.selectors.hasErrorByMode(mode as LoadsTrackingMode)
  );

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  const copyCapabilitiesToLocalForm = () =>
    setLocalCapabilities(capabilities?.map((c: any) => c.enabled));

  /*
   * Copy capabilities to local form when API changes
   */
  useEffect(() => {
    copyCapabilitiesToLocalForm();
    // eslint-disable-next-line
  }, [capabilities]);

  /*
   * Resets capabilities if an error occurs
   */
  useEffect(() => {
    copyCapabilitiesToLocalForm();
    // eslint-disable-next-line
  }, [hasError]);

  /*
   * Enable / disable document depending on tracking status
   */
  useEffect(() => {
    if (!localCapabilities.length) {
      return;
    }

    // Gets index of tracking capability
    const trackingCapabilityIndex = findCapabilityIndex(
      "tracking",
      capabilities
    );
    const documentCapabilityIndex = findCapabilityIndex(
      "document",
      capabilities
    );

    const loadsCapabilityIndex = findCapabilityIndex(
      "loads",
      capabilities
    );

    const isTrackingEnabled = localCapabilities[trackingCapabilityIndex];
    // Disables document capability if tracking capability is
    if (!isTrackingEnabled && localCapabilities[documentCapabilityIndex]) {
      const _localCapabilities = [...localCapabilities];
      _localCapabilities[documentCapabilityIndex] = false;
      setLocalCapabilities(_localCapabilities);
    }

    if(!isTrackingEnabled && mode=='ocean' && localCapabilities[loadsCapabilityIndex])
    {
      const _localCapabilities = [...localCapabilities];
      _localCapabilities[loadsCapabilityIndex] = false;
      setLocalCapabilities(_localCapabilities);
    }
  }, [localCapabilities, capabilities]);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const onChangeSwitch = (enabled: boolean, cIndex: number) => {
    // Update local state
    const localCapabilitiesCopy = [...localCapabilities];
    localCapabilitiesCopy[cIndex] = enabled;
    setLocalCapabilities(localCapabilitiesCopy);
  };

  const onClickCancel = () => {
    copyCapabilitiesToLocalForm();
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const isTrackingEnabled =
    localCapabilities[capabilities?.findIndex((c: any) => c.id === "tracking")];

  // Only the below modes have some sort of prerequisites
  const shouldShowPrerequisites = ["ltl", "parcel", "ocean", "courier"].includes(mode);

  return (
    <Accordion title={"Services Provided"} defaultOpened>
      <div
        data-test-id="capabilities-container"
        className={styles.containerSection}
      >
        {capabilities?.map((c: any, cIndex: number) => {
          // NOTE 1: we are not allowing capability disalbe for FTL right now
          // NOTE 2: document can only be enabled if tracking is already enabled
          const isDocument = c.id === "document";
          const isLoadsCapability = c.id === "loads";
          const isCapabilityDefined: boolean = c.is_defined === true;
          const isCapabilityEnabled: boolean = c.enabled === true;
          const isLoadsCapabilityDefined =
            isLoadsCapability && isCapabilityDefined;
          const isLtlMode = mode === "ltl";
          const shouldDisableCapability = (isDocument || (isLoadsCapability && mode=='ocean')) && !isTrackingEnabled;
          const shouldShowTooltipForLoads = isLtlMode && isLoadsCapability;
          const shouldShowLoadsCapabilityStatus =
            isLoadsCapability && isCapabilityEnabled;
          const loadsCapabilityStatus = isLoadsCapabilityDefined ? (
            <label id="loadsCapabilityDefined">
              {t("Loads for this carrier will be created in few minutes.")}
            </label>
          ) : (
            <label id="loadsCapabilityNotDefined">
              {t(
                "FourKites will soon setup necessary integrations and create the loads soon."
              )}
            </label>
          );
          const loadsTooltipText = t(
            "Auto load creation enables FourKites to create loads directly " +
              "by pulling shipment reference numbers from your carrier " +
              "portal. Once you provide your carrier portal credentials, " +
              "FourKites will periodically pull your shipment details from " +
              "this carrier and create them on FourKites. " +
              "This allows you to skip manually creating these loads from your end"
          );
          const loadsTooltip = (
            <Tooltip text={loadsTooltipText} placement={"bottom"}>
              <span className={styles.capabilityTooltip}>
                <InfoIcon fill={"#868e96"} size="20px" />
              </span>
            </Tooltip>
          );

          return (
            <div
              data-test-id="capability-wrapper"
              key={cIndex}
              className={styles.capabilityWrapper}
            >
              <div className={styles.capabilityToggle}>
                <Switch
                  size="large"
                  defaultLabel={t(c.name)}
                  checked={localCapabilities[cIndex] || false}
                  onChange={(e: ChangeEvent<HTMLInputElement>) => {
                    onChangeSwitch(!localCapabilities[cIndex], cIndex);
                  }}
                  disabled={shouldDisableCapability}
                />

                {shouldShowTooltipForLoads && loadsTooltip}
              </div>
              <span className={styles.loadsCapabilityStatusLabel}>
                {shouldShowLoadsCapabilityStatus && loadsCapabilityStatus}
              </span>
            </div>
          );
        })}

        <CapabilitiesAuthentication
          mode={mode}
          credentials={credentials}
          capabilities={capabilities}
          localCapabilities={localCapabilities}
          onClickCancel={onClickCancel}
          onClickConnect={onClickConnect}
          onClickDisconnect={onClickDisconnect}
        />

        {shouldShowPrerequisites && (
          <CapabilitiesPrerequisites capabilities={capabilities} />
        )}
      </div>
    </Accordion>
  );
};

export function findCapabilityIndex(capabilityId: string, capabilities: any[]) {
  // Gets index of document capability
  let capabilityIndex = capabilities?.findIndex(
    (c: any) => c.id === capabilityId
  );
  return capabilityIndex >= 0 ? capabilityIndex : -1;
}

export default Capabilities;
