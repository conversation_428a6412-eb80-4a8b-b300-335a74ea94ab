@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.containerSection {
  display: flex;
  flex-direction: column;
}

.subtitle {
  font: $typography-standard-bold;
  color: $color-neutral-600;
  margin-top: 28px;
  margin-bottom: 24px;
}

.carrierAuthorizationHeaderContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.link {
  color: $color-primary-500;
  font-family: Lato;
  font-size: 16px;
  letter-spacing: 0;
  text-decoration: underline;
  cursor: pointer;

  &:visited {
    color: $color-primary-500;
  }
}

.carrierAuthorizationForm {
  display: flex;
  flex-direction: column;
  margin-right: 72px;
  margin-bottom: 8px;
}

.carrierAuthorizationRow {
  display: flex;
  width: 100%;
  flex-wrap: wrap;

  > div {
    margin-right: 16px;

    &:last-child {
      margin-right: 0;
    }
  }
}

.carrierAuthorizationInput {
  flex: 1;
  margin-bottom: 16px;

  > div {
    width: 100%;

    > div {
      display: flex;

      > input {
        width: 100%;
      }
    }
  }
}

.carrierAuthorizationButtonsContainer {
  display: flex;
  width: 100%;
  justify-content: flex-end;

  > button {
    display: flex;
    align-items: center;
    align-content: center;
    justify-content: center;
  }
}

.carrierAuthorizationButton {
  width: 108px;
  margin-left: 16px;
}

.loader {
  display: flex;
  align-items: left;
  align-content: center;
  justify-content: center;
  margin-top: 32px;
  margin-bottom: 32px;
}
