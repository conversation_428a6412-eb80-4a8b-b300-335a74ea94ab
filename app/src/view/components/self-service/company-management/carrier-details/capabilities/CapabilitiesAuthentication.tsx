import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

import { Button, EyeIcon, Edit1Icon } from "@fourkites/elemental-atoms";
import { Input } from "@fourkites/elemental-input";
import { Spinner } from "@fourkites/elemental-loading-indicator";

import { useAppSelector } from "state/hooks";
import { LoadsTrackingMode } from "state/BaseTypes";
import { CarriersNetworkState } from "state/modules/shipper/CarriersNetwork";

import { findCapabilityIndex } from "./Capabilities";

import styles from "./CapabilitiesAuthentication.module.scss";

const CapabilitiesAuthentication = ({
  mode,
  credentials,
  capabilities,
  localCapabilities,
  onClickCancel,
  onClickConnect,
  onSave,
  onClickDisconnect,
}: any) => {
  const { t } = useTranslation();



  const isEditingCarriers: boolean = useAppSelector(
    CarriersNetworkState.selectors.isEditingCarriersByMode(mode)
  );
  const isGettingCarriers: boolean = useAppSelector(
    CarriersNetworkState.selectors.isLoadingCarriersByMode(mode)
  );
  const hasError: boolean = useAppSelector(
    CarriersNetworkState.selectors.hasErrorByMode(mode as LoadsTrackingMode)
  );

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [isEditingCredentials, setIsEditingCredentials] =
    useState<boolean>(false);
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [apiKey, setApiKey] = useState("");
  const [accountNumber, setAccountNumber] = useState("");
  const [revealPassword, setRevealPassword] = useState<boolean>(false);

  const onChangeUsername = (e: any) => setUsername(e.target.value);
  const onChangePassword = (e: any) => setPassword(e.target.value);
  const onChangeApiKey = (e: any) => setApiKey(e.target.value);
  const onChangeAccountNumber = (e: any) => setAccountNumber(e.target.value);

  // Used to control which form inputs we will show
  let showUsername = false,
    showPassword = false,
    showApiKey = false,
    showAccountNumber = false,
    showFormButtons = false;

  const resetCredentials = () => {
    setUsername(credentials?.username || "");
    setPassword(credentials?.password || "");
    setApiKey(credentials?.api_key || "");
    setAccountNumber(credentials?.account_number || "");
  };

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  /*
   * Update forms with values we receive from API
   */
  useEffect(resetCredentials, [credentials]);

  /*
   * Resets capabilities if an error occurs
   */
  useEffect(resetCredentials, [hasError]);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const onConfirmAction = () => {
    // Update edit state
    setIsEditingCredentials(false);

    // Create form
    let newCapabilities: any = [];
    capabilities.forEach((c: any, index: number) => {
      newCapabilities.push({
        id: c.id,
        enabled: localCapabilities[index],
      });
    });
    let credentials = {
      username: showUsername && username ? username : null,
      password: showPassword && password ? password : null,
      api_key: showApiKey && apiKey ? apiKey : null,
      account_number: showAccountNumber && accountNumber ? accountNumber : null,
    };

    // Update API
    if (onClickConnect) {
      onClickConnect({ capabilities: newCapabilities, credentials });
    } else if (onSave) {
      onSave({ capabilities: newCapabilities, credentials });
    }
  };

  const onCancel = () => {
    setIsEditingCredentials(false);
    resetCredentials();
    onClickCancel();
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  if (!capabilities) {
    return null;
  }

  // Loops through capabilities to determine what credentials are required
  capabilities.forEach((c: any, index: number) => {
    const showFormInput = (inputValue: string, capabilityIndex: number) => {
      const showIputValues = ["required"];
      return (
        localCapabilities[capabilityIndex] &&
        showIputValues.includes(inputValue)
      );
    };

    // Verify if should show form inputs
    showUsername = showUsername || showFormInput(c.username, index);
    showPassword = showPassword || showFormInput(c.password, index);
    showApiKey = showApiKey || showFormInput(c.api_key, index);
    showAccountNumber =
      showAccountNumber || showFormInput(c.account_number, index);

    // Verify if should show form buttons
    showFormButtons = showFormButtons || c.enabled !== localCapabilities[index];
  });
  showFormButtons = showFormButtons || isEditingCredentials;

  // State derived variables for rendering
  const isLoading = isEditingCarriers || isGettingCarriers;

  const showCredentialsInputs = [
    showUsername,
    showPassword,
    showApiKey,
    showAccountNumber,
  ].some((x: boolean) => x);

  const isTrackingEnabled =
    localCapabilities[findCapabilityIndex("tracking", capabilities)];
  const isLoadsEnabled =
    localCapabilities[findCapabilityIndex("loads", capabilities)];

  const shouldShowConnectButton = isTrackingEnabled || isLoadsEnabled;

  const disableConnectButton =
    (showUsername && !username) ||
    (showPassword && !password) ||
    (showApiKey && !apiKey) ||
    (showAccountNumber && !accountNumber);

  const disableInputs = !isEditingCredentials && !showFormButtons;

  if (isLoading) {
    return (
      <div className={styles.containerSection}>
        <div className={styles.loader}>
          <Spinner isLoading size="medium" />
        </div>
      </div>
    );
  }

  if (!showCredentialsInputs && !showFormButtons) {
    return null;
  }

  return (
    <div className={styles.containerSection}>
      {showCredentialsInputs && (
        <span className={styles.carrierAuthorizationHeaderContainer}>
          <span className={styles.subtitle}>{t("Carrier Authentication")}</span>

          {/* 
          // 2022-10-10 - Commenting this as part of SELF-2125. Delete when
          // coming back to this if still commented, together with styles  
          <a className={styles.link} href="https://fourkites.com">
            {t("Why does FourKites need this information?")}
          </a>
          */}
        </span>
      )}

      <div className={styles.carrierAuthorizationForm}>
        <div className={styles.carrierAuthorizationRow}>
          {showUsername && (
            <div className={styles.carrierAuthorizationInput}>
              <Input
                label={`${t("Username")}`}
                value={username}
                onChange={onChangeUsername}
                disabled={disableInputs}
                required
              />
            </div>
          )}

          {showPassword && (
            <div className={styles.carrierAuthorizationInput}>
              <Input
                label={`${t("Password")}`}
                type={revealPassword ? "text" : "password"}
                value={password}
                onChange={onChangePassword}
                disabled={disableInputs}
                icon={<EyeIcon />}
                onIconClick={() => setRevealPassword(!revealPassword)}
                required
              />
            </div>
          )}
        </div>

        {showApiKey && (
          <div className={styles.carrierAuthorizationInput}>
            <Input
              label={`${t("API key")}`}
              value={apiKey}
              onChange={onChangeApiKey}
              disabled={disableInputs}
              required
            />
          </div>
        )}

        {showAccountNumber && (
          <div className={styles.carrierAuthorizationInput}>
            <Input
              label={`${t("Account number")}`}
              value={accountNumber}
              onChange={onChangeAccountNumber}
              disabled={disableInputs}
              required
            />
          </div>
        )}
      </div>

      <div className={styles.carrierAuthorizationButtonsContainer}>
        {showFormButtons && (
          <>
            <Button
              className={styles.carrierAuthorizationButton}
              size="medium"
              theme="secondary"
              onClick={onCancel}
            >
              {t("Cancel")}
            </Button>

            {onSave ? (
              <Button
                className={styles.carrierAuthorizationButton}
                size="medium"
                onClick={onConfirmAction}
                disabled={disableConnectButton}
              >
                {t("Save")}
               </Button>
              )  : shouldShowConnectButton ? (
                    <Button
                      className={styles.carrierAuthorizationButton}
                      size="medium"
                      onClick={onConfirmAction}
                      disabled={disableConnectButton}
                    >
                      {t("Connect")}
                    </Button>
                  ) : (
                    <Button
                      className={styles.carrierAuthorizationButton}
                      size="medium"
                      onClick={onClickDisconnect}
                    >
                      {t("Disconnect")}
                    </Button>
                )
              }
          </>
        )}

        {!showFormButtons && showCredentialsInputs && (
          <Button
            className={styles.carrierAuthorizationButton}
            theme="tertiary"
            size="medium"
            onClick={() => {
              setIsEditingCredentials(true);
            }}
          >
            <Edit1Icon fill="#0e65e5" iconClass={"button-icon-left"} />
            {t("Edit")}
          </Button>
        )}
      </div>
    </div>
  );
};

export default CapabilitiesAuthentication;