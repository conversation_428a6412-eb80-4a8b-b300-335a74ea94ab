@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.prerequisitesContainer {
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  background-color: $color-neutral-100;
  padding: 16px;
  margin-top: 24px;
}

.prerequisitesTitle {
  font: $typography-standard-bold;
  color: $color-neutral-600;
  margin-bottom: 8px;
}

.prerequisites {
  font: $typography-standard-semibold;
  margin-top: 16px;
}

.link {
  color: $color-primary-500;
  font-family: Lato;
  font-size: 16px;
  letter-spacing: 0;
  text-decoration: underline;
  cursor: pointer;

  &:visited {
    color: $color-primary-500;
  }
}
