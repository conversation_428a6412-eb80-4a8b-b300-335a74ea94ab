import React from "react";
import { useTranslation } from "react-i18next";

import styles from "./CapabilitiesPrerequisites.module.scss";

const CapabilitiesPrerequisites = ({ capabilities }: any) => {
  const { t } = useTranslation();

  /*
   * Returns a formatted prerequisites text with links rendered on <a> elements
   */
  const getFormattedPrerequisites = (prerequisitesString: string) => {
    if (!prerequisitesString) {
      return "Credentials are not required for this service.";
    }

    var expressionMatch =
      /(\b(https?|):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gi;
    var elementcontent = prerequisitesString?.replace(
      expressionMatch,
      "<a href='$1' target='_blank'>$1</a>"
    );
    var newExpressionMatch = /(^|[^\/])(www\.[\S]+(\b|$))/gim;
    var newContent = elementcontent?.replace(
      newExpressionMatch,
      '$1<a className={styles.link} target="_blank" href="http://$2">$2</a>'
    );
    var newLineExpressionMatch = /(?:\r\n|\r|\n)/g;
    var finalContent = newContent.replace(newLineExpressionMatch, "<br/><br/>");

    return finalContent ? finalContent : prerequisitesString;
  };

  return (
    <div className={styles.prerequisitesContainer}>
      <span className={styles.prerequisitesTitle}>{t("Prerequisite")}</span>

      {capabilities?.map((c: any, index: number) => (
        <div key={index} style={{ display: "flex", flexDirection: "column" }}>
          <span
            className={styles.prerequisites}
          >{`${c.name.toUpperCase()}`}</span>

          <div
            style={{ wordWrap: "break-word" }}
            dangerouslySetInnerHTML={{
              __html: getFormattedPrerequisites(c.prerequisite),
            }}
          />
        </div>
      ))}
    </div>
  );
};

export default CapabilitiesPrerequisites;
