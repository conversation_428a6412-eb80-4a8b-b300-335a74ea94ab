@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.container {
  display: flex;
  flex-direction: column;
}

.title {
  font: $typography-standard-bold;
  color: $color-neutral-600;
  margin-bottom: 8px;
}

.content {
  display: flex;
  flex-direction: column;
  flex: 1;

  & > div:first-child {
    flex: 5;
  }
}

.modaltext {
  display: flex;
  flex-direction: column;
  font-size: "30px";
  font-weight: 600;
  letter-spacing: 5;
  line-height: 44px;
}

.iconWrapper {
  margin-top: 4px; /* Adjust to align icon with first line of text */
}


.outerContainer {
  display: flex;
  flex-direction: column;
  width: 95%;
  font-size: 19px;
  font-weight: 400;
  letter-spacing: 0.5px;
  line-height: 40px;
  word-spacing: 3.9px;
}

.modalContainer {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
  margin-right: 8px ;
  width: 100%;
}

.yellowWarningBox {
  background-color: #fff9e6;
  border: 1px solid #ffbf0a;
  border-radius: 4px;
  padding: 12px;
  margin-top: 8px;
  font-weight: 500;
  width: 100%;
}