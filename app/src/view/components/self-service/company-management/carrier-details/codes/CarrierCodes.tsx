import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Modal } from "@fourkites/elemental-modal";

import { AlertCircleIcon, AlertTriangleIcon } from "@fourkites/elemental-atoms";
import { Spinner } from "@fourkites/elemental-loading-indicator";
import { Accordion } from "@fourkites/elemental-accordion";
import { Tooltip } from "@fourkites/elemental-tooltip";

import { useAppDispatch, useAppSelector } from "state/hooks";
import { CarriersNetworkDetailsState } from "state/modules/shipper/CarriersNetworkDetails";

import MultiInput from "view/components/base/multi-input/MultiInput";

import {
  MAX_CARRIER_CODES,
  MAX_CARRIER_CODE_LENGTH,
  MIN_CARRIER_CODES_TO_BE_PRESENT,
} from "view/components/self-service/company-management/CompanyManagementUtils";

import styles from "./CarrierCodes.module.scss";
import CarrierCodesProps from "./CarrierCodes.types";

const CarrierCodes = ({
  shipperId,
  carrierId,
  mode,
  carrierCodes,
  isLoading,
}: CarrierCodesProps) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const [modalState, setModalState] = useState({
    isOpen: false,
    codeToRemove: ""
  });

  const isEditingCarrierCodes: boolean = useAppSelector(
    CarriersNetworkDetailsState.selectors.isEditingCarrierCodes()
  );

  const onAddCarrierCode = (code: string) => {
    dispatch(
      CarriersNetworkDetailsState.actions.createCarrierCode({
        shipperId,
        carrierId,
        mode,
        code: code.toUpperCase(),
      })
    );
  };

  const handleRemoveCarrierCode = (code: string) => {
    setModalState({
      isOpen: true,
      codeToRemove: code
    });
  };

  const onConfirmRemove = () => {
    dispatch(
      CarriersNetworkDetailsState.actions.deleteCarrierCode({
        shipperId,
        carrierId,
        mode,
        code: modalState.codeToRemove.toUpperCase(),
      })
    );
    setModalState({ isOpen: false, codeToRemove: "" });
  };

  const closeModal = () => {
    setModalState({ isOpen: false, codeToRemove: "" });
  };

  const shouldShowAlert = false;
  // carrierCodes?.custom?.length === 0 && carrierCodes?.default.length === 0;

  const actionButton = shouldShowAlert
    ? {
        component: (
          <Tooltip
            placement="right"
            text={t("You need to provide at least 1 carrier code")}
            theme="dark"
          >
            <span>
              <AlertTriangleIcon size="24px" fill="#da1e28" />
            </span>
          </Tooltip>
        ),
        onClick: () => {},
      }
    : undefined;

  const infoMessage = t(
    "Custom carrier codes are codes that you use to identify " +
      "this particular carrier in your TMS and other apps"
  );

  const tooltipComponent = (
    <div>
      <span className={"elemental-multi-input-value-tooltip-content"}>
        <AlertCircleIcon size="14px" fill="#fff" />
        <b>{t(" Atleast 1 carrier code needs to be added")}</b>
      </span>
      <br />
      <span className={"elemental-multi-input-value-tooltip-content"}>
        {t("Please add another carrier code if you want to remove this one")}
      </span>
    </div>
  );

  const multiInputValidation = {
    maxNumberOfValues: MAX_CARRIER_CODES,
    maxValueLength: MAX_CARRIER_CODE_LENGTH,
    minNumberOfValues: MIN_CARRIER_CODES_TO_BE_PRESENT,
    minNumberOfValuesMessageComponent: tooltipComponent,
  };

  return (
    <>
      <Accordion title={t("Carrier Codes")} actionButton={actionButton}>
        <div className={styles.container}>
          {isLoading ? (
            <Spinner isLoading size="small" />
          ) : (
            <div className={styles.content}>
              <label className={styles.infoMessage}>{t(infoMessage)}</label>
              <br />
              <div>
                <MultiInput
                  placeholder="Enter carrier code"
                  values={carrierCodes?.custom || []}
                  defaultValues={carrierCodes?.default || ["DEFAULT"]}
                  onAddValue={onAddCarrierCode}
                  onRemoveValue={handleRemoveCarrierCode}
                  disabled={isEditingCarrierCodes}
                  validation={multiInputValidation}
                />
              </div>
              <div>
                {isEditingCarrierCodes && <Spinner isLoading size="small" />}
              </div>
            </div>
          )}
        </div>
      </Accordion>

      {/* Confirmation Modal */}
      <Modal
        size="small"
        title={t("Confirm SCAC Removal")}
        show={modalState.isOpen}
        closeButtonProps={{
          label: t("Cancel"),
          onClick: closeModal,
        }}
        saveButtonProps={{
          label: t("Delete SCAC"),
          onClick: onConfirmRemove,
          theme: "danger"
        }}
      >
        <div className={styles.outerContainer}>
          <div className={styles.modalContainer}>
            <span className={styles.iconWrapper}>
              <AlertTriangleIcon size="33px" fill="#ffbf0a" />
            </span>
            <div >
              {t("You are about to remove SCAC code")}{" "}
              <strong>{modalState.codeToRemove}</strong>{" "}
              {t("from this carrier in {{mode}} context", { mode: mode.toUpperCase() })}<br />
              {t("Removing this SCAC code may impact load creation and tracking")}<br />
              <b>{t("This action will be logged for audit purposes")}</b>
            </div>
          </div>
          
          <div className={styles.yellowWarningBox}>
            {t("Please ensure you have a valid reason for removing this SCAC Code as it may cause issues with load creation and tracking")}
          </div>
        </div>    
      </Modal>
    </>
  );
};

export default CarrierCodes;
