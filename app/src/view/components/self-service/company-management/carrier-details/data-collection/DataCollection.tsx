import React, { useState, useEffect, ChangeEvent } from "react";
import { useTranslation } from "react-i18next";

import { Switch } from "@fourkites/elemental-switch";
import { Accordion } from "@fourkites/elemental-accordion";

import { useAppSelector } from "state/hooks";
import { LoadsTrackingMode } from "state/BaseTypes";
import { CarriersNetworkState } from "state/modules/shipper/CarriersNetwork";

import CapabilitiesAuthentication from "../capabilities/CapabilitiesAuthentication";
import CapabilitiesPrerequisites from "../capabilities/CapabilitiesPrerequisites";

import styles from "../capabilities/Capabilities.module.scss";
import { Tooltip } from "@fourkites/elemental-tooltip";
import { InfoIcon } from "@fourkites/elemental-atoms";

const DataCollection = ({
  mode,
  credentials,
  onClickConnect,
  onClickSave,
}: any) => {
  const { t } = useTranslation();

  const [localCapabilities, setLocalCapabilities] = useState<any[]>([]);

  const hasError: boolean = useAppSelector(
    CarriersNetworkState.selectors.hasErrorByMode(mode as LoadsTrackingMode)
  );

  /*****************************************************************************
   * COMPUTED VALUES
   ****************************************************************************/

  const hasValidCredentials = !!(credentials && 
    (credentials.username || credentials.password || credentials.api_key || credentials.account_number));

  const dataCollectionCapability = [{
    "id": "data_collection",
    "name": "Data Collection", 
    "api_key": null,
    "password": "required",
    "username": "required", 
    "account_number": null,
    "enabled": hasValidCredentials
  }];

  const copyCapabilitiesToLocalForm = () =>
    setLocalCapabilities(dataCollectionCapability?.map((c: any) => c.enabled));

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  useEffect(() => {
    copyCapabilitiesToLocalForm();
  }, [hasValidCredentials, hasError]); // Include hasValidCredentials to ensure sync

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const onChangeSwitch = (enabled: boolean, cIndex: number) => {
    const localCapabilitiesCopy = [...localCapabilities];
    localCapabilitiesCopy[cIndex] = enabled;
    setLocalCapabilities(localCapabilitiesCopy);
  };

  const onClickCancel = () => {
    copyCapabilitiesToLocalForm();
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <Accordion title={"Data Collection"} defaultOpened>
      <div
        data-test-id="capabilities-container"
        className={styles.containerSection}
      >
        {dataCollectionCapability?.map((c: any, cIndex: number) => {
          const isCapabilityEnabled: boolean = c.enabled === true;

          return (
            <div
              data-test-id="capability-wrapper"
              key={c.id} // Use stable key instead of index
              className={styles.capabilityWrapper}
            >
              <div className={styles.capabilityToggle}>
                <Switch
                  size="large"
                  defaultLabel={t(c.name)}
                  checked={localCapabilities[cIndex] || false}
                  onChange={(e: ChangeEvent<HTMLInputElement>) => {
                    onChangeSwitch(!localCapabilities[cIndex], cIndex);
                  }}
                />
              </div>
              <span className={styles.loadsCapabilityStatusLabel}>
                {isCapabilityEnabled && (
                  <label id="dataCollectionEnabled">
                    {t("Data collection is enabled.")}
                  </label>
                )}
              </span>
            </div>
          );
        })}

      <CapabilitiesAuthentication
        mode={mode}
        credentials={credentials}
        capabilities={dataCollectionCapability}
        localCapabilities={localCapabilities}
        onClickCancel={onClickCancel}
        onSave={onClickSave}
      />
      </div>
    </Accordion>
  );
};

export function findCapabilityIndex(capabilityId: string, capabilities: any[]) {
  // Gets index of document capability
  let capabilityIndex = capabilities?.findIndex(
    (c: any) => c.id === capabilityId
  );
  return capabilityIndex >= 0 ? capabilityIndex : -1;
}

export default DataCollection;