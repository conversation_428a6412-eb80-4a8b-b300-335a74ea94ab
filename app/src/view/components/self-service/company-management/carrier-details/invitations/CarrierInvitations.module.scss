@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.container {
  display: flex;
  flex-direction: column;
  padding: 16px;
  padding-top: 0;

  > div {
    display: flex;
    flex-direction: column;
  }

  > div[id="onboarding-status"] {
    > label[id="title"] {
      display: flex;
      align-items: center;
      align-content: center;
      font-weight: 700;
      font-size: 14px;
      line-height: 17px;
    }

    > label[id="description"] {
      margin-top: 4px;
      font-weight: 400;
      font-size: 14px;
      line-height: 17px;
    }
  }
}

.loader {
  display: flex;
  align-items: center;
  align-content: center;
  justify-content: center;
  width: 100%;
}

.buttonsContainer {
  display: flex;
  flex-wrap: wrap;
  margin-top: 24px;

  > button {
    display: flex;
    align-items: center;
    margin-right: 16px;
    margin-bottom: 16px;
  }

  > button:last-child {
    margin-left: 0;
  }
}

.sortIcon {
  margin-right: 4px;
  transform: rotate(90deg);
}

.fourkitesIcon {
  margin-right: 4px;
}
