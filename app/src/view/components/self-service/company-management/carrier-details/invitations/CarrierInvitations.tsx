import { useTranslation } from "react-i18next";
import { FourkitesIcon, SortIcon } from "@fourkites/elemental-atoms";
import { Spinner } from "@fourkites/elemental-loading-indicator";
import { Accordion } from "@fourkites/elemental-accordion";

import LogsTable from "../logs/LogsTable";
import InvitationActions from "./InvitationActions";

import styles from "./CarrierInvitations.module.scss";
import CarrierInvitationsProps from "./CarrierInvitations.types";

const CarrierInvitations = ({
  mode,
  invitations,
  isLoading,
  shipperId,
  carrierId,
  networkId,
  onClosePanel,
  isPendingInvite,
  isExpiredInvite,
}: CarrierInvitationsProps) => {
  const isOnboarded = carrierId != null;
  const connectionAccepted = invitations?.connection_accepted;
  const invitationsLogs = invitations?.logs;

  return (
    <>
      <div className={styles.container}>
        <InvitationStatus
          isOnboarded={isOnboarded}
          connectionAccepted={connectionAccepted}
        />

        <InvitationActions
          mode={mode}
          shipperId={shipperId}
          networkId={networkId}
          invitationId={invitations?.id}
          carrierName={invitations?.name}
          isOnboarded={isOnboarded}
          onClosePanel={onClosePanel}
        />
      </div>

      {isLoading ? (
        <div className={styles.loader}>
          <Spinner isLoading size="medium" />
        </div>
      ) : (
        <>
          <InvitationData
            isOnboarded={isOnboarded}
            invitationsLogs={invitationsLogs}
            isPendingInvite={isPendingInvite}
            isExpiredInvite={isExpiredInvite}
          />
        </>
      )}
    </>
  );
};

const InvitationStatus = ({ isOnboarded }: any) => {
  const { t } = useTranslation();

  return (
    <div data-test-id="carrier-invitations-status-note" id="onboarding-status">
      <label id="title">
        {isOnboarded ? (
          <>
            <SortIcon iconClass={styles.sortIcon} />
            {t("Data Sharing Requested")}
          </>
        ) : (
          <>
            <FourkitesIcon iconClass={styles.fourkitesIcon} />
            {t("Pending FourKites® Account Creation")}
          </>
        )}
      </label>
      <label id="description">
        {isOnboarded
          ? t(
              "The carrier is yet to share their tracking & location data with you."
            )
          : t(
              "The carrier has been invited to join the FourKites® network, " +
                "but they are yet to sign up and create an account."
            )}
      </label>
    </div>
  );
};

const InvitationData = ({
  isOnboarded,
  invitationsLogs,
  isPendingInvite,
  isExpiredInvite,
}: any) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <>
      <Accordion title={t("Recommendations")} defaultOpened>
        <InvitedRecommendations
          isOnboarded={isOnboarded}
          isPendingInvite={isPendingInvite}
          isExpiredInvite={isExpiredInvite}
        />
      </Accordion>
      <Accordion
        title={isOnboarded ? t("Request Logs") : t("Invitation Logs")}
        defaultOpened
      >
        <div style={{ margin: -16 }}>
          <LogsTable logs={invitationsLogs || []} invitationLogs />
        </div>
      </Accordion>
    </>
  );
};

const InvitedRecommendations = ({
  isOnboarded,
  isPendingInvite,
  isExpiredInvite,
}: any) => {
  const { t } = useTranslation();

  let recommendations: string[] = [];

  if (isPendingInvite && !isOnboarded) {
    /**
     * These are recommendations for Invited status.
     */
    recommendations = [
      "This carrier does not yet have an account on FourKites.",
      "They were sent an invitation email, but have not yet taken action.",
      "Please follow up with your carrier, and if necessary, add a new contact person to receive a new invitation.",
    ];
  }

  if (isPendingInvite && isOnboarded) {
    /**
     * These are recommendations for Data Sharing Requested status.
     */
    recommendations = [
      "This carrier has an account on FourKites, but has not accepted your request to connect.",
      "Please reach out to this carrier. Ask them to log in to FourKites, navigate to FourKites Connect, and accept your data sharing request.",
    ];
  }

  if (isExpiredInvite) {
    /**
     * These are recommendations for Invitation Expired status.
     */
    recommendations = [
      "This carrier's invitation has expired after 21 days due to security precautions.",
      "Please resend an invitation to connect with this carrier, or remove the invitation.",
    ];
  }

  return (
    <ul>
      {recommendations.map((recommendation: string) => (
        <li>{t(recommendation)}</li>
      ))}
    </ul>
  );
};

export default CarrierInvitations;
