import React, { useState } from "react";
import {
  Button,
  HeadphoneIcon,
  MailIcon,
  UserPlusIcon,
  AlertOctagonIcon,
} from "@fourkites/elemental-atoms";
import { useTranslation } from "react-i18next";

import { Spinner } from "@fourkites/elemental-loading-indicator";

import { CarrierContact } from "state/BaseTypes";
import { useAppDispatch, useAppSelector } from "state/hooks";

import { CarriersNetworkState } from "state/modules/shipper/CarriersNetwork";
import { CarrierInvitationsState } from "state/modules/shipper/CarrierInvitations";
import { CarriersNetworkDetailsState } from "state/modules/shipper/CarriersNetworkDetails";
import UsersState from "state/modules/Users";

import { showToast } from "view/components/base/toast/Toast";

import AddContactModal from "../modals/AddContactModal";
import ContactFKConfirmationModal from "../modals/ContactFKConfirmationModal";
import RemoveInvitationModal from "../modals/RemoveInvitationModal";

import styles from "./CarrierInvitations.module.scss";

const InvitationActions = ({
  carrierName,
  mode,
  isOnboarded,
  shipperId,
  networkId,
  invitationId,
  onClosePanel,
}: any) => {
  const { t } = useTranslation();

  const dispatch = useAppDispatch();

  // User Data
  const currentUser = useAppSelector(UsersState.selectors.getCurrentUser);

  const isEditingCarrierInvitations = useAppSelector(
    CarrierInvitationsState.selectors.isEditingCarrierInvitationsByMode(mode)
  );
  const isEditingCarrierContacts = useAppSelector(
    CarriersNetworkDetailsState.selectors.isEditingCarrierContacts()
  );

  /*****************************************************************************
   * STATE
   ****************************************************************************/
  const [showAddContactModal, setShowAddContactModal] =
    useState<boolean>(false);
  const [showContactFKModal, setShowContactFKModal] = useState<boolean>(false);
  const [showRemoveInvitationModal, setShowRemoveInvitationModal] =
    useState<boolean>(false);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const fetchInvitationsOnSuccess = () => {
    dispatch(
      CarrierInvitationsState.actions.getCarrierInvitations({
        shipperId,
        networkId,
        mode,
      })
    );
  };

  // **************************** SEND INVITATIONS *****************************

  const onResendInvitation = async () => {
    const response = await dispatch(
      CarrierInvitationsState.actions.resendInvitation({
        shipperId,
        networkId,
        mode,
      })
    );

    if ("error" in response) {
      showToast(
        t("An error occurred"),
        t("There was an error inviting the contact."),
        "error"
      );
      return;
    }

    showToast(t("Contact invited"), t("Invitation resent successfully."), "ok");
    fetchInvitationsOnSuccess();
  };

  // *************************** REMOVE INVITATIONS ****************************

  const onRemoveInvitation = () => {
    setShowRemoveInvitationModal(true);
  };

  const onConfirmRemoveInvitation = async () => {
    const response = await dispatch(
      CarrierInvitationsState.actions.deleteInvitation({
        shipperId,
        invitationId,
      })
    );

    if ("error" in response) {
      showToast(
        t("An error occurred"),
        t("There was an error removing the invite."),
        "error"
      );
      return;
    }

    showToast(
      t("Invitation removed"),
      t("Invitation removed successfully."),
      "ok"
    );

    // Fetches list of carriers (invitations) again and close panel
    dispatch(
      CarriersNetworkState.actions.getCarriers({
        shipperId,
        mode,
      })
    );
    setShowRemoveInvitationModal(false);
    onClosePanel();
  };

  // ***************************** CONTACTS *****************************

  const onAddNewContact = () => {
    setShowAddContactModal(true);
  };

  const onConfirmAddNewContact = async (contact: CarrierContact) => {
    setShowAddContactModal(false);

    const response = await dispatch(
      CarriersNetworkDetailsState.actions.addCarrierContact({
        shipperId,
        networkId,
        mode,
        contact,
        user: {
          id: currentUser?.userId,
          first_name: currentUser?.firstName,
          last_name: currentUser?.lastName,
          email: currentUser?.emailAddress,
        },
      })
    );

    fetchInvitationsOnSuccess();
  };

  const onContactFourKites = () => {
    setShowContactFKModal(true);
  };

  const onConfirmContactFK = async () => {
    setShowContactFKModal(false);
    const response = await dispatch(
      CarriersNetworkDetailsState.actions.initiateHelpRequest({
        shipperId,
        networkId,
      })
    );

    if ("error" in response) {
      showToast(t("Error"), t("Couldn't contact FourKites support."), "error");
      return;
    }
    showToast(
      t("Contact FourKites"),
      t("FourKites team will get in touch with you shortly."),
      "ok"
    );
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const resendLabel = isOnboarded
    ? t("Resend Data Sharing Request")
    : t("Resend Invite");

  if (isEditingCarrierInvitations || isEditingCarrierContacts) {
    return (
      <div className={styles.loadingWrapper}>
        <Spinner isLoading size="medium" />
      </div>
    );
  }

  return (
    <div>
      <div className={styles.buttonsContainer}>
        <Button onClick={onResendInvitation}>
          <MailIcon fill="white" iconClass={"button-icon-left"} />
          {resendLabel}
        </Button>

        {!isOnboarded && (
          <Button theme="secondary" variant="outline" onClick={onAddNewContact}>
            <UserPlusIcon fill="#21252a" iconClass={"button-icon-left"} />
            {t("Add New Carrier Contact")}
          </Button>
        )}

        <Button
          theme="secondary"
          variant="outline"
          onClick={onContactFourKites}
        >
          <HeadphoneIcon iconClass={"button-icon-left"} />
          {t("Contact FourKites")}
        </Button>

        <Button theme="danger" variant="outline" onClick={onRemoveInvitation}>
          <AlertOctagonIcon iconClass={"button-icon-left"} fill="#da1e28" />
          {t("Remove Invite")}
        </Button>
      </div>

      <AddContactModal
        showModal={showAddContactModal}
        onClose={() => setShowAddContactModal(false)}
        onOk={onConfirmAddNewContact}
      />

      <ContactFKConfirmationModal
        showModal={showContactFKModal}
        onProceed={onConfirmContactFK}
        onClose={() => setShowContactFKModal(false)}
      />

      <RemoveInvitationModal
        carrierName={carrierName}
        showModal={showRemoveInvitationModal}
        onClose={() => setShowRemoveInvitationModal(false)}
        onConfirm={onConfirmRemoveInvitation}
      />
    </div>
  );
};

export default InvitationActions;
