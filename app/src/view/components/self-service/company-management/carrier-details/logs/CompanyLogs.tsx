import React from "react";
import { useTranslation } from "react-i18next";

import { Accordion } from "@fourkites/elemental-accordion";

import LogsTable from "./LogsTable";

import styles from "./CompanyLogs.module.scss";
import CompanyLogsProps from "./CompanyLogs.types";

const CompanyLogs = ({ logs }: CompanyLogsProps) => {
  const { t } = useTranslation();

  return (
    <Accordion title={t("Logs")}>
      <div>
        {logs && logs?.length > 0 ? (
          <LogsTable logs={logs} />
        ) : (
          <span>{t("No logs")}</span>
        )}
      </div>
    </Accordion>
  );
};

export default CompanyLogs;
