import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { InfoIcon } from "@fourkites/elemental-atoms";
import { Tooltip } from "@fourkites/elemental-tooltip";
import { Table } from "@fourkites/elemental-table";

import Contact from "view/components/base/contact/Contact";

import {
  getUserLocaleDate,
  sortByCreatedAtDesc,
} from "view/components/base/DateUtils";

import styles from "./LogsTable.module.scss";
import LogsTableProps from "./LogsTable.types";

const LogsTable = ({ logs, invitationLogs = false }: LogsTableProps) => {
  const { t } = useTranslation();

  const columns = useMemo(() => {
    return [
      ...(invitationLogs
        ? [
            {
              Header: "S. No",
              accessor: "serial_number",
            },
          ]
        : []),

      ...(!invitationLogs
        ? [
            {
              Header: "Event",
              accessor: "event_name",
            },
          ]
        : []),

      {
        Header: invitationLogs ? "Invite sent on" : "Date/Time",
        accessor: "created_at",
        Cell: (cellProps: any) => {
          return (
            <div
              data-test-id="company-logs-invites-cell"
              className={styles.dateWrapper}
            >
              {cellProps.cell.value}
            </div>
          );
        },
      },
      {
        Header: invitationLogs ? "Invite sent to" : "User",
        accessor: "sent_to",
        Cell: (cellProps: any) => {
          const contacts = cellProps?.cell?.value;
          if (contacts == null || contacts?.length === 0) {
            return "-";
          }
          return <SentToCell contacts={contacts} />;
        },
      },
      {
        Header: "",
        accessor: "sent_by",
        Cell: (cellProps: any) => <SentByCell c={cellProps?.cell?.value} />,
      },
    ];
  }, []);

  const data = useMemo(() => {
    return [...logs]
      ?.sort(sortByCreatedAtDesc)
      ?.map((log: any, index: number) => ({
        serial_number: index + 1,
        created_at: getUserLocaleDate(log?.created_at),
        sent_by: log?.sent_by,
        sent_to: log?.sent_to,
        event_name: log?.event?.name,
      }));
  }, [logs]);

  const paginationParams = {
    paginated: false,
  };

  return data ? (
    <div
      data-test-id="company-logs-table-wrapper"
      className={styles.tableContainer}
    >
      <Table
        data={data}
        columns={columns}
        striped
        pagination={paginationParams}
      />
    </div>
  ) : (
    <label> There are no logs for this carrier.</label>
  );
};

/*******************************************************************************
 * CELL RENDERERS
 ******************************************************************************/

const SentToCell = ({ contacts }: any) => {
  // Join contacts in list View
  const c = contacts[0];
  const otherContacts = contacts.length - 1;
  const otherContactsNames =
    contacts
      ?.slice(1)
      ?.map((v: any) => `${v?.first_name} ${v?.last_name || ""}`)
      ?.join(", ") || "";

  return (
    <div className={styles.contactsWrapper}>
      <Contact
        contact={{
          avatar: c?.avatar,
          firstName: c?.first_name,
          lastName: c?.last_name,
          position: c?.position,
          email: c?.email,
          secondaryEmails: c?.secondary_emails || [],
          phones: c?.phones || [],
          messaging: c?.messaging,
        }}
        contactInline
      />

      {otherContacts > 0 && (
        <span>
          <Tooltip placement="bottom" text={otherContactsNames} theme="dark">
            <span className={styles.otherContacts}>+{otherContacts}</span>
          </Tooltip>
        </span>
      )}
    </div>
  );
};

const SentByCell = ({ c }: any) => {
  const sentToLabel =
    c?.first_name != null && c?.first_name !== ""
      ? `Invite sent by ${c?.first_name} ${c?.last_name}`
      : `Invite sent by ${c?.email}`;

  return (
    <div
      data-test-id="company-logs-contacts"
      className={styles.contactsWrapper}
    >
      <Tooltip placement="bottom" text={sentToLabel} theme="dark">
        <span>
          <InfoIcon fill={"#21252a"} />
        </span>
      </Tooltip>
    </div>
  );
};

export default LogsTable;
