@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.inputsRow {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
}

.inputWrapper {
  flex: 1;
  margin-right: 16px;
  margin-top: 16px;

  &:last-child {
    margin-right: 0;
  }

  > div {
    width: 100%;

    > div {
      display: flex;

      > input {
        width: 100%;
      }
    }
  }
}

.levelLabel {
  display: flex;
  align-items: center;
  gap: 5px;
}

.levelSelect {
  > button {
    height: 34px !important;
  }
}

.headerTitle {
  font: $typography-large-semibold;
  display: flex;
  align-items: center;
  padding: 16px 0 0 24px; // top, right, bottom, left
  border-bottom: 1px solid $color-neutral-300;
}
