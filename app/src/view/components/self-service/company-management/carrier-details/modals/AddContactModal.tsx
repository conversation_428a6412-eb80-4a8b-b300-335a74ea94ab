import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import { Input } from "@fourkites/elemental-input";
import { Modal } from "@fourkites/elemental-modal";

import {
  isEmailInvalid,
  isFieldInvalid,
  getFirstName,
  getLastName,
} from "view/components/base/FormUtils";

import styles from "./AddContactModal.module.scss";

const AddContactModal = ({ showModal, onClose, onOk }: any) => {
  const { t } = useTranslation();

  const [contactForm, setContactForm] = useState<any>({
    name: "",
    email: "",
  });

  const onChangeForm = (value: string, formField: string) => {
    setContactForm({
      ...contactForm,
      [formField]: value,
    });
  };

  const onSave = () => {
    if (
      !isEmailInvalid(contactForm?.email) &&
      !isFieldInvalid(contactForm?.name)
    ) {
      const firstName = getFirstName(contactForm?.name);
      const lastName = getLastName(contactForm?.name, null);

      onOk({
        email: contactForm?.email,
        firstName,
        lastName,
      });
    }
  };

  return (
    <Modal
      size="small"
      title={t("Add Carrier Contact")}
      subtitle={t("Add a new contact for this carrier")}
      show={showModal}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: onClose,
      }}
      saveButtonProps={{
        label: t("Send Invite"),
        onClick: onSave,
      }}
    >
      <div className={styles.container}>
        <label>
          {t(
            "We will send a Signup Invite to your Carrier and get them Onboarded. " +
              "You can track this progress from the Carrier Management screen."
          )}
        </label>

        <div className={styles.inputsRow}>
          <div className={styles.inputWrapper}>
            <Input
              required
              label={`${t("Contact Name")}`}
              value={contactForm?.name}
              invalid={isFieldInvalid(contactForm?.name)}
              errorLabel={t("First name and last name are required")}
              onChange={(e: any) => onChangeForm(e.target.value, "name")}
            />
          </div>

          <div className={styles.inputWrapper}>
            <Input
              required
              label={`${t("Contact Email")}`}
              value={contactForm?.email}
              invalid={isEmailInvalid(contactForm?.email)}
              errorLabel={t("A valid email is required")}
              onChange={(e: any) => onChangeForm(e.target.value, "email")}
            />
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default AddContactModal;
