import React, { useEffect, useState } from "react";

import { useTranslation } from "react-i18next";

import { InfoIcon } from "@fourkites/elemental-atoms";
import { Input } from "@fourkites/elemental-input";
import { Modal } from "@fourkites/elemental-modal";
import { PhoneInput } from "@fourkites/elemental-phone-input";
import { Select } from "@fourkites/elemental-select";
import { Tooltip } from "@fourkites/elemental-tooltip";

import {
  isEmailInvalid,
  isFieldInvalid,
  getFirstName,
  getLastName,
  isNorthAmericaPhoneInvalid,
} from "view/components/base/FormUtils";
import { useAppDispatch, useAppSelector } from "state/hooks";
import { CommonCompanyContactsState } from "state/modules/CommonCompanyContacts";
import {
  AddContactPayload,
  AddYourContactModalProps,
} from "./AddYourContactModal.types";
import styles from "./AddContactModal.module.scss";

const AddYourContactModal = ({
  showModal,
  onClose,
  companyName,
  companyId,
  scac,
  fetchYourContacts,
  contactDetails,
  mode,
  contactId,
}: AddYourContactModalProps) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * REDUX
   ****************************************************************************/
  const dispatch = useAppDispatch();

  const isContactLoading: any = useAppSelector(
    CommonCompanyContactsState.selectors.isLoadingCompanyContacts()
  );

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [contactNumber, setContactNumber] = useState("");

  const [contactForm, setContactForm] = useState<any>({
    name: "",
    email: "",
  });
  const [country, setCountry] = useState("US");
  const [selectedOption, setSelectedOption] = useState("");

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  useEffect(() => {
    if (contactDetails) {
      setContactForm({
        name: contactDetails.name,
        email: contactDetails.email,
        designation: contactDetails.department,
      });
      setContactNumber(contactDetails.phone_number || "");
      setSelectedOption(
        contactDetails.level_1
          ? "Level 1"
          : contactDetails.level_2
          ? "Level 2"
          : ""
      );
    }
  }, [contactDetails]);

  /*****************************************************************************
   * HANDLERS
   ****************************************************************************/

  let menuOptionsTypes: any = {
    level_1: "Level 1",
    level_2: "Level 2",
  };
  const menuOptions: any = Object.values(menuOptionsTypes);

  const onChangeForm = (value: string, fieldName: string) => {
    setContactForm({
      ...contactForm,
      [fieldName]: value,
    });
  };

  // Save or Edit the contact details
  const onSave = async () => {
    if (
      !isEmailInvalid(contactForm?.email) &&
      !isFieldInvalid(contactForm?.name) &&
      !isFieldInvalid(contactForm?.designation) &&
      (contactNumber === "" || !isNorthAmericaPhoneInvalid(contactNumber))
    ) {
      const firstName = getFirstName(contactForm?.name);
      const lastName = getLastName(contactForm?.name, "");

      // Map the input fields to the correct keys in the payload
      const payload: AddContactPayload = {
        name: `${firstName} ${lastName}`.trim(),
        email: contactForm?.email,
        phone_number: contactNumber,
        department: contactForm?.designation,
        level_1: selectedOption === "Level 1",
        level_2: selectedOption === "Level 2",
      };

      // Dispatch the createCompanyContact action
      if (mode === "edit" && contactId) {
        await dispatch(
          CommonCompanyContactsState.actions.updateCompanyContact({
            contactId,
            companyId,
            payload,
          })
        ).finally(() => {
          fetchYourContacts();
        });
      } else {
        await dispatch(
          CommonCompanyContactsState.actions.createCompanyContact({
            companyId,
            scac,
            payload,
          })
        ).finally(() => {
          fetchYourContacts();
        });
      }
      onModalClose();
    }
  };

  const onModalClose = () => {
    setContactForm({
      name: "",
      email: "",
      designation: "",
    });
    setContactNumber("");
    setSelectedOption("");
    onClose();
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <Modal
      size="small"
      title={`${
        mode === "edit" ? t("Edit Contact") : t("Add Contact")
      } - ${companyName}`}
      show={showModal}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: onModalClose,
      }}
      customHeader={
        <div className={styles.modalHeader}>
          <label className={styles.headerTitle}>{`${
            mode === "edit" ? t("Edit Contact") : t("Add Contact")
          } - ${companyName}`}</label>
        </div>
      }
      saveButtonProps={{
        label: mode === "edit" ? t("Save & Update") : t("Save & Create"),
        onClick: onSave,
        disabled: isContactLoading,
      }}
    >
      <ContactForm
        t={t}
        contactForm={contactForm}
        isFieldInvalid={isFieldInvalid}
        isEmailInvalid={isEmailInvalid}
        isNorthAmericaPhoneInvalid={isNorthAmericaPhoneInvalid}
        onChangeForm={onChangeForm}
        country={country}
        setCountry={setCountry}
        contactNumber={contactNumber}
        setContactNumber={setContactNumber}
        selectedOption={selectedOption}
        setSelectedOption={setSelectedOption}
        menuOptions={menuOptions}
      />
    </Modal>
  );
};

const ContactForm: React.FC<{
  t: (key: string) => string;
  contactForm: {
    name: string;
    email: string;
    designation?: string;
  };
  isFieldInvalid: (field: string) => boolean;
  isEmailInvalid: (email: string) => boolean;
  isNorthAmericaPhoneInvalid: (phone: string) => boolean;
  onChangeForm: (value: string, field: string) => void;
  country: string;
  setCountry: (country: string) => void;
  contactNumber: string;
  setContactNumber: (number: string) => void;
  selectedOption: string;
  setSelectedOption: (option: string) => void;
  menuOptions: any[];
}> = ({
  t,
  contactForm,
  isFieldInvalid,
  isEmailInvalid,
  isNorthAmericaPhoneInvalid,
  onChangeForm,
  country,
  setCountry,
  contactNumber,
  setContactNumber,
  selectedOption,
  setSelectedOption,
  menuOptions,
}) => (
  <div className={styles.container}>
    <div className={styles.inputsRow}>
      <div className={styles.inputWrapper}>
        <Input
          required
          label={t("Contact Name")}
          value={contactForm.name}
          invalid={isFieldInvalid(contactForm.name)}
          errorLabel={t("First name and last name are required")}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            onChangeForm(e.target.value, "name")
          }
        />
      </div>
    </div>

    <div className={styles.inputsRow}>
      <div className={styles.inputWrapper}>
        <Input
          required
          label={t("Email ID")}
          value={contactForm.email}
          invalid={isEmailInvalid(contactForm.email)}
          errorLabel={t("A valid email is required")}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            onChangeForm(e.target.value, "email")
          }
        />
      </div>

      <div className={styles.inputWrapper}>
        <PhoneInput
          label="Phone (Optional)"
          className="phone-number"
          placeholder="Enter Phone Number"
          required={false}
          country={country}
          hideLabel={false}
          invalid={!!contactNumber && isNorthAmericaPhoneInvalid(contactNumber)}
          errorLabel={t("A valid phone number is required")}
          size="medium"
          onChangeCountry={setCountry}
          onChange={setContactNumber}
          value={contactNumber}
        />
      </div>
    </div>

    <div className={styles.inputsRow}>
      <div className={styles.inputWrapper}>
        <Input
          required
          label={t("Designation")}
          value={contactForm.designation || ""}
          invalid={isFieldInvalid(contactForm.designation ?? "")}
          errorLabel={t("Correct designation is required")}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            onChangeForm(e.target.value, "designation")
          }
        />
      </div>

      <div className={styles.inputWrapper}>
        <Select
          className={styles.levelSelect}
          label={
            <span className={styles.levelLabel}>
              {t("Level of Contact ")}
              <Tooltip
                key={"levelOfContact"}
                placement="top"
                className={styles.tootlTipMessage}
                contentComponent={
                  <div>
                    {t("Level 1 = Regular requests")} <br />{" "}
                    {t("Level 2 = Priority requests")}
                  </div>
                }
                theme="dark"
              >
                <span>
                  <InfoIcon fill={"#21252a"} size="16px" />
                </span>
              </Tooltip>
            </span>
          }
          onChange={(value: any) => {
            setSelectedOption(value[0]);
          }}
          options={menuOptions}
          select="single"
          value={[selectedOption]}
          placeHolder={[selectedOption][0]}
          size="medium"
        />
      </div>
    </div>
  </div>
);

export default AddYourContactModal;
