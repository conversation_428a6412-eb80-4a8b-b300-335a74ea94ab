export interface AddYourContactModalProps {
  showModal: boolean;
  onClose: () => void;
  companyName: string;
  companyId: string;
  scac: string;
  fetchYourContacts: () => void;
  mode: "add" | "edit";
  contactId?: number;
  contactDetails?: AddContactPayload;
}

export interface AddContactPayload {
  name: string;
  email: string;
  phone_number?: string;
  department: string;
  level_1: boolean;
  level_2: boolean;
}