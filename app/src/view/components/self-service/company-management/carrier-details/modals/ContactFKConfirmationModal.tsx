import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import { Input } from "@fourkites/elemental-input";
import { Modal } from "@fourkites/elemental-modal";
import styles from "./AddContactModal.module.scss";

const ContactFKConfirmationModal = ({ showModal, onProceed, onClose}: any) => {
  const { t } = useTranslation();

  return (
    <Modal
      size="small"
      title={t("Contact FourKites")}
      show={showModal}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: onClose,
      }}
      saveButtonProps={{
        label: t("Proceed"),
        onClick: onProceed,
      }}
    >
      <div className={styles.container}>
        <label>
          {t(
            "Click Proceed if you want to request for  support from the FourKites Carrier operations team."
          )}
        </label>

      </div>
    </Modal>
  );
};

export default ContactFKConfirmationModal;
