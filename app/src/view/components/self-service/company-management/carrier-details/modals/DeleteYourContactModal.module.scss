@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.contactWrapper {
  display: flex;
  margin-top: 16px;
  gap: 16px;
  background-color: $color-neutral-200;
  padding: 10px;
  align-items: center;
  border-radius: 5px;
}

.contactName {
    font: $typography-large-semibold
}

.departmentLabel {
    font: $typography-small-regular;
    color: $color-neutral-700;
}

.modalFooter {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    padding-top: 10px;
    padding-bottom: 10px;
    padding-right: 24px;
    border-top: 1px solid $color-neutral-300;
}

.modalHeader {
    display: flex;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid $color-neutral-300;
    gap: 12px;
}

.headerTitle {
    font: $typography-large-semibold;
}
