import { useTranslation } from "react-i18next";

import { Modal } from "@fourkites/elemental-modal";
import { Avatar } from "@fourkites/elemental-avatar";
import { Button } from "@fourkites/elemental-atoms";
import { AlertTriangleColoredIcon } from "@fourkites/elemental-atoms";

import { DeleteYourContactModalProps } from "./DeleteYourContactModal.types";
import { startWithUpperCase } from "view/components/base/StringUtils";
import {
  CommonCompanyContactsState,
  deleteCompanyContact,
} from "state/modules/CommonCompanyContacts";
import { useAppDispatch, useAppSelector } from "state/hooks";
import styles from "./DeleteYourContactModal.module.scss";

/*****************************************************************************
 * COMPONENT
 ****************************************************************************/
const DeleteYourContactModal = ({
  showModal,
  onClose,
  companyName,
  contactId,
  companyId,
  fetchYourContacts,
  contactDetails,
}: DeleteYourContactModalProps) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * REDUX
   ****************************************************************************/
  const dispatch = useAppDispatch();
  const isContactLoading: any = useAppSelector(
    CommonCompanyContactsState.selectors.isLoadingCompanyContacts()
  );

  /*****************************************************************************
   * HANDLERS
   ****************************************************************************/
  const onSave = () => {
    if (contactId) {
      dispatch(
        deleteCompanyContact({
          contactId: contactId,
          companyId: companyId,
        })
      ).finally(() => {
        fetchYourContacts();
      });
    }
    onClose();
  };

  const onModalClose = () => {
    onClose();
  };

  /*****************************************************************************
   * LOGIC
   ****************************************************************************/
  const [firstName, lastName] = contactDetails?.name.split(" ") || [];
  const parsedFirstName = startWithUpperCase(firstName);
  const parsedLastName = startWithUpperCase(lastName);

  const getDepartmentName = () => {
    let departmentName = "";
    if (contactDetails?.level_1) {
      departmentName = "Level 1 Contact";
    } else {
      departmentName = "Level 2 Contact";
    }
    if (contactDetails?.department) {
      departmentName += ` (${contactDetails.department})`;
    }
    return departmentName;
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/
  return (
    <Modal
      size="small"
      title={`${t("Confirm Delete Contact")} - ${companyName}`}
      show={showModal}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: onModalClose,
      }}
      customHeader={
        <div className={styles.modalHeader}>
          <AlertTriangleColoredIcon size={"1.8em"} />
          <label className={styles.headerTitle}>{`${t(
            "Confirm Delete Contact"
          )} - ${companyName}`}</label>
        </div>
      }
      customFooter={
        <div className={styles.modalFooter}>
          <Button onClick={onModalClose} theme="secondary">
            {t("Cancel")}
          </Button>
          <Button onClick={onSave} disabled={isContactLoading} theme="danger">
            {t("Confirm Delete")}
          </Button>
        </div>
      }
    >
      <div className={styles.container}>
        {t(
          "Are you sure you want to delete this contact? This action is irreversible!"
        )}

        <div className={styles.contactWrapper}>
          <Avatar
            size="xl"
            imageSrc={""}
            name={{
              firstName: parsedFirstName,
              lastName: parsedLastName,
            }}
          />
          <div>
            <label className={styles.contactName}>{`${parsedFirstName || ""} ${
              parsedLastName || ""
            }`}</label>
            <div>
              {contactDetails?.email} | {contactDetails?.phone_number}
            </div>
            <div className={styles.departmentLabel}>{getDepartmentName()}</div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteYourContactModal;
