import React from "react";
import { useTranslation } from "react-i18next";

import { Modal } from "@fourkites/elemental-modal";

const DisconnectCarrierModal: React.FC<{
  showModal: boolean;
  onClose: Function;
  onDisconnect: Function;
}> = ({ showModal, onClose, onDisconnect }: any) => {
  const { t } = useTranslation();

  const subtitle = "Are you sure you want to disconnect?";

  const message = t(
    "Any network relation between you and the carrier will be revoked, " +
      "including carrier codes (SCACs)."
  );

  return (
    <Modal
      size="small"
      title={t("Disconnect Carrier")}
      subtitle={subtitle}
      show={showModal}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: onClose,
      }}
      saveButtonProps={{
        label: t("Disconnect"),
        onClick: onDisconnect,
      }}
    >
      {message}
    </Modal>
  );
};

export default DisconnectCarrierModal;
