import React from "react";
import { useTranslation } from "react-i18next";

import { Modal } from "@fourkites/elemental-modal";

const NetworkErrorModal = ({ showModal, onClose, onOk }: any) => {
  const { t } = useTranslation();

  return (
    <Modal
      size="small"
      title={t("Error")}
      subtitle={t("Error when connecting the carrier")}
      show={showModal}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: onClose,
      }}
      saveButtonProps={{
        label: t("Confirm"),
        onClick: onOk,
      }}
    >
      {t(
        "Unable to connect due to incorrect credentials, please enter the correct credentials."
      )}
    </Modal>
  );
};

export default NetworkErrorModal;
