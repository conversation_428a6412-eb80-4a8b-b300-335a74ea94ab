import { useTranslation } from "react-i18next";

import deleteModalIcon from "assets/img/deleteModalIcon.png";

import { Modal } from "@fourkites/elemental-modal";

import styles from "./RemoveInvitationModal.module.scss";

const RemoveInvitationModal = ({
  showModal,
  carrierName,
  onClose,
  onConfirm,
}: any) => {
  const { t } = useTranslation();

  return (
    <Modal
      size="small"
      title={t("Are you sure you want to remove this invitation?")}
      show={showModal}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: onClose,
      }}
      saveButtonProps={{
        label: t("Confirm Remove"),
        onClick: onConfirm,
      }}
    >
      <div
        className={styles.container}
        data-testid="delete-invitation-modal-content"
      >
        <img src={deleteModalIcon} alt="Delete" />
        <label>
          {carrierName +
            t(" will not be able to join your network until invited again.")}
        </label>
      </div>
    </Modal>
  );
};

export default RemoveInvitationModal;
