import React from "react";
import { useTranslation } from "react-i18next";

import { InfoIcon } from "@fourkites/elemental-atoms";
import { Spinner } from "@fourkites/elemental-loading-indicator";
import { Accordion } from "@fourkites/elemental-accordion";
import { Tooltip } from "@fourkites/elemental-tooltip";

import IntegrationStatus from "view/components/base/integration-indicators/IntegrationStatus";

import styles from "./NetworkHealth.module.scss";
import NetworkHealthProps from "./NetworkHealth.types";

const NetworkHealth = ({
  isLoading,
  trackingQuality,
  loadVolume,
  locationDataIntegrations,
  assetAssignmentIntegrations,
  managedCompanyType,
}: NetworkHealthProps) => {
  const { t } = useTranslation();

  // Order of precedence for integrations: API >> File >> GPS >> Mobile >> NULL
  let sortedIntegrations = locationDataIntegrations
    ? [...locationDataIntegrations]
    : [];
  sortedIntegrations?.sort((a: any, b: any) => {
    const integrationsOrder = {
      api: 1,
      file: 2,
      eld_gps: 3,
      mobile: 4,
    };
    //@ts-ignore
    const typeA = integrationsOrder[a?.type];
    //@ts-ignore
    const typeB = integrationsOrder[b?.type];
    return typeA < typeB ? -1 : typeA > typeB ? 1 : 0;
  });

  const myTrackingQuality = trackingQuality?.mine?.tracked_percentage;
  const overallTrackingQuality = trackingQuality?.overall?.tracked_percentage;
  const myAssignedQuality = trackingQuality?.mine?.assigned_percentage;
  const isShipper = managedCompanyType === "shipper";

  return (
    <Accordion title={t("Network Health")} defaultOpened>
      <div className={styles.container}>
        {isLoading && <Spinner isLoading size="small" />}

        {!isLoading && (
          <>
            <div className={styles.infoRow}>
              <NetworkHealthElement
                title={
                  isShipper ? t("Your Tracking Methods") : t("Tracking Method")
                }
                tooltipText={t(
                  "The location sources that this carrier has used to track loads on FourKites"
                )}
                data={
                  locationDataIntegrations?.length > 0
                    ? sortedIntegrations?.map(
                        (integration: any, index: number) => {
                          return (
                            <IntegrationStatus
                              key={index}
                              status={integration?.status}
                              type={integration?.type}
                            />
                          );
                        }
                      )
                    : "--"
                }
              />

              <NetworkHealthElement
                title={isShipper ? t("30 Day Load Volume") : t("Load Volume")}
                tooltipText={t(
                  "The number of your loads for this carrier over the past 30 days"
                )}
                data={loadVolume || "--"}
              />
            </div>

            <div className={styles.infoRow}>
              <NetworkHealthElement
                title={
                  isShipper
                    ? t("Your Assignment Methods")
                    : t("Assignment Method")
                }
                tooltipText={t(
                  "The methods carrier has used to assign trucks, trailers, or drivers to loads"
                )}
                data={
                  assetAssignmentIntegrations?.length > 0
                    ? assetAssignmentIntegrations?.map(
                        (integration: any, index: number) => {
                          return (
                            <IntegrationStatus
                              key={index}
                              status={integration.status}
                              type={integration.type}
                            />
                          );
                        }
                      )
                    : "--"
                }
              />
              <NetworkHealthElement
                title={isShipper ? t("30 Day Assignment %") : t("Assignment %")}
                tooltipText={t(
                  "The % of your loads with a truck, trailer, or driver assignment in the past 30 days"
                )}
                data={myAssignedQuality ? `${myAssignedQuality}%` : "--"}
              />
            </div>

            <div className={styles.infoRow}>
              <NetworkHealthElement
                title={
                  isShipper
                    ? t("Tracking Quality For This Shipper")
                    : t("Tracking Quality (For You)")
                }
                tooltipText={t(
                  "The % of your loads this carrier has tracked over the past 30 days"
                )}
                data={myTrackingQuality ? `${myTrackingQuality}%` : "--"}
              />
              <NetworkHealthElement
                title={t("Tracking Quality (All Shippers)")}
                tooltipText={t(
                  "The % of all loads this carrier has tracked over the past 30 days"
                )}
                data={
                  overallTrackingQuality ? `${overallTrackingQuality}%` : "--"
                }
              />
            </div>
          </>
        )}
      </div>
    </Accordion>
  );
};

const NetworkHealthElement: React.FC<{
  title: string;
  data: any;
  tooltipText: string;
}> = ({ title, data, tooltipText }) => {
  return (
    <div className={styles.infoWrapper}>
      <span id="title">
        <label>{title}</label>
        <Tooltip text={tooltipText} placement={"bottom"}>
          <span>
            <InfoIcon fill={"#868e96"} size="20px" />
          </span>
        </Tooltip>
      </span>
      <label id="description">{data}</label>
    </div>
  );
};

export default NetworkHealth;
