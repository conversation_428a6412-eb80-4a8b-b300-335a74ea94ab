@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.headerContainer {
  display: flex;
  margin: 16px;
  margin-bottom: 16px;
}

.logo {
  display: flex;
  padding: 4px;
  margin-right: 16px;
  height: 64px;
  background-color: white;
  border: 1px solid $color-neutral-300;
  border-radius: 4px;

  > img {
    height: 64px;
    width: 96px;
    object-fit: contain;
  }
}

.title {
  display: flex;
  align-content: center;
  align-items: center;
  margin: 0;
  font-weight: bold;
  color: $color-neutral-700;
}

.headerContent {
  display: flex;
  flex-direction: column;
  width: 100%;

  > div[id="company-name"] {
    display: flex;
    width: 100%;
    align-content: space-evenly;
    justify-content: space-between;

    > h5 {
      font-style: normal;
      font-weight: 700;
      font-size: 20px;
      line-height: 30px;
    }
  }

  > div[id="company-details"] {
    display: flex;
    flex-direction: column;
    font-size: 14px;
    letter-spacing: 0;
    line-height: 24px;

    > label[id="type-and-address"] {
      color: $color-neutral-700;
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
    }

    > label[id="event-date"] {
      color: $color-neutral-600;
      margin-top: 8px;
      font-weight: 400;
      font-size: 14px;
      line-height: 17px;
    }
  }
}

.headerRight {
  display: flex;
  align-content: center;
  align-items: baseline;
}

.menuButton {
  padding: 0;
  background-color: transparent;
  border: none;
  cursor: pointer;
  border-radius: 4px;
  padding-top: 2px;
  padding-left: 4px;
  padding-right: 4px;

  &:hover {
    background-color: $color-neutral-200;
  }
}

.menuIcon {
  height: 24px;
  width: 24px;
}

.statusContainer {
  display: flex;
  margin-right: 16px;
}

.menuContainer {
  cursor: pointer;
  padding: 8px;
}
