import React from "react";
import { useTranslation } from "react-i18next";

import { MenuKebabFillIcon, XIcon } from "@fourkites/elemental-atoms";

import placeholderLogo from "assets/img/placeholderLogo.png";

import CodesIndicator from "view/components/base/company-identifications/CodesIndicator";
import IdentificationsIndicator from "view/components/base/company-identifications/IdentificationsIndicator";
import FlyoutMenu from "view/components/base/flyout-menu/FlyoutMenu";
import HighlightsIndicator from "view/components/base/highlights-indicators/HighlightsIndicator";

import {
  timePassedSinceDate,
  getUserLocaleDate,
} from "view/components/base/DateUtils";

import { getCompanyAddress } from "view/components/self-service/company-management/CompanyManagementUtils";

import ConnectivityProgress from "./ConnectivityProgress";

import styles from "./CompanyDetailsHeader.module.scss";
import CompanyDetailsHeaderProps from "./CompanyDetailsHeader.types";

const CompanyDetailsHeader = ({
  mode,
  companyDetails,
  companyType,
  onClickDisconnect,
  onClosePanel,
}: CompanyDetailsHeaderProps) => {
  const { t } = useTranslation();

  const networkStatus: string = companyDetails?.status;
  const isConnectedToNetwork = ["connected", "in_progress"].includes(
    networkStatus
  );
  const isPending = networkStatus === "invited";
  const isExpired = networkStatus === "expired";
  const isCarrier = companyType === "carrier";

  // Carrier Elements
  const codes = companyDetails?.carrier_codes;
  const companyCodes =
    codes != null ? [...codes?.default, ...codes?.custom] : [];
  const showCodes = companyCodes?.length > 0;
  const carrierUsdotAndCodes = showCodes ? (
    <CodesIndicator codes={codes} />
  ) : (
    <IdentificationsIndicator
      type={"usdot"}
      label={"DOT"}
      identifications={companyDetails?.identifications}
    />
  );

  // Connectivity dates
  const toFormattedDate = (date: string) =>
    date ? `${getUserLocaleDate(date)}` : "--";
  // TODO: BE is not returning disconnected_at yet, so we are ommiting it
  const eventDateLabel = isConnectedToNetwork
    ? t("Connected on: ")
    : isPending || isExpired
    ? t("Invited on: ")
    : ""; //t("Disconnected on: ");
  const eventDate = isConnectedToNetwork
    ? toFormattedDate(companyDetails?.connectivity?.connected_at)
    : isPending || isExpired
    ? toFormattedDate(companyDetails?.connectivity?.last_request_at)
    : ""; //toFormattedDate(companyDetails?.connectivity?.disconnected_at);

  return (
    <>
      <div
        data-test-id="company-details-header-container"
        className={styles.headerContainer}
      >
        <div className={styles.logo}>
          <img
            data-test-id="company-details-header-image"
            src={companyDetails?.logo ? companyDetails?.logo : placeholderLogo}
            alt="Company logo"
          />
        </div>

        <div
          data-test-id="company-details-header-content"
          className={styles.headerContent}
        >
          <div id={"company-name"}>
            <h5
              data-test-id="company-details-header-name"
              className={styles.title}
            >
              {companyDetails?.name}
              {isCarrier && (
                <HighlightsIndicator highlights={companyDetails.highlights} />
              )}
            </h5>

            <div className={styles.headerRight}>
              {isConnectedToNetwork && (
                <FlyoutMenu
                  anchor={
                    <button className={styles.menuButton}>
                      <MenuKebabFillIcon iconClass={styles.menuIcon} />
                    </button>
                  }
                >
                  <span
                    className={styles.menuContainer}
                    onClick={onClickDisconnect}
                  >
                    {t("Disconnect")}
                  </span>
                </FlyoutMenu>
              )}
              <button className={styles.menuButton} onClick={onClosePanel}>
                <XIcon iconClass={styles.menuIcon} />
              </button>
            </div>
          </div>

          <div
            id={"company-details"}
            data-test-id="company-details-header-identifications"
          >
            {isCarrier ? (
              <>
                {carrierUsdotAndCodes}
                <label id="event-date">
                  {eventDateLabel}
                  {eventDate}
                </label>
              </>
            ) : (
              <>
                <label id="type-and-address">
                  {companyDetails?.type === "shipper"
                    ? t("Shipper")
                    : t("Broker")}
                  {" • "}
                  {getCompanyAddress(companyDetails)}
                </label>
                <label id="event-date">
                  {eventDateLabel}
                  {eventDate}
                </label>
              </>
            )}
          </div>
        </div>
      </div>

      <ConnectivityProgress
        networkStatus={networkStatus}
        companyType={companyType}
        connectivityStatus={companyDetails?.connectivity?.status}
        loadVolume={companyDetails?.load_volume?.value}
        trackingPercentage={
          companyDetails?.tracking_quality?.mine?.tracked_percentage
        }
        assignmentPercentage={
          companyDetails?.tracking_quality?.mine?.assigned_percentage
        }
      />
    </>
  );
};

export default CompanyDetailsHeader;
