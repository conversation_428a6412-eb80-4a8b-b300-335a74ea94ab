@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.container {
  display: flex;
  flex-direction: column;
  padding-bottom: 16px;
  padding-right: 16px;
  padding-left: 16px;
}

.progress {
  display: flex;
  align-items: center;
  align-content: center;

  .step {
    display: flex;
    align-items: center;
    align-content: center;

    > label {
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      margin-left: 6px;
    }
  }

  .divider {
    display: flex;
    width: 20px;
    height: 0px;
    border: 1px solid $color-neutral-400;
    margin-left: 8px;
    margin-right: 8px;
  }
}

.description {
  display: flex;
  align-items: center;
  align-content: center;
  margin-top: 8px;

  > label {
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    margin-left: 6px;
  }
}
