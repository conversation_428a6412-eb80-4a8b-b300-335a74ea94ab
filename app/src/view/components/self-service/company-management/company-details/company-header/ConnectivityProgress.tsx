import React from "react";
import { useTranslation } from "react-i18next";

import {
  XCircleInvertedIcon,
  SlashErrorInvertedColoredIcon,
  XCircleInvertedColoredIcon,
  WarningTriangleColoredIcon,
  InfoInvertedColoredIcon,
  SuccessInvertedColoredIcon,
  CheckIcon,
} from "@fourkites/elemental-atoms";

import styles from "./ConnectivityProgress.module.scss";

const ConnectivityProgress = ({
  networkStatus,
  companyType,
  connectivityStatus,
  loadVolume,
  trackingPercentage,
  assignmentPercentage,
}: any) => {
  const { t } = useTranslation();

  if (networkStatus === "invited") {
    return null;
  }

  const isCarrier = companyType === "carrier";
  const adjustedConnectivityStatus =
    networkStatus === "disconnected" || networkStatus === "invited"
      ? null
      : connectivityStatus;

  const descriptionContent = getConnectivityStatusDescriptionAndIcon(
    t,
    adjustedConnectivityStatus,
    companyType,
    networkStatus,
    loadVolume,
    trackingPercentage,
    assignmentPercentage
  );
  const firstStepProgress = getFirstStepIconAndDividerColor(
    adjustedConnectivityStatus
  );
  const secondStepProgress = getSecondStepIconAndDividerColor(
    adjustedConnectivityStatus
  );
  const thirdStepProgress = getThirdIconAndDividerColor(
    t,
    adjustedConnectivityStatus,
    networkStatus,
    loadVolume,
    trackingPercentage,
    assignmentPercentage
  );

  return (
    <div className={styles.container}>
      {isCarrier && (
        <div className={styles.progress}>
          <span className={styles.step}>
            {firstStepProgress?.icon}
            <label>{t("Tracking Setup")}</label>
          </span>
          <div
            className={styles.divider}
            style={{ borderColor: secondStepProgress?.color }}
          />
          <span className={styles.step}>
            {secondStepProgress?.icon}
            <label>
              {adjustedConnectivityStatus !== "special_instructions"
                ? t("Assigning Loads")
                : t("Special Instructions")}
            </label>
          </span>
          <div
            className={styles.divider}
            style={{ borderColor: thirdStepProgress?.color }}
          />
          <span className={styles.step}>
            {thirdStepProgress?.icon}
            <label>{thirdStepProgress?.text}</label>
          </span>
        </div>
      )}
      <div className={styles.description}>
        {descriptionContent?.icon}
        <label>{descriptionContent?.description}</label>
      </div>
    </div>
  );
};

const getFirstStepIconAndDividerColor = (connectivityStatus: string) => {
  const iconsAndColors = {
    all_set: NO_DATA_STEP,
    no_location_data_integration: {
      icon: <SlashErrorInvertedColoredIcon />,
      color: "#cfd4da",
    },
    location_data_integration_error: {
      icon: <XCircleInvertedColoredIcon />,
      color: "#cfd4da",
    },
    location_data_integration_pending: {
      icon: <WarningTriangleColoredIcon />,
      color: "#cfd4da",
    },
    special_instructions: SUCCESS_STEP,
    awaiting_carrier_codes: SUCCESS_STEP,
    awaiting_customer_codes: SUCCESS_STEP,
    awaiting_loads_additions: SUCCESS_STEP,
    ok: SUCCESS_STEP,
  };

  return (
    //@ts-ignore
    iconsAndColors[connectivityStatus] || NO_DATA_STEP
  );
};

const getSecondStepIconAndDividerColor = (connectivityStatus: string) => {
  const iconsAndColors = {
    all_set: NO_DATA_STEP,
    no_location_data_integration: NO_DATA_STEP,
    location_data_integration_error: NO_DATA_STEP,
    location_data_integration_pending: NO_DATA_STEP,
    special_instructions: {
      icon: <WarningTriangleColoredIcon />,
      color: "#cfd4da",
    },
    awaiting_carrier_codes: SUCCESS_STEP,
    awaiting_customer_codes: SUCCESS_STEP,
    awaiting_loads_additions: SUCCESS_STEP,
    ok: SUCCESS_STEP,
  };

  return (
    //@ts-ignore
    iconsAndColors[connectivityStatus] || NO_DATA_STEP
  );
};

const getLoadAndTrackingDetails = (
  loadVolume: number,
  trackingPercentage: number,
  assignmentPercentage: number
) => {
  const hasNoLoads =
    loadVolume === 0 && assignmentPercentage === 0 && trackingPercentage === 0;
  const hasNoTrackingDetails =
    loadVolume > 0 && assignmentPercentage > 0 && trackingPercentage === 0;
  const hasNoTrackingAndAssignmentDetails =
    loadVolume > 0 && assignmentPercentage === 0 && trackingPercentage === 0;
  const hasTrackingDetails =
    loadVolume > 0 && assignmentPercentage > 0 && trackingPercentage > 0;

  return {
    hasNoLoads: hasNoLoads,
    hasNoTrackingDetails: hasNoTrackingDetails,
    hasNoTrackingAndAssignmentDetails: hasNoTrackingAndAssignmentDetails,
    hasTrackingDetails: hasTrackingDetails,
  };
};

const getThirdIconAndDividerColorBasedOnParams = (
  t: Function,
  networkStatus: string,
  loadVolume: number,
  trackingPercentage: number,
  assignmentPercentage: number
) => {
  const {
    hasNoLoads,
    hasNoTrackingDetails,
    hasNoTrackingAndAssignmentDetails,
    hasTrackingDetails,
  } = getLoadAndTrackingDetails(
    loadVolume,
    trackingPercentage,
    assignmentPercentage
  );

  if (networkStatus !== "connected") {
    return SUCCESS_STEP;
  }

  if (hasNoLoads || hasNoTrackingDetails || hasNoTrackingAndAssignmentDetails) {
    return {
      icon: <WarningTriangleColoredIcon />,
      color: "#cfd4da",
      text: t("Ready to Track"),
    };
  }

  if (hasTrackingDetails) {
    return SUCCESS_STEP;
  }
};

const getThirdIconAndDividerColor = (
  t: Function,
  connectivityStatus: string,
  networkStatus: string,
  loadVolume: number,
  trackingPercentage: number,
  assignmentPercentage: number
) => {
  const iconsAndColors = {
    all_set: NO_DATA_STEP,
    no_location_data_integration: NO_DATA_STEP,
    location_data_integration_error: NO_DATA_STEP,
    location_data_integration_pending: NO_DATA_STEP,
    special_instructions: NO_DATA_STEP,
    awaiting_carrier_codes: {
      icon: <InfoInvertedColoredIcon />,
      color: "#cfd4da",
      text: t("Loads Tracking"),
    },
    awaiting_customer_codes: {
      icon: <InfoInvertedColoredIcon />,
      color: "#cfd4da",
      text: t("Loads Tracking"),
    },
    awaiting_loads_additions: {
      icon: <WarningTriangleColoredIcon />,
      color: "#cfd4da",
      text: t("Loads Tracking"),
    },
    ok: getThirdIconAndDividerColorBasedOnParams(
      t,
      networkStatus,
      loadVolume,
      trackingPercentage,
      assignmentPercentage
    ),
  };

  return (
    //@ts-ignore
    iconsAndColors[connectivityStatus] || NO_DATA_STEP
  );
};

const getDescriptionAndIconBasedOnParams = (
  t: Function,
  networkStatus: string,
  loadVolume: number,
  trackingPercentage: number,
  assignmentPercentage: number
) => {
  const {
    hasNoLoads,
    hasNoTrackingDetails,
    hasNoTrackingAndAssignmentDetails,
    hasTrackingDetails,
  } = getLoadAndTrackingDetails(
    loadVolume,
    trackingPercentage,
    assignmentPercentage
  );

  if (networkStatus !== "connected") {
    return SUCCESS_STEP_DESCRIPTION_ICON;
  }

  if (hasNoLoads || hasNoTrackingDetails || hasNoTrackingAndAssignmentDetails) {
    return {
      icon: <WarningTriangleColoredIcon />,
      description: hasNoLoads
        ? t(
            "Loads have not yet been assigned to this carrier. FourKites is " +
              "unable to determine if the loads will track until loads are assigned."
          )
        : t("See recommendations"),
    };
  }

  if (hasTrackingDetails) {
    return SUCCESS_STEP_DESCRIPTION_ICON;
  }
};

const getConnectivityStatusDescriptionAndIcon = (
  t: Function,
  connectivityStatus: string,
  companyType: string,
  networkStatus: string,
  loadVolume: number,
  trackingPercentage: number,
  assignmentPercentage: number
) => {
  const isCarrierView = companyType === "shipper";

  const statusMap = {
    account_creation_pending: {
      icon: <XCircleInvertedIcon fill="#cfd4da" />,
      description: "Status not available.",
    },
    no_location_data_integration: {
      icon: <SlashErrorInvertedColoredIcon />,
      description: isCarrierView
        ? t("You have yet to add a tracking method.")
        : t("The carrier has yet to add a tracking method."),
    },
    location_data_integration_error: {
      icon: <XCircleInvertedColoredIcon />,
      description: isCarrierView
        ? t("You have added a GPS device, but there are errors in the setup.")
        : t(
            "The carrier has added a GPS device, but there are errors in the setup."
          ),
    },
    location_data_integration_pending: {
      icon: <WarningTriangleColoredIcon />,
      description: isCarrierView
        ? t(
            "You have started to add a GPS device, but has not completed the setup."
          )
        : t(
            "The carrier has started to add a GPS device," +
              " but has not completed the setup."
          ),
    },
    no_asset_assignment_integration: {
      icon: <SlashErrorInvertedColoredIcon />,
      description: isCarrierView
        ? t("You have yet to add a tracking method.")
        : t("The carrier has yet to add a tracking method."),
    },
    special_instructions: {
      label: t("Special instructions"),
      icon: <WarningTriangleColoredIcon />,
      description: isCarrierView
        ? t("Your special onboarding instructions were sent to the customer.")
        : t(
            "This carrier has special onboarding instructions. " +
              "Please see recommendations for details."
          ),
    },
    asset_assignment_integration_error: {
      icon: <XCircleInvertedColoredIcon />,
      description: isCarrierView
        ? t(
            "You have started to add a tracking method, but is not assigning loads."
          )
        : t(
            "The carrier has added a tracking method," +
              " but it's not assigning loads yet."
          ),
    },
    awaiting_carrier_codes: {
      icon: <InfoInvertedColoredIcon />,
      description: t(
        "The customer has yet to add the codes it uses to identify you."
      ),
    },
    awaiting_customer_codes: {
      icon: <InfoInvertedColoredIcon />,
      description: t(
        "The carrier has successfully added a tracking method, " +
          "but has not added bill-to codes to start tracking."
      ),
    },
    awaiting_loads_additions: {
      icon: <WarningTriangleColoredIcon />,
      description: isCarrierView
        ? t(
            "The customer needs to assign loads to you in order to start tracking."
          )
        : t(
            "Loads have not yet been assigned to this carrier. FourKites is " +
              "unable to determine if the loads will track until loads are assigned."
          ),
    },
    ok: getDescriptionAndIconBasedOnParams(
      t,
      networkStatus,
      loadVolume,
      trackingPercentage,
      assignmentPercentage
    ),
  };

  return (
    //@ts-ignore
    statusMap[connectivityStatus] || {
      label: "--",
      description: t("Status not available."),
      icon: <XCircleInvertedIcon fill="#cfd4da" />,
    }
  );
};

const NO_DATA_STEP = {
  icon: <XCircleInvertedIcon fill="#cfd4da" />,
  color: "#cfd4da",
  text: "No Data",
};

const SUCCESS_STEP = {
  icon: <SuccessInvertedColoredIcon />,
  color: "#64D88E",
  text: "Loads Tracking",
};

const SUCCESS_STEP_DESCRIPTION_ICON = {
  icon: <CheckIcon fill="#24A148" />,
  description: "Everything is ok!",
};

export default ConnectivityProgress;
