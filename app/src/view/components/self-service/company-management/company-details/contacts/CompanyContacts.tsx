import React, { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { ButtonGroup, GroupButton } from "@fourkites/elemental-button-group";
import { Button, PlusIcon } from "@fourkites/elemental-atoms";
import { Spinner } from "@fourkites/elemental-loading-indicator";
import { Accordion } from "@fourkites/elemental-accordion";

import { useAppDispatch, useAppSelector } from "state/hooks";
import { CommonCompanyContactsState } from "state/modules/CommonCompanyContacts";

import Contact from "view/components/base/contact/Contact";

import CompanyContactsProps from "./CompanyContacts.types";
import styles from "./CompanyContacts.module.scss";
import AddYourContactModal from "../../carrier-details/modals/AddYourContactModal";
import DeleteYourContactModal from "../../carrier-details/modals/DeleteYourContactModal";

const CompanyContacts = ({
  contacts,
  companyId = "",
  scac = "",
  showYourContacts = false,
  companyName = "",
}: CompanyContactsProps) => {
  const { t } = useTranslation();
  const YOUR_CONTACTS = "your_contacts";

  /*****************************************************************************
   * REDUX
   ****************************************************************************/

  const dispatch = useAppDispatch();

  // Contact Details - Your Contacts
  const yourContacts: any = useAppSelector(
    CommonCompanyContactsState.selectors.getCommonCompanyContacts()
  );
  const isLoadingContacts: any = useAppSelector(
    CommonCompanyContactsState.selectors.isLoadingCompanyContacts()
  );

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [selectedContactType, setSelectedContactType] =
    useState("global_contacts");
  const [isAddContactClicked, setIsAddContactClicked] = useState(false);
  const [selectedContactId, setSelectedContactId] = useState();
  const [showDeleteContactModal, setShowDeleteContactModal] = useState(false);

  /*****************************************************************************
   * HANDLERS
   ****************************************************************************/
  const handleAddContact = () => {
    setIsAddContactClicked(true);
  };

  const onSelectContactType = (buttonId: string) => {
    setSelectedContactType(buttonId);
    if (buttonId === YOUR_CONTACTS && !yourContacts) {
      fetchYourContacts();
    }
  };

  const fetchYourContacts = () => {
    dispatch(
      CommonCompanyContactsState.actions.retrieveCompanyContacts({
        companyId,
        scac,
      })
    );
  };

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  useEffect(() => {
    return () => {
      dispatch(CommonCompanyContactsState.actions.resetCompanyContacts());
    };
  }, []);

  // Fetch contact details for selected contact
  const getContactDetails = (contactId: number) => {
    const contact = yourContacts?.find((c: any) => c.contact_id === contactId);
    if (!contact) {
      return;
    }

    return {
      name: contact.contact_name,
      email: contact.email,
      phone_number: contact.phone_number,
      department: contact.department,
      level_1: contact.level_1,
      level_2: contact.level_2,
    };
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  let contactsData = contacts;
  if (selectedContactType === YOUR_CONTACTS) {
    if (yourContacts) {
      contactsData = yourContacts?.map?.((c: any) => ({
        id: c.contact_id,
        avatar: "",
        first_name: c.contact_name,
        last_name: "",
        position: `${c.level_1 ? "Level 1" : "Level 2"} Contact (${
          c.department
        })`,
        email: c.email,
        secondary_emails: [],
        phones: [],
        messaging: "",
      }));
    } else {
      contactsData = [];
    }
  }

  return (
    <Accordion title={t("Contacts")}>
      {isAddContactClicked ? (
        <AddYourContactModal
          showModal={isAddContactClicked}
          onClose={() => {
            setIsAddContactClicked(false);
            setSelectedContactId(undefined);
          }}
          companyId={companyId}
          scac={scac}
          companyName={companyName}
          fetchYourContacts={fetchYourContacts}
          mode={selectedContactId ? "edit" : "add"}
          contactDetails={
            selectedContactId ? getContactDetails(selectedContactId) : undefined
          }
          contactId={selectedContactId}
        />
      ) : (
        <></>
      )}
      <DeleteYourContactModal
        showModal={showDeleteContactModal}
        onClose={() => {
          setShowDeleteContactModal(false);
          setSelectedContactId(undefined);
        }}
        companyName={companyName}
        contactId={selectedContactId}
        companyId={companyId}
        fetchYourContacts={fetchYourContacts}
        contactDetails={
          selectedContactId ? getContactDetails(selectedContactId) : undefined
        }
      />
      <div className={styles.contactsContainer}>
        {showYourContacts && (
          <div className={styles.buttonContainer}>
            <ButtonGroup
              size="small"
              className={styles.buttonGroupContainer}
              onButtonClick={onSelectContactType}
              selectedButtons={[selectedContactType]}
            >
              <GroupButton
                data-testid="button-global-contacts"
                buttonId={"global_contacts"}
              >
                {t("Global Contacts")}
              </GroupButton>
              <GroupButton
                data-testid="button-your-contacts"
                buttonId={"your_contacts"}
              >
                {t("Your Contacts")}
              </GroupButton>
            </ButtonGroup>

            <Button
              className={styles.addButton}
              size="small"
              theme="primary"
              onClick={handleAddContact}
            >
              <PlusIcon fill="white" size="16" />
              {t(" Add Contact")}
            </Button>
          </div>
        )}
        {isLoadingContacts && (
          <div className={styles.loader}>
            <Spinner isLoading size="small" />
          </div>
        )}
        {contactsData?.length > 0 && !isLoadingContacts ? (
          contactsData?.map((c: any, idx: number) => (
            <div key={idx}>
              <Contact
                contact={{
                  avatar: c.avatar,
                  firstName: c.first_name,
                  lastName: c.last_name,
                  position: c.position,
                  email: c.email,
                  secondaryEmails: c.secondary_emails || [],
                  phones: c.phones || [],
                  messaging: c.messaging,
                }}
                allowMoreActions={selectedContactType === "your_contacts"}
                onEditClick={() => {
                  setIsAddContactClicked(true);
                  setSelectedContactId(c.id);
                }}
                onDeleteClick={() => {
                  setSelectedContactId(c.id);
                  setShowDeleteContactModal(true);
                }}
              />
            </div>
          ))
        ) : (
          <div className={styles.noContactsMessage}>
            <span>{t("No contacts")}</span>
          </div>
        )}
      </div>
    </Accordion>
  );
};

export default CompanyContacts;
