@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.infoRow {
  display: flex;
  margin-bottom: 16px;
}

.infoWrapper {
  display: flex;
  flex: 1;
  flex-direction: column;

  > label[id="title"] {
    font-weight: 600;
    font-size: 14px;
    line-height: 17px;
    margin-right: 8px;
  }

  > label[id="description"] {
    color: $color-neutral-900;
    font-size: 16px;
    letter-spacing: 0;
    line-height: 28px;
    padding-right: 16px;
  }
}

.modesWrapper {
  display: flex;

  > span {
    display: flex;
    align-items: center;
    margin-right: 12px;

    > label {
      margin-left: 6px;
    }
  }
}
