import React from "react";
import { useTranslation } from "react-i18next";

import { Spinner } from "@fourkites/elemental-loading-indicator";
import { Accordion } from "@fourkites/elemental-accordion";

import { getCompanyAddress } from "view/components/self-service/company-management/CompanyManagementUtils";
import { startWithUpperCase } from "view/components/base/StringUtils";
import { getCompanyIdentification, CompanyIdentificationType } from "view/components/base/company-identifications/IdentificationsIndicator";

import { getModeIconAndDescription } from "view/components/base/ModeUtils";

import styles from "./CompanyInfo.module.scss";
import CompanyInfoProps from "./CompanyInfo.types";

const CompanyInfo = ({
  address,
  type,
  trackingModes,
  fleetSize,
  isLoading,
  identifications,
}: CompanyInfoProps) => {
  const { t } = useTranslation();

  return (
    <Accordion title={t("Company Info")}>
      {isLoading ? (
        <Spinner isLoading size="small" />
      ) : (
        <>
          <div className={styles.infoRow}>
            <div
              data-test-id="company-info-wrapper-address"
              className={styles.infoWrapper}
            >
              <label data-test-id="company-info-address-title" id="title">
                {t("Address")}
              </label>
              <label
                data-test-id="company-info-address-description"
                id="description"
              >
                {getCompanyAddress({ address })}
              </label>
            </div>
            <div
              data-test-id="company-info-wrapper-type"
              className={styles.infoWrapper}
            >
              <label data-test-id="company-info-type-title" id="title">
                {t("Type")}
              </label>
              <label
                data-test-id="company-info-type-description"
                id="description"
              >
                {startWithUpperCase(type)}
              </label>
            </div>
          </div>

          <div className={styles.infoRow}>
            <div className={styles.infoWrapper}>
              <label id="title">{t("Modes")}</label>
              <div className={styles.modesWrapper}>
                {trackingModes?.length > 0
                  ? trackingModes?.map((mode: string, index: number) => {
                      const { Icon, description } = getModeIconAndDescription(
                        mode
                      );

                      return (
                        <span key={index}>
                          <Icon size="16px" fill={"#868e96"} />
                          <label id="description">{description}</label>
                        </span>
                      );
                    })
                  : "--"}
              </div>
            </div>
            <div className={styles.infoWrapper}>
              <label id="title">{t("Fleet Size")}</label>
              <label id="description">{fleetSize || "--"}</label>
            </div>
          </div>

          <div className={styles.infoRow}>
            <div className={styles.infoWrapper}>
              <label id="title">{t("USDOT")}</label>
              <div className={styles.modesWrapper}>
                {getParsedIdentifications(identifications, "usdot")}
              </div>
            </div>
            <div className={styles.infoWrapper}>
              <label id="title">{t("MC Number")}</label>
              <div className={styles.modesWrapper}>
              {getParsedIdentifications(identifications, "mc")}
              </div>
            </div>
          </div>

          <div className={styles.infoRow}>
            <div className={styles.infoWrapper}>
              <label id="title">{t("NMFTA SCAC")}</label>
              <div className={styles.modesWrapper}>
              {getParsedIdentifications(identifications, "scac")}
              </div>
            </div>
          </div>
        </>
      )}
    </Accordion>
  );
};

const getParsedIdentifications = (
  identifications: any,
  identificationType: CompanyIdentificationType
) => {
  let identificationValues: string[] = getCompanyIdentification(identifications, identificationType) || [];
  return identificationValues.length > 0 ? identificationValues.toString() : "--"
};

export default CompanyInfo;
