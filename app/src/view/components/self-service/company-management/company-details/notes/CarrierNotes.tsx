import React from "react";
import { useTranslation } from "react-i18next";

import { Accordion } from "@fourkites/elemental-accordion";

import styles from "./CarrierNotes.module.scss";
import CarrierNotesProps from "./CarrierNotes.types";

const CarrierNotes = ({}: CarrierNotesProps) => {
  const { t } = useTranslation();

  return (
    <Accordion title={t("Carrier Notes")}>
      <div>
        <span className={styles.prerequisitesTitle}>{t("Carrier Notes")}</span>
      </div>
    </Accordion>
  );
};

export default CarrierNotes;
