export const CARRIER_RECOMMENDED_ACTIONS: Map<string, string[]> = new Map([
  [
    "no_location_data_integration",
    [
      "<sub-heading>This carrier has an account on FourKites, but has not yet connected an ELD/GPS location source.<sub-heading>",
      "Ask the carrier to connect their ELD device. The carrier can request help from FourKites once " +
        "they attempt to connect.",
      "In the meantime, the carrier will receive daily notifications with an assignment link for each " +
        "load tendered to them. Ask the carrier to provide a mobile phone number for the driver on each " +
        "load to track via the FourKites Driver App, " +
        "<url>CarrierLink::https://www.fourkites.com/network/carrier-link-advanced-mobile/<url>.",
      "Carrier can adjust their unassigned load notifications by logging in to FourKites and clicking the " +
        '"Assign Now" button.',
    ],
  ],
  [
    "location_data_integration_pending",
    [
      "This carrier has an account on FourKites, and has an ELD provider which require manual setup.",
      "This process may take up to 10 business days, depending on the ELD provider.",
      "In the meantime, the carrier will receive daily notifications and an assignment link for each load " +
        "tendered to them. Ask the carrier to provide a mobile phone number for the driver on each load to " +
        "track via the FourKites Driver App, " +
        "<url>CarrierLink::https://www.fourkites.com/network/carrier-link-advanced-mobile/<url>.",
      "Carrier can adjust their unassigned load notifications by logging in to FourKites and clicking the " +
        '"Assign Now" button.',
    ],
  ],
  [
    "location_data_integration_error",
    [
      "This carrier has created their FourKites account, but has not successfully connected their ELD device " +
        "for location data.",
      "You can remind your carrier that help is available from FourKites while attempting to connect their ELD provider.",
    ],
  ],
  [
    "awaiting_customer_codes",
    [
      "<sub-heading>This carrier already has an automated integration with FourKites, but is not yet including your load data.<sub-heading>",
      "Ask the carrier to add your loads to their data feed with FourKites. Carriers may refer to your " +
        "Sample Reference Numbers to know which load identifier to send to FourKites.",
      "After they have added your loads to the data feed, the carrier needs to log into FourKites, " +
        'and add their "Bill-to Code" for your company.',
      '(A "bill-to-code" or a "customer-code" is how carriers indicate which shipper a load belongs to in ' +
        "their data feed to FourKites. FourKites asks for this code to troubleshoot issues, and to ensure " +
        'data is applied to the right loads. "Bill-to-codes" are used in your carriers TMS, and it is their ' +
        "responsibility to provide them to FourKites after adding your data.)",
    ],
  ],
  [
    "awaiting_loads_no_tracking_no_assignment",
    [
      "Validate your Carrier Codes for this carrier.",
      "Loads are only counted on this page after they are delivered or expired (if untracked).",
    ],
  ],
  [
    "awaiting_loads_gps_tracking_automated_assignment",
    [
      "Validate your Carrier Codes for this carrier.",
      "Loads are only counted on this page after they are delivered or expired (if untracked).",
    ],
  ],
  [
    "awaiting_loads_file_tracking_automated_assignment",
    [
      "Validate your Carrier Codes for this carrier.",
      "Loads are only counted on this page after they are delivered or expired (if untracked).",
    ],
  ],
  [
    "all_set_gps_manual_no_tracking_no_assignment",
    [
      "<sub-heading>The carrier is not assigning assets to your loads.<sub-heading>",
      'Ask the carrier to log into FourKites, go to the "Overview" Tab, and click "Assign Now"',
      "From that page, the carrier can also set up or adjust email notification settings for unassigned loads.",
    ],
  ],
  [
    "all_set_gps_manual_no_tracking_zeroplus_assignment",
    [
      "<sub-heading>The carrier is assigning assets to loads, but they are not tracking.<sub-heading>",
      "Ask the carrier to validate that their asset ID format matches the asset IDs of their ELD provider. " +
        "(i.e. Truck123 vs. Truck_123).",
      "Ask the carrier to ensure their ELD provider is registered with FourKites, and that they can " +
        "successfully test an asset ID for each provider.",
    ],
  ],
  [
    "all_set_gps_manual_tracking_lessthan_assignment_lessthan_ninety",
    [
      "<sub-heading>The carrier is assigning assets to loads that aren't tracking.<sub-heading>",
      "Ask the carrier needs to validate ELD connection, by testing an asset ID.",
      "Ask the carrier to validate that their asset ID format matches the asset IDs of their ELD provider. " +
        "(i.e. Truck123 vs. Truck_123)",
    ],
  ],
  [
    "all_set_gps_manual_tracking_equals_assignment_lessthan_ninety",
    [
      "<sub-heading>The carrier is assigning assets to most loads, but not all.<sub-heading>",
      "Remind the carrier that they can request a free integration with FourKites to reduce " +
        "manual assignment labor from this " +
        "<url>link::https://fourkites.my.site.com/hc/s/contactsupport<url>.",
    ],
  ],
  [
    "all_set_gps_manual_tracking_lessthan_ninety_assignment_morethan_ninety",
    [
      "<sub-heading>The carrier is assigning assets to loads, but some loads are not tracking.<sub-heading>",
      "Ask the carrier to validate that their asset ID format matches the asset IDs of their ELD provider. " +
        "(i.e. Truck123 vs. Truck_123).",
      "Ask the carrier to ensure their ELD provider is registered with FourKites, and that they can " +
        "successfully test an asset ID for each provider.",
    ],
  ],
  [
    "all_set_gps_manual_tracking_morethan_ninety_assignment_morethan_ninety",
    [],
  ],
  [
    "all_set_gps_automated_no_tracking_no_assignment",
    [
      "<sub-heading>This carrier has an automated integration with FourKites, but is not including your load data.<sub-heading>",
      "Ask the carrier to add your loads to their data feed with FourKites. Carriers may refer to your " +
        "Sample Reference Numbers to know which load identifier to send to FourKites.",
    ],
  ],
  [
    "all_set_gps_automated_no_tracking_zeroplus_assignment",
    [
      "<sub-heading>The carrier is assigning assets to loads via an automated integration, but loads are not tracking.<sub-heading>",
      "Ask the carrier to ensure that they are sending ALL of your load data with a reference number which matches " +
        "your load data in FourKites.",
      "Ask the carrier to validate that their asset ID format matches the asset IDs of their ELD provider. " +
        "(i.e. Truck123 vs. Truck_123).",
      "Ask the carrier to ensure their ELD provider is registered with FourKites, and that they can " +
        "successfully test an asset ID for each provider.",
    ],
  ],
  [
    "all_set_gps_automated_tracking_lessthan_assignment_lessthan_ninety",
    [
      "<sub-heading>The carrier is assigning assets to some loads via an automated integration, but loads are not tracking.<sub-heading>",
      "Ask the carrier to ensure that they are sending your load data with a reference number which matches your " +
        "load data in FourKites.",
      "Ask the carrier to be sure they are sending data for all of your loads to FourKites, spanning multiple " +
        "bill-to-codes if applicable.",
      "Ask the carrier to validate that their asset ID format matches the asset IDs of their ELD provider. " +
        "(i.e. Truck123 vs. Truck_123).",
      "Ask the carrier to ensure all of their ELD providers are registered with FourKites, and that they can " +
        "successfully test an asset ID for each provider.",
    ],
  ],
  [
    "all_set_gps_automated_tracking_equals_assignment_lessthan_ninety",
    [
      "<sub-heading>The carrier is assigning assets to some loads via an automated integration, but not all.<sub-heading>",
      "Ask the carrier to be sure they are sending data for all of your loads to FourKites, spanning " +
        "multiple bill-to-codes if applicable.",
    ],
  ],
  [
    "all_set_gps_automated_tracking_lessthan_ninety_assignment_morethan_ninety",
    [
      "<sub-heading>The carrier is assigning assets to loads, but some loads are not tracking.<sub-heading>",
      "Ask the carrier to validate that their asset ID format matches the asset IDs of their ELD provider. " +
        "(i.e. Truck123 vs. Truck_123).",
      "Ask the carrier to ensure their ELD provider is registered with FourKites, and that they can " +
        "successfully test an asset ID for each provider.",
    ],
  ],
  [
    "all_set_gps_automated_tracking_morethan_ninety_assignment_morethan_ninety",
    [],
  ],
  [
    "all_set_file_automated_no_tracking_no_assignment",
    [
      "<sub-heading>This carrier has an automated integration with FourKites, but is not including your load data.<sub-heading>",
      "Ask the carrier to add your loads to their data feed with FourKites. Carriers may refer to your " +
        "Sample Reference Numbers to know which load identifier to send to FourKites.",
    ],
  ],
  [
    "all_set_file_automated_no_tracking_zeroplus_assignment",
    [
      "<sub-heading>The carrier is sending incomplete data to FourKites.<sub-heading>",
      "Ask carrier to ensure they have location data for your loads in their TMS.",
      "Ask the carrier to review and troubleshoot their data feed to FourKites.",
    ],
  ],
  [
    "all_set_file_automated_tracking_lessthan_assignment_lessthan_ninety",
    [
      "<sub-heading>The carrier is sending incomplete data to FourKites.<sub-heading>",
      "Ask carrier to ensure they have location data for your loads in their TMS.",
      "Ask the carrier to review and troubleshoot their data feed to FourKites.",
      "Ask the carrier to ensure they send data to FourKites before the pickup appointment.",
    ],
  ],
  [
    "all_set_file_automated_tracking_equals_assignment_lessthan_ninety",
    [
      "<sub-heading>The carrier is not sending data for all of your loads.<sub-heading>",
      "Ask the carrier to ensure that they are sending your load data with a reference number which " +
        "matches your load data in FourKites.",
      "Ask the carrier to be sure they are sending data for all of your loads to FourKites, spanning " +
        "multiple bill-to-codes if applicable.",
    ],
  ],
  [
    "all_set_file_automated_tracking_lessthan_ninety_assignment_morethan_ninety",
    [
      "<sub-heading>The carrier is sending incomplete data to FourKites.<sub-heading>",
      "Ask carrier to ensure they have location data for your loads in their TMS.",
    ],
  ],
  [
    "all_set_file_automated_tracking_morethan_assignment",
    [
      "<sub-heading>The carrier is not sending data for all of your loads.<sub-heading>",
      "Ask the carrier to ensure that they are sending your load data with a reference number which matches your load data in FourKites.",
      "Ask the carrier to be sure they are sending data for all of your loads to FourKites, spanning multiple bill-to-codes if applicable.",
      "(Since this carreir tracks via File/API, assignment data is not necessary.)",
    ],
  ],
  [
    "all_set_file_automated_tracking_morethan_ninety_assignment_morethan_ninety",
    [],
  ],
]);

export const SHIPPER_RECOMMENDED_ACTIONS: Map<string, string[]> = new Map([
  [
    "no_location_data_integration",
    [
      "Connect your ELD provider to FourKites " +
        "<url>here::https://app.fourkites.com/self-service/carrier/tracking/eld-gps<url>.",
    ],
  ],
  [
    "location_data_integration_pending",
    [
      "You should have received an email with a ticket number for your request to our support team. " +
        "They will work with the ELD provider to set up the connection. If the status is not “Connected” " +
        "after 3-4 days, please reply all to the FourKites ticket with subject line “FourKites Support Ticket " +
        "Created:” for a status update.",

      "In the meantime, you will receive daily email with an assignment link for each load tendered to you. " +
        "Please provide the mobile phone number for the driver on each load, so they can track via our drive app, " +
        "<url>CarrierLink::https://www.fourkites.com/network/carrier-link-advanced-mobile/<url>.",

      'You can adjust your unassigned load notifications by going to the Overview tab, then clicking "Assign Now"',
    ],
  ],
  [
    "location_data_integration_error",
    [
      "We're here to help. Submit a support ticket by going back to the ELD registration and instructions page " +
        'and click "I need some support. Have FourKites reach out to me"',
      "In the meantime, you will receive daily email with an assignment link for each load tendered to you. " +
        "Please provide the mobile phone number for the driver on each load, so they can track via our drive app, " +
        "<url>CarrierLink::https://www.fourkites.com/network/carrier-link-advanced-mobile/<url>.",
      'You can adjust your unassigned load notifications by going to the Overview tab, then clicking "Assign Now"',
    ],
  ],
  [
    "awaiting_customer_codes",
    [
      "<statement>You have an automated integration with FourKites, and already send us data via a File or API for other " +
        'customers. (Each customer is identified in your data feed with a "bill-to-code", or "customer code.")<statement>',
      "<sub-heading>To track for this new customer:<sub-heading>",
      'Add load data for this customer in your existing FourKites data feed.  Refer to the "Sample ' +
        'Reference Numbers" in the section below.',
      'After you have included this customer\'s data in your feed, please input the "bill-to-code" for ' +
        "this customer in the field below.",
      'The customer will move to the "Connected" status the day after you input the "bill-to-code" for ' +
        'this customer.  We use the "bill-to-code" to ensure the data is applied to the correct customer, ' +
        "and to troubleshoot any future problems.",
      'You can find more information about adding "bill-to-codes" ' +
        "<url>here::https://fourkites.my.site.com/hc/s/article/FourKites-Connect-FAQs#r<url>.",
    ],
  ],
  [
    "special_instructions",
    [
      "Your customer has been provided your special instructions to connect with your company. ",
      "You can view or edit your special instrucions " +
        "<url>here::https://app.fourkites.com/self-service/carrier/company/onboarding-instructions<url>.",
    ],
  ],
  [
    "awaiting_loads_no_tracking_no_assignment",
    [
      "This customer has not tendered you any loads on FourKites in the past 30 days. " +
        "There may be an issue with their data integration.",
      "Loads are only counted on this page after they are delivered or expired (if untracked)",
    ],
  ],
  [
    "awaiting_loads_gps_tracking_automated_assignment",
    [
      "This customer has not tendered you any loads on FourKites in the past 30 days.",
      "Loads are only counted on this page after they are delivered or expired (if untracked)",
    ],
  ],
  [
    "awaiting_loads_file_tracking_automated_assignment",
    [
      "This customer has not tendered you any loads on FourKites in the past 30 days.",
      "Loads are only counted on this page after they are delivered or expired (if untracked)",
    ],
  ],
  [
    "all_set_gps_manual_no_tracking_no_assignment",
    [
      "You need to assign Asset ID's to loads. Be sure to type the Truck or Trailer number exactly as " +
        "it appears in your ELD platform.",
      'Go to the "Overview" Tab and click "Assign Now"',
      "From that page, you can also set up or adjust your notification settings for unassigned loads. " +
        "And find instructions to automate this workflow",
    ],
  ],
  [
    "all_set_gps_manual_no_tracking_zeroplus_assignment",
    [
      "<sub-heading>You are assigning assets to loads, but they are not tracking.<sub-heading>",
      "Ensure your asset ID format matches the asset IDs in your ELD provider. (i.e. Truck123 vs. Truck_123)",
      'Test an asset ID in the "Tracking Integrations" tab to be sure we can locate an asset.',
    ],
  ],
  [
    "all_set_gps_manual_tracking_lessthan_assignment_lessthan_ninety",
    [
      "<sub-heading>You are assigning assets to loads, but they are not tracking.<sub-heading>",
      "Validate that all of your ELD providers are connected to FourKites. Test an asset ID for " +
        'each on the "Tracking Integrations" tab.',
      "Ensure your asset ID format matches the asset IDs in your ELD provider. (i.e. Truck123 vs. Truck_123)",
    ],
  ],
  [
    "all_set_gps_manual_tracking_equals_assignment_lessthan_ninety",
    [
      "Tracking is working, but you're not assigning assets to all loads.",
      "FourKites offers a free integration with your TMS to remove your manual work load. " +
        "Click <url>here::https://fourkites.my.site.com/hc/s/contactsupport<url> to request an " +
        "integration.",
    ],
  ],
  [
    "all_set_gps_manual_tracking_lessthan_ninety_assignment_morethan_ninety",
    [
      "<sub-heading>You are assigning assets to loads, but they are not tracking.<sub-heading>",
      "Validate that all of your ELD providers are connected to FourKites. Test an asset ID for each on the " +
        '"Tracking Integrations" tab.',
      "Ensure your asset ID format matches the asset IDs in your ELD provider.(i.e. Truck123 vs. Truck_123)",
    ],
  ],
  [
    "all_set_gps_manual_tracking_morethan_ninety_assignment_morethan_ninety",
    [],
  ],
  [
    "all_set_gps_automated_no_tracking_no_assignment",
    [
      "<sub-heading>Your integration to FourKites does not contain the correct data for this customer.<sub-heading>",
      "Ensure data for this customer is included in your data feed to FourKites.",
      "Validate you're sending a load identifier similar to the provided Sample Reference Numbers for this customer.",
    ],
  ],
  [
    "all_set_gps_automated_no_tracking_zeroplus_assignment",
    [
      "<sub-heading>Your integration to FourKites does not contain the correct data for this customer.<sub-heading>",
      "Ensure ALL data for this customer is included in your data feed to FourKites.",
      "Validate you're sending a load identifier similar to the provided Sample Reference Numbers for this customer.",
      "Ensure your asset ID format matches the asset IDs in your ELD provider. (i.e. Truck123 vs. Truck_123)",
    ],
  ],
  [
    "all_set_gps_automated_tracking_lessthan_assignment_lessthan_ninety",
    [
      "<sub-heading>You are assigning assets to loads, but they are not tracking.<sub-heading>",
      "Ensure your asset ID format matches the asset IDs in your ELD provider. (i.e. Truck123 vs. Truck_123)",
      "Ensure the ELD providers for your entire fleet are connected to FourKites.",
      'Test an asset ID in the "Tracking Integrations" tab to be sure we can locate an asset.',
    ],
  ],
  [
    "all_set_gps_automated_tracking_equals_assignment_lessthan_ninety",
    [
      "<statement>You are not assigning assets to all loads for this customer, but tracking is properly configured.<statement>",
      "Be sure you're including data from all bill-to-codes for this customer",
    ],
  ],
  [
    "all_set_gps_automated_tracking_lessthan_ninety_assignment_morethan_ninety",
    [
      "<sub-heading>You are assigning assets to most loads, but not all are tracking.<sub-heading>",
      "Ensure your asset ID format matches the asset IDs in your ELD provider. (i.e. Truck123 vs. Truck_123)",
      "Ensure the ELD providers for your entire fleet are connected to FourKites.",
      'Test an asset ID in the "Tracking Integrations" tab to be sure we can locate an asset.',
    ],
  ],
  [
    "all_set_gps_automated_tracking_morethan_ninety_assignment_morethan_ninety",
    [],
  ],
  [
    "all_set_file_automated_no_tracking_no_assignment",
    [
      "<sub-heading>Your integration to FourKites does not contain the correct data for this customer.<sub-heading>",
      "Ensure data for this customer is included in your data feed to FourKites.",
      "Validate you're sending a load identifier similar to the provided Sample Reference Numbers for this customer.",
    ],
  ],
  [
    "all_set_file_automated_no_tracking_zeroplus_assignment",
    [
      "<sub-heading>Your integration to FourKites does not contain the correct data for this customer.<sub-heading>",
      "Ensure location data for this customer is included in both your TMS, and your data " +
        "feed to FourKites. (Else, connect your ELD directly to FourKites)",
      "Validate you're sending a load identifier similar to the provided Sample Reference Numbers " +
        "for this customer.",
    ],
  ],
  [
    "all_set_file_automated_tracking_lessthan_assignment_lessthan_ninety",
    [
      "<sub-heading>Your integration to FourKites does not contain the correct data for this customer.<sub-heading>",
      "Ensure location data for this customer is included in both your TMS, and your data " +
        "feed to FourKites. (Else, connect your ELD directly to FourKites)",
      "Validate you're sending a load identifier similar to the provided Sample Reference Numbers " +
        "for this customer.",
    ],
  ],
  [
    "all_set_file_automated_tracking_lessthan_ninety_assignment_morethan_ninety",
    [
      "<sub-heading>Your integration to FourKites does not contain the correct data for this customer.<sub-heading>",
      "Ensure location data for this customer is included in both your TMS, and your data " +
        "feed to FourKites. (Else, connect your ELD directly to FourKites)",
      "Validate you're sending a load identifier similar to the provided Sample Reference Numbers " +
        "for this customer.",
    ],
  ],
  [
    "all_set_file_automated_tracking_equals_assignment_lessthan_ninety",
    [
      "You are not sending data for all loads of this customer, but tracking is properly configured.",
      "Be sure you're including data from all bill-to-codes for this customer.",
    ],
  ],
  [
    "all_set_file_automated_tracking_morethan_ninety_assignment_morethan_ninety",
    [],
  ],
]);
