import { useTranslation } from "react-i18next";

import { Spin<PERSON> } from "@fourkites/elemental-loading-indicator";
import { Accordion } from "@fourkites/elemental-accordion";

import { useAppSelector } from "state/hooks";
import { UsersState } from "state/modules/Users";
import {
  CARRIER_RECOMMENDED_ACTIONS,
  SHIPPER_RECOMMENDED_ACTIONS,
} from "./RecommendedActions.constants";

import RecommendedActionsProps from "./RecommendedActions.types";
import styles from "./RecommendedActions.module.scss";
import classNames from "classnames";

const RecommendedActions = ({
  mode,
  managedCompanyType,
  trackedPercentage,
  assignedPercentage,
  connectivityStatus,
  specialInstructions,
  isLoading,
  recommendationType,
}: RecommendedActionsProps) => {
  const { t } = useTranslation();

  // User
  const companyName: string = useAppSelector(
    UsersState.selectors.getCompanyName
  );
  const directAssignmentInfo = useAppSelector(
    UsersState.selectors.getCompanyDirectAssignmentInfo
  );

  const isManagingCarriers = managedCompanyType === "carrier";
  const defaultOpened =
    !(connectivityStatus || "").startsWith("all_set") || trackedPercentage < 1;

  const title =
    specialInstructions?.enabled && specialInstructions?.description
      ? t("Special Instructions:")
      : t("Recommendations to start tracking:");

  if (!recommendationType) {
    return null;
  }

  return (
    <Accordion title={t("Recommendations")} defaultOpened={defaultOpened}>
      <div className={styles.container}>
        {isLoading && <Spinner isLoading size="small" />}

        {!isLoading && (
          <div className={styles.content}>
            <label className={styles.title}>
              {title}
              <br />
            </label>
            {isManagingCarriers
              ? getActionsForCarrier(t, recommendationType, specialInstructions)
              : getActionsForShipper(
                  t,
                  recommendationType,
                  specialInstructions
                )}
          </div>
        )}
      </div>
    </Accordion>
  );
};

const getActionsForCarrier = (
  t: Function,
  recommendationType: string,
  specialInstructions: { description: string; enabled: boolean }
) => {
  let recommendations: string[] =
    CARRIER_RECOMMENDED_ACTIONS.get(recommendationType) || [];

  if (recommendationType == "special_instructions") {
    recommendations = [specialInstructions?.description || "--"];
  }

  return buildRecommendations(recommendations, t);
};

const getActionsForShipper = (
  t: Function,
  recommendationType: string,
  specialInstructions: { description: string; enabled: boolean }
) => {
  const recommendations: string[] =
    SHIPPER_RECOMMENDED_ACTIONS.get(recommendationType) || [];

  return buildRecommendations(recommendations, t);
};

const buildRecommendations = (recommendations: string[], t: Function) => {
  return (
    <div className={classNames(styles.recommendationContainer)}>
      {recommendations.map((recommendation: string) =>
        buildRecommendationContent(recommendation, t)
      )}
    </div>
  );
};

const buildRecommendationContent = (recommendation: string, t: Function) => {
  if (!recommendation) {
    return null;
  }
  // recommendation content shall be build with following preference.
  // SubHeading -> Statement -> Bullet Point.
  // We expect URLs only in the statement or bullet points.
  // By default, we treat the content to be a bullet point.

  if (recommendation.indexOf("<sub-heading>") !== -1) {
    // If content is a sub heading. No URL expected in sub-heading
    const textClassName = classNames(styles.subheadingRecommendation);
    recommendation = recommendation.replaceAll("<sub-heading>", "");
    return <p className={textClassName}>{t(recommendation)}</p>;
  }

  // if the recommendation contains <url>{.....}<url> tags,
  // split the string using <url>, we expect the string at index 1 to be the url component
  // In the url component, the format expected is "DisplayString::https://ActualURL"
  // Split the url component with "::" and string at index 0 is display value and
  // string at index 1 is the link to go to.
  const hasUrlComponent: boolean = recommendation.indexOf("<url>") !== -1;

  let textClass = styles.bulletRecommendation;

  if (recommendation.indexOf("<statement>") !== -1) {
    textClass = styles.statementRecommenation;
    recommendation = recommendation.replaceAll("<statement>", "");
  }

  const textClassName = classNames(textClass);

  if (!hasUrlComponent) {
    return <p className={textClassName}>{t(recommendation)}</p>;
  }

  const recommendationWithUrls: string[] = recommendation.split("<url>");

  if (recommendationWithUrls.length <= 1 || recommendationWithUrls.length > 3) {
    return <>{t(recommendation)}</>;
  }

  const urlData: string[] = recommendationWithUrls[1].split("::");

  if (urlData.length < 2) {
    return <>{t(recommendation)}</>;
  }

  const urlText: string = urlData[0];
  const urlLink: string = urlData[1];

  return (
    <p className={textClassName}>
      {t(recommendationWithUrls[0])}
      <a target="_blank" href={urlLink}>
        {t(urlText)}
      </a>
      {recommendationWithUrls.length > 2 && t(recommendationWithUrls[2])}
    </p>
  );
};

export default RecommendedActions;
