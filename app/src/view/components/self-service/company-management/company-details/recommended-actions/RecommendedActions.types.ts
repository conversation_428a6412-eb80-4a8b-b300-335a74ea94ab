import { LoadsTrackingMode } from "state/BaseTypes";

interface RecommendedActionsProps {
  mode: LoadsTrackingMode;
  isLoading: boolean;
  connectivityStatus: string;
  managedCompanyType: "shipper" | "carrier";
  trackedPercentage: number;
  assignedPercentage: number;
  specialInstructions: { description: string; enabled: boolean };
  recommendationType: string;
}

export default RecommendedActionsProps;
