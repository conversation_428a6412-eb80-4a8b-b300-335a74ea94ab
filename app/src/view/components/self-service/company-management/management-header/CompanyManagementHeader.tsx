import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useLocation } from "react-router";

import { SearchIcon, XIcon } from "@fourkites/elemental-atoms";
import { Input } from "@fourkites/elemental-input";

import { useAppSelector, useAppDispatch, useDebounce } from "state/hooks";
import { CarriersNetworkState } from "state/modules/shipper/CarriersNetwork";
import { ShippersNetworkState } from "state/modules/carrier/ShippersNetwork";

import NetworkStatusFilter from "./network-status-filter/NetworkStatusFilter";
import CarrierManagementHeader from "./carrier/CarrierManagementHeader";
import ShipperManagementHeader from "./shipper/ShipperManagementHeader";

import CompanyManagementHeaderProps from "./CompanyManagementHeader.types";
import styles from "./CompanyManagementHeader.module.scss";

const CompanyManagementHeader = ({
  mode,
  managerCompanyId,
  managedCompanyType,
  onShowManualCarrierAdditions,
  onShowBulkCarrierAdditions,
  onShowCarrierInviteTemplate,
  pageSize,
}: CompanyManagementHeaderProps) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * REDUX
   ****************************************************************************/

  const dispatch = useAppDispatch();
  const location: any = useLocation();

  const isGettingCarriers = useAppSelector(
    CarriersNetworkState.selectors.isLoadingCarriersByMode(mode)
  );
  const isGettingShippers = useAppSelector(
    ShippersNetworkState.selectors.isLoadingShippersByMode(mode)
  );

  const carriersNetworkFilters = useAppSelector(
    CarriersNetworkState.selectors.networkFiltersByMode(mode)
  );
  const shippersNetworkFilters = useAppSelector(
    ShippersNetworkState.selectors.networkFiltersByMode(mode)
  );

  const isManagingCarriers = managedCompanyType === "carrier";
  const { networkStatus, query } = isManagingCarriers
    ? carriersNetworkFilters
    : shippersNetworkFilters;

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  // NOTE: this is used as an auxiliary to useDebounce. We can't use the redux
  // value directly, otherwise we have an infinite loop when we don't receive
  // data back
  const [userQuery, setUserQuery] = useState<string | null>(null);

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  /**
   * Lands on carrier page with network status filter data passed from overview
   */
  useEffect(() => {
    if (location?.state?.networkStatus) {
      let state = location.state;
      onSelectNetworkStatus(location?.state?.networkStatus);
      delete state.networkStatus;
    }
  }, [location]);

  /**
   * Filters by list and paginate. useDebounce will persist the query and
   * dispatch an action to filter for new queries. This way we can automatically
   * filter without throttling the server with requests
   */
  useDebounce(
    () => {
      if (userQuery == undefined) {
        return;
      }

      dispatch(
        changeNetworkFiltersHandler({
          mode: mode,
          query: userQuery,
        })
      );

      // Paginate list of shippers or FTL for carriers
      const allowPagination =
        (isManagingCarriers && mode === "ftl") || !isManagingCarriers;

      // If pagination is allowed,query the server again to fetch data
      if (allowPagination) {
        updateCompaniesList();
      }
    },
    300, // timeout
    [userQuery]
  );

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const changeNetworkFiltersHandler = isManagingCarriers
    ? CarriersNetworkState.actions.changeNetworkFilters
    : ShippersNetworkState.actions.changeNetworkFilters;

  const updateCompaniesList = () => {
    if (isManagingCarriers) {
      dispatch(
        CarriersNetworkState.actions.getCarriers({
          shipperId: managerCompanyId,
          mode: mode,
          pageSize,
        })
      );
    } else {
      dispatch(
        ShippersNetworkState.actions.getShippers({
          carrierId: managerCompanyId,
          mode: mode,
          pageSize,
        })
      );
    }
  };

  /*
   * When user sets query, we change the local state. The local state will be
   * watched by useDebounce
   */
  const onChangeQuery = (userQuery: string) => {
    setUserQuery(userQuery);
  };

  const onSelectNetworkStatus = (status: string) => {
    dispatch(
      changeNetworkFiltersHandler({
        mode: mode,
        networkStatus: status,
      })
    );

    updateCompaniesList();
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  // What other modes include here? Courier?
  const modesWithoutAdditions = ["ltl", "parcel", "ocean", "air"].includes(mode);
  const isLoading = isManagingCarriers ? isGettingCarriers : isGettingShippers;
  const companiesLabel = isManagingCarriers ? "Carrier" : "Shipper";
  const placeholder = isManagingCarriers
    ? t(`Search ${companiesLabel} by name or USDOT`)
    : t(`Search ${companiesLabel} by name`);

  const queryValue = userQuery || query || "";

  return (
    <div className={styles.container}>
      <div id="left">
        <div className={styles.inputWrapper}>
          <Input
            label={""}
            placeholder={placeholder}
            value={queryValue}
            onChange={(e: any) => onChangeQuery(e.target.value)}
            icon={queryValue ? <XIcon /> : <SearchIcon />}
            onIconClick={() => onChangeQuery("")}
            size="medium"
          />
        </div>
      </div>

      <div id="right">
        <NetworkStatusFilter
          isCarrier={isManagingCarriers}
          isLoading={isLoading}
          networkStatus={networkStatus}
          modesWithoutAdditions={modesWithoutAdditions}
          onSelectNetworkStatus={onSelectNetworkStatus}
        />

        {isManagingCarriers ? (
          <CarrierManagementHeader
            mode={mode}
            modesWithoutAdditions={modesWithoutAdditions}
            onShowManualCarrierAdditions={onShowManualCarrierAdditions}
            onShowBulkCarrierAdditions={onShowBulkCarrierAdditions}
            onShowCarrierInviteTemplate={onShowCarrierInviteTemplate}
          />
        ) : (
          <ShipperManagementHeader
            mode={mode}
            modesWithoutAdditions={modesWithoutAdditions}
          />
        )}
      </div>
    </div>
  );
};

export default CompanyManagementHeader;
