@import "@fourkites/elemental-atoms/build/scss/colors/index";

.flyoutItems {
  display: block;
  flex-direction: column;
  align-items: center;
  z-index: 10;
  position: absolute;
  width: 232px;
  margin-top: 4px;
  border-radius: 4px;
  box-shadow: 0 6px 14px 3px rgba(0, 0, 0, 0.1);
  background-color: white;
}

.flyoutItemsExpanded {
  transform: translateX(-63px);
  width: 295px;
}

.flyoutItem {
  display: flex;
  cursor: pointer;
  align-items: center;
  padding: 16px;
  padding-left: 24px;

  &:hover {
    background-color: $color-neutral-200;
  }
}

.flyoutItemLoadCreationNumbers {
  padding: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid $color-neutral-400;
  
  >span {
    align-self: flex-start;
    padding: 3px;
  }
}

.flyoutItemNavigationLink {
  display: flex;
  align-items: center;
  gap: 4px;
  height: 15px;
  color: $color-primary-500;
  margin-top:6px;
  text-decoration: underline;
  cursor: pointer;

  &:visited {
    color: $color-primary-500;
  }
}

.flyoutItemsContainer {
  opacity: 0.5;
  pointer-events: none;
}

.warning-icon {
  margin-top: 1px;
  align-self: flex-start;
}
