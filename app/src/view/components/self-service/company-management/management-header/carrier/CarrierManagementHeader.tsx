import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Link, useHistory } from "react-router-dom";
import copyToClipboard from "copy-to-clipboard";

import {
  Button,
  PlusIcon,
  SearchIcon,
  UploadIcon,
  CopyIcon,
  WarningCircleColoredIcon,
  CaretRightIcon,
  LockFillIcon,
  MailIcon,
} from "@fourkites/elemental-atoms";

import { FOURKITES_APP_URL } from "api/http/apiUtils";

import { dataIntegrationRoutes } from "router/shipper/ShipperDataIntegrationsRouter";

import { useAppDispatch, useAppSelector } from "state/hooks";
import { CarrierInvitationsState } from "state/modules/shipper/CarrierInvitations";
import UsersState from "state/modules/Users";

import FlyoutSelector from "view/components/base/flyout-selector/FlyoutSelector";
import { showToast } from "view/components/base/toast/Toast";

import styles from "./CarrierManagementHeader.module.scss";
import { ShipperSettingsState } from "state/modules/shipper/ShipperSettings";
import classNames from "classnames";

const CarrierManagementHeader = ({
  mode,
  modesWithoutAdditions,
  onShowManualCarrierAdditions,
  onShowBulkCarrierAdditions,
  onShowCarrierInviteTemplate,
}: any) => {
  const { t } = useTranslation();

  const inviterToken = useAppSelector(
    CarrierInvitationsState.selectors.inviterToken()
  );

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const onCopyInviterCode = async () => {
    const inviterTokenUrl = `${FOURKITES_APP_URL}self-service/signup?inviter=${inviterToken}`;
    await copyToClipboard(inviterTokenUrl);
    showToast(t("Invite link copied to clipboard."), t(""), "ok");
  };

  const shipperId: string = useAppSelector(UsersState.selectors.getCompanyId);

  const dispatch = useAppDispatch();

  const fetchLoadNumberFormats = () => {
    dispatch(
      ShipperSettingsState.actions.retrieveLoadNumberFormats({
        shipperId,
      })
    );
  };

  useEffect(() => {
    if (shipperId) {
      fetchLoadNumberFormats();
    }
  }, [shipperId]);

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const canAddCarriers = !modesWithoutAdditions;
  if (canAddCarriers) {
    return (
      <CarrierAdditionsButtons
        inviterToken={inviterToken}
        onShowManualCarrierAdditions={onShowManualCarrierAdditions}
        onShowBulkCarrierAdditions={onShowBulkCarrierAdditions}
        onCopyInviterCode={onCopyInviterCode}
        onShowCarrierInviteTemplate={onShowCarrierInviteTemplate}
      />
    );
  }

  return <BatchUploadsButton mode={mode} />;
};

const CarrierAdditionsButtons = ({
  inviterToken,
  onShowManualCarrierAdditions,
  onShowBulkCarrierAdditions,
  onCopyInviterCode,
  onShowCarrierInviteTemplate,
}: any) => {
  const { t } = useTranslation();

  const loadNumberFormats: string[] = useAppSelector(
    ShipperSettingsState.selectors.loadNumberFormats()
  );

  const loadFormatsData = loadNumberFormats == null ? [] : loadNumberFormats;

  const superAdmin = useAppSelector(UsersState.selectors.getIsSuperAdmin);
  const isCompanyAdmin = useAppSelector(UsersState.selectors.getIsCompanyAdmin);
  const companyType = useAppSelector(UsersState.selectors.getCompanyType);
  const country = useAppSelector(UsersState.selectors.getCompanyCountry);
  const isShipper = companyType?.includes("shipper");
  const hasLoadFormats = loadFormatsData.length == 0;

  const isCountryUSorCA = country == "US" || country == "CA";

  const loadFormatsDataAdminConditions =
    hasLoadFormats && (superAdmin || isCompanyAdmin) && isShipper;
  const loadFormatsDataUserManagerConditions =
    hasLoadFormats && !(superAdmin || isCompanyAdmin) && isShipper;

  const flyoutItemsClasses = classNames(styles.flyoutItems, {
    [styles.flyoutItemsExpanded]: hasLoadFormats && isShipper,
  });

  const icon =
    hasLoadFormats && isShipper ? (
      <LockFillIcon fill="#0e65e5" className={"button-icon-left"} />
    ) : (
      <PlusIcon fill="#0e65e5" iconClass={"button-icon-left"} />
    );

  return (
    <FlyoutSelector icon={icon}>
      {isCountryUSorCA && (
        <div className={flyoutItemsClasses} data-testid="flyout-items">
          {loadFormatsDataAdminConditions && <LoadCreationNumbersAdminDiv />}
          {loadFormatsDataUserManagerConditions && (
            <LoadCreationNumbersUserManagerDiv />
          )}
          <div
            className={
              hasLoadFormats && isShipper ? styles.flyoutItemsContainer : ""
            }
          >
            <span
              data-test-id="flyout-selector-item-search-and-add"
              className={styles.flyoutItem}
              onClick={onShowManualCarrierAdditions}
              data-testid="search-and-add-carrier"
            >
              <SearchIcon iconClass={"button-icon-left"} />
              {t("Search & Add")}
            </span>

            <span
              data-test-id="flyout-selector-item-bulk-upload"
              className={styles.flyoutItem}
              onClick={onShowBulkCarrierAdditions}
              data-testid="bulk-upload-carriers"
            >
              <UploadIcon iconClass={"button-icon-left"} />
              {t("Bulk Upload")}
            </span>

            {inviterToken && (
              <span
                data-test-id="flyout-selector-item-copy-inviter-code"
                className={styles.flyoutItem}
                onClick={onCopyInviterCode}
                data-testid="copy-invite-code"
              >
                <CopyIcon iconClass={"button-icon-left"} />
                {t("Copy Invite Link")}
              </span>
            )}
            <span
                data-test-id="flyout-selector-item-carrier-invites"
                className={styles.flyoutItem}
                onClick={onShowCarrierInviteTemplate}
                data-testid="bulk-upload-carriers"
              >
                <MailIcon iconClass={"button-icon-left"} />
                {t("Invite Carrier")}
              </span>
          </div>
        </div>
      )}
    </FlyoutSelector>
  );
};

const BatchUploadsButton = ({ mode }: any) => {
  const { t } = useTranslation();
  const history = useHistory();

  const companyType = useAppSelector(UsersState.selectors.getCompanyType);
  const isShipper = companyType?.includes("shipper");

  /*
   * Redirect to data integrations page when user click on button
   */
  const onCreateLoads = () => {
    history.push(
      `${dataIntegrationRoutes.batchUploads}?show-additions-modal=1&mode=${mode}`
    );
  };

  // Gets label for create loads button
  const buttonLabelsMap: any = {
    ltl: t("Create Loads with PRO#"),
    parcel: t("Create Loads with Tracking#"),
    ocean: t("Create Loads with Booking#"),
  };
  let buttonLabel = buttonLabelsMap[mode] || t("Create Loads");

  // Only shippers can create loads, so we don't show the buttons if the company
  // is not a shipper
  if (!isShipper || mode === "air") {
    return null;
  }

  return (
    <Button
      data-test-id="carrier-management-header-batch-uploads-button"
      theme="tertiary"
      size="medium"
      onClick={onCreateLoads}
    >
      <PlusIcon fill="#0e65e5" iconClass={"button-icon-left"} />
      {buttonLabel}
    </Button>
  );
};

const LoadCreationNumbersAdminDiv: React.FC = () => {
  const { t } = useTranslation();
  return (
    <div
      data-test-id="flyout-selector-item-load-creation-numbers"
      className={styles.flyoutItemLoadCreationNumbers}
    >
      <span>
        <WarningCircleColoredIcon size="25px" className={"warning-icon"} />
      </span>
      <span>
        {t("Add sample reference numbers before inviting carriers")}
        <br />
        <Link
          className={styles.flyoutItemNavigationLink}
          to={dataIntegrationRoutes.loadMatching}
        >
          {t("Click here to add")} <CaretRightIcon fill="#0e65e5" />
        </Link>
      </span>
    </div>
  );
};

const LoadCreationNumbersUserManagerDiv: React.FC = () => {
  const { t } = useTranslation();
  return (
    <div
      data-test-id="flyout-selector-item-load-creation-numbers"
      className={styles.flyoutItemLoadCreationNumbers}
    >
      <span>
        <WarningCircleColoredIcon size="25px" iconClass={"warning-icon"} />
      </span>
      <span>
        {t(
          "Please ask your account administrator to add Sample Reference Numbers in the Data Integrations tab before inviting carriers to your network"
        )}
      </span>
    </div>
  );
};

export default CarrierManagementHeader;
