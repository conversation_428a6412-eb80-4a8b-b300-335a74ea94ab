import React from "react";
import { useTranslation } from "react-i18next";

import { Label } from "@fourkites/elemental-atoms";
import { Spinner } from "@fourkites/elemental-loading-indicator";

import { ButtonGroup, GroupButton } from "@fourkites/elemental-button-group";

import styles from "./NetworkStatusFilter.module.scss";

const NetworkStatusFilter: React.FC<{
  isLoading: boolean;
  networkStatus: string;
  modesWithoutAdditions: boolean;
  onSelectNetworkStatus: Function;
  isCarrier: boolean
}> = ({
  networkStatus,
  isLoading,
  modesWithoutAdditions,
  onSelectNetworkStatus,
  isCarrier
}) => {
  const { t } = useTranslation();

  return (
    <div className={styles.container}>
      {isLoading && (
        <div id="loading-wrapper">
          <Spinner isLoading size="small" />
        </div>
      )}

      <Label size="medium">{t("Status")}</Label>

      <ButtonGroup
        onButtonClick={onSelectNetworkStatus}
        selectedButtons={[networkStatus]}
        disabled={isLoading}
        size="medium"
        data-testid="network-status-button-group"
      >
        {!modesWithoutAdditions && (
          <GroupButton
            buttonId={"invited"}
            data-testid="button-network-status-invited"
          >
            {t("Invited")}
          </GroupButton>
        )}
        {!modesWithoutAdditions && (
          <GroupButton
            data-testid="button-network-status-in-progress"
            buttonId={"in_progress"}
          >
            {t("In Progress")}
          </GroupButton>
        )}
        <GroupButton
          data-testid="button-network-status-connected"
          buttonId="connected"
        >
          {t("Connected")}
        </GroupButton>
        <GroupButton
          data-testid="button-network-status-disconnected"
          buttonId={"disconnected"}
        >
          {t("Disconnected")}
        </GroupButton>
        {/* TODO: as an immediate problem with slowness, as pagination still
  has not been implemented on backend, on 2022-04-07 it was decided to
  remove the all page only for FTL */}
        {modesWithoutAdditions && isCarrier && (
          <GroupButton
            data-test-id="button-network-status-all"
            buttonId="all"
            selected
          >
            {t("All")}
          </GroupButton>
        )}
      </ButtonGroup>
    </div>
  );
};

export default NetworkStatusFilter;
