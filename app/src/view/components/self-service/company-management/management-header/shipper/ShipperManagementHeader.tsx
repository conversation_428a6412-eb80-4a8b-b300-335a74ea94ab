import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import { Button, PlusIcon } from "@fourkites/elemental-atoms";

import TokenConnectionModal from "./TokenConnectionModal";

const ShipperManagementHeader = ({ mode, modesWithoutAdditions }: any) => {
  const { t } = useTranslation();

  const [showModal, setShowModal] = useState<boolean>(false);

  const onConnectViaInviteCode = () => {
    setShowModal(true);
  };

  const canConnectToShippers = !modesWithoutAdditions;
  if (!canConnectToShippers) {
    return null;
  }

  return (
    <>
      <Button theme="tertiary" size="medium" onClick={onConnectViaInviteCode}>
        <PlusIcon fill="#0e65e5" iconClass={"button-icon-left"} />
        {t("Connect via Invite Link")}
      </Button>

      <TokenConnectionModal
        mode={mode}
        show={showModal}
        onClose={() => setShowModal(false)}
      />
    </>
  );
};
export default ShipperManagementHeader;
