import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import { Input } from "@fourkites/elemental-input";
import { Modal } from "@fourkites/elemental-modal";
import { Spinner } from "@fourkites/elemental-loading-indicator";

import { useAppSelector, useAppDispatch } from "state/hooks";
import { ShippersNetworkState } from "state/modules/carrier/ShippersNetwork";
import { ShippersNetworkDetailsState } from "state/modules/carrier/ShippersNetworkDetails";
import { UsersState } from "state/modules/Users";

import { showToast } from "view/components/base/toast/Toast";

import { isFieldInvalid } from "view/components/base/FormUtils";

import styles from "./ShipperManagementHeader.module.scss";

const TokenConnectionModal = ({ mode, show, onClose }: any) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * REDUX
   ****************************************************************************/

  const dispatch = useAppDispatch();

  const carrierId: string = useAppSelector(UsersState.selectors.getCompanyId);

  const isEditing = useAppSelector(
    ShippersNetworkDetailsState.selectors.isEditingShipperDetails()
  );
  const isRetrievingShippers = useAppSelector(
    ShippersNetworkState.selectors.isLoadingShippersByMode(mode)
  );

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [inviterToken, setInviterToken] = useState<string>("");
  const [confirmed, setConfirmed] = useState<boolean>(false);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/
  /*
   * Clean up state before leaving
   */
  const cleanStateAndClose = () => {
    setInviterToken("");
    setConfirmed(false);

    onClose();
  };

  /*
   * Gets token from URL
   */
  const getParsedTokenFromUrl = (tokenUrl: string) => {
    const urlParam = "inviter=";

    // If user copied whole url, returns just token
    if (tokenUrl.includes(urlParam)) {
      return tokenUrl?.split(urlParam)[1];
    }

    return tokenUrl;
  };

  /*
   * Update list of shippers and finish process
   */
  const onFinish = async () => {
    await dispatch(
      ShippersNetworkState.actions.getShippers({ carrierId, mode })
    );

    cleanStateAndClose();
  };

  /*
   * Call API to connect with customer
   */
  const onConfirmToken = async () => {
    setConfirmed(true);
    if (isFieldInvalid(inviterToken)) {
      return;
    }

    const response = await dispatch(
      ShippersNetworkDetailsState.actions.connectToShipperViaToken({
        mode,
        carrierId,
        inviterToken: getParsedTokenFromUrl(inviterToken),
      })
    );

    // Show error
    if ("error" in response) {
      showToast(
        t("Error connecting"),
        t("There was an error connecting to your customer"),
        "error"
      );
      onFinish();
      return;
    }

    // Confirm success
    const customerName =
      response?.payload?.data?.shipper_name || "your customer";
    showToast(
      t("Customer Connected Successfully"),
      `${t("You are now connected to")} ${customerName}`,
      "ok"
    );
    onFinish();
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <Modal
      size="small"
      title={t("Connect to Customer")}
      subtitle={t("Use the customer token to connect")}
      show={show}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: cleanStateAndClose,
      }}
      saveButtonProps={{
        label: t("Connect"),
        onClick: onConfirmToken,
      }}
    >
      <div className={styles.container}>
        <label>
          {t(
            "Type or paste the Invite Link obtained from the customer to " +
              "connect with them."
          )}
        </label>

        {isEditing || isRetrievingShippers ? (
          <div className={styles.loader}>
            <Spinner isLoading size="medium" />
          </div>
        ) : (
          <div className={styles.inputsRow}>
            <div className={styles.inputWrapper}>
              <Input
                required
                label={`${t("Invite Link")}`}
                placeholder={t("Type or paste the Invite Link")}
                value={inviterToken}
                invalid={confirmed && isFieldInvalid(inviterToken)}
                errorLabel={t("Please provide a valid code")}
                onChange={(e: any) => setInviterToken(e.target.value)}
              />
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};
export default TokenConnectionModal;
