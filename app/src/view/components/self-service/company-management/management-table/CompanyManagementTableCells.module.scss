@import "@fourkites/elemental-atoms/build/scss/colors/index";

/*******************************************************************************
* IDENTIFICATIONS CELL
*******************************************************************************/

// Containers

.identificationCell {
  display: flex;
  flex-direction: column;
  padding-left: 14px;
}

.nameAndHightlightsContainer {
  display: flex;
  align-items: center;
  align-content: center;
}

.identificationLabel {
  font-size: 14px;
  line-height: 17px;
  color: $color-neutral-600;
  margin-top: 4px;
}

/*******************************************************************************
* STATUS CELL
*******************************************************************************/

.statusContainer {
  display: flex;
  align-content: center;
  align-items: center;
}

.textCell {
  display: flex;
  align-content: center;
  align-items: center;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: $color-neutral-900;
}

.nonResponsiveIcon {
  margin-left: 12px;
}

.headerCell {
  display: flex;
  align-content: center;
  align-items: center;

  > span {
    display: flex;
    align-content: center;
    align-items: center;
  }

  > label {
    margin-left: 12px;
  }
}

.tootlTipMessage {
  background-color: $color-neutral-900;
  box-shadow: 0 5px 10px 5px rgba(0, 0, 0, 0.509);
}

.toolTipContent {
  display: flex;
  align-content: center;
  align-items: center;
}
