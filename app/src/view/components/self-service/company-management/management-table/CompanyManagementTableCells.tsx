import {
  ClockIcon,
  InfoIcon,
  WarningCircleColoredIcon,
} from "@fourkites/elemental-atoms";
import { Toolt<PERSON> } from "@fourkites/elemental-tooltip";

import IntegrationStatus from "view/components/base/integration-indicators/IntegrationStatus";
import HighlightsIndicator from "view/components/base/highlights-indicators/HighlightsIndicator";
import CodesIndicator from "view/components/base/company-identifications/CodesIndicator";
import IdentificationsIndicator from "view/components/base/company-identifications/IdentificationsIndicator";

import { getFormattedDate } from "view/components/self-service/company-management/CompanyManagementUtils";

import { daysPassedSinceDate } from "view/components/base/DateUtils";

import { toKebabCase } from "view/components/base/StringUtils";

import styles from "./CompanyManagementTableCells.module.scss";

/*******************************************************************************
 * COMPANY IDENTIFICATION CELL
 ******************************************************************************/

export const CompanyIdentificationHeaderCell = ({
  t,
  companyType,
}: any): JSX.Element => {
  return (
    <div
      data-testid="company-name-cell-header-container"
      className={styles.identificationCell}
    >
      {companyType === "carrier" ? t("Carrier") : t("Customer")}
    </div>
  );
};

export const CompanyIdentificationCell = ({
  t,
  name,
  companyType,
  // Carrier specific
  identifications,
  codes,
  highlights,
  // Shipper specific
  customerType,
}: any): JSX.Element => {
  const companyCodes =
    codes != null ? [...codes?.default, ...codes?.custom] : [];
  const showCodes = companyCodes?.length > 0;

  const carrierUsdotAndCodes = showCodes ? (
    <CodesIndicator codes={codes} />
  ) : (
    <IdentificationsIndicator
      type="usdot"
      label="DOT"
      identifications={identifications}
    />
  );

  const customerDetails = (
    <label className={styles.identificationLabel}>
      {getCompanyTypeLabel(customerType)}
    </label>
  );

  return (
    <div
      className={styles.identificationCell}
      data-test-id="company-name-cell-container"
    >
      <span className={styles.nameAndHightlightsContainer}>
        {name}{" "}
        {companyType === "carrier" && (
          <HighlightsIndicator highlights={highlights} />
        )}
      </span>

      {companyType === "carrier" ? carrierUsdotAndCodes : customerDetails}
    </div>
  );
};

/*******************************************************************************
 * INVITATION DETAILS CELL
 ******************************************************************************/

export const InvitationDetailsCell = ({
  t,
  connectivity,
}: any): JSX.Element | null => {
  if (!connectivity) {
    return <EmptyCell />;
  }

  const { checks, is_responsive, last_request_at } = connectivity;

  const tooltipComponent = (
    <div>
      <span className={styles.toolTipContent}>
        <WarningCircleColoredIcon size="14px" />
        <b>{t(" Non Responsive Carrier")}</b>
      </span>
      <br />
      <span className={styles.toolTipContent}>
        {t(
          "This carrier has been flagged as 'Non-responsive'\n" +
            "since they haven't taken any action/responded in a while."
        )}
      </span>
    </div>
  );

  const isNonResponsive = is_responsive === false;
  const isNonResponsiveElement = isNonResponsive ? (
    <Tooltip
      key={"is-non-responsive-tooltip"}
      placement="bottom"
      className={styles.tootlTipMessage}
      contentComponent={tooltipComponent}
      theme="dark"
    >
      <span>
        <WarningCircleColoredIcon size="14px" />
      </span>
    </Tooltip>
  ) : null;

  const daysPassedSince = daysPassedSinceDate(last_request_at) > 0 ?
   daysPassedSinceDate(last_request_at) + " days ago" : "today";


  let detailsLabel = (checks?.is_onboarded_to_platform
    ? t("Request sent ")
    : t("Invited ")) + daysPassedSince;

  // TODO: since last request is not being returned by BE ye, returning just
  // Invited and Request Sent
  //detailsLabel = detailsLabel`${detailsLabel} ${timePassedSinceDate(
  //  last_request_at
  //)}`;

  return (
    <span className={styles.textCell}>
      {detailsLabel}
      <span className={styles.nonResponsiveIcon}>{isNonResponsiveElement}</span>
    </span>
  );
};

/*******************************************************************************
 * CAPABILITIES CELL
 ******************************************************************************/

export const CompanyCapabilitiesListCell = ({
  capabilities,
}: any): JSX.Element => {
  const enabledCapabilitiesCount = capabilities
    ?.map((c: any) => c.enabled)
    ?.filter(Boolean).length;

  return (
    <div>
      {capabilities?.map((c: any, index: number) => {
        const separator = index < enabledCapabilitiesCount - 1 ? ", " : "";
        return c.enabled ? (
          <span
            className={styles.textCell}
            data-test-id="capabilities"
            key={index}
          >{`${c.name}${separator}`}</span>
        ) : null;
      })}
    </div>
  );
};

/*******************************************************************************
 * TRACKING METHOD CELL
 ******************************************************************************/

export const TrackingMethodHeaderCell = ({ t }: any): JSX.Element => {
  const tooltipComponent = (
    <div>
      <span className={styles.toolTipContent}>
        <InfoIcon fill={"#fff"} size="20px" /> <b>{t(" Tracking Method")}</b>
      </span>
      <br />
      <span className={styles.toolTipContent}>
        {t("This is the method most\nfrequently used to track loads")}
      </span>
    </div>
  );

  return (
    <HeaderWithTooltipCell
      header={t("Tracking Method")}
      tooltipComponent={tooltipComponent}
    />
  );
};

export const TrackingMethodCell = ({
  locationDataIntegrations,
}: any): JSX.Element => {
  if (!locationDataIntegrations || locationDataIntegrations?.length === 0) {
    return <EmptyCell />;
  }

  // Order of precedence for integrations: API >> File >> GPS >> Mobile >> NULL
  let sortedIntegrations = [...locationDataIntegrations];
  sortedIntegrations?.sort((a: any, b: any) => {
    const integrationsOrder = {
      api: 1,
      file: 2,
      eld_gps: 3,
      mobile: 4,
    };
    //@ts-ignore
    const typeA = integrationsOrder[a?.type];
    //@ts-ignore
    const typeB = integrationsOrder[b?.type];
    return typeA < typeB ? -1 : typeA > typeB ? 1 : 0;
  });
  const integration = sortedIntegrations[0];

  return (
    <span>
      <IntegrationStatus
        status={integration?.status}
        type={integration?.type}
        showStatus={false}
      />
    </span>
  );
};

/*******************************************************************************
 * LOAD VOLUME CELL
 ******************************************************************************/

export const LoadVolumeHeaderCell = ({ t, updatedAt }: any): JSX.Element => {
  const tooltipComponent = (
    <div>
      <span className={styles.toolTipContent}>
        <InfoIcon fill={"#fff"} size="20px" /> <b>{t(" Load Volume")}</b>
      </span>
      <br />
      <span className={styles.toolTipContent}>
        {t("Based on loads delivered or expired in the last 30 days")}
      </span>
      <br />
      <span className={styles.toolTipContent}>
        {t("Last updated ")}
        <ClockIcon fill={"#fff"} size="20px" />
      </span>
      <span className={styles.toolTipContent}>
        {getFormattedDate(updatedAt)}
      </span>
    </div>
  );

  return (
    <HeaderWithTooltipCell
      header={t("Load Volume")}
      tooltipComponent={tooltipComponent}
    />
  );
};

export const LoadVolumeCell = ({ loadVolume }: any): JSX.Element => {
  if (loadVolume?.value == null) {
    return <EmptyCell />;
  }

  return (
    <span className={styles.textCell}>
      {Number(loadVolume?.value).toLocaleString()}
    </span>
  );
};

/*******************************************************************************
 * TRACKING QUALITY CELL
 ******************************************************************************/

export const TrackingQualityHeaderCell = ({
  t,
  updatedAt,
}: any): JSX.Element => {
  const tooltipComponent = (
    <div>
      <span className={styles.toolTipContent}>
        <InfoIcon fill={"#fff"} size="20px" /> <b>{t(" Tracking Quality")}</b>
      </span>
      <br />
      <span className={styles.toolTipContent}>
        {t("Based on loads delivered or expired in the last 30 days")}
      </span>
      <br />
      <span className={styles.toolTipContent}>
        {t("Last updated ")}
        <ClockIcon fill={"#fff"} size="20px" />
      </span>
      <span className={styles.toolTipContent}>
        { getFormattedDate(updatedAt) }
      </span>
    </div>
  );

  return (
    <HeaderWithTooltipCell
      header={t("Tracking %")}
      tooltipComponent={tooltipComponent}
    />
  );
};

export const TrackingQualityCell = ({ trackingQuality }: any): JSX.Element => {
  if (!trackingQuality?.mine) {
    return <EmptyCell />;
  }

  return (
    <span
      className={styles.textCell}
    >{`${trackingQuality?.mine?.tracked_percentage}%`}</span>
  );
};

/*******************************************************************************
 * ASSIGNMENT QUALITY CELL
 ******************************************************************************/

export const AssignmentQualityHeaderCell = ({
  t,
  updatedAt,
}: any): JSX.Element => {
  const tooltipComponent = (
    <div>
      <span className={styles.toolTipContent}>
        <InfoIcon fill={"#fff"} size="20px" /> <b>{t(" Assignment Quality")}</b>
      </span>
      <br />
      <span className={styles.toolTipContent}>
        {t("Based on loads delivered or expired in the last 30 days")}
      </span>
      <br />
      <span className={styles.toolTipContent}>
        {t("Last updated ")}
        <ClockIcon fill={"#fff"} size="20px" />
      </span>
      <span className={styles.toolTipContent}>
        { getFormattedDate(updatedAt) }
      </span>
    </div>
  );

  return (
    <HeaderWithTooltipCell
      header={t("Assignment %")}
      tooltipComponent={tooltipComponent}
    />
  );
};

export const AssignmentQualityCell = ({
  trackingQuality,
}: any): JSX.Element => {
  if (!trackingQuality?.mine) {
    return <EmptyCell />;
  }

  return (
    <span
      className={styles.textCell}
    >{`${trackingQuality?.mine?.assigned_percentage}%`}</span>
  );
};

/*******************************************************************************
 * HEADER WITH TOOLTIP
 ******************************************************************************/

export const HeaderWithTooltipCell = ({
  header,
  tooltipComponent,
}: any): JSX.Element => {
  return (
    <div key={header} className={styles.headerCell}>
      <Tooltip
        key={header}
        placement="bottom"
        className={styles.tootlTipMessage}
        contentComponent={tooltipComponent}
        theme="dark"
      >
        <span>
          <InfoIcon fill={"#21252a"} size="16px" />
        </span>
      </Tooltip>
      <label data-testid={toKebabCase(header)}>{header}</label>
    </div>
  );
};

/*******************************************************************************
 * EMPTY CELL
 ******************************************************************************/

export const EmptyCell = ({}: any): JSX.Element => {
  return <span className={styles.textCell}>--</span>;
};

/*******************************************************************************
 * HELPERS
 *******************************************************************************/

const getCompanyTypeLabel = (companyType: string) => {
  const labels: any = {
    broker: "Broker",
    shipper: "Shipper",
    managing_carrier: "Broker",
    //operating_carrier: "Operating Carrier",
  };

  return labels[companyType];
};
