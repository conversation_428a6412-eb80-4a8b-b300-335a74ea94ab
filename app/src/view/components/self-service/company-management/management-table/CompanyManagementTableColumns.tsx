import React from "react";
import { NetworkStatusType } from "state/BaseTypes";

import {
  AssignmentQualityHeaderCell,
  AssignmentQualityCell,
  CompanyCapabilitiesListCell,
  CompanyIdentificationCell,
  CompanyIdentificationHeaderCell,
  InvitationDetailsCell,
  LoadVolumeHeaderCell,
  LoadVolumeCell,
  TrackingMethodHeaderCell,
  TrackingMethodCell,
  TrackingQualityHeaderCell,
  TrackingQualityCell,
} from "./CompanyManagementTableCells";

import {
  ConnectivityStatusCell,
  ConnectivityStatusHeaderCell,
} from "./ConnectivityStatusCell";

import { RecommendedActionCell } from "./RecommendedActionCell";

const getColumns = (
  t: Function,
  mode: string,
  companyType: "carrier" | "shipper",
  networkStatus: NetworkStatusType,
  isSidePanelVisible: boolean
) => {
  const isManagingCarriers = companyType == "carrier";

  if (isManagingCarriers) {
    return getCarrierListColumns(t, mode, networkStatus, isSidePanelVisible);
  }

  return getCustomersListColumns(t, mode, networkStatus, isSidePanelVisible);
};

const getCarrierListColumns = (
  t: Function,
  mode: string,
  networkStatus: NetworkStatusType,
  isSidePanelVisible: boolean
) => {
  const columnsByStatus = {
    invited: [
      getIdentificationColumn(t, "carrier", networkStatus),
      getInvitationDetailsColumn(t),
      getConnectivityStatusColumn(t, "carrier", mode, networkStatus),
      ...(!isSidePanelVisible
        ? [getRecommendedActionColumn(t, "carrier", networkStatus)]
        : []),
    ],
    in_progress: [
      getIdentificationColumn(t, "carrier", networkStatus),
      getTrackingMethodColumn(t),
      getLoadVolumeColumn(t),
      getTrackingQualityColumn(t),
      getConnectivityStatusColumn(t, "carrier", mode, networkStatus),
      ...(!isSidePanelVisible
        ? [getRecommendedActionColumn(t, "carrier", networkStatus)]
        : []),
    ],
    connected: [
      getIdentificationColumn(t, "carrier", networkStatus),
      getTrackingMethodColumn(t),
      getLoadVolumeColumn(t),
      getTrackingQualityColumn(t),
      ...(!isSidePanelVisible
        ? [getConnectivityStatusColumn(t, "carrier", mode, networkStatus)]
        : []),
      ...(!isSidePanelVisible
        ? [getRecommendedActionColumn(t, "carrier", networkStatus)]
        : []),
    ],
    disconnected: [
      getIdentificationColumn(t, "carrier", networkStatus),
      // TODO: NEW-DESIGN - add disconnected on
      getConnectivityStatusColumn(t, "carrier", mode, networkStatus),
      ...(!isSidePanelVisible
        ? [getRecommendedActionColumn(t, "carrier", networkStatus)]
        : []),
    ],
    all: [
      getIdentificationColumn(t, "carrier", networkStatus),
      getTrackingMethodColumn(t),
      getLoadVolumeColumn(t),
      getTrackingQualityColumn(t),
      getConnectivityStatusColumn(t, "carrier", mode, networkStatus),
      ...(!isSidePanelVisible
        ? [getRecommendedActionColumn(t, "carrier", networkStatus)]
        : []),
    ],
  };

  return columnsByStatus[networkStatus];
};

const getCustomersListColumns = (
  t: Function,
  mode: string,
  networkStatus: NetworkStatusType,
  isSidePanelVisible: boolean
) => {
  const columnsByStatus = {
    invited: [
      getIdentificationColumn(t, "shipper", networkStatus),
      getCapabilitiesListColumn(t),
      getInvitationDetailsColumn(t),
      ...(!isSidePanelVisible
        ? [getRecommendedActionColumn(t, "shipper", networkStatus)]
        : []),
    ],
    in_progress: [
      getIdentificationColumn(t, "shipper", networkStatus),
      getLoadVolumeColumn(t),
      getAssignmentColumn(t),
      getTrackingQualityColumn(t),
      getConnectivityStatusColumn(t, "shipper", mode, networkStatus),
      ...(!isSidePanelVisible
        ? [getRecommendedActionColumn(t, "shipper", networkStatus)]
        : []),
    ],
    connected: [
      getIdentificationColumn(t, "shipper", networkStatus),
      getLoadVolumeColumn(t),
      getAssignmentColumn(t),
      getTrackingQualityColumn(t),
      ...(!isSidePanelVisible
        ? [getConnectivityStatusColumn(t, "shipper", mode, networkStatus)]
        : []),
      ...(!isSidePanelVisible
        ? [getRecommendedActionColumn(t, "shipper", networkStatus)]
        : []),
    ],
    disconnected: [
      getIdentificationColumn(t, "shipper", networkStatus),
      getCapabilitiesListColumn(t),
      // TODO: NEW-DESIGN - add disconnected on
      getConnectivityStatusColumn(t, "shipper", mode, networkStatus),
      ...(!isSidePanelVisible
        ? [getRecommendedActionColumn(t, "shipper", networkStatus)]
        : []),
    ],
    all: [
      getIdentificationColumn(t, "shipper", networkStatus),
      getLoadVolumeColumn(t),
      getAssignmentColumn(t),
      getTrackingQualityColumn(t),
      getConnectivityStatusColumn(t, "shipper", mode, networkStatus),
      ...(!isSidePanelVisible
        ? [getRecommendedActionColumn(t, "shipper", networkStatus)]
        : []),
    ],
  };

  return columnsByStatus[networkStatus];
};

/*******************************************************************************
 * COLUMN FUNCTIONS
 ******************************************************************************/

const getIdentificationColumn = (
  t: Function,
  companyType: "carrier" | "shipper",
  networkStatus: NetworkStatusType
) => ({
  accessor: "identification",
  sortType: sortIdentification,
  Header: <CompanyIdentificationHeaderCell t={t} companyType={companyType} />,
  Cell: (cellProps: any) => {
    const row = cellProps?.row?.original;
    return (
      <CompanyIdentificationCell
        t={t}
        companyType={companyType}
        name={row?.name}
        // Carrier company specific
        identifications={row?.identifications}
        codes={row?.carrier_codes}
        highlights={row?.highlights}
        // Shipper company specific
        customerType={row?.type}
      />
    );
  },
});

/*
 * Returns column definition for invitation details
 */
const getInvitationDetailsColumn = (t: Function) => ({
  id: "last_request_at",
  accessor: "connectivity",
  sortType: sortStatus,
  Header: t("Details"),
  disableSortBy: true,
  Cell: (cellProps: any) => (
    <InvitationDetailsCell t={t} connectivity={cellProps?.cell?.value} />
  ),
});

/*
 * Returns column definition for connectivity status
 */
const getConnectivityStatusColumn = (
  t: Function,
  companyType: "carrier" | "shipper",
  mode: string,
  networkStatus: NetworkStatusType
) => ({
  id: "connectivity_status",
  accessor: "connectivity",
  sortType: sortStatus,
  Header: (h: any) => {
    return (
      <ConnectivityStatusHeaderCell
        t={t}
        companyType={companyType}
        networkStatus={networkStatus}
      />
    );
  },
  Cell: (cellProps: any) => {
    const row = cellProps?.row?.original;
    return (
      <ConnectivityStatusCell
        t={t}
        mode={mode}
        networkStatusFilter={networkStatus}
        companyNetworkStatus={row?.status}
        connectivity={cellProps?.cell?.value}
      />
    );
  },
});

/*
 * Returns column definition for capabilities
 */
const getCapabilitiesListColumn = (t: Function) => ({
  id: "services_provided",
  accessor: "capabilities",
  Header: "Services Provided",
  disableSortBy: true,
  Cell: (cellProps: any) => (
    <CompanyCapabilitiesListCell capabilities={cellProps?.cell?.value} />
  ),
});

/*
 * Returns column definition for tracking method
 */
const getTrackingMethodColumn = (t: Function) => ({
  id: "tracking_method",
  accessor: "location_data_integrations",
  disableSortBy: true,
  Header: (h: any) => {
    return <TrackingMethodHeaderCell t={t} />;
  },
  Cell: (cellProps: any) => (
    <TrackingMethodCell locationDataIntegrations={cellProps?.cell?.value} />
  ),
});

/*
 * Returns column definition for load volume
 */
const getLoadVolumeColumn = (t: Function) => ({
  id: "load_volume",
  accessor: "load_volume",
  sortType: sortLoadVolume,
  Header: (h: any) => {
    const updatedAt = h?.data[0] ? h?.data[0]?.load_volume?.updated_at : null;

    return <LoadVolumeHeaderCell t={t} updatedAt={updatedAt} />;
  },
  Cell: (cellProps: any) => (
    <LoadVolumeCell loadVolume={cellProps?.cell?.value} />
  ),
});

/*
 * Returns column definition for tracking quality
 */
const getTrackingQualityColumn = (t: Function) => ({
  id: "my_tracked_percentage",
  accessor: "tracking_quality",
  sortType: sortTrackingQuality,
  Header: (h: any) => {
    const updatedAt = h?.data[0] ? h?.data[0]?.load_volume?.updated_at : null;
    return <TrackingQualityHeaderCell t={t} updatedAt={updatedAt} />;
  },
  Cell: (cellProps: any) => (
    <TrackingQualityCell trackingQuality={cellProps?.cell?.value} />
  ),
});

/*
 * Returns column definition for assignment
 */
const getAssignmentColumn = (t: Function) => ({
  id: "my_assigned_percentage",
  accessor: "tracking_quality",
  sortType: sortAssignmentQuality,
  Header: (h: any) => {
    const updatedAt = h?.data[0] ? h?.data[0]?.load_volume?.updated_at : null;
    return <AssignmentQualityHeaderCell t={t} updatedAt={updatedAt} />;
  },
  Cell: (cellProps: any) => (
    <AssignmentQualityCell trackingQuality={cellProps?.cell?.value} />
  ),
});

/*
 * Returns column definition for invitation details
 */
const getRecommendedActionColumn = (
  t: Function,
  companyType: "carrier" | "shipper",
  networkStatus: NetworkStatusType
) => ({
  id: "action",
  accessor: "connectivity",
  sortType: sortStatus,
  disableSortBy: true,
  Header: t("Action"),
  Cell: (cellProps: any) => {
    const row = cellProps?.row?.original;
    return (
      <RecommendedActionCell
        t={t}
        companyType={companyType}
        networkStatus={networkStatus}
        invitationStatus={row?.status}
        connectivity={cellProps?.cell?.value}
        trackingQuality={row?.tracking_quality}
      />
    );
  },
});

/*******************************************************************************
 * SORTING
 ******************************************************************************/

const sortLoadVolume = (rowA: any, rowB: any, desc: boolean) => {
  const valueA =
    rowA.original?.load_volume?.value != null
      ? rowA.original?.load_volume?.value
      : -1;
  const valueB =
    rowB.original?.load_volume?.value != null
      ? rowB.original?.load_volume?.value
      : -1;

  return generalIntegerSort(valueA, valueB, desc);
};

const sortTrackingQuality = (rowA: any, rowB: any, desc: boolean) => {
  const valueA =
    rowA.original?.tracking_quality?.mine?.tracked_percentage != null
      ? rowA.original?.tracking_quality?.mine?.tracked_percentage
      : -1;
  const valueB =
    rowB.original?.tracking_quality?.mine?.tracked_percentage != null
      ? rowB.original?.tracking_quality?.mine?.tracked_percentage
      : -1;

  return generalIntegerSort(valueA, valueB, desc);
};

const sortAssignmentQuality = (rowA: any, rowB: any, desc: boolean) => {
  const valueA =
    rowA.original?.tracking_quality?.mine?.assigned_percentage != null
      ? rowA.original?.tracking_quality?.mine?.assigned_percentage
      : -1;
  const valueB =
    rowB.original?.tracking_quality?.mine?.assigned_percentage != null
      ? rowB.original?.tracking_quality?.mine?.assigned_percentage
      : -1;

  return generalIntegerSort(valueA, valueB, desc);
};

const sortStatus = (rowA: any, rowB: any, desc: boolean) => {
  const valueA = rowA.original?.status;
  const valueB = rowB.original?.status;

  return generalStringSort(valueA, valueB, desc);
};

const sortIdentification = (rowA: any, rowB: any, desc: boolean) => {
  const valueA = rowA.original?.name?.toLowerCase();
  const valueB = rowB.original?.name?.toLowerCase();

  return generalStringSort(valueA, valueB, desc);
};

const generalIntegerSort = (valueA: any, valueB: any, desc: boolean) => {
  if (desc) {
    return Math.sign(valueA - valueB);
  }

  return Math.sign(valueB - valueA);
};

const generalStringSort = (valueA: any, valueB: any, desc: boolean) => {
  if (valueA === valueB) return 0;
  if (valueA == null) return 1;
  if (valueB == null) return -1;
  if (desc) return valueA < valueB ? -1 : 1;
  else return valueB < valueA ? -1 : 1;
};

export default getColumns;
