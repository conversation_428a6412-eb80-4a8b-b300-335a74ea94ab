@import "@fourkites/elemental-atoms/build/scss/colors/index";

.isOnboardedToPlatform {
  display: flex;
  flex-direction: row;
  width: max-content;
  align-items: center;
  padding: 0px 16px 0px 8px;
  gap: 8px;
  height: 28px;
  background: #ffecbd;
  border-radius: 30px;

  // font
  font-weight: 600;
  font-size: 14px;
  line-height: 28px;
  color: $color-neutral-900;
}

.isInvitationExpired {
  composes: isOnboardedToPlatform;
  background: $color-accent-cherry-300;
}

.isConnectedToMyNetwork {
  composes: isOnboardedToPlatform;
  background: #dbf6ff;

  > svg {
    transform: rotate(90deg);
  }
}

.dataSharingIcon{
  > svg {
    transform: rotate(90deg);
  }
}

.textCell {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: $color-neutral-900;
}

.statusCell {
  display: flex;
  align-items: center;
  align-content: center;

  > label {
    font-weight: 400;
    font-size: 14px;
    line-height: 17px;
    color: $color-neutral-900;
    margin-left: 8px;
  }
}

.tootlTipMessage {
  background-color: $color-neutral-900;
  box-shadow: 0 5px 10px 5px rgba(0, 0, 0, 0.509);
}

.toolTipContent {
  display: flex;
  align-content: center;
  align-items: center;

  > label {
    margin-left: 4px;
  }
}

.tooltipDivider {
  width: 100%;
  height: 1px;
  background-color: #888;
  margin-top: 6px;
  margin-bottom: 6px;
}
