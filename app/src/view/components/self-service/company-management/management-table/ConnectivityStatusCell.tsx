import {
  FourkitesIcon,
  XCircleInvertedIcon,
  SortIcon,
  CheckIcon,
  AlertCircleIcon,
  SlashErrorInvertedColoredIcon,
  InfoInvertedColoredIcon,
  XCircleInvertedColoredIcon,
  WarningTriangleColoredIcon,
} from "@fourkites/elemental-atoms";
import { Tooltip } from "@fourkites/elemental-tooltip";

import { NetworkStatusType } from "state/BaseTypes";

import { timePassedSinceDate } from "view/components/base/DateUtils";
import { startWithUpperCase } from "view/components/base/StringUtils";

import { HeaderWithTooltipCell } from "./CompanyManagementTableCells";

import styles from "./ConnectivityStatusCell.module.scss";

export const ConnectivityStatusHeaderCell = ({
  t,
  companyType,
  networkStatus,
}: any): JSX.Element => {
  const tooltipStatus = getTooltipStatus(t, companyType, networkStatus);
  const tooltipComponent = (
    <div>
      {tooltipStatus?.map((status: any, index: number) => {
        return (
          <div key={index}>
            <span className={styles.toolTipContent}>
              {status.icon && status.icon}{" "}
              <label>
                <b>{status.label}</b>
              </label>
            </span>
            <span className={styles.toolTipContent}>{status.description}</span>
            {index < tooltipStatus.length - 1 && (
              <div className={styles.tooltipDivider} />
            )}
          </div>
        );
      })}
    </div>
  );

  if (tooltipStatus?.length == 0) {
    return <>{t("Status")}</>;
  }

  return (
    <HeaderWithTooltipCell
      header={t("Status")}
      tooltipComponent={tooltipComponent}
    />
  );
};

export const ConnectivityStatusCell = ({
  t,
  mode,
  networkStatusFilter,
  companyNetworkStatus,
  connectivity,
}: any): JSX.Element | null => {
  if (!connectivity) {
    return <span className={styles.textCell}>--</span>;
  }

  if (networkStatusFilter === "invited") {
    return (
      <InvitedStatus
        t={t}
        isOnboardedToPlatform={connectivity?.checks?.is_onboarded_to_platform}
        companyNetworkStatus={companyNetworkStatus}
      />
    );
  }

  if (networkStatusFilter === "disconnected") {
    return (
      <DisconnectedStatus
        t={t}
        disconnectedAt={connectivity?.disconnected_at}
      />
    );
  }

  return (
    <ConnectedOrInProgressStatus
      t={t}
      mode={mode}
      companyNetworkStatus={companyNetworkStatus}
      connectivityStatus={connectivity?.status}
    />
  );
};

export const InvitedStatus = ({
  t,
  isOnboardedToPlatform,
  companyNetworkStatus,
}: any): JSX.Element | null => {
  let status = "";
  if (companyNetworkStatus === "expired") {
    status = companyNetworkStatus;
  } else {
    if (isOnboardedToPlatform) {
      status = "pending_data_sharing_acceptance";
    } else {
      status = "pending_account_creation";
    }
  }
  return <TooltipComponent t={t} status={status} />;
};

export const DisconnectedStatus = ({
  t,
  disconnectedAt,
}: any): JSX.Element | null => {
  if (disconnectedAt == null) {
    return <span className={styles.textCell}>{t("Disconnected")}</span>;
  }

  return (
    <span className={styles.textCell}>
      {`${t("Disconnected since")} ${timePassedSinceDate(disconnectedAt)}`}
    </span>
  );
};

export const ConnectedOrInProgressStatus = ({
  t,
  mode,
  companyNetworkStatus,
  connectivityStatus,
}: any): JSX.Element | null => {
  // TODO: BE is not returning the connectivity status for other modes yet,
  // so we need to handle the special of ftl
  if (mode !== "ftl") {
    const nonFtlStatus = {
      disconnected: t("Disconnected"),
      in_progress: t("In Progress"),
      connected: t("Connected"),
    };

    const label =
      //@ts-ignore
      nonFtlStatus[companyNetworkStatus] || t("Available to Connect");

    return <span className={styles.textCell}>{label}</span>;
  }

  // Show connected or disconnected if we don't have the connectivity status
  if (connectivityStatus == null) {
    return <span className={styles.textCell}>--</span>;
  }

  const status = getConnectivityStatusLabelDescriptionAndIcon(
    t,
    connectivityStatus,
    "carrier"
  );

  return (
    <span className={styles.statusCell}>
      {status.icon && status.icon} <label>{status.label}</label>
    </span>
  );
};

export const TooltipComponent = ({ t, status }: any): JSX.Element | null => {
  let toolTipDetails = getTooltipDetailsForConnectivityStatus(t, status);

  const tooltipComponent = (
    <div>
      <span className={styles.toolTipContent}>
        {toolTipDetails.tooltipIcon}
        <b>{toolTipDetails.tooltipStatus}</b>
      </span>
      <br />
      <span className={styles.toolTipContent}>
        {toolTipDetails.tooltipDescription}
      </span>
    </div>
  );

  return (
    <Tooltip
      placement="bottom"
      className={styles.tootlTipMessage}
      contentComponent={tooltipComponent}
      theme="dark"
    >
      <span>{toolTipDetails.icon}</span>
    </Tooltip>
  );
};

/*******************************************************************************
 * HELPERS
 ******************************************************************************/

const getTooltipStatus = (
  t: Function,
  companyType: string,
  networkStatus: NetworkStatusType
) => {
  const carrierStatusByNetworkType = {
    invited: [],
    in_progress: [
      "no_location_data_integration",
      "location_data_integration_pending",
      "location_data_integration_error",
      "special_instructions",
      "awaiting_customer_codes",
      "awaiting_loads_additions",
    ],
    connected: ["awaiting_loads_additions", "ok"],
    disconnected: [],
    all: [],
  };
  const shipperStatusByNetworkType = {
    invited: [],
    in_progress: [
      "no_location_data_integration",
      "location_data_integration_pending",
      "location_data_integration_error",
      "special_instructions",
      "awaiting_carrier_codes",
      "awaiting_loads_additions",
    ],
    connected: ["awaiting_loads_additions", "ok"],
    disconnected: [],
    all: [],
  };

  let status =
    companyType === "carrier"
      ? carrierStatusByNetworkType
      : shipperStatusByNetworkType;
  const allStatus = status[networkStatus] || [];

  return allStatus?.map((status: string) =>
    getConnectivityStatusLabelDescriptionAndIcon(t, status, companyType)
  );
};

export const getConnectivityStatusLabelDescriptionAndIcon = (
  t: Function,
  connectivityStatus: string,
  companyType: string
) => {
  const isCarrierView = companyType === "shipper";

  const statusMap = {
    account_creation_pending: {
      label: "No data",
      icon: <XCircleInvertedIcon fill="#cfd4da" />,
      description: "Status not available.",
    },
    no_location_data_integration: {
      label: t("No tracking method"),
      icon: <SlashErrorInvertedColoredIcon />,
      description: isCarrierView
        ? t("You have yet to add a tracking method.")
        : t("The carrier has yet to add a tracking method."),
    },
    location_data_integration_error: {
      label: t("GPS error"),
      icon: <XCircleInvertedColoredIcon />,
      description: isCarrierView
        ? t("You have added a GPS device, but there are errors in the setup.")
        : t(
            "The carrier has added a GPS device, but there are errors in the setup."
          ),
    },
    location_data_integration_pending: {
      label: t("GPS in progress"),
      icon: <WarningTriangleColoredIcon />,
      description: isCarrierView
        ? t(
            "You have started to add a GPS device, but has not completed the setup."
          )
        : t(
            "The carrier has started to add a GPS device," +
              " but has not completed the setup."
          ),
    },
    special_instructions: {
      label: t("Special instructions"),
      icon: <WarningTriangleColoredIcon />,
      description: isCarrierView
        ? t("Your special onboarding instructions were sent to the customer.")
        : t(
            "This carrier has special onboarding instructions. " +
              "Please see recommendations for details."
          ),
    },
    awaiting_carrier_codes: {
      label: t("Awaiting carrier codes"),
      icon: <InfoInvertedColoredIcon />,
      description: t(
        "The customer has yet to add the codes it uses to identify you."
      ),
    },
    awaiting_customer_codes: {
      label: t("Awaiting bill-to codes"),
      icon: <InfoInvertedColoredIcon />,
      description: t(
        "The carrier has successfully added a tracking method, " +
          "but has not added bill-to codes to start tracking."
      ),
    },
    awaiting_loads_additions: {
      label: t("Ready to Track"),
      icon: <WarningTriangleColoredIcon />,
      description: isCarrierView
        ? t(
            "The customer needs to assign loads to you in order to start tracking."
          )
        : t(
            "Loads have not yet been assigned to this carrier. FourKites is " +
              "unable to determine if the loads will track until loads are assigned."
          ),
    },
    ok: {
      label: t("All set"),
      icon: <CheckIcon fill="#24A148" />,
      description: t("Everything is ok!"),
    },
  };

  if ((connectivityStatus.toLowerCase() || "").startsWith("all_set")) {
    return statusMap["ok"];
  }

  if ((connectivityStatus.toLowerCase() || "").startsWith("awaiting_loads")) {
    return statusMap["awaiting_loads_additions"];
  }

  return (
    //@ts-ignore
    statusMap[connectivityStatus] || {
      label: "--",
      description: t("Status not available."),
      icon: <XCircleInvertedIcon fill="#cfd4da" />,
    }
  );
};

export function getTooltipDetailsForConnectivityStatus(
  t: Function,
  status: string
) {
  let toolTipDetails = {
    tooltipStatus: "",
    tooltipDescription: "",
    tooltipIcon: <></>,
    icon: <></>,
  };

  switch (status) {
    case "expired":
      toolTipDetails.tooltipStatus = t(" Invitation Expired");
      toolTipDetails.tooltipDescription = t(
        "The carrier’s invitation expired after 21 days, due to security " +
          "precautions.Please resend an invitation to connect with this carrier."
      );
      toolTipDetails.tooltipIcon = (
        <AlertCircleIcon fill={"#ffffff"} size="20px" />
      );
      toolTipDetails.icon = (
        <span className={styles.isInvitationExpired}>
          <AlertCircleIcon />
          {t("Invitation Expired")}
        </span>
      );
      break;

    case "pending_data_sharing_acceptance":
      toolTipDetails.tooltipStatus = t(" Data Sharing Requested");
      toolTipDetails.tooltipDescription = t(
        "You are connected to the carrier, but they are " +
          "yet to share their location & tracking data with you"
      );
      toolTipDetails.tooltipIcon = (
        <span className={styles.dataSharingIcon}>
          <SortIcon fill={"#ffffff"} size="20px" />
        </span>
      );
      toolTipDetails.icon = (
        <span className={styles.isConnectedToMyNetwork}>
          <SortIcon />
          {t("Data Sharing Requested")}
        </span>
      );
      break;

    case "pending_account_creation":
      toolTipDetails.tooltipStatus = t(" Pending FourKites account creation");
      toolTipDetails.tooltipDescription = t(
        "You have invited the carrier to join the FourKites® network, " +
          "but they are yet to sign up and create an account"
      );
      toolTipDetails.tooltipIcon = (
        <FourkitesIcon fill={"#ffffff"} size="20px" />
      );
      toolTipDetails.icon = (
        <span className={styles.isOnboardedToPlatform}>
          <FourkitesIcon />
          {t("Pending FourKites account creation")}
        </span>
      );
      break;
    default:
      break;
  }
  return toolTipDetails;
}
