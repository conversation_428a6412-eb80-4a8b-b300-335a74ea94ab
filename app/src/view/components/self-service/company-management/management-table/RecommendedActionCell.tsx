import React from "react";
import { useHistory } from "react-router-dom";

import {
  CheckIcon,
  LinkButton,
  Button,
  CaretRightIcon,
} from "@fourkites/elemental-atoms";

import { externalUrls, DIRECT_ASSIGNMENT_APP_URL } from "api/http/apiUtils";

import { useAppSelector } from "state/hooks";
import { UsersState } from "state/modules/Users";

import { dataIntegrationRoutes } from "router/shipper/ShipperDataIntegrationsRouter";
import { carrierTrackingIntegrationsRoutes } from "router/carrier/CarrierTrackingIntegrationsRouter";

import { NetworkStatusType } from "state/BaseTypes";

import { EmptyCell } from "./CompanyManagementTableCells";

import styles from "./RecommendedActionCell.module.scss";

export const RecommendedActionCell = ({
  t,
  companyType,
  networkStatus,
  invitationStatus,
  connectivity,
  trackingQuality,
}: any): JSX.Element | null => {
  const history = useHistory();

  // User
  const companyName: string = useAppSelector(
    UsersState.selectors.getCompanyName
  );
  const directAssignmentInfo = useAppSelector(
    UsersState.selectors.getCompanyDirectAssignmentInfo
  );

  if (!connectivity) {
    return <EmptyCell />;
  }

  if (companyType === "carrier") {
    return getActionLinkForCarrier(
      t,
      history,
      networkStatus,
      invitationStatus,
      connectivity?.status,
      trackingQuality
    );
  }

  return getActionLinkForCustomer(
    t,
    history,
    networkStatus,
    connectivity?.status,
    trackingQuality,
    directAssignmentInfo,
    companyName
  );
};

const getActionLinkForCarrier = (
  t: Function,
  history: any,
  networkStatus: NetworkStatusType,
  invitationStatus: string,
  status: string,
  trackingQuality: any
) => {
  switch (networkStatus) {
    case "invited":
      const isInvitationExpired = invitationStatus === "expired";
      if (isInvitationExpired) {
        return <ActionLink title={t("Resend Invitation")} />;
      }
      return <ActionLink title={t("Send reminder")} />;

    case "in_progress":
      // TODO: we need to add send reminder when we have reminder of tracking method
      return <ActionLink title={t("View recommendations")} />;

    case "connected":
      const isAwaitingLoads = status === "awaiting_loads_additions";
      if (isAwaitingLoads) {
        return (
          <ActionLink
            title={t("Create Loads")}
            onClick={(e: any) => {
              e.stopPropagation();
              // TODO: handle other modes
              history.push(
                `${dataIntegrationRoutes.batchUploads}?show-additions-modal=1&mode=ftl`
              );
            }}
          />
        );
      }

      if (!isAwaitingLoads) {
        return <ActionLink title={t("View recommendations")} />;
      }

      return <EmptyCell />;
    case "disconnected":
      return <ActionLink title={t("Reconnect")} />;

    default:
      return <EmptyCell />;
  }
};

const getActionLinkForCustomer = (
  t: Function,
  history: any,
  networkStatus: NetworkStatusType,
  status: string,
  trackingQuality: any,
  directAssignmentInfo: any,
  companyName: string
) => {
  const directAssignmentUrl = `${DIRECT_ASSIGNMENT_APP_URL}/${
    directAssignmentInfo?.apiToken
  }/${btoa(companyName)}`;

  switch (networkStatus) {
    case "invited":
      return (
        <Button theme="tertiary" data-test-id="accept-request">
          <CheckIcon fill="#0e65e5" iconClass={"button-icon-left"} />
          {t("Accept Request")}
        </Button>
      );

    case "in_progress":
      // Tracking method error
      if (
        [
          "no_location_data_integration",
          "location_data_integration_error",
          "location_data_integration_pending",
        ].includes(status)
      ) {
        return (
          <ActionLink
            title={t("Complete GPS Setup")}
            onClick={(e: any) => {
              e.stopPropagation();
              history.push(carrierTrackingIntegrationsRoutes?.eldGps);
            }}
          />
        );
      }

      // Waiting loads additions
      if (status === "awaiting_loads_additions") {
        return <EmptyCell />;
      }

      // If other error, we redirect to recommendations
      return <ActionLink title={t("View recommendations")} />;

    case "connected":
      const needsAssignment = trackingQuality?.mine?.assigned_percentage < 1;

      // If not assigned, we show link to assign loads
      if (needsAssignment) {
        return (
          <AssignmentAction t={t} directAssignmentUrl={directAssignmentUrl} />
        );
      }

      // If loads are not tracking because of consistency, we show recommendations
      if (!needsAssignment && trackingQuality?.mine?.tracked_percentage === 0) {
        return <ActionLink title={t("View recommendations")} />;
      }

      return <EmptyCell />;

    case "disconnected":
      // TODO: we dont have reconnect option for carriers yet
      return <EmptyCell />;

    default:
      return <EmptyCell />;
  }
};

const ActionLink = ({ title, onClick }: any) => {
  return (
    <div className={styles.container} onClick={onClick}>
      <Button size="small" variant="flat">
        {title}
        <CaretRightIcon fill="#0e65e5" iconClass={"button-icon-right"} />
      </Button>
    </div>
  );
};

const AssignmentAction = ({ t, directAssignmentUrl }: any) => {
  return (
    <div className={styles.container}>
      <LinkButton
        theme="primary"
        variant="flat"
        target="_blank"
        href={directAssignmentUrl}
        data-test-id="link-asset-assign"
      >
        {t("Assign loads")}
        <CaretRightIcon fill="#0e65e5" iconClass={"button-icon-right"} />
      </LinkButton>
    </div>
  );
};
