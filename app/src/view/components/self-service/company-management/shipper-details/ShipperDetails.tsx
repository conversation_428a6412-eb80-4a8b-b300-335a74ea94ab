import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { Spinner } from "@fourkites/elemental-loading-indicator";

import { useAppDispatch, useAppSelector } from "state/hooks";
import { ShippersNetworkDetailsState } from "state/modules/carrier/ShippersNetworkDetails";
import { ShippersNetworkState } from "state/modules/carrier/ShippersNetwork";

import { showToast } from "view/components/base/toast/Toast";

import CompanyDetailsHeader from "../company-details/company-header/CompanyDetailsHeader";
import CompanyContacts from "../company-details/contacts/CompanyContacts";
import CompanyInfo from "../company-details/info/CompanyInfo";
import RecommendedActions from "../company-details/recommended-actions/RecommendedActions";

import ShipperConnectionRequests from "./connection-requests/ShipperConnectionRequests";
import ShipperCapabilities from "./capabilities/ShipperCapabilities";
import CustomerCodes from "./codes/CustomerCodes";
import LoadMatching from "./load-matching/LoadMatching";

import DisconnectShipperModal from "./modals/DisconnectShipperModal";

import styles from "./ShipperDetails.module.scss";
import ShipperDetailsProps from "./ShipperDetails.types";
import NetworkHealth from "../carrier-details/network-health/NetworkHealth";

const ShipperDetails = ({
  mode,
  shipperId,
  carrierId,
  networkId,
  shipperData,
  onClosePanel,
}: ShipperDetailsProps) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * REDUX
   ****************************************************************************/

  const dispatch = useAppDispatch();

  // Carrier details
  const shipperDetails = useAppSelector(
    ShippersNetworkDetailsState.selectors.shipperDetails()
  );
  const isLoadingShipperDetails: boolean = useAppSelector(
    ShippersNetworkDetailsState.selectors.isLoadingShipperDetails()
  );

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [showModal, setShowModal] = useState<boolean>(false);

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  useEffect(() => {
    const modesWithoutAdditions = ["ltl", "parcel", "ocean"].includes(mode);

    const networkStatus = shipperData?.status;
    const isAddedToNetwork = networkStatus != null || modesWithoutAdditions;

    // Only fetch details of carriers in the network
    if (!isAddedToNetwork) {
      return;
    }

    // Otherwise, get shipper details
    dispatch(
      ShippersNetworkDetailsState.actions.retrieveShipperDetails({
        carrierId,
        shipperId,
        mode,
      })
    );
    // eslint-disable-next-line
  }, [shipperId, carrierId, mode, shipperData]);

  const onClickDisconnect = () => {
    setShowModal(true);
  };

  const onConfirmDisconnect = async () => {
    setShowModal(false);

    const disconnectResponse = await dispatch(
      ShippersNetworkState.actions.disconnectShipper({
        carrierId,
        shipperId,
        mode,
      })
    );
    if ("error" in disconnectResponse) {
      // TODO: handle error
      return;
    }

    showToast(t("The customer was disconnected from your network."), "", "ok");

    fetchShipperData();
  };

  const fetchShipperData = () => {
    // Updates table component
    dispatch(ShippersNetworkState.actions.getShippers({ carrierId, mode }));

    // Updates details pane component
    dispatch(
      ShippersNetworkDetailsState.actions.retrieveShipperDetails({
        carrierId,
        shipperId,
        mode,
      })
    );
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const connectionPending = ["invited", "pending"].includes(
    shipperDetails?.status
  );
  const isDisconnected = shipperDetails?.status === "disconnected";

  if (isLoadingShipperDetails) {
    return (
      <div key={shipperId} className={styles.container}>
        <div className={styles.loader}>
          <Spinner isLoading size="medium" />
        </div>
      </div>
    );
  }

  return (
    <div key={shipperId} className={styles.container}>
      <div className={styles.wrapper}>
        <CompanyDetailsHeader
          mode={mode}
          companyType={"shipper"}
          isLoading={isLoadingShipperDetails}
          companyDetails={shipperDetails}
          onClickDisconnect={onClickDisconnect}
          onClosePanel={onClosePanel}
        />

        <NetworkHealth
          managedCompanyType={"shipper"}
          isLoading={isLoadingShipperDetails}
          trackingQuality={shipperDetails?.tracking_quality}
          loadVolume={shipperDetails?.load_volume?.value}
          locationDataIntegrations={shipperDetails?.location_data_integrations}
          assetAssignmentIntegrations={
            shipperDetails?.asset_assignment_integrations
          }
        />

        {connectionPending && (
          <ShipperConnectionRequests
            mode={mode}
            shipperId={shipperId}
            carrierId={carrierId}
            networkId={networkId}
          />
        )}

        {!connectionPending && !isDisconnected && (
          <RecommendedActions
            mode={mode}
            managedCompanyType={"shipper"}
            isLoading={isLoadingShipperDetails}
            trackedPercentage={
              shipperDetails?.tracking_quality?.mine?.tracked_percentage
            }
            assignedPercentage={
              shipperDetails?.tracking_quality?.mine?.assigned_percentage
            }
            connectivityStatus={shipperDetails?.connectivity?.status}
            specialInstructions={
              shipperDetails?.special_onboarding_instructions
            }
            recommendationType={shipperDetails?.recommendation_type}
          />
        )}

        {!isDisconnected && (
          <ShipperCapabilities
            capabilities={shipperDetails?.capabilities || []}
          />
        )}

        {!connectionPending && !isDisconnected && (
          <CustomerCodes
            shipperId={shipperId}
            carrierId={carrierId}
            mode={mode}
            isLoading={isLoadingShipperDetails}
            customerCodes={shipperDetails?.customer_codes}
            assetAssignmentIntegrations={
              shipperDetails?.asset_assignment_integrations
            }
          />
        )}

        <LoadMatching
          isLoading={isLoadingShipperDetails}
          loadsAdditionsSettings={shipperDetails?.loads_additions_settings}
        />

        <CompanyInfo
          type={shipperDetails?.type}
          address={shipperDetails?.address}
          trackingModes={shipperDetails?.tracking_modes || []}
          fleetSize={shipperDetails?.fleet_size}
          isLoading={isLoadingShipperDetails}
          identifications={shipperDetails?.identifications}
        />

        <CompanyContacts 
        contacts={shipperDetails?.contacts || []}
        companyId={shipperId}
        scac={shipperDetails?.reference_codes?.custom?.join(",") || ''} 
        companyName={shipperDetails?.name || ''}
        />
      </div>

      <DisconnectShipperModal
        showModal={showModal}
        onClose={() => setShowModal(false)}
        onDisconnect={onConfirmDisconnect}
      />
    </div>
  );
};

export default ShipperDetails;
