import React from "react";

import { Accordion } from "@fourkites/elemental-accordion";

import ShipperCapabilitiesProps from "./ShipperCapabilities.types";

const ShipperCapabilities = ({ capabilities }: ShipperCapabilitiesProps) => {
  return (
    <Accordion title={"Capabilities"} defaultOpened>
      <div>
        {capabilities?.map((capability: any, index: number) => {
          return capability?.enabled ? (
            <span key={index}>
              {`• ${capability.name}`}
              <br />
            </span>
          ) : null;
        })}
      </div>
    </Accordion>
  );
};

export default ShipperCapabilities;
