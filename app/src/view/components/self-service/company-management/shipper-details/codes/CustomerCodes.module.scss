@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.container {
  display: flex;
  flex-direction: column;
}

.title {
  font: $typography-standard-bold;
  color: $color-neutral-600;
  margin-bottom: 8px;
}

.content {
  display: flex;
  flex-direction: column;

  > label {
    margin-bottom: 12px;
  }
}

.infoMessage {
  width: 100%;
  margin: 15px 0;
}

.toolTipInfoMessageContainer {
  display: flex;
  flex-direction: column;
}

.toolTipHeadingContainer {
  display: flex;
}

.tootTipContainer {
  padding-right: 10px;
}

.tootlTipMessage {
  background-color: $color-neutral-900;
  box-shadow: 0 5px 10px 5px rgba(0, 0, 0, 0.509);
}
