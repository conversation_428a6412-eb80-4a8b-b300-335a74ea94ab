import React from "react";
import { useTranslation } from "react-i18next";

import { AlertTriangleIcon, InfoIcon } from "@fourkites/elemental-atoms";
import { Spinner } from "@fourkites/elemental-loading-indicator";
import { Accordion } from "@fourkites/elemental-accordion";
import { Tooltip } from "@fourkites/elemental-tooltip";

import { useAppDispatch, useAppSelector } from "state/hooks";

import MultiInput from "view/components/base/multi-input/MultiInput";

import { ShippersNetworkDetailsState } from "state/modules/carrier/ShippersNetworkDetails";

import styles from "./CustomerCodes.module.scss";
import CustomerCodesProps from "./CustomerCodes.types";

const CustomerCodes = ({
  shipperId,
  carrierId,
  mode,
  isLoading,
  customerCodes,
  assetAssignmentIntegrations,
}: CustomerCodesProps) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  const isEditingCustomerCodes: boolean = useAppSelector(
    ShippersNetworkDetailsState.selectors.isEditingCustomerCodes()
  );

  const onAddCustomerCode = (code: string) => {
    dispatch(
      ShippersNetworkDetailsState.actions.createCustomerCode({
        carrierId,
        shipperId,
        mode,
        code: code.toUpperCase(),
      })
    );
  };

  const onRemoveCustomerCode = (code: string) => {
    dispatch(
      ShippersNetworkDetailsState.actions.deleteCustomerCode({
        carrierId,
        shipperId,
        mode,
        code: code.toUpperCase(),
      })
    );
  };

  // We only show customer codes alerts if carrier has automated assignment
  const hasAutomatedAssignment =
    assetAssignmentIntegrations?.find((i: any) => i?.type === "automated") ||
    null;
  const shouldShowAlert =
    customerCodes?.custom?.length === 0 &&
    customerCodes?.default.length === 0 &&
    hasAutomatedAssignment != null;

  const actionButton = shouldShowAlert
    ? {
        component: (
          <Tooltip
            placement="left"
            text={t("You need to provide at least 1 customer code")}
            theme="dark"
          >
            <span>
              <AlertTriangleIcon size="24px" fill="#da1e28" />
            </span>
          </Tooltip>
        ),
        onClick: () => {},
      }
    : undefined;

  const infoMessage =
    "You will first need to add the bill-to or customer code for this shipper " +
    "to your TMS integration with FourKites. After you have " +
    "added it to your TMS integration, please add them here. Providing " +
    "these helps FourKites diagnose your connection, in case of issues.";

  const contentComponent = (
    <div>
      <span className={styles.toolTipHeadingContainer}>
        <InfoIcon fill={"#0e65e5"} size="20px" /> <b>{t(" Bill-to Codes")}</b>
      </span>
      <br />
      {t(
        "Bill-to or shipper/customer codes are\n" +
          "reference codes that you use to identify your\n" +
          "customer in your TMS. The data that your\n" +
          "TMS sends to FourKites, is often filtered by\n" +
          "these codes, and you will need to add these\n" +
          "codes to your filter for each new shipper you\n" +
          "connect with on FourKites."
      )}
    </div>
  );

  const multiInputValidation = {
    maxNumberOfValues: 150,
    maxValueLength: 64,
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <Accordion
      title={t("Customer Bill-to Codes")}
      defaultOpened
      actionButton={actionButton}
    >
      <>
        {isLoading && (
          <div className={styles.container}>
            <Spinner isLoading size="small" />
          </div>
        )}

        {!isLoading && (
          <div className={styles.container}>
            <div className={styles.content}>
              <div className={styles.toolTipInfoMessageContainer}>
                <div className={styles.toolTipHeadingContainer}>
                  <Tooltip
                    placement="right"
                    className={styles.tootlTipMessage}
                    contentComponent={contentComponent}
                    theme="dark"
                  >
                    <span className={styles.tootTipContainer}>
                      <InfoIcon fill={"#868e96"} size="20px" />
                    </span>
                  </Tooltip>
                  <label>
                    <b>
                      {t(
                        "If you use an API or File integration with FourKites"
                      )}
                      &#174;
                    </b>
                  </label>
                </div>
                <label className={styles.infoMessage}>{t(infoMessage)}</label>
              </div>

              <MultiInput
                label={t("Customer bill-to codes")}
                placeholder={t("eg: XYRM")}
                values={customerCodes?.custom || []}
                defaultValues={customerCodes?.default || []}
                onAddValue={onAddCustomerCode}
                onRemoveValue={onRemoveCustomerCode}
                disabled={isEditingCustomerCodes}
                validation={multiInputValidation}
              />
              <div>
                {isEditingCustomerCodes && <Spinner isLoading size="small" />}
              </div>
            </div>
          </div>
        )}
      </>
    </Accordion>
  );
};

export default CustomerCodes;
