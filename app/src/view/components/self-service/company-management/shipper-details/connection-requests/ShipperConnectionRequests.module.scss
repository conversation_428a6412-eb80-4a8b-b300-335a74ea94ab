@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.container {
  display: flex;
  flex-direction: column;
  padding-left: 16px;
  margin-bottom: 16px;

  > label[id="title"] {
    display: flex;
    align-items: center;
    align-content: center;
    font-weight: 700;
    font-size: 14px;
    line-height: 17px;
  }

  > label[id="description"] {
    margin-top: 4px;
    font-weight: 400;
    font-size: 14px;
    line-height: 17px;
    margin-bottom: 12px;
  }

  > div {
    display: flex;
    width: 100%;
    justify-content: flex-start;

    > button {
      display: flex;
      align-items: center;
      align-content: center;
      justify-content: center;
      margin-right: 16px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  > div[id="loader"] {
    display: flex;
    width: 100%;
    justify-content: center;
  }
}

.sortIcon {
  margin-right: 4px;
  transform: rotate(90deg);
}
