import React from "react";
import { useTranslation } from "react-i18next";

import { <PERSON><PERSON>, CheckIcon, SortIcon } from "@fourkites/elemental-atoms";
import { Spinner } from "@fourkites/elemental-loading-indicator";

import { useAppDispatch, useAppSelector } from "state/hooks";
import { ShippersNetworkState } from "state/modules/carrier/ShippersNetwork";
import { ShippersNetworkDetailsState } from "state/modules/carrier/ShippersNetworkDetails";

import styles from "./ShipperConnectionRequests.module.scss";
import ShipperConnectionRequestsProps from "./ShipperConnectionRequests.types";

const ShipperConnectionRequests = ({
  mode,
  shipperId,
  carrierId,
  networkId,
}: ShipperConnectionRequestsProps) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * REDUX
   ****************************************************************************/

  const dispatch = useAppDispatch();

  const isEditingCarrierInvitationsByMode: boolean = useAppSelector(
    ShippersNetworkDetailsState.selectors.isEditingShipperDetails()
  );

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const onUpdateConnectionRequest = async (accepted: boolean) => {
    const response = await dispatch(
      ShippersNetworkDetailsState.actions.acceptShipperInvitation({
        mode,
        carrierId,
        networkId,
        accepted,
      })
    );

    if ("error" in response) {
      // TODO: handle error
      return;
    }

    await dispatch(
      ShippersNetworkState.actions.getShippers({ carrierId, mode })
    );

    await dispatch(
      ShippersNetworkDetailsState.actions.retrieveShipperDetails({
        carrierId,
        shipperId,
        mode,
      })
    );
  };

  const onAcceptConnectionRequest = () => {
    onUpdateConnectionRequest(true);
  };

  // TODO
  //const onRejectConnectionRequest = () => {
  //  onUpdateConnectionRequest(false);
  //};

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  // TODO: what to do after reject?

  return (
    <div className={styles.container}>
      <label id="title">
        <SortIcon iconClass={styles.sortIcon} />
        {t("Data Sharing Requested")}
      </label>
      <label id="description">
        {t("Customer requested to perform data sharing with you")}
      </label>

      {isEditingCarrierInvitationsByMode ? (
        <div id="loader">
          <Spinner isLoading size="medium" />
        </div>
      ) : (
        <div>
          <Button
            className={styles.carrierAuthorizationButton}
            size="large"
            onClick={onAcceptConnectionRequest}
          >
            <CheckIcon fill="white" iconClass={"button-icon-left"} />
            {t("Accept")}
          </Button>
          {/*
          <Button
            className={styles.carrierAuthorizationButton}
            size="large"
            theme="secondary"
            variant="outline"
            onClick={onRejectConnectionRequest}
          >
            <XIcon fill="#21252A" iconClass={"button-icon-left"} />
            {t("Reject")}
          </Button>
          */}
        </div>
      )}
    </div>
  );
};

export default ShipperConnectionRequests;
