@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container {
  color: $color-neutral-900;

  .highlightText {
    font-weight: 600;
  }

  .infoMessage {
    width: 100%;
    margin: 15px 0;
  }

  .loadReferenceTitle {
    font-weight: 600;
    line-height: 20px;
    display: block;
  }

  .loadReferenceNumbers {
    div {
      margin: 10px 10px 0 0;
    }
  }

  .noLoadFormatsData {
    text-align: center;
    margin-top: 20px;
    color: $color-neutral-600;

    img {
      vertical-align: middle;
      margin-right: 10px;
    }
  }
}

.toolTipInfoMessageContainer {
  display: flex;
  flex-direction: column;
}

.tootlTipMessage {
  background-color: $color-neutral-900;
  box-shadow: 0 5px 10px 5px rgba(0, 0, 0, 0.509);
}

.toolTipHeadingContainer {
  display: flex;
  align-items: center;
  align-content: center;
}

.tootTipContainer {
  display: flex;
  align-items: center;
  align-content: center;
  padding-right: 10px;
}
