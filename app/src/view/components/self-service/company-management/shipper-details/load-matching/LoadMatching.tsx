import React from "react";
import { useTranslation } from "react-i18next";

import { Spin<PERSON> } from "@fourkites/elemental-loading-indicator";
import { Accordion } from "@fourkites/elemental-accordion";
import { Chip } from "@fourkites/elemental-chip";
import { InfoIcon } from "@fourkites/elemental-atoms";
import { Tooltip } from "@fourkites/elemental-tooltip";

import noDataIcon from "assets/img/noData.svg";

import styles from "./LoadMatching.module.scss";
import LoadMatchingProps from "./LoadMatching.types";

const LoadMatching = ({
  isLoading,
  loadsAdditionsSettings,
}: LoadMatchingProps) => {
  const { t } = useTranslation();

  const hasLoadMatchingData =
    loadsAdditionsSettings?.load_number_formats?.length;

  /*****************************************************************************
   * RENDER
   ****************************************************************************/
  const loadMatchingData = hasLoadMatchingData ? (
    <LoadMatchingData loadsAdditionsSettings={loadsAdditionsSettings} />
  ) : (
    <NoLoadMatchingData />
  );

  return (
    <Accordion title={t("Sample Load Reference Numbers")} defaultOpened>
      <div className={styles.container}>
        {isLoading ? <Spinner isLoading size="small" /> : loadMatchingData}
      </div>
    </Accordion>
  );
};

export default LoadMatching;

const LoadMatchingData = ({ loadsAdditionsSettings }: any) => {
  const { t } = useTranslation();

  const infoMessage =
    "Please make sure load reference numbers for this shipper in the below " +
    "format are included in your integration with FourKites.";

  return (
    <div>
      <div className={styles.toolTipInfoMessageContainer}>
        <div className={styles.toolTipHeadingContainer}>
          <ToolTipComponent />
          <label className={styles.highlightText}>
            <b>
              {t("If you use an API or File integration with FourKites")}
              &#174;
            </b>
          </label>
        </div>
        <label className={styles.infoMessage}>{t(infoMessage)}</label>
      </div>

      <span className={styles.loadReferenceTitle}>
        {t("Example shipper load reference")}
      </span>
      <div className={styles.loadReferenceNumbers}>
        {loadsAdditionsSettings?.load_number_formats?.map(
          (loadNumberFormat: string, index: number) => (
            <Chip
              key={index}
              size="small"
              shape="rounded"
              theme="light"
              closeable={false}
            >
              {loadNumberFormat}
            </Chip>
          )
        )}
      </div>
    </div>
  );
};

const NoLoadMatchingData = () => {
  const { t } = useTranslation();

  return (
    <div>
      <div className={styles.toolTipInfoMessageContainer}>
        <div className={styles.toolTipHeadingContainer}>
          <ToolTipComponent />
          <label>
            <b>
              {t(
                "These are the numbers or reference codes" +
                  " that the shipper uses to communicate with you."
              )}
            </b>
          </label>
        </div>
        <div></div>
      </div>

      <div className={styles.noLoadFormatsData}>
        <img src={noDataIcon} />
        <span> {t("No data provided by shipper")}</span>
      </div>
    </div>
  );
};

const ToolTipComponent = () => {
  const { t } = useTranslation();

  const contentComponent = (
    <div>
      <span className={styles.toolTipHeadingContainer}>
        <InfoIcon fill={"#0e65e5"} size="20px" /> <b>{t(" Load Reference")}</b>
      </span>
      <br />
      {t(
        "The load reference number is the unique\n" +
          "identifier of this load between you and this\n" +
          "shipper. It typically has a standarad format."
      )}
      <br />
      <br />
      {t(
        "Please ensure the data field with numbers in\n" +
          "the below format are included in your\n" +
          "integration with fourkites.\n"
      )}
    </div>
  );

  return (
    <Tooltip
      placement="right"
      className={styles.tootlTipMessage}
      contentComponent={contentComponent}
      theme="dark"
    >
      <span className={styles.tootTipContainer}>
        <InfoIcon fill={"#868e96"} size="20px" />
      </span>
    </Tooltip>
  );
};
