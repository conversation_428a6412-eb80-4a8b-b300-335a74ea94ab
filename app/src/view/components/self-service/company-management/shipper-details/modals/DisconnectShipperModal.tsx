import React from "react";
import { useTranslation } from "react-i18next";

import { Modal } from "@fourkites/elemental-modal";

const DisconnectShipperModal: React.FC<{
  showModal: boolean;
  onClose: Function;
  onDisconnect: Function;
}> = ({ showModal, onClose, onDisconnect }: any) => {
  const { t } = useTranslation();

  const subtitle = "Are you sure you want to disconnect?";

  const message = t(
    "Any network relation between you and the customer will be revoked, " +
      "including bill-to codes."
  );

  return (
    <Modal
      size="small"
      title={t("Disconnect Customer")}
      subtitle={subtitle}
      show={showModal}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: onClose,
      }}
      saveButtonProps={{
        label: t("Disconnect"),
        onClick: onDisconnect,
      }}
    >
      {message}
    </Modal>
  );
};

export default DisconnectShipperModal;
