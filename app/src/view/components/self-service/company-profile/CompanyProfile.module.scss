@import "@fourkites/elemental-atoms/build/scss/colors/index";

.formContainer {
  display: flex;

  > div[id="inputs-form"] {
    width: 100%;
    flex: 7;
    margin-right: 88px;
  }

  > div[id="logo-container"] {
    flex: 1;
  }
}

.formColumn {
  display: flex;
  flex-direction: column;
  width: 100%;

  &:first-child {
    margin-right: 44px;
  }

  &:last-child {
    margin-left: 44px;
  }
}

.formSection {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 32px;

  > label {
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    margin-bottom: 12px;
  }

  > div {
    width: 100%;
    margin-bottom: 12px;

    > span {
      width: 100%;
    }

    > div > input {
      width: 100%;
    }
  }

  .formRow {
    display: flex;

    > div {
      width: 100%;

      &:first-child {
        margin-right: 6px;
      }

      &:last-child {
        margin-left: 6px;
      }
    }
  }
}

.logoWrapper {
  display: flex;

  .logo {
    object-fit: contain;
    height: 150px;
    width: 250px;
    border-radius: 4px;
    border: 1px dashed $color-neutral-500;
    background-color: white;
    padding: 8px;
    margin-right: 24px;
  }

  > span {
    display: flex;
    align-items: baseline;

    > label {
      font-size: 14px;
      line-height: 21px;
      color: $color-neutral-700;
      max-width: 160px;
      margin-left: 12px;
    }
  }
}

.identificationsWrapper {
  display: flex;
  width: auto;
  flex-direction: column;

  > span {
    width: max-content !important;
  }

  // Link
  > a {
    display: flex;
    align-items: center;
    margin-top: 12px;

    > label {
      margin-left: 12px;
      text-decoration: underline;
    }
  }
}

.badgeContainer {
  display: flex;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }

  .premierWrapper {
    display: flex;
    align-items: center;
    align-content: center;
    height: 20px;
    width: fit-content;
    padding: 3px;
    background: #ffffff;
    border: 1px solid #cfd4da;
    font-size: 12px;
    border-radius: 4px;
    margin-left: 12px;
  }

  .premierIcon {
    object-fit: contain;
    height: 14px;
    width: 14px;
    margin-right: 4px;
  }

  .sustainableIcon {
    margin-left: 12px;
    object-fit: contain;
    height: 22px;
    width: 22px;
  }
}
