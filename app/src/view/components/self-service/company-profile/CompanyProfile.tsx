import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

import { InfoIcon, SearchIcon } from "@fourkites/elemental-atoms";
import { Input } from "@fourkites/elemental-input";
import { Select } from "@fourkites/elemental-select";
import { Spinner } from "@fourkites/elemental-loading-indicator";

import { useAppDispatch } from "state/hooks";
import { CarrierCompanyDetailsState } from "state/modules/carrier/CarrierCompanyDetails";
import { ShipperCompanyDetailsState } from "state/modules/shipper/ShipperCompanyDetails";

import placeholderLogo from "assets/img/placeholderLogo.png";
import premierCarrierIcon from "assets/img/premierCarrierIcon.svg";
import sustainableCarrierIcon from "assets/img/sustainableCarrierIcon.svg";

import {
  isFieldInvalid,
  fieldHasOnlyNumbers,
  isValidCanadianPostalCode,
  isValidUSPostalCode,
} from "view/components/base/FormUtils";

import IdentificationsIndicator, {
  getCompanyIdentification,
} from "view/components/base/company-identifications/IdentificationsIndicator";
import FileUpload from "view/components/base/file-upload/FileUpload";

import CompanySection from "../../base/editable-section/EditableSection";

import styles from "./CompanyProfile.module.scss";
import { CANADA_STATES, US_STATES } from "./CompanyProfile.constants";

const CompanyProfile: React.FC<{
  showLoader: boolean;
  companyId: string;
  companyDetails: any;
}> = ({ showLoader, companyId, companyDetails }) => {
  const { t } = useTranslation();

  const dispatch = useAppDispatch();

  /*****************************************************************************
   * STATE
   ****************************************************************************/
  const [form, setForm] = useState<any>({
    companyName: "",
    companyType: ["carrier"],
    country: "US",
    addressLine1: "",
    addressLine2: "",
    state: "",
    city: "",
    zip: "",
    operationalProfile: "carrier_operating_fleet",
    identifications: [],
  });
  const [imageFile, setImageFile] = useState<any>(null);
  const [imagePreview, setPreview] = useState<any>(null);
  const [disabled, setDisabled] = useState<boolean>(true);
  const [confirmed, setConfirmed] = useState<boolean>(true);

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  const resetLocalForm = () => {
    setImageFile(null);
    setPreview(null);
    setConfirmed(false);
    setForm({
      logo: companyDetails?.logo,
      companyName: companyDetails?.name || "",
      companyType: companyDetails?.types?.map((t: string) => t.toLowerCase()),
      countryCode: companyDetails?.address?.country_code,
      addressLine1: companyDetails?.address?.line1 || "",
      addressLine2: companyDetails?.address?.line2 || "",
      state: companyDetails?.address?.state || "",
      city: companyDetails?.address?.city || "",
      zip: companyDetails?.address?.zip || "",
      identifications: companyDetails?.identifications,
    });
  };

  // Copies values to local form
  useEffect(() => {
    if (companyDetails) {
      resetLocalForm();
    }
  }, [companyDetails]);

  // creates preview guaranteeing memory will be freed
  useEffect(() => {
    if (!imageFile) {
      return;
    }

    // create the preview
    const objectUrl = URL.createObjectURL(imageFile);
    setPreview(objectUrl);

    // free memory when ever this component is unmounted
    return () => URL.revokeObjectURL(objectUrl);
  }, [imageFile]);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const onEdit = () => {
    setDisabled(false);
  };

  const onCancel = () => {
    resetLocalForm();
    setDisabled(true);
  };

  const onSave = async () => {
    setConfirmed(true);

    if (!areFieldsValid) {
      return;
    }

    await onSaveConfirmed();
    setDisabled(true);
  };

  const onSaveConfirmed = async () => {
    const basicPayload = {
      logo: imageFile,
      name: form?.companyName,
      address: {
        line1: form?.addressLine1,
        line2: form?.addressLine2,
        zip: form?.zip,
        city: form?.city,
        state: form?.state,
        country_code: form?.countryCode,
      },
    };

    const onSaveShipper = async () => {
      await dispatch(
        ShipperCompanyDetailsState.actions.updateShipperCompanyDetails({
          shipperId: companyId,
          shipperCompanyDetails: basicPayload,
        })
      );
    };

    const onSaveCarrier = async () => {
      await dispatch(
        CarrierCompanyDetailsState.actions.updateCarrierCompanyDetails({
          carrierId: companyId,
          carrierCompanyDetails: basicPayload,
        })
      );
    };

    if (isShipper) {
      await onSaveShipper();
    } else {
      await onSaveCarrier();
    }
  };

  const onChangeForm = (formField: string, value: string | string[]) => {
    setForm({
      ...form,
      [formField]: value,
    });
  };

  const onConfirmFile = (fileList: any) => {
    setImageFile(fileList[0]);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const isShipper = form?.companyType?.includes("shipper");
  const isCarrier = form?.companyType?.includes("carrier");
  const isBroker = form?.companyType?.includes("broker");

  const companyTypeValue =
    isCarrier && isBroker
      ? "Carrier and Broker"
      : isBroker
      ? "Broker"
      : "Carrier";

  const isPremierCarrier = companyDetails?.highlights?.is_premier_carrier;
  const isSustainableCarrier =
    companyDetails?.highlights?.is_sustainable_carrier;

  // If user uploaded image, use it as source; if not, use the aready uploaded
  // image or fallback to the placeholder logo
  const imageSource = imagePreview
    ? imagePreview
    : form?.logo || placeholderLogo;

  const isCompanyNameValid = !isFieldInvalid(form?.companyName);
  const isCountryCodeValid = !isFieldInvalid(form?.countryCode);
  const isZipValid =
    form?.countryCode === "CA"
      ? isValidCanadianPostalCode(form?.zip)
      : form?.countryCode === "US"
      ? isValidUSPostalCode(form?.zip)
      : !isFieldInvalid(form?.zip);

  const areFieldsValid =
    isCompanyNameValid && isCountryCodeValid && (!form.zip || isZipValid);

  const hasUsdot =
    getCompanyIdentification(form?.identifications, "usdot")?.length > 0;
  const hasEin =
    getCompanyIdentification(form?.identifications, "ein")?.length > 0;
  const hasVat =
    getCompanyIdentification(form?.identifications, "vat")?.length > 0;
  const hasMc =
    getCompanyIdentification(form?.identifications, "mc")?.length > 0;
  const hasIdentifications = hasUsdot || hasEin || hasVat || hasMc;

  return (
    <CompanySection
      title={t("Business Details")}
      onEdit={onEdit}
      onSave={onSave}
      onCancel={onCancel}
      areFieldsValid={areFieldsValid}
      editable
    >
      {showLoader ? (
        <div className={styles.formContainer}>
          <Spinner isLoading size="medium" />
        </div>
      ) : (
        <div className={styles.formContainer}>
          <div className={styles.formColumn}>
            <div className={styles.formSection}>
              <label>{t("Basic Information")}</label>
              <Input
                label={t("Company Name")}
                placeholder={t("Company Name")}
                value={form?.companyName}
                onChange={(e) => onChangeForm("companyName", e.target.value)}
                disabled={disabled}
                invalid={confirmed && isFieldInvalid(form?.companyName)}
                errorLabel={t("Please, add a valid name.")}
                required
              />
              {/*Don't show if is shipper only*/}
              {(isBroker || isCarrier) && (
                <Select
                  label={t("Company Type")}
                  options={["Carrier", "Broker", "Carrier and Broker"]}
                  value={[companyTypeValue]}
                  onChange={(selectedOptions: any) => {
                    const values =
                      selectedOptions[0] === "Carrier and Broker"
                        ? ["carrier", "broker"]
                        : selectedOptions[0] === "Broker"
                        ? ["broker"]
                        : ["carrier"];
                    onChangeForm("companyType", values);
                  }}
                  disabled
                  required
                />
              )}
              <Select
                label={t("Primary Country")}
                options={["US", "CA"]}
                value={[form?.countryCode]}
                onChange={(selectedOptions: any) => {
                  onChangeForm("countryCode", selectedOptions[0]);
                  setForm({
                    ...form,
                    ["state"]: "",
                    ["countryCode"]: selectedOptions[0],
                  });
                }}
                disabled
                required
              />
            </div>

            {!isShipper && (isPremierCarrier || isSustainableCarrier) && (
              <div className={styles.formSection}>
                <label>{t("Badges Earned")}</label>
                {isPremierCarrier && (
                  <span className={styles.badgeContainer}>
                    {t("FourKites® Premier Carrier")}
                    <span className={styles.premierWrapper}>
                      <img
                        src={premierCarrierIcon}
                        className={styles.premierIcon}
                      />
                      {t("PREMIER")}
                    </span>
                  </span>
                )}
                {isSustainableCarrier && (
                  <span className={styles.badgeContainer}>
                    {t("Recognised SmartWay Partner")}
                    <img
                      src={sustainableCarrierIcon}
                      className={styles.sustainableIcon}
                    />
                  </span>
                )}
              </div>
            )}

            <div className={styles.formSection}>
              <label>{t("Address")}</label>

              <Input
                label={t("Line 1")}
                placeholder={t("Building")}
                value={form?.addressLine1}
                onChange={(e) => onChangeForm("addressLine1", e.target.value)}
                disabled={disabled}
              />

              <Input
                label={t("Line 2")}
                placeholder={t("Street, Locality")}
                value={form?.addressLine2}
                onChange={(e) => onChangeForm("addressLine2", e.target.value)}
                disabled={disabled}
              />

              <Input
                label={t("City")}
                placeholder={t("City")}
                value={form?.city}
                onChange={(e) => onChangeForm("city", e.target.value)}
                disabled={disabled}
              />

              <div className={styles.formRow}>
                <Select
                  label={t("State / Province")}
                  value={[form?.state]}
                  options={
                    form?.countryCode === "US"
                      ? US_STATES
                      : form?.countryCode === "CA"
                      ? CANADA_STATES
                      : []
                  }
                  onChange={(selectedOptions: any) => {
                    onChangeForm("state", selectedOptions[0]);
                  }}
                  disabled={disabled}
                />
                <Input
                  label={t("Zip or Postal Code")}
                  placeholder={t("Zip or Postal Code")}
                  value={form?.zip}
                  onChange={(e) => onChangeForm("zip", e.target.value)}
                  invalid={
                    form?.zip &&
                    ((form?.countryCode === "CA" &&
                      !isValidCanadianPostalCode(form?.zip)) ||
                      (form?.countryCode === "US" &&
                        !isValidUSPostalCode(form?.zip)))
                  }
                  errorLabel={t("Please, add a valid zip or postalcode.")}
                  disabled={disabled}
                />
              </div>
            </div>
          </div>

          <div className={styles.formColumn}>
            <div className={styles.formSection}>
              <label>{t("Company Logo")}</label>
              <div className={styles.logoWrapper}>
                <div id="logo-container">
                  <img
                    className={styles.logo}
                    src={imageSource}
                    alt="Company logo"
                  />
                </div>
                {!disabled && (
                  <span>
                    <InfoIcon fill="#495057" />
                    <label>
                      {t("Image file size should be less than 2 MB")}
                      <br />
                      <br />
                      {t("Permitted image formats: .jpg, .jpeg, .png.")}
                    </label>
                  </span>
                )}
              </div>

              {!disabled && (
                <FileUpload
                  type="input"
                  onConfirmFile={onConfirmFile}
                  allowedFileFormats={["image/png", "image/jpg", "image/jpeg"]}
                  maxFileSizeMb={2}
                />
              )}
            </div>

            {hasIdentifications && (
              <div className={styles.formSection}>
                <label>{t("Company Identifiers")}</label>

                {hasUsdot && (
                  <div className={styles.identificationsWrapper}>
                    <IdentificationsIndicator
                      type="usdot"
                      label="DOT"
                      identifications={form?.identifications}
                      hideIfEmpty
                    />
                    <a
                      href="https://safer.fmcsa.dot.gov/CompanySnapshot.aspx"
                      target="_blank"
                    >
                      <SearchIcon fill="#0e65e5" />
                      <label>{t("Find my USDOT number")}</label>
                    </a>
                  </div>
                )}

                {hasEin && (
                  <div className={styles.identificationsWrapper}>
                    <IdentificationsIndicator
                      type="ein"
                      label="EIN"
                      identifications={form?.identifications}
                      hideIfEmpty
                    />
                  </div>
                )}

                {hasMc && (
                  <div className={styles.identificationsWrapper}>
                    <IdentificationsIndicator
                      type="mc"
                      label="MC"
                      identifications={form?.identifications}
                      hideIfEmpty
                    />
                  </div>
                )}

                {hasVat && (
                  <div className={styles.identificationsWrapper}>
                    <IdentificationsIndicator
                      type="vat"
                      label="VAT"
                      identifications={form?.identifications}
                      hideIfEmpty
                    />
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </CompanySection>
  );
};

export default CompanyProfile;
