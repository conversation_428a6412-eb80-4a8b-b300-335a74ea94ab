@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 612px;
  z-index: 10;
  position: absolute;

  > div[id="suggestions"] {
    position: relative;
    z-index: 10;
    height: fit-content;
    max-height: 360px;
    overflow: auto;
    margin-top: 4px;
    margin-left: 8px;
    width: 100%;
    border-radius: 4px;
    box-shadow: 0 6px 14px 3px rgba(0, 0, 0, 0.1);
    background-color: white;
  }
}

.inputWrapper {
  margin-left: 9px;
  width: 612px;

  &:last-child {
    margin-right: 0;
  }

  > div {
    width: 100%;

    > div {
      display: flex;

      > input {
        width: 100%;
      }
    }
  }
}

.suggestionContainer {
  display: flex;
  padding: 16px;
  cursor: pointer;

  &:hover {
    background-color: $color-neutral-200;
  }
}

.suggestionContainerDisabled {
  composes: suggestionContainer;
  opacity: 0.5;
  cursor: not-allowed;
}

.notFoundContainer {
  display: flex;
  flex-direction: column;
  padding: 16px 24px 16px 24px;
  align-content: left;

  &:hover {
    background-color: $color-neutral-200;
  }

  > span[id="title"] {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 24px;
  }

  > button {
    width: 200px;
    margin-top: 36px;
  }
}

.statusWrapper {
  width: 150px;
  display: flex;
  height: fit-content;
  margin-left: 16px;
  justify-content: flex-end;
}

.loader {
  display: flex;
  padding: 16px;
  justify-content: center;
}
