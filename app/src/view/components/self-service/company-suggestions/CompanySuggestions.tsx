import React, { Fragment, useState, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";

import { Button, SearchIcon, XIcon } from "@fourkites/elemental-atoms";
import { Input } from "@fourkites/elemental-input";
import { Spinner } from "@fourkites/elemental-loading-indicator";

import { useAppSelector, useAppDispatch, useDebounce } from "state/hooks";
import { CompaniesSearchState } from "state/modules/CompaniesSearch";

import StatusTag from "view/components/base/status-indicators/StatusTag";

import styles from "./CompanySuggestions.module.scss";
import CompanySuggestionsProps from "./CompanySuggestions.types";

import CompanySummary from "view/components/self-service/company-summary/CompanySummary";

const CompanySuggestions = ({
  managerCompanyId,
  suggestedCompanyType,
  mode,
  onSelectSuggestion,
  onSelectNotFound,
  isOnboarding = false,
  invitationToken = "",
}: CompanySuggestionsProps) => {
  const { t } = useTranslation();

  const wrapperRef = useRef(null);

  const dispatch = useAppDispatch();
  const isSearching = useAppSelector(
    CompaniesSearchState.selectors.isSearching()
  );
  let suggestions: any[] =
    useAppSelector(CompaniesSearchState.selectors.getSearchedCompanies()) || [];

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [query, setQuery] = useState<string>("");
  const [isOutside, setIsOutside] = useState<boolean>(true);

  // Don't show suggestions if we have less than 3 chars
  suggestions = !query || query?.length < 3 ? [] : suggestions;

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  useEffect(() => {
    const handleClickOutside = (event: any) => {
      // @ts-ignore
      if (wrapperRef?.current && !wrapperRef?.current.contains(event.target)) {
        setIsOutside(true);
      }
    };
    // Bind the event listener
    document.addEventListener("mousedown", handleClickOutside);
    // Unbind the event listener on clean up
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [wrapperRef]);

  useDebounce(
    () => {
      // Only search after 3 chars
      if (!query || query?.length < 3) {
        return;
      }

      if (isOnboarding) {
        dispatch(
          CompaniesSearchState.actions.searchCompaniesExternally({
            query,
            invitationToken,
          })
        );
      } else {
        dispatch(
          CompaniesSearchState.actions.searchCompanies({
            companyId: managerCompanyId!,
            companyType: suggestedCompanyType!,
            mode: mode!,
            query,
          })
        );
      }
    },
    300, // timeout
    [query]
  );

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const onInputChange = (e: any) => {
    setQuery(e.target.value);
    setIsOutside(false);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const suggestionComponents = suggestions?.map((d: any, idx: number) => (
    <Fragment key={idx}>
      <SuggestionSummary
        data={d}
        onSelectSuggestion={onSelectSuggestion}
        setIsOutside={setIsOutside}
      />
    </Fragment>
  ));

  return (
    <div ref={wrapperRef} className={styles.container}>
      <div className={styles.inputWrapper}>
        <Input
          label={""}
          placeholder={"Search for Carrier Name, USDOT#, MC# or SCAC"}
          value={query}
          onChange={onInputChange}
          icon={query ? <XIcon /> : <SearchIcon />}
          onIconClick={() => {
            setQuery("");
            setIsOutside(true);
          }}
          size="large"
        />
      </div>

      {query && !isOutside && (
        <div id="suggestions" data-testid="suggestions">
          {suggestionComponents}

          {isSearching && (
            <div className={styles.loader}>
              <Spinner isLoading size="small" />
            </div>
          )}

          {!isSearching && suggestionComponents.length === 0 && (
            <SugesstionsNotFound
              query={query}
              onSelectNotFound={onSelectNotFound}
              isOnboarding={isOnboarding}
            />
          )}
        </div>
      )}
    </div>
  );
};

const SuggestionSummary = ({ data, onSelectSuggestion, setIsOutside }: any) => {
  // TODO: NEW-DESIGN check just for invited when API is ready
  const networkStatus: string = data?.status?.network || data?.status;
  const isPending = ["pending", "invited"].includes(networkStatus);
  const isConnected = ["connected", "in_progress"].includes(networkStatus);

  const companyOnPlatform = data?.permalink != null;

  const shouldDisableSelection = isConnected || isPending;
  const suggestionContainerClass = shouldDisableSelection
    ? styles.suggestionContainerDisabled
    : styles.suggestionContainer;

  return (
    <div
      className={suggestionContainerClass}
      onClick={() => {
        if (!shouldDisableSelection) {
          onSelectSuggestion(data);
          setIsOutside(true);
        }
      }}
    >
      <CompanySummary data={data} />

      {companyOnPlatform && (
        <div className={styles.statusWrapper}>
          <StatusTag label={"On FourKites"} variant={"ok"} />
        </div>
      )}

      {!companyOnPlatform && !isPending && (
        <div
          className={styles.statusWrapper}
          data-testid="invite-carrier-status-tag"
        >
          <StatusTag label={"Invite Carrier"} variant={"alert"} />
        </div>
      )}
    </div>
  );
};

const SugesstionsNotFound = ({
  onSelectNotFound,
  query,
  isOnboarding,
}: any) => {
  const { t } = useTranslation();

  const hasNotFoundOption = onSelectNotFound != null;
  const helpText = isOnboarding
    ? t("We could not find your company in our database.")
    : hasNotFoundOption
    ? t(
        "But hey! You can share their contact and we can onboard them for you. It only takes a minute."
      )
    : t("Sorry about that. Please search with a different parameter.");

  return (
    <div className={styles.notFoundContainer}>
      <span id="title">{`${t("Seems we can’t find")} "${query}"`}</span>
      <span>{helpText}</span>

      {hasNotFoundOption && (
        <Button onClick={() => onSelectNotFound(query)}>
          {isOnboarding ? t("Proceed anyway") : t("Invite Carrier")}
        </Button>
      )}
    </div>
  );
};

export default CompanySuggestions;
