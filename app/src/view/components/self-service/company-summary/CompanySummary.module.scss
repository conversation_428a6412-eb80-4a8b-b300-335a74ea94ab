@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.container {
  display: flex;
  width: 100%;
}

.containerDisabled {
  display: flex;
  width: 100%;
  cursor: not-allowed;
}

.content {
  display: flex;
  flex-direction: column;
  width: 100%;

  > div[id="company-name"] {
    display: flex;
    width: 100%;
    align-content: space-evenly;
    justify-content: space-between;
    align-items: baseline;
  }

  > div[id="company-ids"] {
    margin-top: 8px;
    font-size: 14px;
    letter-spacing: 0;
    line-height: 24px;
  }
}

.headerRight {
  display: flex;
  align-content: center;
  align-items: center;
  min-width: fit-content;
  margin-left: 8px;
}

.logo {
  object-fit: contain;
  height: 64px;
  width: 96px;
  border: 1px solid $color-neutral-300;
  border-radius: 4px;
  background-color: white;
  padding: 8px;
  margin-right: 24px;
}

.title {
  margin: 0;
  font-weight: bold;
  color: $color-neutral-700;
}
