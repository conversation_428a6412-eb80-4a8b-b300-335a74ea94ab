import React from "react";
import { useTranslation } from "react-i18next";

import placeholderLogo from "assets/img/placeholderLogo.png";

import StatusTag from "view/components/base/status-indicators/StatusTag";
import IdentificationsIndicator from "view/components/base/company-identifications/IdentificationsIndicator";

import styles from "./CompanySummary.module.scss";

const CompanySummary = ({ data }: any) => {
  const { t } = useTranslation();

  // TODO: NEW-DESIGN check just data.status when BE is ready
  const networkStatus: string = data?.status?.network || data?.status;
  const isConnected = ["connected", "in_progress"].includes(networkStatus);
  // TODO: NEW-DESIGN leave only pending when BE changes
  const isPending = ["pending", "invited"].includes(networkStatus);

  const shouldDisableSelection = isConnected || isPending;

  return (
    <div
      className={
        shouldDisableSelection ? styles.containerDisabled : styles.container
      }
      id="company-summary-container"
      data-testid="company-summary-container"
    >
      <img
        className={styles.logo}
        src={data?.logo ? data?.logo : placeholderLogo}
        alt="Company logo"
        data-testid="company-logo"
      />

      <div className={styles.content}>
        <div id={"company-name"}>
          <h5 className={styles.title} data-testid="company-name-header">
            {data?.name}
          </h5>

          <div className={styles.headerRight}>
            {shouldDisableSelection && (
              <StatusTag
                label={isPending ? t("Invited") : t("In Network")}
                variant={isPending ? "alert" : "ok"}
              />
            )}
          </div>
        </div>
        <div id={"company-ids"}>
          <IdentificationsIndicator
            type="usdot"
            label="DOT"
            identifications={data?.identifications}
          />
          <IdentificationsIndicator
            type="mc"
            label="MC"
            identifications={data?.identifications}
          />
          <IdentificationsIndicator
            type="scac"
            label="NMFTA SCAC"
            identifications={data?.identifications}
          />
        </div>
      </div>
    </div>
  );
};

export default CompanySummary;
