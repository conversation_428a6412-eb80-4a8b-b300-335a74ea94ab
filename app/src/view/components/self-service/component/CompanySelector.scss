@import "@fourkites/elemental-atoms/build/scss/colors/index";

$selector-background-grey: #dee2e6;
$hover-bg-color: #efebea;
$menu-width: 384px;
$clear-btn-width: 36px;

@mixin menu-item {
  padding: 8px 18px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.1s, color 0.1s;
}

@mixin menu-container {
  background-color: $color-neutral-00;
  box-sizing: border-box;
  border-radius: 6px;
  box-shadow: 0 8px 16px 5px rgba(0, 0, 0, 0.18);
  z-index: 100;
}

.typeahead-input {
  box-sizing: border-box;
  width: calc(#{$menu-width} - #{$clear-btn-width}) !important;
  height: 48px;
  display: flex;
  align-items: center;
  padding: 0 18px;
  background-color: $selector-background-grey;
  border-radius: 4px 0 0 4px;
  border: none;
  font-size: 16px;
  outline: none !important;
  font-family: Lato, sans-serif;
}

.rbt-menu.dropdown-menu.show {
  @include menu-container;

  width: $menu-width !important;
  position: relative;
  top: 5px !important;
}

.dropdown-item {
  @include menu-item;

  text-decoration: none;
  color: black;
  margin: 5px;

  &:hover {
    background-color: $hover-bg-color;
    color: black;
  }

  &.active {
    background-color: $hover-bg-color;
  }

  &:last-of-type {
    border-radius: 0 0 4px 4px;
  }

  &:first-of-type {
    border-radius: 4px 4px 0 0;
  }

  &__company-list-link {
    @include menu-item;

    text-decoration: underline;
    color: $color-primary-400;
    font-size: 14px;
    font-style: italic;
    margin: 5px;

    &:hover {
      background-color: $hover-bg-color;
    }

    .external-link-icon {
      margin-left: 5px;
    }
  }
}

.global-header__company-selector-container {
  .elemental-SearchList {
    z-index: 1;
    border-radius: 8px;
    box-shadow: 0 6px 14px 3px $color-neutral-500;
    margin-top: 0.25em;
    background-color: $color-neutral-00;
    position: absolute;
  }
}

.elemental-GlobalHeader__company-selector-containers{
  position: absolute;
  right: 24px;
  top: 65px;
  z-index: 999;
}

.rbt-input-hint {
  box-sizing: border-box;
  width: 340px !important;
  display: flex;
  align-items: center;
  padding: 0 18px;
  font-size: 16px;
  font-family: Lato, sans-serif;

  &[placeholder] {
    text-overflow: ellipsis;
  }
}

.company-selector {
  height: 48px;

  &__clear-btn {
    height: 48px;
    background-color: $selector-background-grey;
    display: flex;
    align-items: center;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    width: $clear-btn-width;
  }

  .disabled {
    cursor: auto;
  }
}

.sr-only {
  visibility: hidden;
}