import React, { useState, useRef, useEffect, useCallback } from "react";
import { useTranslation } from "react-i18next";
import {
  CompanyDetails,
  CompanySelectorProps,
  AdminCompanySelectorProps,
} from "./CompanySelector.types";
import "./CompanySelector.scss";
import { SearchableSelect } from "@fourkites/elemental-searchable-select";
import {
  fetchCompanies,
  retrieveInitialCompany,
  saveCompanySelectionToMemory,
} from "api/CompaniesServiceApi";

/*
   This function is responsible for handling the selection of a company. 
   It makes request to change
   sets the selected company if the request is successful. 
  */
const AdminCompanySelector: React.FC<AdminCompanySelectorProps> = ({
  onSubmit,
  user,
  domains,
  showHierarchy = false,
}) => {
  const company  = user?.company;
  const  defaultCompanyID  = company?.defaultId;

  const companyDomain = domains?.company;
  const defaultCompanyLabelFooter = " (Primary)";

  const [selectedValueLabel, setSelectedValueLabel] = useState("");
  const [companyList, setCompanyList] = useState([]);
  const [selectedCompanyValue, setSelectedCompanyValue] =
    useState<CompanyDetails | undefined>(user?.company?.context);
  const [searchText, setSearchText] = useState("");

  /*
   This function is responsible for handling the selection of a company. 
   sets the selected company 
  */
  const handleCompanySelection = async (selectedCompany: any) => {
    selectedCompany = {
      id: selectedCompany[0].value,
      label: selectedCompany[0].label,
    };
  
    setSelectedCompanyValue(selectedCompany);
    setSelectedValueLabel(getCompanyLabel(selectedCompany));
    if (onSubmit) {
      onSubmit(selectedCompany);
    }
    if (searchText !== "") {
      setSearchText("");
      setCompanyList([]);
    }
  };

  /*
  This function is responsible for handling the auto-complete functionality of the company search, 
  i.e., fetching companies based on a search string and processing the returned data for display. 
  */
  const handleAutocompleteSearch = async (value: React.SetStateAction<string>, callback: (arg0: any) => void) => {
    setSearchText(value);
    const data = await fetchCompanies(
      companyDomain || '',
      defaultCompanyID || '',
      user ,
      value
    );
    setCompanyList(data);
    callback(data);
  };

  const getCompanyLabel = useCallback(
    (company: { id: any; label: string; }) =>
      company.id === defaultCompanyID &&
      !company.label.endsWith(defaultCompanyLabelFooter)
        ? company.label + defaultCompanyLabelFooter
        : company.label,
    [defaultCompanyID]
  );

  /*
  This function fetches company data from a given endpoint and 
  processes the data into a format which is suitable for the application. 
  It is also responsible for setting the parent company and the selected company based on the fetched data. 
  */
  const fetchAndProcessCompanies = useCallback(async () => {
    if (companyList.length === 0 && searchText === "") {
      const data = await fetchCompanies(
        companyDomain || '',
        defaultCompanyID || '',
        user,
        ""
      );
      setCompanyList(data);
    }
  }, [companyDomain, defaultCompanyID, user, companyList.length, searchText]);

  useEffect(() => {
    fetchAndProcessCompanies();
  }, [fetchAndProcessCompanies]);

  useEffect(() => {
    if (selectedCompanyValue) {
      setSelectedValueLabel(getCompanyLabel(selectedCompanyValue));
    }
  }, [selectedCompanyValue, getCompanyLabel]);

  const selectedValues = selectedCompanyValue
    ? [
        {
          value: selectedCompanyValue.id,
          label: getCompanyLabel(selectedCompanyValue),
        },
      ]
    : [];
  // Set initialValues to selector (3pl) company
  const initialValues = (
    selectedCompanyValue != null
      ? companyList.filter(
          (company: CompanyDetails) => company.id !== selectedCompanyValue.id
        )
      : companyList
  ).map((company: any) => {
    return { value: company.id, label: getCompanyLabel(company) };
  });

  return (
    <div className="global-header__company-selector-container">
      <SearchableSelect
        size="large"
        select="single"
        placeholder="Select Company"
        searchPlaceholder="Search Company"
        selectedValueLabel={selectedValueLabel}
        autocompleteCallback={handleAutocompleteSearch}
        onChange={handleCompanySelection}
        showSelectAll={false}
        selectedValues={selectedValues}
        initialValues={initialValues}
      />
    </div>
  );
};

/* fetches default company
Render componder only if user is admin and 3pl
*/

const CompanySelector: React.FC<CompanySelectorProps> = ({
  shouldRenderAdminCompanySelector,
  onSubmit,
  user,
  domains,
  performInitialQuery = false,
}) => {

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const { t } = useTranslation();
  const [defaultSelected, setDefaultSelected] = useState<CompanyDetails>();
  const [selected, setSelected] = useState([] as any);
  const { authToken = '', deviceId = '', userId = '' } = user || {};


  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/


  useEffect(() => {
    if (onSubmit && defaultSelected) {
      onSubmit(defaultSelected);
    }
  }, [defaultSelected, onSubmit]);

  useEffect(() => {
    const fetchDefaultCompany = async () => {
      let savedCompany = (await retrieveInitialCompany(
        authToken,
        deviceId,
        userId,
        domains?.company || '',
        performInitialQuery
      )) as CompanyDetails;
      if (savedCompany) {
        saveCompanySelectionToMemory(savedCompany);
        setDefaultSelected(savedCompany);
        setSelected([savedCompany]);
      }
    };

    fetchDefaultCompany();
  }, [authToken, deviceId, userId, domains, performInitialQuery]);

  return (
    <AdminCompanySelector
      onSubmit={onSubmit}
      domains={domains}
      user={user}
      showHierarchy={false}
    />
  );

};

export default CompanySelector;
