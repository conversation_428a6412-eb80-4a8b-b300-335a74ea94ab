export type CompanyDetails = {
  id:  string;
  label: string;
};

export type CompanyAutocompleteResponseDetails = {
  id:  string;
  description: string;
};

export interface InputElement extends HTMLInputElement {
  clear: () => void;
}

export interface CompanySelectorProps {
  /**
   * handler for selecting a new company from the async input
   */
  onSubmit?: (company: CompanyDetails) => void;
  /**
   * current logged in user
   */
  user?: {
    userId: string;
    deviceId: string;
    authToken: string;
    superAdmin: string;
    imageSrc?: string;
    role?: string;
    name?: {
      firstName: string;
      lastName: string;
    };
    company?: {
      defaultTypes: string[];
      defaultId: string;
      context: CompanyDetails;
    };
    displayCompanySwitcher?: boolean;
  };
  /**
   * Domains for different API endpoints. It includes:
   * - company: Domain for the company-service-related APIs (e.g. company-api-fkdev.fourkites.com).
   * - user: Domain for user-service-related APIs (e.g. user-api-fkdev.fourkites.com).
   */
  domains?: {
    company: string;
    user: string;
  };
  /**
   * If true, the initial query in the Company Selector will happen, else it won't be perform.
   * When the initial query is performed, the initial query will be an 'a'.
   */
  performInitialQuery?: boolean;

  /**
   * determines whether the label should display the hierarchy of parent and child companies.
   */
  showHierarchy?: boolean;

  /**  Determine whether user is an admin and the default company type is parent.
   If true, the CompanySelector will render the AdminCompanySelector component.
   If false, the CompanySelector will render the component for a super admin.
  */
  shouldRenderAdminCompanySelector: boolean;
}

export interface AdminCompanySelectorProps
  extends Pick<
    CompanySelectorProps,
    Exclude<keyof CompanySelectorProps, "shouldRenderAdminCompanySelector">
  > {}