import React, { useState, useEffect } from "react";
import { useHistory } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { useAppSelector, useAppDispatch } from "state/hooks";
import UsersState, { getCookies } from "state/modules/Users";

import { CompanyDetails } from "view/components/self-service/component/CompanySelector.types";
import CompanySelector from "view/components/self-service/component/CompanySelector"

import { COMPANY_API_DOMAIN, fourkitesUrls } from "api/http/apiUtils";
import { startWithUpperCase } from "view/components/base/StringUtils";


import "./CompanySelector.scss";

const CompanySwitcher: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const history = useHistory();

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const currentUser = useAppSelector(UsersState.selectors.getCurrentUser);
  const superAdmin = useAppSelector(UsersState.selectors.getIsSuperAdmin);
  const isCompanyAdmin = useAppSelector(UsersState.selectors.getIsCompanyAdmin);
  const termsAgreed = useAppSelector(UsersState.selectors.getTermsAgreed);
  const { authToken, userId, deviceId } = getCookies();
  const shouldRenderAdminCompanySelector =
    currentUser?.role === "admin" &&
    (currentUser?.defaultCompanyType?.includes?.("3pl") ?? false);

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const currentUserObject = {
    authToken,
    userId,
    deviceId,
    superAdmin: superAdmin ? "true" : "false",
    name: {
      firstName: startWithUpperCase(currentUser?.firstName),
      lastName: startWithUpperCase(currentUser?.lastName),
    },
    isCompanyAdmin: isCompanyAdmin ? "true" : "false",
    role: currentUser?.role,
    company: {
      defaultTypes: currentUser?.defaultCompanyType,
      defaultId: currentUser?.defaultCompanyID,
      context: currentUser?.companyContext,
    },
    displayCompanySwitcher: false,
  };

  const domains = {
    company: COMPANY_API_DOMAIN,
    user: currentUser?.userServiceDomain,
  };

  const selectCompany = (selectedCompany: CompanyDetails) => {
    if (!selectedCompany || !selectedCompany?.id) {
        return;
      }
    dispatch(UsersState.actionCreators.setSelectedCompany(selectedCompany?.id));
   };


  return (
    shouldRenderAdminCompanySelector && (
      <div className="elemental-GlobalHeader__company-selector-containers">
        <CompanySelector
          shouldRenderAdminCompanySelector={shouldRenderAdminCompanySelector}
          domains={domains}
          user={currentUserObject}
          onSubmit={(newSelectedCompany: CompanyDetails) =>
            selectCompany(newSelectedCompany)
          }
          performInitialQuery={false}
        />
      </div>
    )
  );
};

export default CompanySwitcher;