import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import { ArrowLeftIcon } from "@fourkites/elemental-atoms";

import ProviderSelection from "./selection/ProviderSelection";
import ProviderCredentials from "./credentials/ProviderCredentials";

import styles from "./EldGpsIntegrationAddition.module.scss";
import EldGpsIntegrationAdditionProps from "./EldGpsIntegrationAddition.types";

const EldGpsIntegrationAddition = ({
  isModal,
  isExternallyUsed,
  onBack,
  onProviderChanged,
  onProviderNotFound,
  onComplete,
}: EldGpsIntegrationAdditionProps) => {
  const { t } = useTranslation();

  const [provider, setProvider] = useState<any>(null);

  const onSelectProvider = (provider: any) => {
    setProvider(provider);

    onProviderChanged(provider);
  };

  const onCredentialsUpdated = () => {
    //TODO: close tutorial
    onComplete();
  };

  const onClickBack = () => {
    //TODO: close tutorial
    if (!provider) {
      if (onBack) onBack();
    } else {
      setProvider(null);
      onProviderChanged(null);
    }
  };

  const showBackArrow = (onBack != null && !isModal) || provider != null;

  return (
    <div
      className={styles.container}
      id="eld-gps-integration-addition-container"
      data-test-id="eld-gps-integration-additions-modal"
    >
      <h1>
        {showBackArrow && (
          <button onClick={onClickBack}>
            <ArrowLeftIcon />
          </button>
        )}

        {provider
          ? t("Authenticate")
          : t("Search & choose your location provider")}
      </h1>

      {/* */}

      {provider ? (
        <ProviderCredentials
          isCreating={true}
          isModal={isModal}
          isExternallyUsed={isExternallyUsed}
          provider={provider}
          onCredentialsUpdated={onCredentialsUpdated}
        />
      ) : (
        <ProviderSelection
          isModal={isModal}
          onSelectProvider={onSelectProvider}
          onProviderNotFound={onProviderNotFound}
          isExternallyUsed={isExternallyUsed}
        />
      )}
    </div>
  );
};

export default EldGpsIntegrationAddition;
