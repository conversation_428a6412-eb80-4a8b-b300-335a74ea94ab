import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

import { useAppSelector, useAppDispatch } from "state/hooks";
import { EldGpsIntegrationsState } from "state/modules/carrier/EldGpsIntegrations";
import { UsersState } from "state/modules/Users";
import { ExternalParametersState } from "state/modules/external/ExternalParameters";

import { showToast } from "view/components/base/toast/Toast";

import ProviderInstructions from "./ProviderInstructions";
import ProviderCredentialsForm from "./ProviderCredentialsForm";

import styles from "./ProviderCredentials.module.scss";
import ProviderCredentialsProps from "./ProviderCredentials.types";

const ProviderCredentials = ({
  isModal,
  isExternallyUsed,
  isCreating,
  provider,
  integrationId,
  onCredentialsUpdated,
}: ProviderCredentialsProps) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  /*****************************************************************************
   * REDUX
   ****************************************************************************/
  const carrierId: string = useAppSelector(UsersState.selectors.getCompanyId);
  const user = useAppSelector(UsersState.selectors.getCurrentUser);
  const externalUser = useAppSelector(
    ExternalParametersState.selectors.externalUser()
  );

  // If used externally, we use external user
  const currentUser = isExternallyUsed ? externalUser : user;

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [providerType, setProviderType] = useState<"truck" | "trailer">(
    "truck"
  );
  const [assetNumber, setAssetNumber] = useState(undefined);
  const [credentials, setCredentials] = useState<any[]>([]);

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  /*
   * Populates credentials form when we receive a new provider
   */
  useEffect(() => {
    const providerCredentials = provider?.credentials?.map((cred: any) => ({
      [cred?.id]: cred?.value ? cred?.value : "",
    }));
    setCredentials(providerCredentials);
  }, [provider]);

  /*****************************************************************************
   * INTERNAL_METHODS
   ****************************************************************************/

  /*
   * Parses credentials of provider to create new integration
   */
  const getParsedCredentials = () => {
    let newCredentials = {};

    // Parses through list of required credentials to create a key:value dict of
    // credentials filled by the user
    for (let cred of credentials) {
      newCredentials = { ...newCredentials, ...cred };
    }
    return newCredentials;
  };

  /*
   * Handler to create a new credential
   */
  const onCreateCredentials = async () => {
    const response = await dispatch(
      EldGpsIntegrationsState.actions.createIntegration({
        carrierId: carrierId,
        userId: currentUser?.userId,
        modes: ["ltl", "ftl"],
        assetNumber: assetNumber,
        provider: {
          id: provider?.id,
          type: providerType,
          alias: "", //TODO,
          credentials: getParsedCredentials(),
        },
      })
    );

    if ("error" in response) {
      //TODO: handler error
    }

    // Finish adding integration
    onCredentialsUpdated();
  };

  /*
   * Handler update credentials
   */
  const onUpdateCredentials = async () => {
    const response = await dispatch(
      EldGpsIntegrationsState.actions.updateIntegration({
        carrierId: carrierId,
        userId: currentUser?.userId,
        modes: ["ltl", "ftl"],
        integrationId: integrationId!,
        assetNumber: assetNumber,
        provider: {
          id: provider?.id,
          type: providerType,
          alias: "", //TODO,
          credentials: getParsedCredentials(),
        },
      })
    );

    if ("error" in response) {
      showToast(
        t("Error"),
        t("We could not update your credentials. Please try again shortly."),
        "error"
      );
    }

    // Finish adding integration
    showToast(
      t("Success"),
      t("Your integrations were updated successfully."),
      "ok"
    );
    onCredentialsUpdated();
  };

  /*
   * Handler to create new help requests for adding credentials
   */
  const onHelpRequest = async (helpRequestMessage: string) => {
    const response = await dispatch(
      EldGpsIntegrationsState.actions.createIntegrationHelpRequest({
        carrierId: carrierId,
        modes: ["ltl", "ftl"],
        provider: {
          id: provider?.id,
          name: provider?.name,
          type: providerType,
        },
        userMessage: helpRequestMessage,
      })
    );

    // Show error if error
    if ("error" in response) {
      showToast(
        t("Error"),
        t("We could not process your request. Please try again shortly."),
        "error"
      );
    }

    // Confirm success
    showToast(
      t("Support contacted"),
      t("An email was sent to support. We will get back to you soon."),
      "ok"
    );
    onCredentialsUpdated();
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  // XXX: ideally instructions_url should not exist anymore, but it was so far
  // not fixed on BE
  let instructions_urls = provider?.instructions_url?.split(",");
  let instructions_url = instructions_urls?.length ? instructions_urls[0] : "";
  const pdfInstructions = provider?.instructions_url_pdf
    ? provider?.instructions_url_pdf
    : instructions_url;

  return (
    <div
      className={styles.container}
      id="provider-container"
      data-test-id="provider-credentials-container"
    >
      <ProviderCredentialsForm
        provider={provider}
        providerType={providerType}
        setProviderType={setProviderType}
        assetNumber={assetNumber}
        setAssetNumber={setAssetNumber}
        credentials={credentials}
        setCredentials={setCredentials}
        onComplete={isCreating ? onCreateCredentials : onUpdateCredentials}
        onHelpRequest={onHelpRequest}
      />

      <ProviderInstructions
        isModal={isModal}
        isCreating={isCreating}
        isExternallyUsed={isExternallyUsed}
        pdfInstructions={pdfInstructions}
        htmlInstructions={provider?.instructions_url_html}
      />
    </div>
  );
};

export default ProviderCredentials;
