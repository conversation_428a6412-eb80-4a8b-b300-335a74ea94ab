import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import {
  ArrowRightIcon,
  Button,
  InfoIcon,
  EyeIcon,
} from "@fourkites/elemental-atoms";
import { Input } from "@fourkites/elemental-input";
import { RadioButton } from "@fourkites/elemental-radio-button";
import { Spinner } from "@fourkites/elemental-loading-indicator";

import { useAppSelector } from "state/hooks";

import { EldGpsIntegrationsState } from "state/modules/carrier/EldGpsIntegrations";

import { isFieldInvalid } from "view/components/base/FormUtils";

import PeoplenetDsaFields from "./peoplenet/PeoplenetDsaFields";

import ConfirmHelpRequestModal from "./ConfirmHelpRequestModal";

import styles from "./ProviderCredentials.module.scss";

const ProviderCredentialsForm = ({
  provider,
  providerType,
  setProviderType,
  assetNumber,
  setAssetNumber,
  credentials,
  setCredentials,
  onComplete,
  onHelpRequest,
}: any) => {
  const { t } = useTranslation();

  const isCreatingIntegration = useAppSelector(
    EldGpsIntegrationsState.selectors.isCreating()
  );

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [credentialsConfirmed, setCredentialsConfirmed] =
    useState<boolean>(false);
  const [show, setShow] = useState<boolean>(false);
  const [revealPassword, setRevealPassword] = useState<boolean>(false);

  const requiresAsset = provider?.requires_asset;
  const requiredCredentials = provider?.credentials;

  // TODO: removing while BE is not ready
  const isPeoplenetProvider = provider?.id === "peoplenet";

  const areCredentialsInvalid =
    (requiresAsset && isFieldInvalid(assetNumber)) ||
    credentials?.some((cred: any) =>
      isFieldInvalid(cred[Object.keys(cred)[0]])
    );

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const getCredentialValue = (index: number, credentialId: string) => {
    return credentials[index] != null ? credentials[index][credentialId] : "";
  };

  const onChangeCredentialValue = (
    index: number,
    credentialId: string,
    e: any
  ) => {
    let credentialsCopy = [...credentials];
    credentialsCopy[index][credentialId] = e.target.value;
    setCredentials(credentialsCopy);
  };

  const onConfirmCredentials = () => {
    setCredentialsConfirmed(true);

    if (!areCredentialsInvalid) {
      onComplete();
    }
  };

  const [helpRequestMessage, setHelpRequestMessage] = useState<string>("");
  const [isMandatoryFieldsFilled, setIsMandatoryFieldsFilled] =
    useState<boolean>(true);

  const onCloseModal = () => {
    setShow(false);
    setIsMandatoryFieldsFilled(false);
    setHelpRequestMessage("");
  };

  const onConfirmHelpRequest = () => {
    if (helpRequestMessage) {
      onHelpRequest(helpRequestMessage);
      onCloseModal();
    } else {
      setIsMandatoryFieldsFilled(false);
    }
  };

  const onTextChange = (helpRequestMessage: string) => {
    setHelpRequestMessage(helpRequestMessage);
    setIsMandatoryFieldsFilled(!!helpRequestMessage);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const showForm = !isCreatingIntegration;

  return (
    <div
      id="provider-credentials-form"
      className={styles.formContainer}
      data-test-id="provider-credentials-form"
    >
      <span>
        <label
          className={styles.formLabel}
          id="provider-credentials-form-label"
          data-testid="provider-credentials-form-label"
        >
          {t("Share your ")}
          <b>{provider?.name}</b>
          {t(" login credentials with FourKites. ")}
        </label>
      </span>

      <div id="security-alert">
        <InfoIcon fill="#ff8932" size={"24px"} />
        <label>
          {t("We never share your credentials to brokers or shippers.")}
          <br />
          {t(
            "We only track loads in our platform which you are assigned to carry."
          )}
        </label>
      </div>

      {isCreatingIntegration && (
        <div className={styles.loader}>
          <Spinner isLoading size="medium" />
        </div>
      )}

      {showForm && (
        <>
          <div id="location-provider-type">
            <label
              className={styles.formLabel}
              id="location-provider-type-label"
            >
              {t("Choose your location provider type")}
            </label>

            {/*TODO: throws warnings due to onChange missing*/}
            <span className={styles.radioButtonWrapper}>
              <RadioButton
                label="Truck #"
                checked={providerType === "truck"}
                onClick={(e: any) => setProviderType("truck")}
                size="medium"
              />
              <RadioButton
                label="Trailer #"
                checked={providerType === "trailer"}
                onClick={(e: any) => setProviderType("trailer")}
                size="medium"
              />
            </span>

            {requiresAsset && (
              <div className={styles.assetNumberInput}>
                <Input
                  label={`${t(
                    `Enter any Asset ID that uses ${provider?.name} equipment`
                  )}`}
                  value={assetNumber || ""}
                  onChange={(e: any) => setAssetNumber(e.target.value)}
                  invalid={credentialsConfirmed && isFieldInvalid(assetNumber)}
                  errorLabel={t("Field is required")}
                  required
                />
              </div>
            )}
          </div>

          <div className={styles.credentialsWrapper} id="credentials-wrapper">
            {requiredCredentials?.map(
              (requiredCredential: any, index: number) => {
                const credentialId = requiredCredential?.id;
                const value = getCredentialValue(index, credentialId);

                const isPassword = credentialId === "password";
                const inputType = isPassword
                  ? revealPassword
                    ? "text"
                    : "password"
                  : "text";
                const onIconClick = isPassword
                  ? () => setRevealPassword(!revealPassword)
                  : undefined;

                return (
                  <div
                    key={credentialId}
                    className={styles.providerCredentialInput}
                  >
                    <Input
                      label={requiredCredential?.name}
                      value={value}
                      onChange={(e: any) =>
                        onChangeCredentialValue(index, credentialId, e)
                      }
                      type={inputType}
                      invalid={credentialsConfirmed && isFieldInvalid(value)}
                      errorLabel={t("Field is required")}
                      required
                      icon={isPassword ? <EyeIcon /> : undefined}
                      onIconClick={onIconClick}
                    />
                  </div>
                );
              }
            )}

            {isPeoplenetProvider ? (
              <PeoplenetDsaFields
                areCredentialsInvalid={areCredentialsInvalid}
                setCredentialsConfirmed={setCredentialsConfirmed}
                onDataShareAgreementCreated={onConfirmCredentials}
              />
            ) : (
              <Button size="medium" onClick={onConfirmCredentials}>
                {t("Save & Connect")}
                <ArrowRightIcon fill="white" iconClass={"button-icon-right"} />
              </Button>
            )}
          </div>

          <a
            className={styles.formLink}
            data-testid="provider-credentials-form-link"
            onClick={() => setShow(true)}
          >
            {t("I need some support. Have FourKites reach out to me")}
          </a>
        </>
      )}

      <ConfirmHelpRequestModal
        show={show}
        helpRequestMessage={helpRequestMessage}
        isMandatoryFieldsFilled={isMandatoryFieldsFilled}
        onTextChange={onTextChange}
        onClose={onCloseModal}
        onConfirm={onConfirmHelpRequest}
      />
    </div>
  );
};

export default ProviderCredentialsForm;
