import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Document, Page } from "react-pdf";

import styles from "./ProviderInstructions.module.scss";

const ProviderInstructions = ({
  isModal,
  isCreating,
  isExternallyUsed,
  pdfInstructions,
  htmlInstructions,
}: any) => {
  // Adjust margins depending on how it is being used
  let marginTop: string | number = isModal ? (isCreating ? -84 : -16) : -124;
  marginTop = isExternallyUsed ? 0 : marginTop;

  if (!htmlInstructions && !pdfInstructions) {
    return null;
  }

  return (
    <div
      id="provider-instructions"
      data-test-id="provider-instructions"
      className={styles.instructionsContainer}
      style={{ marginTop: marginTop }}
    >
      {htmlInstructions && (
        <HtmlInstructions htmlInstructions={htmlInstructions} />
      )}

      {!htmlInstructions && pdfInstructions && (
        <PdfInstructions pdfInstructions={pdfInstructions} />
      )}
    </div>
  );
};

const PdfInstructions = ({ pdfInstructions }: any) => {
  const [numPages, setNumPages] = useState(null);

  const onDocumentLoadSuccess = ({ numPages }: any) => {
    setNumPages(numPages);
  };

  const fileParameters = {
    url: pdfInstructions,
    httpHeaders: {
      crossorigin: "anonymous",
      Pragma: "no-cache",
      "Cache-Control": "no-cache",
      "Access-Control-Allow-Origin": "*",
      "no-cors": true,
    },
    withCredentials: false,
  };

  return (
    <div id="document">
      <Document file={fileParameters} onLoadSuccess={onDocumentLoadSuccess}>
        {Array.apply(null, Array(numPages))
          .map((x, i) => i + 1)
          .map((page) => (
            <Page pageNumber={page} />
          ))}
      </Document>
    </div>
  );
};

const HtmlInstructions = ({ htmlInstructions }: any) => {
  const [fetchtedInstructions, setFetchedInstructions] =
    useState<string>("<div/>");

  const fetchHtmlInstructions = async () => {
    try {
      const response = await fetch(htmlInstructions);

      setFetchedInstructions(await response.text());
    } catch (error) {
      setFetchedInstructions("<div>Failed to get instructions.</div>");
    }
  };

  useEffect(() => {
    fetchHtmlInstructions();
  }, [htmlInstructions]);

  return (
    <div id="document" style={{ width: "100%", height: "100%" }}>
      <iframe
        srcDoc={fetchtedInstructions}
        width="100%"
        height="100%"
        style={{ border: "none" }}
      />
    </div>
  );
};

export default ProviderInstructions;
