import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import { ArrowRightIcon, Button } from "@fourkites/elemental-atoms";
import { Input } from "@fourkites/elemental-input";

import { useAppSelector, useAppDispatch } from "state/hooks";
import { EldGpsIntegrationsState } from "state/modules/carrier/EldGpsIntegrations";
import { UsersState } from "state/modules/Users";

import {
  isFieldInvalid,
  getFirstName,
  getLastName,
} from "view/components/base/FormUtils";

import { showToast } from "view/components/base/toast/Toast";

import PeoplenetDsaPreviewModal from "./PeoplenetDsaPreviewModal";
import PeoplenetSignatureModal from "./PeoplenetSignatureModal";

import styles from "./PeoplenetDsaFields.module.scss";

const PeoplenetDsaFields = ({
  areCredentialsInvalid,
  onDataShareAgreementCreated,
  setCredentialsConfirmed,
}: any) => {
  const { t } = useTranslation();

  const dispatch = useAppDispatch();

  /*****************************************************************************
   * REDUX
   ****************************************************************************/
  const carrierId: string = useAppSelector(UsersState.selectors.getCompanyId);

  /*****************************************************************************

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [dsaFieldsConfirmed, setDsaFieldsConfirmed] = useState<boolean>(false);
  const [showDsaPreviewModal, setShowDsaPreviewModal] =
    useState<boolean>(false);
  const [showSignatureModal, setShowSignatureModal] = useState<boolean>(false);
  const [dsaFieldValues, setDsaFieldValues] = useState<any>({
    cid: "",
    name: "",
    position: "",
  });

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const onPreview = () => {
    setCredentialsConfirmed(true);
    setDsaFieldsConfirmed(true);

    const { cid, name, position } = dsaFieldValues;
    const areDsaFieldsInvalid = [cid, name, position]?.some((cred: any) =>
      isFieldInvalid(cred)
    );

    if (!areDsaFieldsInvalid && !areCredentialsInvalid) {
      setShowDsaPreviewModal(true);
    }
  };

  const onSign = () => {
    setShowDsaPreviewModal(false);
    setShowSignatureModal(true);
  };

  const onSubmitSignature = async (signature: File) => {
    const firstName = getFirstName(dsaFieldValues?.name);
    const lastName = getLastName(dsaFieldValues?.name, null);

    const response = await dispatch(
      EldGpsIntegrationsState.actions.createDataShareAgreement({
        carrierId: carrierId,
        dsaFields: {
          cid: dsaFieldValues?.cid,
          first_name: firstName,
          last_name: lastName,
          position: dsaFieldValues?.position,
        },
        signature: signature,
      })
    );

    if ("error" in response) {
      showToast(
        t("Error"),
        t("There was an error when signing the agreement."),
        "error"
      );
    }

    showToast(t("DSA signed"), t("The agreement has been signed."), "ok");

    setShowSignatureModal(false);
    onDataShareAgreementCreated();
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <>
      <div key={"cid"} className={styles.providerCredentialInput}>
        <Input
          label={t("CID")}
          value={dsaFieldValues?.cid}
          onChange={(e: any) =>
            setDsaFieldValues({ ...dsaFieldValues, cid: e.target.value })
          }
          invalid={dsaFieldsConfirmed && isFieldInvalid(dsaFieldValues?.cid)}
          errorLabel={t("Field is required")}
          required
        />
      </div>
      <div key={"name"} className={styles.providerCredentialInput}>
        <Input
          label={t("Name")}
          value={dsaFieldValues?.name}
          onChange={(e: any) =>
            setDsaFieldValues({ ...dsaFieldValues, name: e.target.value })
          }
          invalid={dsaFieldsConfirmed && isFieldInvalid(dsaFieldValues?.name)}
          errorLabel={t("Field is required")}
          required
        />
      </div>
      <div key={"position"} className={styles.providerCredentialInput}>
        <Input
          label={t("Title / Role")}
          value={dsaFieldValues?.position}
          onChange={(e: any) =>
            setDsaFieldValues({ ...dsaFieldValues, position: e.target.value })
          }
          invalid={
            dsaFieldsConfirmed && isFieldInvalid(dsaFieldValues?.position)
          }
          errorLabel={t("Field is required")}
          required
        />
      </div>

      <Button size="medium" onClick={onPreview}>
        {t("Preview & Share Data Sharing Consent")}
        <ArrowRightIcon fill="white" iconClass={"button-icon-right"} />
      </Button>

      <PeoplenetDsaPreviewModal
        show={showDsaPreviewModal}
        onClose={() => setShowDsaPreviewModal(false)}
        onSign={onSign}
      />

      <PeoplenetSignatureModal
        show={showSignatureModal}
        onClose={() => setShowSignatureModal(false)}
        onSubmitSignature={onSubmitSignature}
      />
    </>
  );
};

export default PeoplenetDsaFields;
