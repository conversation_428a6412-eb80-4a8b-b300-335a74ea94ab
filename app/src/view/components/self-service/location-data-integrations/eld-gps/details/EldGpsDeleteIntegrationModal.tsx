import { useTranslation } from "react-i18next";

import deleteModalIcon from "assets/img/deleteModalIcon.png";

import { Modal } from "@fourkites/elemental-modal";

import styles from "./EldGpsDeleteIntegrationModal.module.scss";

const EldGpsDeleteIntegrationModal = ({
  providerName,
  show,
  onClose,
  onDelete,
}: any) => {
  const { t } = useTranslation();

  const title = t("Are you sure you want to delete this integration?");

  return (
    <Modal
      size="small"
      title={title}
      show={show}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: onClose,
      }}
      saveButtonProps={{
        label: t("Delete"),
        onClick: onDelete,
      }}
    >
      <div className={styles.container} data-testid="delete-gps-modal-content">
        <img src={deleteModalIcon} alt="Delete" />
        {`"${providerName || "--"}"` +
          t(
            " Integration will be permanently deleted and cannot be recovered."
          )}
      </div>
    </Modal>
  );
};

export default EldGpsDeleteIntegrationModal;
