@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";


.flyoutContent {
  display: flex;
  flex-direction: column;
  justify-content: left;
  padding-top: 16px;
  padding-bottom: 16px;
  position: relative;
  width: 100%;
  border-radius: 4px;
  box-shadow: 0 6px 14px 3px rgba(0,0,0,0.1);
  background-color: white;
}

.actionsIcon {
  height: 24px;
  width: 24px;
  cursor: pointer;

  &:hover {
    background-color: $color-neutral-300
  }
}

.actionItem {
  display: flex;
  align-items: center;
  padding: 0;
  background-color: transparent;
  border: none;
  cursor: pointer;
  text-align: left;
  padding: 8px 16px;

  &:hover {
    background-color: $color-neutral-300
  }
}
