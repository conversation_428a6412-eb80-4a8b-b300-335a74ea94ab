import React from "react";
import { useTranslation } from "react-i18next";

import {
  Edit1Icon,
  MenuMeatballFillIcon,
  TrashFullIcon,
} from "@fourkites/elemental-atoms";

import FlyoutMenu from "view/components/base/flyout-menu/FlyoutMenu";

import styles from "./IntegrationActions.module.scss";

export const IntegrationActions = ({
  onEditIntegration,
  onCompleteIntegration,
  onDeleteIntegration,
}: any) => {
  const { t } = useTranslation();

  return (
    <div data-testid="integration-actions">
      <FlyoutMenu
        id="integration-actions"
        anchor={
          <MenuMeatballFillIcon iconClass={styles.actionsIcon} fill="#21252a" />
        }
      >
        <div className={styles.flyoutContent}>
          {onEditIntegration && (
            <button
              className={styles.actionItem}
              onClick={onEditIntegration}
              data-testid="edit-integration-button"
            >
              <Edit1Icon fill="#21252a" iconClass={"button-icon-left"} />
              {t("Edit Credentials")}
            </button>
          )}
          {onCompleteIntegration && (
            <button
              className={styles.actionItem}
              onClick={onCompleteIntegration}
              data-testid="complete-integration-button"
            >
              <Edit1Icon fill="#21252a" iconClass={"button-icon-left"} />
              {t("Complete Integration (FK Admin)")}
            </button>
          )}
          <button
            className={styles.actionItem}
            onClick={onDeleteIntegration}
            data-testid="delete-integration-button"
          >
            <TrashFullIcon fill="#21252a" iconClass={"button-icon-left"} />
            {t("Delete Provider")}
          </button>
        </div>
      </FlyoutMenu>
    </div>
  );
};
