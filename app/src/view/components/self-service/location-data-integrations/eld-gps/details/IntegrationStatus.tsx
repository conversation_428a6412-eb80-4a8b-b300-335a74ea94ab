import React from "react";

import { InfoIcon } from "@fourkites/elemental-atoms";
import { Tooltip } from "@fourkites/elemental-tooltip";

import StatusTag from "view/components/base/status-indicators/StatusTag";

import {
  snakeCaseToSpacedCamelCase,
  startWithUpperCase,
} from "view/components/base/StringUtils";
import { getNetworkStatusVariant } from "view/components/self-service/company-management/CompanyManagementUtils";

import styles from "./IntegrationStatus.module.scss";

export const IntegrationStatus = ({
  statusValue,
  statusDescription,
  statusDetail,
}: any) => {
  if (!statusValue) {
    return null;
  }

  /*
   * Get the status label based on different provider status
   */
  const getStatusLabel = (statusValue: string, statusDetail: string) => {
    if (statusValue.includes("validation")) {
      return "Validation in Progress";
    } else {
      return statusDetail && statusValue === "error"
        ? snakeCaseToSpacedCamelCase(statusDetail)
        : startWithUpperCase(statusValue);
    }
  };

  const statusLabel = getStatusLabel(statusValue, statusDetail);

  const statusVariant = statusValue?.includes("validation")
    ? "info"
    : statusValue === "error"
    ? "error"
    : getNetworkStatusVariant(statusValue);

  return (
    <div className={styles.statusWrapper}>
      <StatusTag label={statusLabel} variant={statusVariant} />
      {statusDescription != null && statusDescription !== "" && (
        <div className={styles.statusInfoWrapper}>
          <Tooltip placement="bottom" text={statusDescription} theme="dark">
            <span>
              <InfoIcon fill={"#21252a"} />
            </span>
          </Tooltip>
        </div>
      )}
    </div>
  );
};
