@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.container {
  display: flex;
  padding-bottom: 24px;
}

.containerMobile {
  composes: container;

  > label {
    width: 100%;
  }
}

.subtitle {
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 27px;
  width: 100%;
  margin-top: 24px;
  margin-bottom: 12px;
}

.providerSearch {
  display: flex;
  flex-direction: column;
  flex: 3;

  > h3 {
    font-size: 18px;
    line-height: 27px;
    letter-spacing: 0;
    margin-top: 12px;
    margin-bottom: 12px;
  }

  > label {
    width: 100%;
    color: $color-neutral-700;
    font-size: 18px;
    letter-spacing: 0;
    line-height: 27px;
  }
}

.inputWrapper {
  display: flex;
  width: 642px;

  &.--mobile {
    width: 100%;
  }

  > div {
    width: 100%;

    > div {
      display: flex;

      > input {
        width: 100%;
      }
    }
  }
}

.inputWrapperMobile {
  composes: inputWrapper;
  width: 100%;
}

.providersWrapper {
  display: flex;
  flex-wrap: wrap;
  max-height: 544px;
  max-width: 642px;
  overflow-y: auto;

  > div {
    margin-right: 16px;
    margin-bottom: 16px;
  }
}

.providerContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  align-content: center;
  justify-content: center;
  box-sizing: border-box;
  width: 198px;
  height: 164px;
  border: 1px solid $color-neutral-400;
  border-radius: 6px;
  background-color: $color-neutral-00;

  > img {
    margin-top: 16px;
    object-fit: contain;
    width: 145px;
    height: 60px;
  }

  > label {
    font-size: 18px;
    line-height: 24px;
    letter-spacing: 0;
    font-weight: bold;
    text-align: center;
    margin-top: 8px;
    margin-bottom: 16px;
    max-width: 180px;
    text-overflow: ellipsis;
  }

  > button {
    display: none;
    cursor: pointer;
    border: none;
    bottom: 0;
    width: 100%;
    height: 48px;
    border-radius: 0 0 6px 6px;
    background-color: $color-primary-500;
    color: $color-neutral-00;
    font-size: 16px;
    font-weight: 600;
    letter-spacing: 0;
    line-height: 32px;
    text-align: center;
  }

  &:hover {
    > button {
      display: block;
    }
  }
}

.loader {
  display: flex;
  align-items: center;
  align-content: center;
  justify-content: center;
  width: 100%;
  margin-top: 32px;
  margin-bottom: 32px;
}
