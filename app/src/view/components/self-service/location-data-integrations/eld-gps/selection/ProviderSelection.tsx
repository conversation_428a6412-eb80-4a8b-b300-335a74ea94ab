import React, { useState } from "react";
import { useMediaQuery } from "react-responsive";
import { useTranslation } from "react-i18next";

import classNames from "classnames";

import genericGpsProvider from "assets/img/genericGpsProvider.png";

import { SearchIcon, XIcon } from "@fourkites/elemental-atoms";
import { Input } from "@fourkites/elemental-input";
import { Spinner } from "@fourkites/elemental-loading-indicator";

import { useAppSelector, useAppDispatch, useDebounce } from "state/hooks";
import { LocationProvidersState } from "state/modules/carrier/LocationProviders";

import ProviderNotFound from "./ProviderNotFound";
import SetupExplanation from "./SetupExplanation";
// import EldVoiceOnboardingButton from "../voice-onboarding/EldVoiceOnboardingButton";

import styles from "./ProviderSelection.module.scss";
import ProviderSelectionProps from "./ProviderSelection.types";

const ProviderSelection = ({
  isModal,
  isExternallyUsed,
  onSelectProvider,
  onProviderNotFound,
}: ProviderSelectionProps) => {
  const { t } = useTranslation();

  const isMobile = useMediaQuery({ maxWidth: 720 });

  /*****************************************************************************
   * REDUX
   ****************************************************************************/

  const dispatch = useAppDispatch();

  const locationProviders = useAppSelector(
    LocationProvidersState.selectors.locationProviders()
  );
  const isLoading = useAppSelector(
    LocationProvidersState.selectors.isLoadingLocationProviders()
  );

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [query, setQuery] = useState<string>("");

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  useDebounce(
    () => {
      // Only search after 3 chars
      if (!query || query?.length < 3) {
        //TODO: avoid duplicate calls
        dispatch(
          LocationProvidersState.actions.getLocationProviders({
            showPopular: true,
            query: "",
          })
        );
        return;
      }

      dispatch(
        LocationProvidersState.actions.getLocationProviders({
          showPopular: false,
          query,
        })
      );
    },
    300, // timeout
    [query]
  );

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div
      className={styles.container}
      id="provider-selection"
      data-test-id="provider-selection-page"
    >
      <ProvidersSearch
        isMobile={isMobile}
        isExternallyUsed={isExternallyUsed}
        isLoading={isLoading}
        isModal={isModal}
        locationProviders={locationProviders}
        query={query}
        setQuery={setQuery}
        onProviderNotFound={onProviderNotFound}
        onSelectProvider={onSelectProvider}
      />

      <SetupExplanation
        isMobile={isMobile}
        isExternallyUsed={isExternallyUsed}
      />
    </div>
  );
};

const ProvidersSearch = ({
  isMobile,
  isExternallyUsed,
  isLoading,
  isModal,
  locationProviders,
  query,
  setQuery,
  onProviderNotFound,
  onSelectProvider,
}: any) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const inputWrapperClass = classNames(styles.inputWrapper, {
    [styles.inputWrapperMobile]: isMobile && isExternallyUsed,
  });

  const isProviderNotFound = locationProviders?.length === 0;

  const providersWrapperMaxHeight = isModal
    ? "calc(100vh + -520px)"
    : "calc(100vh + -390px)";

  return (
    <div className={styles.providerSearch} id="providers-search">
      <div className={inputWrapperClass}>
        <Input
          label={""}
          placeholder={t("Select location Provider")}
          value={query}
          onChange={(e: any) => setQuery(e.target.value)}
          icon={query ? <XIcon /> : <SearchIcon />}
          onIconClick={() => setQuery("")}
          size="large"
        />
      </div>

      {/* Voice Onboarding Option - Inline variant */}
      {/* <EldVoiceOnboardingButton variant="inline" /> */}

      <label className={styles.subtitle} id="subtitle">
        {t("Or choose one from the list of popular location providers")}
      </label>

      {isLoading && (
        <div className={styles.loader}>
          <Spinner isLoading size="medium" />
        </div>
      )}

      {!isLoading && isProviderNotFound && (
        <ProviderNotFound onProviderNotFound={onProviderNotFound} />
      )}

      {!isLoading && !isProviderNotFound && (
        <>
          <h3 id="provider-selection-list-header">
            {t("Popular location providers")}
          </h3>
          <div
            id="providers-wrapper"
            className={styles.providersWrapper}
            style={{ maxHeight: providersWrapperMaxHeight }}
          >
            {locationProviders?.map((provider: any, index: number) => (
              <LocationProvider
                key={index}
                provider={provider}
                onSelectProvider={onSelectProvider}
              />
            ))}
          </div>
        </>
      )}
    </div>
  );
};

const LocationProvider = ({ provider, onSelectProvider }: any) => {
  const { t } = useTranslation();

  // Provider name should be display_name or name
  const getProviderName = (provider: any) =>
    provider?.display_name ? provider?.display_name : provider?.name;

  return (
    <div
      className={styles.providerContainer}
      id="provider-selection-provider-container"
      onClick={() => onSelectProvider(provider)}
    >
      <img
        className={styles.logo}
        src={provider?.logo ? provider?.logo : genericGpsProvider}
        alt="Provider logo"
      />
      <label>{getProviderName(provider)}</label>
      <button onClick={() => onSelectProvider(provider)}>{t("Select")}</button>
    </div>
  );
};

export default ProviderSelection;
