import { useTranslation } from "react-i18next";

import { Slide } from "react-slideshow-image";
import "react-slideshow-image/dist/styles.css";

import trustedByLogos from "assets/img/trusted-by-logos.png";
import gpsInfoCard1 from "assets/img/gps-info-card-1.png";
import gpsInfoCard2 from "assets/img/gps-info-card-2.png";

import { fourkitesUrls } from "api/http/apiUtils";

import styles from "./SetupExplanation.module.scss";

const SetupExplanation = ({ isMobile, isExternallyUsed }: any) => {
  const { t } = useTranslation();

  // NOTE: as we have a lot of variations on how this needs to be shown, we use
  // a combination of classes and styles to make it look right in all instances,
  // being:
  // 1) Internal wizard
  // 2) Internal modal
  // 3) External wizard desktop
  // 4) External wizard mobile
  const containerClass = isExternallyUsed
    ? styles.containerExternal
    : styles.container;
  const containerStyle = {
    width: isExternallyUsed && !isMobile ? "50%" : "100%",
  };
  const sliderStyle = { width: isMobile ? 300 : 550 };

  return (
    <div
      className={containerClass}
      style={containerStyle}
      id="setup-explanation"
    >
      <div className={styles.descriptionWrapper}>
        <h5 className={styles.title}>{t("Why join FourKites?")}</h5>

        <label className={styles.label}>
          {t(
            "6,000+ Carriers use FourKites by connecting their ELD devices to our " +
              "network. Connecting your ELD provider to FourKites gives your customers " +
              "the most accurate ETA data and removes the tracking responsibility from " +
              "your drivers. This takes just 2 minutes!"
          )}
        </label>
        <label className={styles.privacyMessage}>
          {t("Privacy and data security are our number one priority.")}
        </label>

        <a
          className={styles.privacyLink}
          href={fourkitesUrls.privacyPolicy}
          target="_blank"
        >
          {t("Learn more about our privacy policy")}
        </a>
      </div>

      <div style={sliderStyle} id="slide-container">
        <Slide cssClass={styles.slider} indicators>
          {slideImages.map((slideImage, index) => (
            <div key={index}>
              <img className={styles.logos} src={slideImage.url} />
            </div>
          ))}
        </Slide>
      </div>

      <div className={styles.descriptionWrapper}>
        <h5 className={styles.subTitle}>{t("Trusted by")}</h5>
        <img className={styles.logos} src={trustedByLogos} />
        <label>{t("...and more!")}</label>
      </div>
    </div>
  );
};

const slideImages = [
  {
    url: gpsInfoCard1,
  },
  {
    url: gpsInfoCard2,
  },
];

export default SetupExplanation;
