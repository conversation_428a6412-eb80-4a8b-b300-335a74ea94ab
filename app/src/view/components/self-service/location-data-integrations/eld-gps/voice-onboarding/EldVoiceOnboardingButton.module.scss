@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.container {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  margin: 24px 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 16px;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.icon {
  width: 48px;
  height: 48px;
  padding: 12px;
  background-color: #dbeafe;
  border-radius: 50%;
}

.title {
  font-weight: 600;
  font-size: 20px;
  line-height: 28px;
  color: #1f2937;
  margin: 0;
}

.description {
  font-size: 16px;
  line-height: 24px;
  color: #6b7280;
  margin: 0;
  max-width: 400px;
}

.inlineContainer {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
}

.inlineContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.inlineText {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.inlineIcon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.inlineLabel {
  font-size: 16px;
  font-weight: 500;
  color: #374151;
  line-height: 24px;
}

// Mobile responsiveness
@media (max-width: 640px) {
  .container {
    padding: 20px;
    margin: 20px 0;
  }

  .title {
    font-size: 18px;
    line-height: 24px;
  }

  .description {
    font-size: 14px;
    line-height: 20px;
  }

  .icon {
    width: 40px;
    height: 40px;
    padding: 10px;
  }

  .inlineContent {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .inlineText {
    justify-content: center;
  }

  .inlineLabel {
    font-size: 14px;
    text-align: center;
  }
}
