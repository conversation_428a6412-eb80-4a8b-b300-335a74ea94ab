import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import { Button, PhoneIcon } from "@fourkites/elemental-atoms";

import EldVoiceOnboardingModal from "./EldVoiceOnboardingModal";

import styles from "./EldVoiceOnboardingButton.module.scss";

interface EldVoiceOnboardingButtonProps {
  className?: string;
  variant?: "full" | "inline";
}

const EldVoiceOnboardingButton: React.FC<EldVoiceOnboardingButtonProps> = ({
  className,
  variant = "full",
}) => {
  const { t } = useTranslation();
  const [showModal, setShowModal] = useState(false);

  const handleOpenModal = () => {
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
  };

  if (variant === "inline") {
    return (
      <>
        <div className={`${styles.inlineContainer} ${className || ""}`}>
          <div className={styles.inlineContent}>
            <div className={styles.inlineText}>
              <PhoneIcon fill="#3b82f6" className={styles.inlineIcon} />
              <span className={styles.inlineLabel}>
                {t("Or let's onboard your ELD over a guided phone call")}
              </span>
            </div>
            <Button
              size="medium"
              theme="primary"
              variant="outline"
              onClick={handleOpenModal}
              data-testid="eld-voice-onboarding-inline-button"
            >
              <PhoneIcon fill="#3b82f6" iconClass="button-icon-left" />
              {t("Get Call")}
            </Button>
          </div>
        </div>

        <EldVoiceOnboardingModal show={showModal} onClose={handleCloseModal} />
      </>
    );
  }

  return (
    <>
      <div className={`${styles.container} ${className || ""}`}>
        <div className={styles.content}>
          <div className={styles.header}>
            <PhoneIcon fill="#3b82f6" className={styles.icon} />
            <h3 className={styles.title}>
              {t("Need Help? Get a Call from Our Team")}
            </h3>
          </div>
          <p className={styles.description}>
            {t(
              "Can't find your ELD provider or need assistance? Our experts can help you set up your ELD device over a personalized call."
            )}
          </p>
          <Button
            size="large"
            theme="primary"
            variant="outline"
            onClick={handleOpenModal}
            data-testid="eld-voice-onboarding-button"
          >
            <PhoneIcon fill="#3b82f6" iconClass="button-icon-left" />
            {t("Get Help via Call")}
          </Button>
        </div>
      </div>

      <EldVoiceOnboardingModal show={showModal} onClose={handleCloseModal} />
    </>
  );
};

export default EldVoiceOnboardingButton;
