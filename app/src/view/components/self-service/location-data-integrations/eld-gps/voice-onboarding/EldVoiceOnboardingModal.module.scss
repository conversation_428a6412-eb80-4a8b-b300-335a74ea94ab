@import "@fourkites/elemental-atoms/build/scss/colors/index";
@import "@fourkites/elemental-atoms/build/scss/typography/index";

.container {
  padding: 24px;
  max-width: 500px;
  margin: 0 auto;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.description {
  p {
    font-size: 16px;
    line-height: 24px;
    color: #374151;
    margin: 0;
    text-align: center;
  }
}

.form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.label {
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: #374151;
  margin-bottom: 4px;
}

.required {
  color: #ef4444;
  margin-left: 4px;
}

.select {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  padding: 12px;
  font-size: 14px;
  background-color: #ffffff;
  cursor: pointer;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  &:disabled {
    background-color: #f3f4f6;
    cursor: not-allowed;
    color: #9ca3af;
  }
}

.fieldDescription {
  font-size: 12px;
  line-height: 16px;
  color: #6b7280;
  margin: 0;
}

.actions {
  display: flex;
  justify-content: center;
  margin-top: 8px;

  button {
    min-width: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }
}

.successState {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 24px;
}

.successIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background-color: #d1fae5;
  border-radius: 50%;
}

.largeIcon {
  width: 40px;
  height: 40px;
}

.successContent {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 400px;
}

.successTitle {
  font-weight: 600;
  font-size: 20px;
  line-height: 28px;
  color: #065f46;
  margin: 0;
}

.successDescription {
  font-size: 16px;
  line-height: 24px;
  color: #374151;
  margin: 0;
}

.instructionsList {
  background-color: #f3f4f6;
  border-radius: 8px;
  padding: 16px;
  text-align: left;
}

.instructionsTitle {
  font-weight: 600;
  font-size: 14px;
  color: #374151;
  margin: 0 0 12px 0;
}

.instructions {
  margin: 0;
  padding-left: 20px;

  li {
    font-size: 14px;
    line-height: 20px;
    color: #6b7280;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.postCallActions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 8px;

  button {
    min-width: 140px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }
}

// Mobile responsiveness
@media (max-width: 640px) {
  .container {
    padding: 16px;
  }

  .content {
    gap: 20px;
  }

  .form {
    gap: 16px;
  }

  .actions {
    button {
      min-width: 100%;
    }
  }

  .successIcon {
    width: 60px;
    height: 60px;
  }

  .largeIcon {
    width: 30px;
    height: 30px;
  }

  .successTitle {
    font-size: 18px;
    line-height: 24px;
  }

  .successDescription {
    font-size: 14px;
    line-height: 20px;
  }

  .postCallActions {
    flex-direction: column;
    gap: 8px;

    button {
      min-width: 100%;
    }
  }
}
