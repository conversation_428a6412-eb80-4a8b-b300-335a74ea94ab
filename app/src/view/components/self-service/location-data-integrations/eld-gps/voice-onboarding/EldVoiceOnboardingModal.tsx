import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import { Button, PhoneIcon } from "@fourkites/elemental-atoms";
import { Modal } from "@fourkites/elemental-modal";
import { Spinner } from "@fourkites/elemental-loading-indicator";

import { useAppSelector } from "state/hooks";
import { UsersState } from "state/modules/Users";

import { showToast } from "view/components/base/toast/Toast";
import { isFieldInvalid } from "view/components/base/FormUtils";
import PhoneInput from "view/components/self-service/carrier-onboarding-survey/PhoneInput";
import { formatPhoneNumber } from "view/components/self-service/carrier-onboarding-survey/countryData";
import VoiceOnboardingApi from "api/carrier/VoiceOnboardingApi";

import styles from "./EldVoiceOnboardingModal.module.scss";

interface EldVoiceOnboardingModalProps {
  show: boolean;
  onClose: () => void;
}

const EldVoiceOnboardingModal: React.FC<EldVoiceOnboardingModalProps> = ({
  show,
  onClose,
}) => {
  const { t } = useTranslation();

  // Get carrier permalink from current context
  const carrierPermalink = useAppSelector(UsersState.selectors.getCompanyId);

  // State for form fields
  const [phoneNumber, setPhoneNumber] = useState("");
  const [country, setCountry] = useState("US");
  const [selectedLanguage, setSelectedLanguage] = useState("en");
  const [isLoading, setIsLoading] = useState(false);
  const [callInitiated, setCallInitiated] = useState(false);

  // Language options
  const languageOptions = [
    { value: "en", label: t("English") },
    { value: "es", label: t("Spanish") },
    { value: "fr", label: t("French") },
    { value: "tr", label: t("Turkish") },
  ];

  // Validation
  const isPhoneNumberInvalid = isFieldInvalid(phoneNumber);

  const handleSubmit = async () => {
    if (isPhoneNumberInvalid) {
      showToast(t("Please enter a valid phone number"), "", "error");
      return;
    }

    if (!carrierPermalink) {
      showToast(t("Carrier information not found"), "", "error");
      return;
    }

    setIsLoading(true);

    try {
      // Format phone number with country code
      const formattedPhoneNumber = formatPhoneNumber(phoneNumber, country);

      const response = await VoiceOnboardingApi.initiateEldOnboardingCall(
        formattedPhoneNumber,
        carrierPermalink,
        selectedLanguage
      );

      // Check if response status indicates success (2xx status codes)
      if (response?.status >= 200 && response?.status < 300) {
        setCallInitiated(true);
        showToast(
          t("ELD onboarding call initiated successfully"),
          t("You will receive a call shortly"),
          "ok"
        );
      } else {
        showToast(
          t("Failed to initiate call"),
          t("Please try again later"),
          "error"
        );
      }
    } catch (error) {
      console.error("Error initiating ELD onboarding call:", error);
      showToast(
        t("Failed to initiate call"),
        t("Please try again later"),
        "error"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setPhoneNumber("");
      setCountry("US");
      setSelectedLanguage("en");
      setCallInitiated(false);
      onClose();
    }
  };

  const handleMinimize = () => {
    // Keep the modal state but close it - user can reopen if needed
    onClose();
  };

  return (
    <Modal
      size="medium"
      title={callInitiated ? t("Call Initiated Successfully") : t("ELD Onboarding via Call")}
      show={show}
      closeButtonProps={{
        label: callInitiated ? t("Close") : t("Cancel"),
        onClick: handleClose,
        disabled: isLoading,
      }}
    >
      <div className={styles.container}>
        <div className={styles.content}>
          {!callInitiated ? (
            <>
              <div className={styles.description}>
                <p>
                  {t(
                    "Get help setting up your ELD device with a personalized call from our team. We'll guide you through the process step by step."
                  )}
                </p>
              </div>

              <div className={styles.form}>
                {/* Phone Number Input */}
                <div className={styles.field}>
                  <PhoneInput
                    label={t("Phone Number")}
                    placeholder={t("Enter your phone number")}
                    errorLabel={t("Please enter a valid phone number")}
                    value={phoneNumber}
                    onChange={setPhoneNumber}
                    country={country}
                    onChangeCountry={setCountry}
                    required
                    disabled={isLoading}
                    invalid={isPhoneNumberInvalid}
                  />
                </div>

                {/* Language Selection */}
                <div className={styles.field}>
                  <label className={styles.label}>
                    {t("Preferred Language for Call")}
                    <span className={styles.required}>*</span>
                  </label>
                  <select
                    className={styles.select}
                    value={selectedLanguage}
                    onChange={(e) => setSelectedLanguage(e.target.value)}
                    disabled={isLoading}
                  >
                    {languageOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                  <p className={styles.fieldDescription}>
                    {t("Select your preferred language for the onboarding call.")}
                  </p>
                </div>
              </div>

              <div className={styles.actions}>
                <Button
                  size="large"
                  theme="primary"
                  onClick={handleSubmit}
                  disabled={isLoading || isPhoneNumberInvalid}
                  data-testid="initiate-call-button"
                >
                  {isLoading ? (
                    <>
                      <Spinner isLoading size="small" />
                      {t("Initiating Call...")}
                    </>
                  ) : (
                    <>
                      <PhoneIcon fill="#fff" iconClass="button-icon-left" />
                      {t("Start ELD Onboarding Call")}
                    </>
                  )}
                </Button>
              </div>
            </>
          ) : (
            <>
              {/* Post-call initiated state */}
              <div className={styles.successState}>
                <div className={styles.successIcon}>
                  <PhoneIcon fill="#10b981" className={styles.largeIcon} />
                </div>
                <div className={styles.successContent}>
                  <h3 className={styles.successTitle}>
                    {t("Call Initiated Successfully!")}
                  </h3>
                  <p className={styles.successDescription}>
                    {t(
                      "You will receive a call shortly from our ELD onboarding specialist. They will guide you through the setup process using this interface."
                    )}
                  </p>
                  <div className={styles.instructionsList}>
                    <p className={styles.instructionsTitle}>
                      {t("What happens next:")}
                    </p>
                    <ul className={styles.instructions}>
                      <li>{t("You'll receive a call within 2-3 minutes")}</li>
                      <li>{t("Our specialist will guide you through the ELD setup")}</li>
                      <li>{t("You may need to interact with this page during the call")}</li>
                      <li>{t("Keep this browser tab open and accessible")}</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className={styles.postCallActions}>
                <Button
                  size="large"
                  theme="primary"
                  variant="outline"
                  onClick={handleMinimize}
                  data-testid="minimize-modal-button"
                >
                  {t("Minimize & Continue")}
                </Button>
                <Button
                  size="large"
                  theme="primary"
                  onClick={handleClose}
                  data-testid="close-modal-button"
                >
                  {t("Close")}
                </Button>
              </div>
            </>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default EldVoiceOnboardingModal;
