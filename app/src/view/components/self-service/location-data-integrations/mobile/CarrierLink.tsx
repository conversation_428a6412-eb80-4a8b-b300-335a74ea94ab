import React from "react";
import { useTranslation } from "react-i18next";

import fourkitesCircleLogo from "assets/img/fourkitesCircleLogo.png";
import appStoreLogo from "assets/img/appStoreLogo.png";
import googlePlayLogo from "assets/img/googlePlayLogo.png";

import styles from "./CarrierLink.module.scss";

const CarrierLink = ({}: any) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div className={styles.container}>
      <h2>
        <img className={styles.fourkitesCircleLogo} src={fourkitesCircleLogo} />
        {t("FourKites CarrierLink - The Ultimate Trucker Toolkit")}
      </h2>

      <div>
        <a
          target="_blank"
          href="https://apps.apple.com/us/app/fourkites-carrierlink/id1038402671"
        >
          <img className={styles.appStoreLogo} src={appStoreLogo} />
        </a>
        <a
          target="_blank"
          href="https://play.google.com/store/apps/details?id=mobile.fourkites.com.carrierLink&hl=en_US&gl=US"
        >
          <img className={styles.googlePlayLogo} src={googlePlayLogo} />
        </a>
      </div>
    </div>
  );
};

export default CarrierLink;
