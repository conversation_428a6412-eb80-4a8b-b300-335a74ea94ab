@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container {
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  width: 100%;
}

.content {
  display: flex;
  flex-direction: column;
  padding: 16px 48px;

  > h1 {
    display: flex;
    align-items: center;
    font-size: 34px;
    letter-spacing: 0;
    line-height: 51px;
    text-align: center;

    > a {
      display: flex;
      align-items: center;
      cursor: pointer;
      margin-right: 16px;
      background-color: transparent;
      border: none;
      border-radius: 24px;

      &:hover {
        background-color: $color-neutral-100;
      }
    }
  }

  > h3 {
    font-size: 24px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 36px;
    padding-top: 18px;
  }

  > label {
    width: 960px;
    color: #495057;
    font-size: 18px;
    letter-spacing: 0;
    line-height: 27px;
  }
}
