import React from "react";
import { useTranslation } from "react-i18next";

import { Modal } from "@fourkites/elemental-modal";

import CarrierLink from "./CarrierLink";

import styles from "./CarrierLinkModal.module.scss";

const CarrierLinkModal = ({ show, onClose }: any) => {
  const { t } = useTranslation();

  return (
    <Modal
      size="full"
      title={t("Mobile App")}
      show={show}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: onClose,
      }}
    >
      <div className={styles.container}>
        <div className={styles.content}>
          <CarrierLink />

          <h3 data-test-id="carrier-link-modal-title">{t("How It Works?")}</h3>

          <label>
            {t(
              "CarrierLink is a free, easy to use and time-saving app for truck " +
                "drivers to communicate about assigned loads without having to " +
                "make check-in calls. Using the app, drivers can send automatic " +
                "location updates which let your customers always know where " +
                "the load is. Avoid all the manual check-in calls to make your " +
                "driving distraction-free and safer."
            )}
            <br /> <br />
            {t(
              "Drivers can also upload documents, communicate about truck " +
                "availability, and access Truck Specific Turn-By-Turn " +
                "Navigation with fuel cost details across various route " +
                "options. The app will only track location only upon the " +
                "driver’s consent and only when there is an active load " +
                "being assigned."
            )}
            <br /> <br />
            {t("Please find a quick ")}
            <a href="https://vimeo.com/392790800" target="_blank">
              {t("introductory video ")}
            </a>{" "}
            {t("and find more information in our ")}
            <a
              href="https://support-fourkites.force.com/publicKB/s/"
              target="_blank"
            >
              {t("knowledge base")}
            </a>
          </label>
        </div>
      </div>
    </Modal>
  );
};

export default CarrierLinkModal;
