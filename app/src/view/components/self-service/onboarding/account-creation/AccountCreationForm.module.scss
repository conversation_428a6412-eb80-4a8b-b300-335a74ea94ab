@import "@fourkites/elemental-atoms/build/scss/colors/index";

.accountFormContainer {
  justify-content: center;
  align-items: center;
  display: flex;
  flex-direction: column;

  > button {
    width: 100%;
    display: flex;
    align-items: center;
    align-content: center;
    justify-content: center;
    text-align: center;
    cursor: pointer;
    background-color: transparent;
    border: none;
    border-radius: 4px;

    &:hover {
      background-color: $color-neutral-100;
    }
  }
}

.accountForm {
  display: flex;
  flex-direction: column;
  margin-top: 24px;
  margin-bottom: 36px;
}

.accountFormRow {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
}

.accountFormInput {
  flex: 1;
  margin-right: 16px;
  margin-bottom: 16px;

  &:last-child {
    margin-right: 0;
  }

  > div {
    width: 100%;

    > div {
      display: flex;

      > input {
        width: 100%;
      }
    }
  }
}

.termsAndConditions {
  display: flex;
  margin-top: 16px;

  > a {
    cursor: pointer;
    margin-left: 8px;
    text-decoration: underline;

    &:visited {
      color: $color-primary-500;
    }
  }
}

.privacyPoliceMessage {
  margin-top: 16px;
  max-width: 330px;
  margin-left: 10px;
  margin-right: 10px;
}
