import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import { Button, EyeIcon, ArrowRightIcon } from "@fourkites/elemental-atoms";
import { Checkbox } from "@fourkites/elemental-checkbox";
import { Input } from "@fourkites/elemental-input";

import {
  isFieldInvalid,
  isEmailInvalid,
  getFirstName,
  getLastName,
} from "view/components/base/FormUtils";

import PasswordValidation from "./PasswordValidation";

import AccountCreationFormProps from "./AccountCreationForm.types";
import styles from "./AccountCreationForm.module.scss";

const AccountCreationForm = ({
  form,
  setForm,
  confirmed,
  passwordHasError,
  setPasswordHasError,
  onConfirm,
}: AccountCreationFormProps) => {
  const { t } = useTranslation();

  const menuRef = React.useRef<HTMLDivElement>(null);

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [showPasswordValidation, setShowPasswordValidation] =
    useState<boolean>(true);
  const [revealPassword, setRevealPassword] = useState<boolean>(false);
  const [termsAgreed, setTermsAgreed] = useState<boolean>(false);

  // We prefill the form with business email
  const businessEmail = form?.email;
  //const arePasswordsEqual = form?.password === form?.passwordConfirmation;

  const passwordErrorMessage = passwordHasError
    ? "Password is not allowed"
    : "Field is required";

  const onChangePassword = (key: string, password: string) => {
    setForm(key, password);
    setPasswordHasError(false);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const firstNameValid = !isFieldInvalid(getFirstName(form?.fullName));
  const lastNameValid = !isFieldInvalid(getLastName(form?.fullName, ""));
  const nameError = firstNameValid
    ? "Last name required"
    : "Full name required";

  return (
    <div className={styles.accountFormContainer}>
      <div className={styles.accountForm}>
        <div className={styles.accountFormRow}>
          <div className={styles.accountFormInput}>
            <Input
              label={`${t("Full Name")}`}
              errorLabel={nameError}
              value={form?.fullName}
              invalid={confirmed && (!firstNameValid || !lastNameValid)}
              onChange={(e: any) => setForm("fullName", e.target.value)}
              required
            />
          </div>
        </div>

        <div className={styles.accountFormRow}>
          <div className={styles.accountFormInput}>
            <Input
              label={`${t("Business Email")}`}
              errorLabel="A valid email is required"
              value={businessEmail}
              invalid={confirmed && isEmailInvalid(businessEmail)}
              onChange={(e: any) => setForm("email", e.target.value)}
              required
            />
          </div>
        </div>

        <div className={styles.accountFormRow}>
          <div className={styles.accountFormInput} ref={menuRef}>
            <Input
              label={`${t("Create Password")}`}
              errorLabel={passwordErrorMessage}
              type={revealPassword ? "text" : "password"}
              value={form.password}
              invalid={
                confirmed &&
                (isFieldInvalid(form?.password) || passwordHasError)
              }
              onChange={(e: any) =>
                onChangePassword("password", e.target.value)
              }
              icon={<EyeIcon />}
              onIconClick={() => setRevealPassword(!revealPassword)}
              required
            />
          </div>
        </div>

        <div className={styles.accountFormRow}>
          <PasswordValidation
            menuRef={menuRef}
            password={form?.password}
            show={showPasswordValidation}
            setShow={setShowPasswordValidation}
          />
        </div>

        <div className={styles.accountFormRow}>
          <span className={styles.termsAndConditions}>
            <Checkbox
              label={
                <span>
                  {t("I agree to the FourKites")}{" "}
                  <a
                    href={
                      "https://www.fourkites.com/legal/general-terms-and-conditions-for-data-providers/"
                    }
                    target="_blank"
                  >
                    {t("Terms & Conditions")}
                  </a>
                </span>
              }
              size="large"
              value={termsAgreed ? "checked" : "unchecked"}
              onChange={(e: any) => setTermsAgreed(!termsAgreed)}
            />
          </span>
        </div>
      </div>

      {/* data-pendo-id to track the event */}
      <Button
        size="large"
        onClick={onConfirm}
        disabled={!termsAgreed}
        data-pendo-id="signup-user-information"
      >
        {t("Continue")}
        <ArrowRightIcon fill="#fff" iconClass={"button-icon-right"} />
      </Button>

      <label className={styles.privacyPoliceMessage}>
        {t(
          "The information you provide to use our platform will be " +
            "processed in accordance with the FourKites Privacy Policy."
        )}
      </label>
    </div>
  );
};

export default AccountCreationForm;
