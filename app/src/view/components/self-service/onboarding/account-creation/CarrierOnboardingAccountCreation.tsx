import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import UsersState from "state/modules/Users";

import {
  isFieldInvalid,
  isEmailInvalid,
  getFirstName,
  getLastName,
} from "view/components/base/FormUtils";

import CompanySummary from "view/components/self-service/company-summary/CompanySummary";

import { validatePassword } from "./PasswordValidation";
import AccountCreationForm from "./AccountCreationForm";
import PasswordAlert from "./PasswordAlert";

import CarrierOnboardingAccountCreationProps from "./CarrierOnboardingAccountCreation.types";
import styles from "./CarrierOnboardingAccountCreation.module.scss";

const CarrierOnboardingAccountCreation = ({
  isExternallyUsed = false,
  inviterName,
  form,
  setForm,
  onConfirm,
}: CarrierOnboardingAccountCreationProps) => {
  const { t } = useTranslation();

  const [confirmed, setConfirmed] = useState<boolean>(false);
  const [passwordHasError, setPasswordHasError] = useState<boolean>(false);
  const [passwordAlert, setPasswordAlert] = useState<string>("");

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const areInputsValid =
    !isFieldInvalid(getFirstName(form?.fullName)) &&
    !isFieldInvalid(getLastName(form?.fullName, "")) &&
    !isEmailInvalid(form?.email) &&
    !isFieldInvalid(form?.password);

  /*
   * Verifies if all conditions are of ok for creating user and, if so, call
   * external handler
   */
  const onConfirmUserCreation = async () => {
    setConfirmed(true);

    // Checks if password is valid
    const { isValid } = validatePassword(form?.password);

    // Checks if password has error
    const { hasError, alert } = await checkPasswordError(form);

    setPasswordHasError(hasError);
    setPasswordAlert(alert);

    if (areInputsValid && isValid && !hasError) {
      onConfirm();
    }
  };

  /*
   * Checks whether password has error by verifying forbidden passwords and
   * forbidden fields
   */
  const checkPasswordError = async (form: any) => {
    const lowerIncludes = (str1: string, str2: string) =>
      (str1 || "").toLowerCase().includes((str2 || "").toLowerCase());

    const { email, password, fullName } = form;

    const hasRepeatedFields =
      lowerIncludes(password, email) ||
      lowerIncludes(email, password) ||
      lowerIncludes(password, fullName);

    const isForbidden = !(await UsersState.helpers.isPasswordAllowed(password));

    const alert = isForbidden
      ? "Password provided is insecure. Try a different one."
      : hasRepeatedFields
      ? "Password should not match your email or name."
      : "";

    const hasError = password && (hasRepeatedFields || isForbidden);

    return { hasError, alert };
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div className={styles.container}>
      <div>
        <h1>{t("Welcome to FourKites!")}</h1>
        <label id="carrier-onboarding-account-creation-subtitle">
          {isExternallyUsed && t("You'll only have to do this once")}
          {!isExternallyUsed && (
            <>
              {t(
                `${inviterName} invites you to be part of their Visibility Network`
              )}
              <br />
              {t(`Share your name and create a password for your account.`)}
            </>
          )}
        </label>

        {isExternallyUsed && (
          <div className={styles.summaryContainer}>
            <CompanySummary data={form.companyDetails} />
          </div>
        )}

        <PasswordAlert
          passwordHasError={passwordHasError}
          alert={passwordAlert}
        />

        <AccountCreationForm
          form={form}
          setForm={setForm}
          confirmed={confirmed}
          passwordHasError={passwordHasError}
          setPasswordHasError={setPasswordHasError}
          onConfirm={onConfirmUserCreation}
        />
      </div>

      {/*
        // TODO: leaving this here in case we revert the flow
      {isUserCreated ? (
        <div>
          <h1>{t("Success!")}</h1>
          <label>
            {t("Your account was successfully created!")}
            <br />
            {t("Please check your email to reset your password.")}
          </label>
        </div>
      ) : (
        <AccountCreationForm
          form={form}
          setForm={setForm}
          onConfirm={onCreateCarrierUser}
        />
      )}
      */}
    </div>
  );
};

export default CarrierOnboardingAccountCreation;
