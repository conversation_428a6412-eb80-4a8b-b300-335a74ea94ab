import React from "react";

import { AlertTriangleIcon } from "@fourkites/elemental-atoms";

import styles from "./PasswordAlert.module.scss";

const PasswordAlert = ({ alert, passwordHasError }: any) => {
  if (!passwordHasError) {
    return null;
  }

  return (
    <div className={styles.passwordAlert} id="password-alert">
      <AlertTriangleIcon size="24px" />
      <label>{alert}</label>
    </div>
  );
};

export default PasswordAlert;
