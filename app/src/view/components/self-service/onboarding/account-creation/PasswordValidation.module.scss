@import "@fourkites/elemental-atoms/build/scss/colors/index";

.passwordValidationMenu {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  padding: 16px;
  box-sizing: border-box;
  border-radius: 6px;
  box-shadow: 0 1px 8px 2px rgba(0,0,0,0.12);
  z-index: 100;
  position: absolute;
  top: 400px;
  left: 1500px;

  > div[id="close-popup"] {
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }
}

.passwordOk {
  display: flex;
  align-content: center;
  align-items: center;
  margin-bottom: 16px;

  > span {
    display: flex;
    flex-direction: column;

    > label[id="description"] {
      max-width: 212px;
      font-size: 14px;
      letter-spacing: 0;
      line-height: 21px;
      margin-left: 8px;
    }

    > label[id="example"] {
      color: $color-neutral-700;
      font-size: 12px;
      letter-spacing: 0;
      line-height: 18px;
      margin-left: 8px;
    }
  }
}

.passwordError {
  composes: passwordOk;

  > span {
    > label[id="description"] {
      text-decoration: line-through;
    }
  }
}
