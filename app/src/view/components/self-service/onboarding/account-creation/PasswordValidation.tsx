import React from "react";
import { useMediaQuery } from "react-responsive";
import { useTranslation } from "react-i18next";

import { CheckIcon, XIcon } from "@fourkites/elemental-atoms";

import styles from "./PasswordValidation.module.scss";

const PasswordValidation = ({ show, setShow, menuRef, password }: any) => {
  const { t } = useTranslation();

  const isMobileOrTablet = useMediaQuery({ maxWidth: 820 });

  if (!show || !password) {
    return null;
  }

  const {
    lengthError,
    numberError,
    lowercaseError,
    uppercaseError,
    specialCharacterError,
  } = validatePassword(password);

  const caseError = lowercaseError || uppercaseError;

  // TODO: think of a better way to do this
  const top = isMobileOrTablet
    ? menuRef?.current?.offsetTop - 320
    : menuRef?.current?.offsetTop - 100;
  const left = isMobileOrTablet
    ? menuRef?.current?.offsetLeft + 80
    : menuRef?.current?.offsetLeft + 380;

  return (
    <div
      id="password-validation-menu"
      className={styles.passwordValidationMenu}
      style={{
        top: top || 400,
        left: left || 1000,
      }}
    >
      <div id="close-popup" onClick={() => setShow(false)}>
        <XIcon fill={"#495057"} />
      </div>

      <span className={lengthError ? styles.passwordError : styles.passwordOk}>
        {lengthError ? (
          <XIcon fill={"#da1e28"} />
        ) : (
          <CheckIcon fill={"#24a148"} />
        )}
        <span>
          <label id="description">
            {t("Password length must be between 8 and 64.")}
          </label>
        </span>
      </span>
      <span className={numberError ? styles.passwordError : styles.passwordOk}>
        {numberError ? (
          <XIcon fill={"#da1e28"} />
        ) : (
          <CheckIcon fill={"#24a148"} />
        )}
        <span>
          <label id="description">
            {t("Have at least one numerical character.")}
          </label>
          <label id="example">{t("(e.g. 1-9)")}</label>
        </span>
      </span>
      <span className={caseError ? styles.passwordError : styles.passwordOk}>
        {caseError ? (
          <XIcon fill={"#da1e28"} />
        ) : (
          <CheckIcon fill={"#24a148"} />
        )}
        <span>
          <label id="description">
            {t(
              "Contain a mix of lowercase and uppercase alphabetic characters."
            )}
          </label>
          <label id="example">{t("(e.g. a-z A-Z)")}</label>
        </span>
      </span>
      <span
        className={
          specialCharacterError ? styles.passwordError : styles.passwordOk
        }
      >
        {specialCharacterError ? (
          <XIcon fill={"#da1e28"} />
        ) : (
          <CheckIcon fill={"#24a148"} />
        )}
        <span>
          <label id="description">
            {t("Have at least one special character.")}
          </label>
          <label id="example">{t("(e.g. ~!@#$%^&*()_-+=)")}</label>
        </span>
      </span>
    </div>
  );
};

export const validatePassword = (password: string) => {
  const lengthError = password?.length < 8 || password?.length > 64;
  const numberError = !/\d/.test(password);
  const lowercaseError = !/[a-z]/.test(password);
  const uppercaseError = !/[A-Z]/.test(password);
  const specialCharacterError =
    !/[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/.test(password);

  return {
    isValid:
      !lengthError &&
      !numberError &&
      !lowercaseError &&
      !uppercaseError &&
      !specialCharacterError,
    lengthError,
    numberError,
    lowercaseError,
    uppercaseError,
    specialCharacterError,
  };
};

export default PasswordValidation;
