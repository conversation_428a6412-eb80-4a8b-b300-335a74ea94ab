@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: center;
  width: 100%;
}

.searchContainer {
  display: inline-block;
  align-items: center;
  align-content: center;
  justify-content: center;
  width: 720px;
  margin-top: 20px;
  margin-bottom: 60px;

  > div {
    width: 720px;

    > div {
      width: 720px;
    }
  }
}

.confirmCompanyWrapper {
  display: flex;
  align-items: center;
  flex: 1;
  width: 100%;
  flex-direction: column;

  > h1 {
    display: flex;
    align-items: center;
    font-size: 28px;
    font-weight: 300;
    letter-spacing: 0;
    line-height: 36px;
    text-align: center;

    > button {
      display: flex;
      align-items: center;
      cursor: pointer;
      margin-right: 16px;
      background-color: transparent;
      border: none;
      border-radius: 24px;

      &:hover {
        background-color: $color-neutral-100;
      }
    }
  }

  > label {
    max-width: 550px;
    padding-left: 24px;
    padding-right: 24px;
    font-size: 18px;
    letter-spacing: 0;
    line-height: 32px;
    text-align: center;
  }

  > div[id="company-data"] {
    width: 100%;
    max-width: 600px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 24px;

    > button {
      display: flex;
      align-items: center;
      align-content: center;
      justify-content: center;
      margin-top: 24px;
      //width: 100%;
    }
  }
}

.confirmCompanyWrapperMobile {
  composes: confirmCompanyWrapper;
  width: calc(100% + -48px);
  padding-left: 24px;
  padding-right: 24px;
}
