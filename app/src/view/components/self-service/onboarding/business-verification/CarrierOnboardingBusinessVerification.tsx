import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useMediaQuery } from "react-responsive";

import { CaretLeftIcon } from "@fourkites/elemental-atoms";

import NotMyCompanyModal from "./NotMyCompanyModal";
import CompanyAlreadyOnboarded from "./CompanyAlreadyOnboarded";
import ConfirmCompanyDetails from "./ConfirmCompanyDetails";
import CarrierRegionAndType from "./CarrierRegionAndType";

import CarrierOnboardingBusinessVerificationProps from "./CarrierOnboardingBusinessVerification.types";
import styles from "./CarrierOnboardingBusinessVerification.module.scss";

const CarrierOnboardingBusinessVerification = ({
  companyHasAdmin,
  companyDetails,
  // setCompanyDetails,
  form,
  onChangeFormField,
  onBack,
  onConfirm,
  onHelpRequested,
}: CarrierOnboardingBusinessVerificationProps) => {
  const { t } = useTranslation();

  const isMobile = useMediaQuery({ maxWidth: 720 });

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [showNotMyCompanyModal, setShowNotMyCompanyModal] =
    useState<boolean>(false);
  const [isNewCompany, setIsNewCompany] = useState<boolean>(false);
  const [isOnboarded, setIsOnboarded] = useState<boolean>(false);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const onClickBack = () => {
    setIsNewCompany(false);
    setIsOnboarded(false);
    // TODO: handle this when we have other flows
    //setCompanyDetails({ contact: { email: companyDetails?.contact?.email } });
    onBack();
  };

  const onSelectDifferentCompany = (companyName: string) => {
    setShowNotMyCompanyModal(true);

    //setIsNewCompany(true);
    //setIsOnboarded(false);
    //setCompanyDetails({ name: companyName });
  };

  /*
  const onSelectSuggestion = (company: any) => {
    setCompanyDetails({});
    setIsNewCompany(false);
    setIsOnboarded(company?.is_onboarded || false);
    setCompanyDetails(company);
  };

  const onSelectNotFound = (query: string) => {
    setIsNewCompany(true);
    setIsOnboarded(false);
    setCompanyDetails({ name: query });
  };

  const onChangeCompany = () => {
    setIsNewCompany(false);
    setIsOnboarded(false);
    setCompanyDetails(null);
  };

  const areNewCompanyFieldsInvalid = () => {
    if (!isNewCompany) {
      return false;
    }

    const identifications = companyDetails?.identifications;
    const usdot =
      identifications?.find((id: any) => id?.type === "usdot")?.value || "";
    const mc =
      identifications?.find((id: any) => id?.type === "mc")?.value || "";

    return (
      (isFieldInvalid(usdot) && isFieldInvalid(mc)) ||
      isFieldInvalid(companyDetails?.name) ||
      isFieldInvalid(companyDetails?.address?.address) ||
      isFieldInvalid(companyDetails?.address?.city) ||
      isFieldInvalid(companyDetails?.address?.country) ||
      isFieldInvalid(companyDetails?.address?.state) ||
      isFieldInvalid(companyDetails?.address?.zip) ||
      isFieldInvalid(companyDetails?.phone)
    );
  };
  */

  /*
   * Sends a help request that something is wrong with the company
   */
  const onConfirmNotMyCompany = (wrongInformation: string[]) => {
    onHelpRequested("company_has_wrong_information", wrongInformation);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const shouldConfirmCompany = companyDetails != null && !companyHasAdmin;

  return (
    <div className={styles.container}>
      {shouldConfirmCompany ? (
        <div
          className={
            isMobile
              ? styles.confirmCompanyWrapperMobile
              : styles.confirmCompanyWrapper
          }
        >
          <h1>
            <button onClick={onClickBack}>
              <CaretLeftIcon />
            </button>
            {t("Business Verification")}
          </h1>

          <label>
            {/*
        t("Lookup for your Company with any Identification Number or Name.")
        */}
            {t("Confirm your company details")}
          </label>

          <CarrierRegionAndType
            companyType={form.type}
            region={form.region}
            setCompanyType={(value: string) => {
              onChangeFormField("type", value);
            }}
            setRegion={(value: string) => {
              onChangeFormField("region", value);
            }}
          />

          <ConfirmCompanyDetails
            companyDetails={companyDetails}
            isNewCompany={isNewCompany}
            onSelectDifferentCompany={onSelectDifferentCompany}
            onConfirm={onConfirm}
            onClickBack={onClickBack}
          />
        </div>
      ) : (
        <CompanyAlreadyOnboarded
          //onChangeCompany={onChangeCompany}
          companyDetails={companyDetails}
          onHelpRequested={onHelpRequested}
        />
      )}

      <NotMyCompanyModal
        show={showNotMyCompanyModal}
        onClose={() => setShowNotMyCompanyModal(false)}
        onConfirm={onConfirmNotMyCompany}
      />
    </div>
  );
};

export default CarrierOnboardingBusinessVerification;
