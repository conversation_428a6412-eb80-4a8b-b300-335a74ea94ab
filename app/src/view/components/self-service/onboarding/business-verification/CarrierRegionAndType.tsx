import React from "react";
import { useTranslation } from "react-i18next";

import { Button, Label, CaretLeftIcon } from "@fourkites/elemental-atoms";
import { ButtonGroup, GroupButton } from "@fourkites/elemental-button-group";
import { Select } from "@fourkites/elemental-select";

import styles from "./CarrierRegionAndType.module.scss";

const CarrierRegionAndType = ({
  region,
  setRegion,
  companyType,
  setCompanyType,
  onBack,
  onConfirm,
}: any) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const onSelectRegion = (value: string[]) => {
    setRegion(value[0]);
  };

  const onSelectCompanyType = (id: string) => {
    setCompanyType(id);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const regionOptions = [
    "US & Canada",
    "Latin America",
    "Europe",
    "APAC",
    "Africa",
    "Middle-East",
  ];

  return (
    <div className={styles.container}>
      <div id="region-wrapper">
        <Select
          label={t("Region")}
          options={regionOptions}
          value={[region]}
          onChange={onSelectRegion}
          size="large"
        />
      </div>

      <div id="company-type-wrapper">
        <Label size="large">{t("Type")}</Label>
        <ButtonGroup
          onButtonClick={onSelectCompanyType}
          selectedButtons={[companyType]}
          size="large"
        >
          <GroupButton buttonId="carrier" selected>
            {t("Carrier")}
          </GroupButton>
          <GroupButton buttonId="broker">{t("Broker")}</GroupButton>
          <GroupButton buttonId="both">{t("Both")}</GroupButton>
        </ButtonGroup>
      </div>
    </div>
  );
};

export default CarrierRegionAndType;
