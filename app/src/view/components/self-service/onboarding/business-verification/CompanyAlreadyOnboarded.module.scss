@import "@fourkites/elemental-atoms/build/scss/colors/index";

.onboardedCompanyContainer {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.onboardedSummaryWrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 912px;
  border: 1px solid $color-neutral-300;
  border-radius: 4px;
  background-color: white;
  padding: 8px 16px;

  > a {
    cursor: pointer;
    width: 150px;
    color: $color-primary-500;
    font-size: 16px;
    letter-spacing: 0;
    line-height: 24px;
    text-decoration: underline;
  }
}

.onboardedSummaryWrapperMobile {
  composes: onboardedSummaryWrapper;
  width: 90%;
}

.onboardedCompanyContent {
  width: 632px;
  justify-content: center;
  text-align: center;

  > img {
    height: 150px;
    width: 150px;
    object-fit: contain;
    margin: 32px 0px 16px 0px;
  }

  > h1 {
    font-size: 24px;
    font-weight: bold;
    letter-spacing: 0;
    line-height: 36px;
  }

  > h2 {
    font-size: 24px;
    letter-spacing: 0;
    line-height: 36px;
  }

  > label {
    font-size: 20px;
    letter-spacing: 0;
    line-height: 30px;
    text-align: center;
    margin-bottom: 24px;
  }

  > a {
    cursor: pointer;
    color: $color-primary-500;
    font-size: 16px;
    letter-spacing: 0;
    line-height: 24px;
    text-decoration: underline;
  }
}
