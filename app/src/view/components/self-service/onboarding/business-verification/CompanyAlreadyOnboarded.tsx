import React from "react";
import { useMediaQuery } from "react-responsive";
import { useTranslation } from "react-i18next";

import { Button, HeadphoneIcon } from "@fourkites/elemental-atoms";

import onboardingCompanyOnboarded from "assets/img/onboardingCompanyOnboarded.png";

import CompanySummary from "view/components/self-service/company-summary/CompanySummary";

import styles from "./CompanyAlreadyOnboarded.module.scss";

const CompanyAlreadyOnboarded = ({
  //onChangeCompany,
  companyDetails,
  onHelpRequested,
}: any) => {
  const { t } = useTranslation();

  const isMobile = useMediaQuery({ maxWidth: 720 });

  return (
    <div className={styles.onboardedCompanyContainer}>
      <div
        className={
          isMobile
            ? styles.onboardedSummaryWrapperMobile
            : styles.onboardedSummaryWrapper
        }
      >
        <CompanySummary data={companyDetails} />
        {/*
        <a onClick={onChangeCompany}>{t("Change Company")}</a>
        */}
      </div>

      <div className={styles.onboardedCompanyContent}>
        <img alt="Company Already Onboarded" src={onboardingCompanyOnboarded} />
        <h1>{t("Good News!")}</h1>
        <h2>
          {t("Your company already has a FourKites account you can join.")}
        </h2>
        <label>
          {t("A notification will be sent to your company Admin.")}
          <br />
          {t("Once your request is accepted you’ll receive a email to login.")}
        </label>
        <br /> <br /> <br />
        <Button
          size="large"
          theme="tertiary"
          onClick={() => onHelpRequested("company_already_has_users")}
        >
          <HeadphoneIcon fill="#0e65e5" iconClass={"button-icon-left"} />
          {t("For queries reach FourKites Support")}
        </Button>
      </div>
    </div>
  );
};

export default CompanyAlreadyOnboarded;
