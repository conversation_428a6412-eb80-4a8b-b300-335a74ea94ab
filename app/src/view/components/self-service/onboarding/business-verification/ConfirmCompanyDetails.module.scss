@import "@fourkites/elemental-atoms/build/scss/colors/index";

.notOnboardedSummaryWrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 568px;
  padding: 16px;
  margin-top: 32px;
  border: 1px solid $color-neutral-300;
  border-radius: 4px;

  > a {
    cursor: pointer;
    width: 180px;
    padding-top: 16px;
    color: $color-primary-500;
    font-size: 16px;
    letter-spacing: 0;
    line-height: 24px;
    text-decoration: underline;
  }
}

.notOnboardedSummaryWrapperMobile {
  composes: notOnboardedSummaryWrapper;
  width: calc(100% + -36px);
}
