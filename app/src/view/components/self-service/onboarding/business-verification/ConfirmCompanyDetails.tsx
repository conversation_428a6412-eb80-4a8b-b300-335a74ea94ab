import { useMediaQuery } from "react-responsive";
import { useTranslation } from "react-i18next";

import { Button, ArrowRightIcon } from "@fourkites/elemental-atoms";

import CompanySummary from "view/components/self-service/company-summary/CompanySummary";
//import CompanySuggestions from "view/components/self-service/company-suggestions/CompanySuggestions";
//import NewCompany, { isFieldInvalid } from "./NewCompany";

import styles from "./ConfirmCompanyDetails.module.scss";

const ConfirmCompanyDetails = ({
  isNewCompany,
  companyDetails,
  onClickBack,
  onSelectDifferentCompany,
  onConfirm,
}: any) => {
  const { t } = useTranslation();

  const isMobile = useMediaQuery({ maxWidth: 720 });

  return (
    <>
      {/*
        //TODO: right now we are not allowing new companies
        <div className={styles.searchContainer}>
          <CompanySuggestions
            suggestedCompanyType={"carrier"}
            onSelectSuggestion={onSelectSuggestion}
            onSelectNotFound={onSelectNotFound}
            invitationToken={invitationToken}
            isOnboarding
          />
        </div>
      */}

      <div id="company-data">
        {/*
          {isNewCompany ? (
          <NewCompany
            companyDetails={companyDetails}
            setCompanyDetails={setCompanyDetails}
          />
        ) : (
        */}
        <div
          className={
            isMobile
              ? styles.notOnboardedSummaryWrapperMobile
              : styles.notOnboardedSummaryWrapper
          }
        >
          <CompanySummary data={companyDetails} />
          <a onClick={() => onSelectDifferentCompany(companyDetails?.name)}>
            {t("Not your Company?")}
          </a>
        </div>
        {/*)}  */}

        {/* data-pendo-id to track the event */}
        <Button
          size="large"
          onClick={onConfirm}
          data-pendo-id="signup-company-business-information"
          //disabled={areNewCompanyFieldsInvalid()}
        >
          {isNewCompany ? t("Save & Continue") : t("Finish Sign Up")}
          <ArrowRightIcon fill="#fff" iconClass={"button-icon-right"} />
        </Button>
      </div>
    </>
  );
};

export default ConfirmCompanyDetails;
