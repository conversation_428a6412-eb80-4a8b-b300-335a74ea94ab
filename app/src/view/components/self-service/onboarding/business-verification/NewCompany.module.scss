@import "@fourkites/elemental-atoms/build/scss/colors/index";

.newCompanyContainer {
  > label {
    font-weight: bold;
  }

  > div {
    display: flex;
    width: 612px;
    flex-direction: column;
    margin-top: 8px;
    box-sizing: border-box;
    border: 1px solid #DEE2E6;
    border-radius: 4px;
    background-color: #FFFFFF;
    padding: 8px 16px 16px 16px;
  }
}

.newCompanyFormRow {
  display: flex;
  width: 100%;
  flex-wrap: wrap;

  > label {
    margin: 14px 0px 4px 0px;
  }
}

.newCompanyFormInput {
  flex: 1;
  margin-right: 16px;
  margin-bottom: 2px;

  &:last-child {
    margin-right: 0;
  }

  > div {
    width: 100%;

    > div {
      display: flex;

      > input {
        width: 100%;
      }
    }
  }
}

.wrongInformationContainer {
  display: flex;
  width: 100%;
  flex-direction: column;
  justify-content: center;
  align-content: center;
  align-items: center;

  > div {
    margin-top: 16px;
  }
}
