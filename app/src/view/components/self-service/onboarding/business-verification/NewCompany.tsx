import React from "react";
import { useTranslation } from "react-i18next";

import { Input } from "@fourkites/elemental-input";
import styles from "./NewCompany.module.scss";

const NewCompany = ({ companyDetails, setCompanyDetails }: any) => {
  const { t } = useTranslation();

  const identifications = companyDetails?.identifications;
  const usdot =
    identifications?.find((id: any) => id?.type === "usdot")?.value || "";
  const mc = identifications?.find((id: any) => id?.type === "mc")?.value || "";

  const addIdentification = (fieldName: string, fieldValue: string) => {
    const idIndex = identifications?.findIndex(
      (id: any) => id.type === fieldName
    );

    if (idIndex == null || idIndex === -1) {
      return [
        ...(identifications || []),
        { type: fieldName, value: fieldValue },
      ];
    }

    return identifications?.map((id: any) =>
      id.type === fieldName ? { type: fieldName, value: fieldValue } : id
    );
  };

  return (
    <div className={styles.newCompanyContainer}>
      <label>{t("Enter business details:")}</label>

      <div>
        <div className={styles.newCompanyFormRow}>
          <div className={styles.newCompanyFormInput}>
            <Input
              size="small"
              label={`${t("Business Name")}`}
              value={companyDetails?.name}
              onChange={(e: any) =>
                setCompanyDetails({ ...companyDetails, name: e.target.value })
              }
              invalid={isFieldInvalid(companyDetails?.name)}
              required
            />
          </div>
        </div>

        <div className={styles.newCompanyFormRow}>
          <div className={styles.newCompanyFormInput}>
            <Input
              size="small"
              label={`${t("USDOT#")}`}
              value={usdot}
              invalid={isFieldInvalid(usdot) && isFieldInvalid(mc)}
              onChange={(e: any) =>
                setCompanyDetails({
                  ...companyDetails,
                  identifications: addIdentification("usdot", e.target.value),
                })
              }
              required
            />
          </div>

          <div className={styles.newCompanyFormInput}>
            <Input
              size="small"
              label={`${t("MC#")}`}
              value={mc}
              invalid={isFieldInvalid(usdot) && isFieldInvalid(mc)}
              onChange={(e: any) =>
                setCompanyDetails({
                  ...companyDetails,
                  identifications: addIdentification("mc", e.target.value),
                })
              }
              required
            />
          </div>
        </div>

        <div className={styles.newCompanyFormRow}>
          <label>{t("Business Address & Phone")}</label>
        </div>

        <div className={styles.newCompanyFormRow}>
          <div className={styles.newCompanyFormInput}>
            <Input
              size="small"
              label={`${t("Address")}`}
              value={companyDetails?.address?.address}
              invalid={isFieldInvalid(companyDetails?.address?.address)}
              onChange={(e: any) =>
                setCompanyDetails({
                  ...companyDetails,
                  address: {
                    ...companyDetails.address,
                    address: e.target.value,
                  },
                })
              }
              required
            />
          </div>
        </div>

        <div className={styles.newCompanyFormRow}>
          <div className={styles.newCompanyFormInput}>
            <Input
              size="small"
              label={`${t("City")}`}
              value={companyDetails?.address?.city}
              invalid={isFieldInvalid(companyDetails?.address?.city)}
              onChange={(e: any) =>
                setCompanyDetails({
                  ...companyDetails,
                  address: {
                    ...companyDetails.address,
                    city: e.target.value,
                  },
                })
              }
              required
            />
          </div>

          <div className={styles.newCompanyFormInput}>
            <Input
              size="small"
              label={`${t("State / Province")}`}
              value={companyDetails?.address?.state}
              invalid={isFieldInvalid(companyDetails?.address?.state)}
              onChange={(e: any) =>
                setCompanyDetails({
                  ...companyDetails,
                  address: {
                    ...companyDetails.address,
                    state: e.target.value,
                  },
                })
              }
              required
            />
          </div>
        </div>

        <div className={styles.newCompanyFormRow}>
          <div className={styles.newCompanyFormInput}>
            <Input
              size="small"
              label={`${t("Zip Code")}`}
              value={companyDetails?.address?.zip}
              invalid={isFieldInvalid(companyDetails?.address?.zip)}
              onChange={(e: any) =>
                setCompanyDetails({
                  ...companyDetails,
                  address: {
                    ...companyDetails.address,
                    zip: e.target.value,
                  },
                })
              }
              required
            />
          </div>

          <div className={styles.newCompanyFormInput}>
            <Input
              size="small"
              label={`${t("Country")}`}
              value={companyDetails?.address?.country}
              invalid={isFieldInvalid(companyDetails?.address?.country)}
              onChange={(e: any) =>
                setCompanyDetails({
                  ...companyDetails,
                  address: {
                    ...companyDetails.address,
                    country: e.target.value,
                  },
                })
              }
              required
            />
          </div>
        </div>

        <div className={styles.newCompanyFormRow}>
          <div className={styles.newCompanyFormInput}>
            <Input
              size="small"
              label={`${t("Phone")}`}
              value={companyDetails?.phone}
              invalid={isFieldInvalid(companyDetails?.phone)}
              onChange={(e: any) =>
                setCompanyDetails({
                  ...companyDetails,
                  phone: e.target.value,
                })
              }
              required
            />
          </div>

          <div className={styles.newCompanyFormInput} />
        </div>
      </div>
    </div>
  );
};

export const isFieldInvalid = (fieldValue: string) => {
  return fieldValue == null || fieldValue?.trim() === "";
};

export default NewCompany;
