import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import { Modal } from "@fourkites/elemental-modal";
import { ButtonGroup, GroupButton } from "@fourkites/elemental-button-group";

import styles from "./NewCompany.module.scss";

const NotMyCompanyModal = ({ show, onClose, onConfirm }: any) => {
  const { t } = useTranslation();

  const [wrongInformation, setWrongInformation] = useState<string[]>([]);

  const onButtonClick = (buttonId: string) => {
    const newState = wrongInformation.includes(buttonId)
      ? wrongInformation.filter((id) => id !== buttonId)
      : [...wrongInformation, buttonId];

    setWrongInformation(newState);
  };

  return (
    <Modal
      size="small"
      title={t("Incorrect Information?")}
      show={show}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: onClose,
      }}
      saveButtonProps={{
        label: t("Confirm"),
        onClick: () => onConfirm(wrongInformation),
      }}
    >
      <div className={styles.wrongInformationContainer}>
        {t(
          "Help FourKites to know what's wrong with our information and we will fix it ASAP."
        )}

        <div>
          <ButtonGroup
            onButtonClick={onButtonClick}
            selectedButtons={wrongInformation}
            size="large"
          >
            <GroupButton buttonId="name" selected>
              {t("Incorrect Company Name")}
            </GroupButton>
            <GroupButton buttonId="identifications">
              {t("Incorrect Company ID # (USDOT/MC)")}
            </GroupButton>
          </ButtonGroup>
        </div>
      </div>
    </Modal>
  );
};

export default NotMyCompanyModal;
