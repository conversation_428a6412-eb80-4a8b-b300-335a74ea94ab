import React from "react";
import { useTranslation } from "react-i18next";
import { fourkitesUrls, externalUrls } from "api/http/apiUtils";
import styles from "./OnboardingFooter.module.scss";

const OnboardingFooter = () => {
  const { t } = useTranslation();

  return (
    <div className={styles.onboardingFormFooter}>
      <a href={fourkitesUrls.privacyPolicy} target="_blank">
        {t("Privacy Policy")}
      </a>
    </div>
  );
};

export default OnboardingFooter;
