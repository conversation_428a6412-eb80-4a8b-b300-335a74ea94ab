import React from "react";

import { ColoredFKLogo } from "@fourkites/elemental-atoms";

import styles from "./CarrierOnboardingProgressHeader.module.scss";

const CarrierOnboardingProgressHeader = ({
  onboardingStep,
  skipFirstStep,
  showLogo = true,
  showLabel = false,
}: any) => {
  const steps = [...(skipFirstStep ? [] : [1]), 2];

  return (
    <div className={styles.container}>
      {showLogo && <ColoredFKLogo />}
      <div>
        <div>
          {steps.map((step: number) => (
            <div
              key={step}
              className={
                step === onboardingStep
                  ? styles.headerStepSelected
                  : styles.headerStep
              }
            />
          ))}
        </div>

        {showLabel && (
          <label id="carrier-onboarding-progress-header-label">
            Step {onboardingStep} of {steps.length}
          </label>
        )}
      </div>
    </div>
  );
};

export default CarrierOnboardingProgressHeader;
