import React from "react";
import { useTranslation } from "react-i18next";

import { fourkitesUrls } from "api/http/apiUtils";

import styles from "./OnboardingHeader.module.scss";

const OnboardingHeader = () => {
  const { t } = useTranslation();

  return (
    <div className={styles.onboardingFormHeader}>
      {/* Removed "Already have a FourKites account? Sign In" for survey page */}
    </div>
  );
};

export default OnboardingHeader;
