@import "@fourkites/elemental-atoms/build/scss/colors/index";

.sidePanelContainer {
  height: 100vh;
  min-height: 700px;
  background-image: url("../../../../../assets/img/onboardingSidePanel.png");
  background-size: cover;
  flex:1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  background-size: cover;

  > div[id="padder"]{
    height: 150px;
  }

  > div[id="description"]{
    display: flex;
    flex-direction: column;
    align-items: left;
    align-content: left;
    margin-top: 16px;
    margin-bottom: 32px;
    margin-left: 32px;
    margin-right: 32px;
    color: $color-neutral-00;

    > h1 {
      font-size: 18px;
      font-weight: 600;
      letter-spacing: 0;
      line-height: 27px;
    }

    > span {
      margin-top: 16px;
      display: flex;
      align-items: center;

      > img {
        height: 20px;
        width: 20px;
      }

      > label {
        margin-left: 8px;
        font-size: 16px;
        letter-spacing: 0;
        line-height: 24px;
      }
    }
  }

  > a {
    margin-top: 54px;
    width: 200px;
  }

  > a[id="know-more"] {
    text-align: center;
    margin-top: 16px;
    width: 100%;
    align-items: center;
    color: $color-neutral-00;
  }
}

.invitationDetails {
  display: flex;
  flex-direction: column;
  width: 70%;
  justify-content: center;
  align-items: center;
  padding: 24px;
  border: 1px solid $color-neutral-400;
  border-radius: 4px;
  background-color: $color-neutral-00;

  > label {
    color: $color-primary-500;
    font-size: 18px;
    font-weight: 600;
    letter-spacing: 0;
    line-height: 27px;
  }

  > a {
    margin-top: 16px;
    margin-bottom: 24px;
  }

  > div[id="logos-wrapper"] {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.logo {
  object-fit: contain;
  height: 52px;
  width: 200px;
}
