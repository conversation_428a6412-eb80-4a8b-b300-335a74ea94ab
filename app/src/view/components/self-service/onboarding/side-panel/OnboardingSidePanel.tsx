import React from "react";
import { useTranslation } from "react-i18next";

import { PlusIcon } from "@fourkites/elemental-atoms";
import { ColoredFKLogo } from "@fourkites/elemental-atoms";
import greenCheck from "assets/img/greenCheck.png";

import { fourkitesUrls } from "api/http/apiUtils";

import styles from "./OnboardingSidePanel.module.scss";

const OnboardingSidePanel = ({ invitationDetails }: any) => {
  const { t } = useTranslation();

  const logo = invitationDetails?.inviter?.logo;

  return (
    <div className={styles.sidePanelContainer}>
      <div id="padder" />
      <div className={styles.invitationDetails}>
        <div id="logos-wrapper">
          {logo != null && (
            <>
              <img className={styles.logo} src={logo} alt="Company logo" />
              <PlusIcon className={styles.icon} />
            </>
          )}
          <ColoredFKLogo className={styles.logo} />
        </div>
      </div>

      <div id="description">
        <h1>{t("Why Join FourKites?")}</h1>
        <span>
          <img src={greenCheck} />
          <label>
            <b>{t("Save time ")}</b>
            {t("by reducing check calls")}
          </label>
        </span>

        <span>
          <img src={greenCheck} />
          <label>
            <b>{t("Improve customer service ")}</b>
            {t("by providing exceptional visibility support")}
          </label>
        </span>

        <span>
          <img src={greenCheck} />
          <label>
            <b>{t("Reduce Dwell time ")}</b>
            {t("by gaining more visibility on appointment times and ETAs")}
          </label>
        </span>

        <span>
          <img src={greenCheck} />
          <label>
            <b>{t("Validate Detention claims ")}</b>
            {t("with greater access to accurate shipment transit data")}
          </label>
        </span>
      </div>

      <div className="video-responsive">
        <iframe
          width="438"
          height="235"
          src={`https://www.youtube.com/embed/sYFlXhTrUe8`}
          frameBorder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
          title="Embedded youtube"
        />
      </div>

      <a
        href={fourkitesUrls.businessKnowledgeBase}
        target="_blank"
        id="know-more"
      >
        {t("Know more about what it means and how it helps your business")}
      </a>
    </div>
  );
};

export default OnboardingSidePanel;
