@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container{
  margin: 24px 24px 40px 24px;
  width: 100%;
}

.titleContainer {
  display: inline-flex;
  justify-content: space-between;
  width: 95%;

  > label {
    color: $color-neutral-700;
    font-size: 20px;
    font-weight: 600;
    letter-spacing: 0;
    line-height: 24px;
  }

}

.content {
  display: flex;
  margin-top: 35px;
}

.verticalDivider {
  height: 70px;
  width: 1px;
  opacity: 0.6;
  background: linear-gradient(180deg, #FFFFFF 0%, #B2B2B2 50.54%, #FFFFFF 100%);
  margin-right: 24px;
}

.principalContent {
  display: flex;
  width: 100%;
  min-width: 230px;
  flex:1;
  flex-direction: column;
  justify-content: center;
  color: $color-neutral-700;
  font-size: 40px;
  font-weight: 600;
  letter-spacing: 0;
  line-height: 43px;

  > label {
    color: $color-neutral-700;
    font-size: 18px;
    font-weight: 600;
    letter-spacing: 0;
    line-height: 30px;
  }
}
