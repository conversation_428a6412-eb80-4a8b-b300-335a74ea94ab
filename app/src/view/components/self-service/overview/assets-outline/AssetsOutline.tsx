import React from "react";
import { useTranslation } from "react-i18next";

import { DIRECT_ASSIGNMENT_APP_URL } from "api/http/apiUtils";

import { LinkButton } from "@fourkites/elemental-atoms";

import styles from "./AssetsOutline.module.scss";
import AssetsOutlineProps from "./AssetsOutline.types";

const AssetsOutline = ({
  companyName,
  assigned,
  notAssigned,
  directAssignmentInfo,
}: AssetsOutlineProps) => {
  const { t } = useTranslation();

  const directAssignmentUrl = `${DIRECT_ASSIGNMENT_APP_URL}/${
    directAssignmentInfo?.apiToken
  }/${btoa(companyName)}`;

  return (
    <div className={styles.container}>
      <div className={styles.titleContainer}>
        <label className={styles.title}>{t("Asset Assignment")}</label>

        <LinkButton
          href={directAssignmentUrl}
          theme="tertiary"
          target="_blank"
          data-test-id="link-asset-assign"
        >
          {t("ASSIGN NOW")}
        </LinkButton>
      </div>

      <div className={styles.content}>
        <div className={styles.principalContent}>
          {assigned}
          <label>{t("Loads Assigned")}</label>
        </div>
        <div className={styles.principalContent}>
          {notAssigned}
          <label>{t("Not Assigned")}</label>
        </div>

        {/*
        <div className={styles.principalContent}>
          {notAssigned}
          <label>{t("Assigned & Not Tracking")}</label>
        </div>
        */}
      </div>
    </div>
  );
};

export default AssetsOutline;
