@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container {
  margin: 20px 20px 40px 20px;
  width: 100%;
}

.header {
  margin-bottom: 20px;

  .title {
    color: $color-neutral-700;
    font-size: 20px;
    font-weight: 600;
    letter-spacing: 0;
    line-height: 24px;
  }
}

.indicatorsContainer {
  display: flex;
  flex-direction: column;
}

.indicatorsContent {
  display: flex;
  width: 100%;
  margin-top: 16px;
  margin-bottom: 12px;

  .mainIndicatorContainer {
    display: flex;
    flex: 1;
    width: 100%;
    min-width: 150px;
    justify-content: center;
    align-items: center;

    .mainIndicatorValue {
      display: flex;
      flex-direction: column;
      flex: 21;
      width: 100%;
      justify-content: center;
      text-align: center;
      color: $color-neutral-700;
      font-size: 40px;
      font-weight: 600;
      letter-spacing: 0;
      line-height: 40px;
    }

    .mainIndicatorLabel {
      width: 100%;
      justify-content: center;
      text-align: center;
      color: $color-neutral-700;
      font-size: 18px;
      font-weight: 600;
      letter-spacing: 0;
      line-height: 18px;
      margin-top: 8px;
    }
  }

  .otherIndicatorContainer {
    display: flex;
    justify-content: center;
    width: 100%;
    min-width: 130px;
    align-items: center;
    flex: 1;

    .otherIndicatorValue {
      display: flex;
      flex-direction: column;
      flex: 1;
      width: 100%;
      color: $color-neutral-700;
      font-size: 28px;
      font-weight: 600;
      letter-spacing: 0;
      line-height: 28px;
    }

    .otherIndicatorLabel {
      color: $color-neutral-700;
      font-size: 14px;
      font-weight: 600;
      letter-spacing: 0;
      line-height: 14px;
      margin-top: 4px;
    }
  }
}

.indicatorsContentSmall {
  composes: indicatorsContent;
  margin-top: 0;
  margin-bottom: 0;

  .mainIndicatorContainer {
    .mainIndicatorValue {
      font-size: 30px;
      line-height: 30px;
    }

    .mainIndicatorLabel {
      font-size: 14px;
      line-height: 14px;
      margin-top: 6px;
    }
  }

  .otherIndicatorContainer {
    .otherIndicatorValue {
      font-size: 20px;
      line-height: 20px;
    }

    .otherIndicatorLabel {
      color: $color-neutral-700;
      font-size: 12px;
      line-height: 12px;
      margin-top: 3px;
    }
  }
}

.verticalDivider {
  height: 70px;
  width: 1px;
  opacity: 0.6;
  background: linear-gradient(180deg, #ffffff 0%, #b2b2b2 50.54%, #ffffff 100%);
  margin-right: 16px;
}

.resendInvites {
  float: right;
}

.tooltipOverlay {
  width: 170px;
  height: 35px;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
  opacity: 0;
}

.resendInviteInfo {
  position: relative;
  width: 180px;
  display: block;
  float: right;
  height: 30px;
}
