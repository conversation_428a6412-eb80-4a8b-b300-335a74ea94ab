import { toKebabCase } from "view/components/base/StringUtils";

import IndicatorsOutlineProps from "./IndicatorsOutline.types";

import styles from "./IndicatorsOutline.module.scss";

const IndicatorsOutline = ({
  title,
  indicatorRows,
  headerComponent,
}: IndicatorsOutlineProps) => {
  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <label className={styles.title}>{title}</label>

        {headerComponent && headerComponent}
      </div>

      <div className={styles.indicatorsContainer}>
        {indicatorRows?.map((indicatorRow: any, index: any) => {
          const principalIndicator = indicatorRow?.principalIndicator;

          return (
            <div
              key={index}
              data-test-id={toKebabCase(title)}
              className={
                index > 0
                  ? styles.indicatorsContentSmall
                  : styles.indicatorsContent
              }
            >
              <div className={styles.mainIndicatorContainer}>
                <div className={styles.mainIndicatorValue}>
                  {principalIndicator?.value}

                  <span className={styles.mainIndicatorLabel}>
                    {principalIndicator?.component}
                  </span>
                </div>
              </div>

              <div className={styles.verticalDivider} />

              {indicatorRow?.otherIndicators?.map(
                (indicator: any, idx: number) => (
                  <div className={styles.otherIndicatorContainer} key={idx}>
                    <div className={styles.otherIndicatorValue}>
                      {indicator.value}

                      <span className={styles.otherIndicatorLabel}>
                        {indicator.component}
                      </span>
                    </div>

                    {idx < indicatorRow?.otherIndicators.length - 1 && (
                      <div className={styles.verticalDivider} />
                    )}
                  </div>
                )
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default IndicatorsOutline;
