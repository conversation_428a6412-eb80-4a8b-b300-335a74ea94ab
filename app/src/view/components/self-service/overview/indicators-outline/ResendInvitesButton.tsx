import { useTranslation } from "react-i18next";

import { But<PERSON> } from "@fourkites/elemental-atoms";
import { Toolt<PERSON> } from "@fourkites/elemental-tooltip";

import { useAppDispatch, useAppSelector } from "state/hooks";
import { CarrierInvitationsState } from "state/modules/shipper/CarrierInvitations";
import { UsersState } from "state/modules/Users";

import { getUserLocaleDate, secondsToHm } from "view/components/base/DateUtils";
import { showToast } from "view/components/base/toast/Toast";

import ResendInvitesButtonProps from "./ResendInvitesButtonProps.types";

import { ShipperIndicatorsState } from "state/modules/shipper/ShipperIndicators";

import styles from "./IndicatorsOutline.module.scss";

// TODO: refactor component
const ResendInvitesButton = ({
  resubmissionsInfo,
}: ResendInvitesButtonProps) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  // Shipper ID
  const shipperId: string = useAppSelector(UsersState.selectors.getCompanyId);

  // TODO: move this out of this component
  const batchResubmitInvites = async () => {
    const mode = "ftl";
    const getShippersIndicators = () => {
      dispatch(
        ShipperIndicatorsState.actions.getShipperIndicators({
          shipperId,
          mode,
        })
      );
    };

    if (shipperId) {
      const response = await dispatch(
        CarrierInvitationsState.actions.resendAllInvitations({
          shipperId,
          mode,
        })
      );

      if ("error" in response) {
        showToast("", t("Error in resending the innvitations."), "error");
        return;
      }

      showToast("", t("Invitations resent successfully."), "ok");
      getShippersIndicators();
    }
  };

  const resendInviteTitle = t("Resend Requests").toUpperCase();
  const isResendInviteEnabled = resubmissionsInfo?.enabled;

  const contentcomponent = (
    <div>
      {t(
        `Last Sent: ${getUserLocaleDate(resubmissionsInfo?.last_sent)}\n` +
          `You can resend invites after ${secondsToHm(
            resubmissionsInfo?.seconds_until_enabled
          )}.`
      )}
    </div>
  );

  const resendInviteBtn = (
    <Button
      theme="tertiary"
      size="small"
      className={styles.resendInvites}
      onClick={(event: any) => {
        event.stopPropagation();
        batchResubmitInvites();
      }}
      disabled={!isResendInviteEnabled}
    >
      {resendInviteTitle}
    </Button>
  );

  const resendInviteBtnWithTooltipInfo = (
    <div className={styles.resendInviteInfo}>
      <Tooltip
        placement="bottom"
        contentComponent={contentcomponent}
        theme="dark"
      >
        <span className={styles.tooltipOverlay}></span>
      </Tooltip>
      {resendInviteBtn}
    </div>
  );

  return isResendInviteEnabled
    ? resendInviteBtn
    : resendInviteBtnWithTooltipInfo;
};

export default ResendInvitesButton;
