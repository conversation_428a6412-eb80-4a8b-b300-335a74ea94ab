import React from "react";
import { useTranslation } from "react-i18next";

import { Button } from "@fourkites/elemental-atoms";
import { Input } from "@fourkites/elemental-input";
import { Select } from "@fourkites/elemental-select";

import Card from "view/components/base/card/Card";

import styles from "./InviteUsers.module.scss";

const InviteUsers: React.FC = () => {
  const { t } = useTranslation();

  const onChangeEmail = (email: string) => {};

  const onSelectRole = () => {};

  return (
    <Card>
      <div className={styles.container}>
        <label id="title">{t("Invite your team")}</label>

        <label>
          {t("Tracking your shipments becomes fun when working as a team!")}
        </label>

        <div className={styles.content}>
          <Input
            label={`${t("Email Address")}`}
            errorLabel="Field is required"
            value={""}
            onChange={(e: any) => onChangeEmail(e.target.value)}
            required
          />

          <Select
            label={t("Role")}
            options={[]}
            value={[""]}
            onChange={onSelectRole}
            size="large"
          />

          <Button size="large" onClick={() => alert("Invite")}>
            {t("Invite")}
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default InviteUsers;
