import React from "react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";

import { Button, ArrowRightIcon } from "@fourkites/elemental-atoms";

import Card from "view/components/base/card/Card";

import { carrierWizardRoutes } from "router/carrier/CarrierWizardRouter";

import styles from "./PendingSetups.module.scss";

const PendingTrackingIntegrations = (props: any) => {
  const { t } = useTranslation();

  return (
    <Card customClass={styles.trackingCard}>
      <div data-test-id="card-tracking-integrations" id="content">
        <h4>{t("Tracking Integration")}</h4>

        <label>{t("Configure how you track shipments")}</label>

        <div id="footer">
          <Link
            className={styles.cardLink}
            to={carrierWizardRoutes.eldGpsTracking}
          >
            <div data-test-id="btn-get-started">
              <Button theme="primary" variant="outline">
                <span className={"button-content"}>
                  {t("Get Started")}
                  <ArrowRightIcon
                    fill="#0e65e5"
                    iconClass={"button-icon-right"}
                  />
                </span>
              </Button>
            </div>
          </Link>
        </div>
      </div>
    </Card>
  );
};

export default PendingTrackingIntegrations;
