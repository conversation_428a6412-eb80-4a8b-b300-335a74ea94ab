@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container {
  display: flex;
  flex-direction: column;
  background-color: $color-neutral-50;
  width: 100%;
  flex-wrap: wrap;
}

.title {
  color: $color-neutral-700;
  font-size: 20px;
  font-weight: 600;
  letter-spacing: 0;
  line-height: 24px;
}

.titleContainer {
  display: block;
  padding-top: 36px;
  padding-bottom: 36px;
}

.content {
  display: flex;
  align-content: center;
  justify-content: center;

  > div {
    display: flex;
    margin-right: 24px;
    width: 100%;

    &:last-child {
      margin-right: 0;
    }
  }
}

.contentColumn {
  display: flex;
  flex: 1;
  height: fit-content;

  > div {
    display: flex;
    width: 100%;
    max-width: 490px;

    &:last-child {
      margin-right: 0;
    }
  }
}

.contentColumnBig {
  composes: contentColumn;

  > div {
    max-width: none;
  }
}

.cardContent {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.divider {
  display: block;
  height: 1px;
  width: 100%;
  background-color: $color-neutral-300;
}

.carrierIndicator {
  display: flex;
  align-content: center;
  align-items: center;

  > label {
    margin-left: 8px;
  }
}

.connectionErrors {
  display: flex;
  flex-direction: column;
  padding: 24px;
}

.loader {
  display: flex;
  align-items: left;
  align-content: center;
  justify-content: center;
  margin-top: 32px;
  margin-bottom: 32px;
}

.modeIndicatorContainer {
  display: flex;
  width: 100%;
  align-items: center;
  align-content: center;
  justify-content: center;

  > label {
    margin-right: 8px;
  }
}
