import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useHistory } from "react-router";

import { Spinner } from "@fourkites/elemental-loading-indicator";
import { Tooltip } from "@fourkites/elemental-tooltip";

import { carrierRoutes } from "router/carrier/CarrierRouter";
import { shipperRoutes } from "router/shipper/ShipperRouter";

import { useAppSelector, useAppDispatch } from "state/hooks";
import { ShipperIndicatorsState } from "state/modules/shipper/ShipperIndicators";
import { UsersState } from "state/modules/Users";

import { getIconForMode } from "view/components/base/ModeUtils";

import BreadcrumbsHeader from "view/components/base/breadcrumbs-header/BreadcrumbsHeader";
import Card from "view/components/base/card/Card";

import IndicatorsOutline from "view/components/self-service/overview/indicators-outline/IndicatorsOutline";
import ResendInvitesButton from "view/components/self-service/overview/indicators-outline/ResendInvitesButton";
import ThreePlStaticInformatory from "view/components/self-service/threePl/ThreePlStaticInformatory";

import styles from "./ShipperOverview.module.scss";

const ShipperOverview: React.FC = () => {
  const { t } = useTranslation();

  const dispatch = useAppDispatch();
  const history = useHistory();

  /*****************************************************************************
   * REDUX
   ****************************************************************************/
  const shipperId: string = useAppSelector(UsersState.selectors.getCompanyId);

  const shipperIndicators = useAppSelector(
    ShipperIndicatorsState.selectors.shipperIndicators()
  );
  const isLoadingShipperIndicators = useAppSelector(
    ShipperIndicatorsState.selectors.isLoadingShipperIndicators()
  );

  const companyType = useAppSelector(UsersState.selectors.getCompanyType);

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  // TODO: fix indicators for ocean
  useEffect(() => {
    const modesWithIndicators = [
      "ltl",
      "ftl",
      "parcel",
      //"ocean"
    ];
    for (let mode of modesWithIndicators) {
      dispatch(
        ShipperIndicatorsState.actions.getShipperIndicators({
          shipperId,
          //@ts-ignore
          mode,
        })
      );
    }
  }, [shipperId]);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  /**
   * Redirect to carriers page with filters from Overview
   */
  const redirectWithCarriersPageFiltered = (networkStatus: string) => {
    history.push({
      pathname:
        isBroker || isCarrier
          ? `${carrierRoutes.carriers}`
          : `${shipperRoutes.carriers}`,
      state: { networkStatus: networkStatus },
    });
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const { invitations, carriers } = shipperIndicators;
  const invitationsIndicators = [
    {
      principalIndicator: {
        value: invitations?.total,
        component: <label>{t("Total")}</label>,
      },
      otherIndicators: [
        {
          value: invitations?.pendingAccountCreation,
          component: <span>{t("Pending account creation")}</span>,
        },
        {
          value: invitations?.dataSharingRequested,
          component: <span>{t("Requested data sharing")}</span>,
        },
      ],
    },
  ];

  const FtlIcon = getIconForMode("ftl");
  const LtlIcon = getIconForMode("ltl");
  //const OceanIcon = getIconForMode("ocean");
  const ParcelIcon = getIconForMode("parcel");

  const carriersIndicators = [
    {
      principalIndicator: {
        value: carriers?.total?.total,
        component: <label>{t("Total")}</label>,
      },
      otherIndicators: [
        {
          value: carriers?.total?.connected,
          component: <label>{t("Connected")}</label>,
        },
        {
          value: carriers?.total?.inProgress,
          component: <label>{t("In Progress")}</label>,
        },
        {
          value: carriers?.total?.disconnected,
          component: <label>{t("Disconnected")}</label>,
        },
      ],
    },
    {
      principalIndicator: {
        value: carriers?.ftl?.total,
        component: (
          <Tooltip placement="bottom" text={t("FTL")}>
            <div className={styles.modeIndicatorContainer}>
              <FtlIcon size="20px" />
            </div>
          </Tooltip>
        ),
      },
      otherIndicators: [
        {
          value: carriers?.ftl?.connected,
          component: <label>{t("Connected")}</label>,
        },
        {
          value: carriers?.ftl?.inProgress,
          component: <label>{t("In Progress")}</label>,
        },
        {
          value: carriers?.ftl?.disconnected,
          component: <label>{t("Disconnected")}</label>,
        },
      ],
    },
    {
      principalIndicator: {
        value: carriers?.ltl?.total,
        component: (
          <Tooltip placement="bottom" text={t("LTL")}>
            <div className={styles.modeIndicatorContainer}>
              <LtlIcon size="20px" />
            </div>
          </Tooltip>
        ),
      },
      otherIndicators: [
        {
          value: carriers?.ltl?.connected,
          component: <label>{t("Connected")}</label>,
        },
        {
          value: carriers?.ltl?.inProgress,
          component: <label>{t("In Progress")}</label>,
        },
        {
          value: carriers?.ltl?.disconnected,
          component: <label>{t("Disconnected")}</label>,
        },
      ],
    },
    {
      principalIndicator: {
        value: carriers?.parcel?.total,
        component: (
          <Tooltip placement="bottom" text={t("Parcel")}>
            <div className={styles.modeIndicatorContainer}>
              <ParcelIcon size="20px" />
            </div>
          </Tooltip>
        ),
      },
      otherIndicators: [
        {
          value: carriers?.parcel?.connected,
          component: <label>{t("Connected")}</label>,
        },
        {
          value: carriers?.parcel?.inProgress,
          component: <label>{t("In Progress")}</label>,
        },
        {
          value: carriers?.parcel?.disconnected,
          component: <label>{t("Disconnected")}</label>,
        },
      ],
    },
    /*{
      principalIndicator: {
        value: carriers?.ocean?.total,
        component: (
          <Tooltip placement="bottom" text={t("Ocean")}>
            <div className={styles.modeIndicatorContainer}>
              <OceanIcon size="20px" />
            </div>
          </Tooltip>
        ),
      },
      otherIndicators: [
        {
          value: carriers?.ocean?.connected,
          component: <label>{t("Connected")}</label>,
        },
        {
          value: carriers?.ocean?.inProgress,
          component: <label>{t("In Progress")}</label>,
        },
        {
          value: carriers?.ocean?.disconnected,
          component: <label>{t("Disconnected")}</label>,
        },
      ],
    },*/
  ];

  const isBroker = companyType?.includes("broker");
  const isCarrier = companyType?.includes("carrier");

  if (isLoadingShipperIndicators) {
    return (
      <div className={styles.container}>
        {companyType?.includes("3pl") && <ThreePlStaticInformatory />}
        <div className={styles.titleContainer}>
          <BreadcrumbsHeader titles={[t("Carriers")]} />
        </div>

        <div className={styles.loader}>
          <Spinner isLoading size="medium" />
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      {companyType?.includes("3pl") && <ThreePlStaticInformatory /> }
      <div className={styles.titleContainer}>
        <BreadcrumbsHeader titles={[t("Carriers")]} />
      </div>

      <div className={styles.content}>
        <div className={styles.contentColumnBig}>
          <Card onClick={() => redirectWithCarriersPageFiltered("connected")}>
            <div
              data-test-id="card-connected-carriers-indicators"
              className={styles.cardContent}
            >
              <IndicatorsOutline
                title="Connected Carriers"
                indicatorRows={carriersIndicators}
              />
            </div>
          </Card>
        </div>

        <div className={styles.contentColumn}>
          <Card onClick={() => redirectWithCarriersPageFiltered("invited")}>
            <IndicatorsOutline
              title="Pending Carrier Connections"
              indicatorRows={invitationsIndicators}
              headerComponent={
                <ResendInvitesButton
                  resubmissionsInfo={invitations?.resubmissions}
                />
              }
            />
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ShipperOverview;
