import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useHistory } from "react-router";

import { useAppSelector, useAppDispatch } from "state/hooks";
import { UsersState } from "state/modules/Users";

import ThreePlStaticInformatory from "view/components/self-service/threePl/ThreePlStaticInformatory";

import styles from "./ThreePlOverview.module.scss";

const ThreePlOverview: React.FC = () => {
  const { t } = useTranslation();

  const dispatch = useAppDispatch();
  const history = useHistory();

  /*****************************************************************************
   * REDUX
   ****************************************************************************/
  const isSuperAdmin = useAppSelector(UsersState.selectors.getIsSuperAdmin);
  const isCompanyAdmin = useAppSelector(UsersState.selectors.getIsCompanyAdmin);
  // User
  const companyName: string = useAppSelector(
    UsersState.selectors.getCompanyName
  ); 
  const companyType = useAppSelector(UsersState.selectors.getCompanyType);

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div className={styles.container}>
        <ThreePlStaticInformatory />
    </div>
  );
};

export default ThreePlOverview;
