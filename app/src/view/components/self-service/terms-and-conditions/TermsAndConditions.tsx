import React, { useMemo, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Modal } from "@fourkites/elemental-modal";
import { fourkitesUrls } from "api/http/apiUtils";

const TermsAndConditionsModal = ({ show, onClose, onAgreed }: any) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const onTermsAndConditionsAgreed = () => {
    onAgreed();
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/
  const title = t("Terms & Conditions");

  return (
    <Modal
      size="small"
      title={title}
      show={show}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: onClose,
      }}
      saveButtonProps={{
        label: t("Agree"),
        onClick: onTermsAndConditionsAgreed,
      }}
    >
      <div>
        <p>
          {t(
            "FourKites, through FourKites Connectivity, makes it easy to " +
              "onboard and connect with your supply chain partners like " +
              "carriers, customers & telematics providers within the platform."
          )}
        </p>
        <p>
          {t("Please click Agree to confirm you are accepting the ")}
          <a
            href={fourkitesUrls.termsAndConditions}
            target="_blank"
            id="terms-and-conditions"
          >
            {t("Terms & Conditions")}
          </a>
        </p>
        <p>
          {t("View ")}
          <a
            href={fourkitesUrls.privacyPolicy}
            target="_blank"
            id="privacy-policy"
          >
            {t("Privacy Policy")}
          </a>
        </p>
      </div>
    </Modal>
  );
};

export default TermsAndConditionsModal;
