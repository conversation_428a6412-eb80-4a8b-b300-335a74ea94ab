@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container {
  display: flex;
  flex-direction: column;
  background-color: $color-neutral-50;
  width: 100%;
}

.title {
  color: $color-neutral-700;
  font-size: 20px;
  font-weight: 600;
  letter-spacing: 0;
  line-height: 24px;
}

.titleContainer {
  display: block;
  padding-top: 36px;
  padding-bottom: 36px;
}

.content {
  display:flex;
  flex-wrap: wrap;
  align-content: flex-start;
  align-items: flex-start;
  margin-bottom: 24px;

  > div  {
    display: flex;
    margin-right: 24px;
    width: 100%;

    &:last-child {
      margin-right: 0;
    }
  }
}

.contentColumn {
  display:flex;
  flex: 1;
  height: fit-content;

  > div  {
    display: flex;
    width: 100%;
    max-width: 490px;

    &:last-child {
      margin-right: 0;
    }
  }
}

.contentColumnBig {
  composes: contentColumn;

  > div {
    max-width: none;
  }
}

.loader {
  display: flex;
  align-items: left;
  align-content: center;
  justify-content: center;
  margin-top: 32px;
  margin-bottom: 32px;
}
