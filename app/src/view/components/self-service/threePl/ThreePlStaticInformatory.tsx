import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";

import BreadcrumbsHeader from "view/components/base/breadcrumbs-header/BreadcrumbsHeader";

import styles from "./ThreePlStaticInformatory.module.scss";

const ThreePlStaticInformatory: React.FC = () => {
  const { t } = useTranslation();

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
        <>
          <div className={styles.titleContainer}>
            <BreadcrumbsHeader titles={[t("Carrier Management")]} />
          </div>

          <p>{t("Carriers must be managed individually for each shipper or customer")}</p>
          <p>{t("Select the company instance of a shipper or customer at the top of this page")}</p>
        </>
  );
};

export default ThreePlStaticInformatory;
