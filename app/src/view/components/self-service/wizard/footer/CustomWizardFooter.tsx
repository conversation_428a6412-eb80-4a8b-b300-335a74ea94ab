import React from "react";
import { useTranslation } from "react-i18next";

import { Button } from "@fourkites/elemental-atoms";

import CustomWizardFooterProps from "./CustomWizardFooter.types";

import styles from "./WizardFooter.module.scss";

const CustomWizardFooter = ({
  footerLinkOptions,
  children,
}: CustomWizardFooterProps) => {
  const { t } = useTranslation();

  return (
    <div className={styles.footerContainer}>
      <div className={styles.leftContainer}>
        {footerLinkOptions?.map((option: any, index: number) => {
          return (
            <div className={styles.buttonContainer} key={index}>
              <Button
                onClick={option.onClick}
                data-testid="btn-contact-support"
                variant="flat"
                theme="primary"
              >
                <span className={"button-content"}>
                  {option.icon}
                  {`${option.label}`}
                </span>
              </Button>
            </div>
          );
        })}
      </div>

      <div className={styles.rightContainer}>{children}</div>
    </div>
  );
};

export default CustomWizardFooter;
