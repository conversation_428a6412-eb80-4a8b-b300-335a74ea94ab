import { useTranslation } from "react-i18next";

import { BookIcon, Button } from "@fourkites/elemental-atoms";

import { onELDHelpClick } from "router/navigationUtils";

import WizardFooterProps from "./WizardFooter.types";

import styles from "./WizardFooter.module.scss";

const WizardFooter = (props: WizardFooterProps) => {
  const { t } = useTranslation();

  return (
    <div className={styles.footerContainer}>
      <div className={styles.leftContainer}>
        <div className={styles.buttonContainer}>
          <Button
            theme="primary"
            variant="flat"
            data-test-id="wizard-footer-help-documentation-link"
            onClick={onELDHelpClick}
          >
            <span className={"button-content"}>
              <BookIcon fill="#0e65e5" iconClass={"button-icon-left"} />
              {t("Help Documentation")}
            </span>
          </Button>
        </div>
      </div>

      <div className={styles.rightContainer}>{props.children}</div>
    </div>
  );
};

export default WizardFooter;
