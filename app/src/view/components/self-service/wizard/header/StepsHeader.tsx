import React from "react";
import { useTranslation } from "react-i18next";

/*
import {
  Button,
  PlayCircleIcon,
  UserPlusIcon,
} from "@fourkites/elemental-atoms";

import BreadcrumbsHeader from "view/components/base/breadcrumbs-header/BreadcrumbsHeader";
*/

import styles from "./StepsHeader.module.scss";

const StepsHeader = ({ step }: any) => {
  const { t } = useTranslation();

  return (
    <div className={styles.container}>
      <span>
        {t("Step")} {step} {t("of")} 2
      </span>

      {/*
      <BreadcrumbsHeader titles={[""]}>
        <LinkButton
          style={{ marginRight: "16px" }}
          theme="primary"
          variant="flat"
          target="_blank"
          href={fourkitesUrls.help}
        >
          <span className={"button-content"}>
            <PlayCircleIcon fill="#0e65e5" iconClass={"button-icon-left"} />
            {t("Watch Demo")}
          </span>
        </LinkButton>
        <Button
          theme="secondary"
          variant="outline"
          onClick={() => alert("Invite")}
        >
          <span className={"button-content"}>
            <UserPlusIcon fill="#21252a" iconClass={"button-icon-left"} />
            {t("Invite your team")}
          </span>
        </Button>
      </BreadcrumbsHeader>
      */}
    </div>
  );
};

export default StepsHeader;
