import React from "react";
import { useTranslation } from "react-i18next";

import { fourkitesUrls } from "api/http/apiUtils";

import {
  LinkButton,
  BulbIcon,
  ColoredFKLogo,
} from "@fourkites/elemental-atoms";

import styles from "./WizardHeader.module.scss";

const WizardHeader: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className={styles.headerContainer}>
      <ColoredFKLogo width={130} height={40} />
      <div className={styles.buttonContainer}>
        <LinkButton
          theme="secondary"
          variant="flat"
          target="_blank"
          href={fourkitesUrls.help}
        >
          <span className={"button-content"}>
            <BulbIcon fill="#21252A" iconClass={"button-icon-left"} />
            {t("Help Tips")}
          </span>
        </LinkButton>
      </div>
    </div>
  );
};

export default WizardHeader;
