import React, { useState, useEffect } from "react";
import { useHistory } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { COMPANY_API_DOMAIN, fourkitesUrls } from "api/http/apiUtils";

import { ExternalLinkIcon } from "@fourkites/elemental-atoms";
import {
  GlobalHeader as Header,
  ProductId,
} from "@fourkites/elemental-global-header";

import { appRoutes } from "router/AppRouter";
import CarrierRouter, {
  carrierRoutes,
  getCarrierRouteId,
} from "router/carrier/CarrierRouter";
import {
  onNewWindow,
  onNav,
  onHelpClick,
  onCommunitySelect,
  onCarrierHelpClick,
  setCookie,
} from "router/navigationUtils";

import { useAppSelector, useAppDispatch } from "state/hooks";
import UsersState, { getCookies } from "state/modules/Users";

import { startWithUpperCase } from "view/components/base/StringUtils";

import TermsAndConditionsModal from "view/components/self-service/terms-and-conditions/TermsAndConditions";
import CompanySwitcher from "view/components/self-service/component/CompanySwitcher"

import styles from "./CarrierPage.module.scss";

const CarrierPage: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const history = useHistory();

  const currentUser = useAppSelector(UsersState.selectors.getCurrentUser);
  const companyType = useAppSelector(UsersState.selectors.getCompanyType);
  const superAdmin = useAppSelector(UsersState.selectors.getIsSuperAdmin);
  const isCompanyAdmin = useAppSelector(UsersState.selectors.getIsCompanyAdmin);
  const termsAgreed = useAppSelector(UsersState.selectors.getTermsAgreed);
  const { authToken, userId, deviceId } = getCookies();

  const isCarrier = companyType?.includes("carrier");
  const isBroker = companyType?.includes("broker");
  const isShipper = companyType?.includes("shipper");

  const currentUserObject = {
    authToken,
    userId,
    deviceId,
    superAdmin: superAdmin ? "true" : "false",
    name: {
      firstName: startWithUpperCase(currentUser?.firstName),
      lastName: startWithUpperCase(currentUser?.lastName),
    },
    isCompanyAdmin: isCompanyAdmin ? "true" : "false",
    role: currentUser?.role,
    company: {
      defaultTypes: currentUser?.defaultCompanyType,
      defaultId: currentUser?.defaultCompanyID,
      context: currentUser?.companyContext,
    },
    displayCompanySwitcher: false,
  };

  const domains = {
    company: COMPANY_API_DOMAIN,
    user: "",
  }

  const onWorkspaceSelect = currentUser?.hasWorkspaceEnabled
  ? () => onNav(fourkitesUrls.myWorkspace)
  : undefined; 

  const onLogoClick = () => {
    currentUser?.hasWorkspaceEnabled ?
    onNav(fourkitesUrls.myWorkspace) : onNav(fourkitesUrls.dashboard)
 }
 const currentUserData = sessionStorage.getItem("fkcUserDetails") || localStorage.getItem("tfcurrent-user");
 const userObject = currentUserData ? JSON.parse(currentUserData) : null;

 const onDeveloperPortalSelect = async () => {
  setCookie();
  const archbeeJwt = await UsersState.helpers.getArchbeeJwt();
  const jwtAddedUrl = `${fourkitesUrls.developerPortalDoc}?jwt=${archbeeJwt}`;
  userObject?.superAdmin || userObject?.companyAdmin
    ? onNewWindow(fourkitesUrls.developerPortal)
    : onNewWindow(jwtAddedUrl)
  };

  const onAppointmentsManagerSelect = userObject?.modules?.includes(
    "appointment-manager"
  )
    ? () => onNav(fourkitesUrls.appointmentsManager)
    : undefined;

  const onDynamicYardSelect = userObject?.modules?.includes("dynamic-yard")
    ? () => onNav(fourkitesUrls.dynamicYard)
    : undefined; 

 const onDynamicYardPlusSelect = userObject?.modules?.includes(
   "dynamic-yard-plus"
 )
   ? () => onNav(fourkitesUrls.dynamicYardPlus)
   : undefined;

 const onPrivateFleetTrackingSelect = userObject?.modules?.includes(
   "nic-private-fleet"
 )
   ? () => onNav(fourkitesUrls.privateFleetTracking)
   : undefined;

 const onRailFleetSelect = userObject?.modules?.includes("nic-rail-fleet")
   ? () => onNav(fourkitesUrls.railFleet)
   : undefined;
  const onDigitalWorkforceSelect = () => onNav(fourkitesUrls.digitalWorkforce);
  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [showTermsAndConditionsModal, setShowTermsAndConditionsModal] =
    useState<boolean>(false);

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  useEffect(() => {
    setShowTermsAndConditionsModal(!termsAgreed);
  }, [termsAgreed]);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const onUserLogoutClick = () => {
    dispatch(
      UsersState.actionCreators.userLogout(() => {
        const isRunninLocally = window.location.href.includes("localhost");

        if (isRunninLocally) {
          history.push(appRoutes.login);
        } else {
          onNav(fourkitesUrls.login);
        }
      })
    );
  };

  const onSelectCompany = (selectedCompany: any) => {
    if (!selectedCompany || !selectedCompany?.id) {
      return;
    }

    // TODO: needs to clear state
    dispatch(UsersState.actionCreators.setSelectedCompany(selectedCompany?.id));
  };

  const onAgreedConditions = async () => {
    try {
      await dispatch(UsersState.actionCreators.setTermsAgreed());
      // Close modal
      setShowTermsAndConditionsModal(false);
    } catch (error) {
      onDeniedConditions();
    }
  };

  const onDeniedConditions = () => {
    setShowTermsAndConditionsModal(false);
    onNav(fourkitesUrls.visibility);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const subHeaderItems = [
    {
      label: t("Overview"),
      id: carrierRoutes.overview,
      pagePath: carrierRoutes.overview,
      headerType: "app_route",
    },
    {
      label: t("Customers"),
      id: carrierRoutes.customers,
      pagePath: carrierRoutes.customers,
      headerType: "app_route",
    },
    ...(isBroker || isShipper
      ? [
          {
            label: t("Carriers"),
            id: carrierRoutes.carriers,
            pagePath: carrierRoutes.carriers,
            headerType: "app_route",
          },
        ]
      : []),
    // Only carriers can see fleets page
    //Hiding Fleet tab as part of SELF-2709
    // ...(isCarrier
    //   ? [
    //       {
    //         label: t("Fleet"),
    //         id: carrierRoutes.fleet,
    //         pagePath: carrierRoutes.fleet,
    //         headerType: "app_route",
    //       },
    //     ]
    //   : []),

    ...(superAdmin || isCompanyAdmin
      ? [
          {
            label: t("Tracking Integrations"),
            id: carrierRoutes.tracking,
            pagePath: carrierRoutes.tracking,
            headerType: "app_route",
          },
        ]
      : []),
    // Only shippers can see data integrations page
    ...(isShipper && (superAdmin || isCompanyAdmin)
      ? [
          {
            label: t("Data Integrations"),
            id: carrierRoutes.dataIntegrations,
            pagePath: carrierRoutes.dataIntegrations,
            headerType: "app_route",
          },
        ]
      : []),
    ...(superAdmin || isCompanyAdmin
      ? [
          {
            label: t("Company"),
            id: carrierRoutes.company,
            pagePath: carrierRoutes.company,
            headerType: "app_route",
          },
        ]
      : []),
    // Only shippers can see address manager page
    ...(isShipper
      ? [
          {
            label: t("Address Manager"),
            id: fourkitesUrls.addressManager,
            pagePath: fourkitesUrls.addressManager,
            headerType: "externalLink",
            icon: <ExternalLinkIcon className="sub-header-icon" />,
          },
        ]
      : []),
  ];

  const onConnectivitySelect = () => onNav(fourkitesUrls.connectivity);

  const initialSelectedProduct = ProductId.FourKitesConnect;

  const shouldShowTcModal = isCompanyAdmin && !termsAgreed;

  return (
    <div className={styles.pageContainer}>
      <CompanySwitcher/>
      <Header
        currentUser={currentUserObject}
        subHeaderItems={subHeaderItems}
        initialSelectedItemId={getCarrierRouteId(history)}
        initialSelectedProductId={initialSelectedProduct}
        onWorkspaceSelect={onWorkspaceSelect}
        onLogoClick={onLogoClick}
        // Product switcher
        onVisibilitySelect={() => onNav(fourkitesUrls.visibility)}
        onAppointmentsManagerSelect={onAppointmentsManagerSelect}
        onDynamicYardSelect={onDynamicYardSelect}
        onCommunitySelect={onCommunitySelect}
        onDeveloperPortalSelect={onDeveloperPortalSelect}
        onFourKitesConnectSelect={onConnectivitySelect}
        onDynamicYardPlusSelect={onDynamicYardPlusSelect}
        onPrivateFleetTrackingSelect={onPrivateFleetTrackingSelect}
        onRailFleetSelect={onRailFleetSelect}
        onDigitalWorkforceSelect={onDigitalWorkforceSelect}
        // Right menu
        onNotificationsClick={() => onNewWindow(fourkitesUrls.notifications)}
        onHelpClick={onCarrierHelpClick}
        onSettingsClick={() => onNav(fourkitesUrls.companySettings)}
        // Avatar menu
        onUserLogoutClick={onUserLogoutClick}
        domains={domains}
        setSelectedCompany={onSelectCompany}
      />

      <CarrierRouter />

      {shouldShowTcModal && (
        <TermsAndConditionsModal
          show={showTermsAndConditionsModal}
          onClose={onDeniedConditions}
          onAgreed={onAgreedConditions}
        />
      )}
    </div>
  );
};

export default CarrierPage;
