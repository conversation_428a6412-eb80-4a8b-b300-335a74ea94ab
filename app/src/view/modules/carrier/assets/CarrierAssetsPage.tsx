import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { useHistory } from "react-router-dom";

import { InfoIcon } from "@fourkites/elemental-atoms";
import {
  HierarchicalSidebar,
  SidebarSection,
} from "@fourkites/elemental-hierarchical-sidebar";

import CarrierAssetsRouter, {
  carrierAssetsRoutes,
  getCarrierAssetsRouteId,
} from "router/carrier/CarrierAssetsRouter";

import styles from "./CarrierAssetsPage.module.scss";

const CarrierAssetsPage: React.FC = () => {
  const { t } = useTranslation();
  const history = useHistory();

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [collapsed, setCollapsed] = useState<boolean>(false);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/
  const onChangeCollapse = (collapsed: boolean) => {
    setCollapsed(collapsed);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const sidebarSections = [
    {
      title: t("Carrier Assets").toUpperCase(),
      links: [
        {
          title: t("Asset Management"),
          id: carrierAssetsRoutes.management,
          to: carrierAssetsRoutes.management,
        },
        /*
        {
          title: t("Asset Assignment"),
          id: carrierAssetsRoutes.assignment,
          to: carrierAssetsRoutes.assignment,
        },
        */
      ],
    },
  ];

  return (
    <div className={styles.pageContainer} data-test-id="assets-page">
      <div className={styles.container}>
        <div>
          <HierarchicalSidebar
            collapsed={collapsed}
            onChangeCollapse={onChangeCollapse}
          >
            {!collapsed && (
              <div>
                {sidebarSections &&
                  sidebarSections.map((section, index) => {
                    return (
                      <SidebarSection
                        key={index}
                        title={section.title}
                        links={section.links}
                        defaultActiveLinkId={
                          getCarrierAssetsRouteId(history) || ""
                        }
                      />
                    );
                  })}

                <div className={styles.info}>
                  <InfoIcon fill={"#0e65e5"} size="20px" />
                  <label>
                    {t(
                      "Your assets will auto-populate on the table as soon as your GPS " +
                        "credentials are validated so you can ping them and start " +
                        "assigning to shipments right away. " +
                        "It can take a few minutes for " +
                        "FourKites to capture this data from your location provider. " +
                        "If you dont see your assets, please add them manually too."
                    )}
                  </label>
                </div>
              </div>
            )}
          </HierarchicalSidebar>
        </div>

        <div className={styles.content}>
          <CarrierAssetsRouter />
        </div>
      </div>
    </div>
  );
};

export default CarrierAssetsPage;
