@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container {
  display: flex;
  flex-direction: column;
  background-color: $color-neutral-50;
  height: 100%;
}

.titleContainer {
  display: block;
  padding-top: 36px;
  margin-left: 36px;
}

.title {
  color: $color-neutral-700;
  font-size: 20px;
  font-weight: 600;
  letter-spacing: 0;
  line-height: 24px;
}

.content {
  display:flex;
  flex-wrap: wrap;
  padding-top: 36px;
  padding-left: 36px;

  > div  {
    display: flex;
    margin-right: 16px;
    margin-bottom: 16px;

    &:last-child {
      margin-right: 0;
    }
  }
}

.contentColumn {
  display:flex;
  width: 100%;
  height: fit-content;
  flex: 1;

  > div  {
    display: flex;
    width: 100%;

    &:last-child {
      margin-right: 0;
    }
  }
}

.cardContent {
  display: flex;
  flex-direction: column;
}

.divider {
  display: block;
  height: 1px;
  width: 100%;
  background-color: $color-neutral-300;
}

.carrierIndicator {
  display: flex;
  align-content: center;
  align-items: center;

  > label {
    margin-left: 8px;
  }
}

.connectionErrors {
  display: flex;
  flex-direction: column;
  padding: 24px;
}
