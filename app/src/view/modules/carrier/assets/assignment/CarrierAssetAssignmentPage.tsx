import React from "react";
import { useTranslation } from "react-i18next";

import BreadcrumbsHeader from "view/components/base/breadcrumbs-header/BreadcrumbsHeader";
import IntegrationCard from "view/components/base/integration-card/IntegrationCard";

import styles from "./CarrierAssetAssignmentPage.module.scss";

const CarrierAssetAssignmentPage: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className={styles.container}>
      <div className={styles.titleContainer}>
        <BreadcrumbsHeader titles={[t("Asset Assignment")]} />
      </div>

      <div className={styles.content}>
        <IntegrationCard />
        <IntegrationCard />
        <IntegrationCard />
        <IntegrationCard />
      </div>
    </div>
  );
};

export default CarrierAssetAssignmentPage;
