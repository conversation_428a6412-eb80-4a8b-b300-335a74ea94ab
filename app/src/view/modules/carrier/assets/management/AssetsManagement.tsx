import React, { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { Spinner } from "@fourkites/elemental-loading-indicator";

import { FleetMode, PaginationType } from "state/BaseTypes";
import { useAppDispatch, useAppSelector } from "state/hooks";
import { CarrierFleetAssetsState } from "state/modules/carrier/CarrierFleetAssets";
import { CarrierFleetsState } from "state/modules/carrier/CarrierFleets";
import { UsersState } from "state/modules/Users";

import { showToast } from "view/components/base/toast/Toast";

import AssetsManagementHeader from "./header/AssetsManagementHeader";
import AddAssetsModal from "./modals/AddAssetsModal";
import LocateAssetModal from "./modals/LocateAssetModal";
import CarrierAssetsTable from "./table/CarrierAssetsTable";

import styles from "./AssetsManagement.module.scss";

const AssetsManagement: React.FC<{
  fleetMode: FleetMode;
}> = ({ fleetMode }) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const carrierId: string = useAppSelector(UsersState.selectors.getCompanyId);

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [fleetId, setFleetId] = useState<string | number>("");
  const [selectedAsset, setSelectedAsset] = useState<any>(null);
  const [showAddAssetsModal, setShowAddAssetsModal] = useState<boolean>(false);
  const [showLocateAssetModal, setShowLocateAssetModal] =
    useState<boolean>(false);
  const [lastPageParams, setLastPageParams] = useState<any>({
    index: -1,
    size: -1,
  });

  /*****************************************************************************
   * REDUX
   ****************************************************************************/

  //Fleets
  const isRetrievingFleets = useAppSelector(
    CarrierFleetsState.selectors.isRetrievingFleetsByMode(fleetMode)
  );

  //Assets
  const carrierAssets = useAppSelector(
    CarrierFleetAssetsState.selectors.fleetAssetsByMode(fleetMode)
  );
  const isRetrievingAssets = useAppSelector(
    CarrierFleetAssetsState.selectors.isRetrievingAssetsByMode(fleetMode)
  );
  const isEditingAssets = useAppSelector(
    CarrierFleetAssetsState.selectors.isEditingAssetsByMode(fleetMode)
  );

  // TODO

  /*****************************************************************************
   * DATA FETCHERS
   ****************************************************************************/

  /******************************* FLEETS *************************************/

  /*
   * Sets the id of the default fleet
   */
  const setDefaultFleetId = useCallback(
    (fleetsResponse: any) => {
      // Set default fleet id
      const fleets = fleetsResponse?.payload?.details;
      const defaultFleet = fleets?.find(
        (fleet: any) => fleet?.is_default === true
      );
      const defaultFleetId = defaultFleet?.id || "";
      setFleetId(defaultFleetId);

      return defaultFleetId;
    },
    [setFleetId]
  );

  /*
   * Handler for fetching the fleets
   */
  const fetchFleets = useCallback(async () => {
    const fleetsResponse = await dispatch(
      CarrierFleetsState.actions.retrieveFleets({
        carrierId,
        fleetMode,
        isDefault: true,
      })
    );

    return fleetsResponse;
  }, [carrierId, fleetMode]);

  /*
   * Gets default fleet for mode, or, if it doesn't exist, create the carrier so
   * its default fleets are automatically created
   */
  const getOrCreateDefaultFleet = useCallback(async () => {
    let fleetsResponse = await fetchFleets();

    const shouldCreateCarrier =
      "error" in fleetsResponse ||
      fleetsResponse?.payload?.details?.length === 0;
    if (shouldCreateCarrier) {
      await dispatch(
        CarrierFleetsState.actions.createCarrier({
          carrierId,
        })
      );

      fleetsResponse = await fetchFleets();
    }

    setDefaultFleetId(fleetsResponse);
  }, [carrierId, fleetMode, setDefaultFleetId]);

  /******************************* ASSETS *************************************/

  /*
   * Fetchs assets for fleet
   */
  const fetchAssetsList = useCallback(
    async ({ pageSize, pageIndex }: PaginationType, forceReload?: boolean) => {
      // Don't fetch if we don't have fleet id
      if (!fleetId) {
        return;
      }

      // Don't fetch if it's the same page
      if (
        !forceReload &&
        pageIndex === lastPageParams.index &&
        pageSize === lastPageParams.size
      ) {
        return;
      }

      await dispatch(
        CarrierFleetAssetsState.actions.retrieveFleetAssets({
          carrierId,
          fleetId,
          fleetMode,
          pageIndex: pageIndex + 1,
          pageSize: pageSize,
        })
      );

      setLastPageParams({ index: pageIndex, size: pageSize });
    },
    [carrierId, fleetId, lastPageParams]
  );

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  /*
   * Fetchers carrier fleet assets
   */
  useEffect(() => {
    if (!carrierId) {
      return;
    }

    getOrCreateDefaultFleet();
  }, [carrierId]);

  /*
   * Fetchers carrier fleet assets
   */
  useEffect(() => {
    if (!fleetId) {
      return;
    }

    fetchAssetsList({ ...DEFAULT_PAGINATION_PARAMETERS }, true);
  }, [fleetId]);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  /*
   * Handler for assets additions
   */
  const onAddAssets = async (assets: any) => {
    if (!fleetId) {
      return;
    }

    const response = await dispatch(
      CarrierFleetAssetsState.actions.createFleetAssets({
        fleetMode,
        carrierId,
        fleetId,
        assets,
      })
    );

    // Clsoe modal
    setShowAddAssetsModal(false);

    if ("error" in response) {
      showToast(
        t("Error."),
        t("There was an error adding the asset."),
        "error"
      );
      return;
    }

    showToast(t("Success!"), t("Your asset was added succesfully."), "error");

    // Load assets list again
    fetchAssetsList(DEFAULT_PAGINATION_PARAMETERS, true);
  };

  /*
   * Handler for asset deletion
   */
  const onDeleteAsset = async (asset: any) => {
    if (!fleetId) {
      return;
    }

    const response = await dispatch(
      CarrierFleetAssetsState.actions.deleteFleetAsset({
        fleetMode,
        carrierId,
        fleetId,
        assetId: asset?.id,
      })
    );

    if ("error" in response) {
      showToast(
        t("Error."),
        t("There was an error deleting the asset."),
        "error"
      );
      return;
    }

    // Load assets list again
    fetchAssetsList(DEFAULT_PAGINATION_PARAMETERS, true);
  };

  // TODO => edit asset
  const onEditAsset = (asset: any) => {
    // TODO: not implemented yet
  };

  //******************************* Asset location *****************************

  /*
   * Handler for getting location of asset
   */
  const onLocateAsset = (asset: any) => {
    setSelectedAsset(asset);
    setShowLocateAssetModal(true);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const isLoading = isRetrievingFleets || isRetrievingAssets || isEditingAssets;
  const showTableLoader = isLoading && carrierAssets.length === 0;

  if (showTableLoader) {
    return (
      <div className={styles.loader}>
        <Spinner isLoading size="medium" />
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <AssetsManagementHeader
        isLoading={isRetrievingAssets}
        fleetMode={fleetMode}
        fetchAssetsList={() => {
          fetchAssetsList({ ...DEFAULT_PAGINATION_PARAMETERS }, true);
        }}
        setShowAddAssetsModal={setShowAddAssetsModal}
      />

      <CarrierAssetsTable
        fleetMode={fleetMode}
        carrierAssets={carrierAssets}
        fetchAssetsList={fetchAssetsList}
        onLocateAsset={onLocateAsset}
        onEditAsset={onEditAsset}
        onDeleteAsset={onDeleteAsset}
      />

      <AddAssetsModal
        mode={fleetMode}
        show={showAddAssetsModal}
        onClose={() => setShowAddAssetsModal(false)}
        onAdd={onAddAssets}
      />

      <LocateAssetModal
        show={showLocateAssetModal}
        asset={selectedAsset}
        onClose={() => setShowLocateAssetModal(false)}
      />
    </div>
  );
};

const DEFAULT_PAGINATION_PARAMETERS = { pageSize: 25, pageIndex: 0 };

export default AssetsManagement;
