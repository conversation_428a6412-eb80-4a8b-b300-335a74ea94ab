import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import { ShipIcon, TruckIcon } from "@fourkites/elemental-atoms";
import { FleetMode } from "state/BaseTypes";

import SubPagePanel from "view/components/base/containers/SubPagePanel";
import TabbedContainer from "view/components/base/tabbed-container/TabbedContainer";

import AssetsManagement from "./AssetsManagement";

import styles from "./CarrierAssetManagementPage.module.scss";

const CarrierAssetManagementPage: React.FC = () => {
  const { t } = useTranslation();

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [fleetMode, setFleetMode] = useState<FleetMode>("otr");

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const tabs = [
    {
      title: "OTR",
      id: "otr",
      icon: <TruckIcon />,
      tabComponent: <AssetsManagement fleetMode={"otr"} />,
    },
    {
      title: t("Ocean"),
      id: "ocean",
      icon: <ShipIcon />,
      tabComponent: <AssetsManagement fleetMode={"ocean"} />,
    },
  ];

  return (
    <div
      className={styles.container}
      data-test-id="assets-management-page-container"
    >
      <SubPagePanel>
        <TabbedContainer
          tabs={tabs}
          selectedTab={fleetMode}
          onSelectTab={(tabId: FleetMode) => setFleetMode(tabId)}
        />
      </SubPagePanel>
    </div>
  );
};

export default CarrierAssetManagementPage;
