import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import {
  Button,
  Label,
  PlusIcon,
  SearchIcon,
  XIcon,
} from "@fourkites/elemental-atoms";
import { ButtonGroup, GroupButton } from "@fourkites/elemental-button-group";
import { Input } from "@fourkites/elemental-input";
import { Spinner } from "@fourkites/elemental-loading-indicator";

import { FleetMode } from "state/BaseTypes";
import { useAppSelector, useDebounce } from "state/hooks";
import { CarrierFleetAssetsState } from "state/modules/carrier/CarrierFleetAssets";

import { useDispatch } from "react-redux";
import styles from "./AssetsManagementHeader.module.scss";

const AssetsManagementHeader: React.FC<{
  fleetMode: FleetMode;
  isLoading: boolean;
  setShowAddAssetsModal: Function;
  fetchAssetsList: Function;
}> = ({ isLoading, fleetMode, setShowAddAssetsModal, fetchAssetsList }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  const assetsFilters = useAppSelector(
    CarrierFleetAssetsState.selectors.assetsFiltersByMode(fleetMode)
  );
  const { assetNumber, loadStatus, trackingStatus, assetType } = assetsFilters;

  const [userQuery, setUserQuery] = useState<string | null>(null);

  const queryValue = userQuery || assetNumber || "";

  /**
   * Filters by list and paginate. useDebounce will persist the query and
   * dispatch an action to filter for new queries. This way we can automatically
   * filter without throttling the server with requests
   */
  useDebounce(
    () => {
      if (userQuery == undefined) {
        return;
      }

      dispatch(
        CarrierFleetAssetsState.actions.changeAssetsFilters({
          fleetMode,
          assetNumber: userQuery,
        })
      );

      fetchAssetsList();
    },
    300, // timeout
    [userQuery]
  );

  /*
   * When user sets query, we change the local state. The local state will be
   * watched by useDebounce
   */
  const onChangeQuery = (userQuery: string) => {
    setUserQuery(userQuery);
  };

  const parentAssetTypeLabel = fleetMode === "otr" ? t("Truck") : t("Vessel");
  const parentAssetTypeKey = fleetMode === "otr" ? "truck" : "vessel";
  const childAssetTypeLabel =
    fleetMode == "otr" ? t("Trailer") : t("Container");
  const childAssetTypeKey = fleetMode === "otr" ? "trailer" : "container";

  return (
    <div className={styles.headerContainer}>
      <div id="left">
        <div className={styles.inputWrapper}>
          <Input
            label={""}
            placeholder={t("Search for asset number")}
            value={queryValue}
            onChange={(e: any) => onChangeQuery(e.target.value)}
            icon={queryValue ? <XIcon /> : <SearchIcon />}
            onIconClick={() => onChangeQuery("")}
            size="medium"
          />
        </div>
      </div>

      <div id="right">
        {/*
        <div className={styles.buttonGroupContainer}>
          {isLoading && (
            <div id="loading-wrapper">
              <Spinner isLoading size="small" />
            </div>
          )}

          <Label size="medium">{t("Asset Type")}</Label>
          <ButtonGroup
            onButtonClick={async (status: string) => {
              await dispatch(
                CarrierFleetAssetsState.actions.changeAssetsFilters({
                  fleetMode,
                  assetType: status !== "all" ? status : null,
                })
              );

              fetchAssetsList();
            }}
            selectedButtons={[assetType || "all"]}
            disabled={isLoading}
            size="medium"
          >
            <GroupButton buttonId="all">{t("All")}</GroupButton>
            <GroupButton buttonId={parentAssetTypeKey}>
              {t(parentAssetTypeLabel)}
            </GroupButton>
            <GroupButton buttonId={childAssetTypeKey}>
              {t(childAssetTypeLabel)}
            </GroupButton>
          </ButtonGroup>
        </div>
          */}

        <div className={styles.buttonGroupContainer}>
          {isLoading && (
            <div id="loading-wrapper">
              <Spinner isLoading size="small" />
            </div>
          )}

          <Label size="medium">{t("Tracking Status")}</Label>
          <ButtonGroup
            onButtonClick={async (status: string) => {
              await dispatch(
                CarrierFleetAssetsState.actions.changeAssetsFilters({
                  fleetMode,
                  trackingStatus: status !== "all" ? status : null,
                })
              );

              fetchAssetsList();
            }}
            selectedButtons={[trackingStatus || "all"]}
            disabled={isLoading}
            size="medium"
          >
            <GroupButton buttonId="all">{t("All")}</GroupButton>
            <GroupButton buttonId="tracking">{t("Tracking")}</GroupButton>
            <GroupButton buttonId="not_tracking">
              {t("Not Tracking")}
            </GroupButton>
          </ButtonGroup>
        </div>

        <div className={styles.buttonGroupContainer}>
          <Label size="medium">{t("Load Status")}</Label>
          <ButtonGroup
            onButtonClick={async (status: string) => {
              await dispatch(
                CarrierFleetAssetsState.actions.changeAssetsFilters({
                  fleetMode,
                  loadStatus: status !== "all" ? status : null,
                })
              );

              fetchAssetsList();
            }}
            selectedButtons={[loadStatus || "all"]}
            disabled={isLoading}
            size="medium"
          >
            <GroupButton buttonId="all">{t("All")}</GroupButton>
            <GroupButton buttonId="available">{t("Available")}</GroupButton>
            <GroupButton buttonId="loads_assigned">
              {t("Loads Assigned")}
            </GroupButton>
          </ButtonGroup>
        </div>

        {/* <Button size={"medium"} onClick={() => setShowAddAssetsModal(true)}>
          <PlusIcon fill="#fff" iconClass={"button-icon-left"} />
          {t("Add Assets")}
        </Button> */}
      </div>
    </div>
  );
};

export default AssetsManagementHeader;
