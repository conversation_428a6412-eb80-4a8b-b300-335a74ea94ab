import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { RadioButton } from "@fourkites/elemental-radio-button";
import { Modal } from "@fourkites/elemental-modal";

import styles from "./AddAssetsModal.module.scss";

const AddAssetsModal = ({ mode, show, onClose, onAdd }: any) => {
  const { t } = useTranslation();

  const [assetType, setAssetType] = useState<string>("");
  const [assetIds, setAssetIds] = useState<string>("");

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  /*
   * Fetchers carrier location
   */
  useEffect(() => {
    setAssetIds("");

    setAssetType(mode === "otr" ? "truck" : "vessel");

    // eslint-disable-next-line
  }, [show, mode]);

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const onAddAssets = () => {
    let assetsString = assetIds.replace(/\s/g, "");

    // Filter assets
    let assets = assetsString.split(",");
    assets = assets?.filter((a: string) => a !== "");
    if (!assets) {
      return;
    }

    let parsedAssets = assets.map((number: string) => ({
      number: number,
      type: assetType,
      //TODO
      identification_type: "unknown",
    }));

    onAdd(parsedAssets);
  };

  // @ts-ignore
  const title = t(`Add ${MODE_LABELS[mode]} Assets`);
  const subtitle = t("Add your assets below.");

  return (
    <Modal
      size="small"
      title={title}
      subtitle={subtitle}
      show={show}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: onClose,
      }}
      saveButtonProps={{
        label: t("Add"),
        onClick: onAddAssets,
      }}
    >
      <div
        className={styles.content}
        data-test-id="assets-management-add-modal-content"
      >
        <label className={styles.formLabel}>{t("Asset type")}</label>

        {mode === "otr" && (
          <OtrAssetTypes
            t={t}
            assetType={assetType}
            setAssetType={setAssetType}
          />
        )}

        {mode === "ocean" && (
          <OceanAssetTypes
            t={t}
            assetType={assetType}
            setAssetType={setAssetType}
          />
        )}

        <label className={styles.formLabel}>{t("Asset IDs")}</label>

        <textarea
          id="asset-ids"
          placeholder={t(
            "Enter Asset IDs separated by a comma, or paste from a spreadsheet"
          )}
          value={assetIds}
          rows={8}
          onChange={(e: any) => setAssetIds(e.target.value)}
        />
      </div>
    </Modal>
  );
};

const OtrAssetTypes = ({ t, assetType, setAssetType }: any) => {
  return (
    <span className={styles.assetType}>
      <RadioButton
        label={t("Truck #")}
        checked={assetType === "truck"}
        onClick={(e: any) => setAssetType("truck")}
        size="medium"
      />
      <RadioButton
        label={t("Trailer #")}
        checked={assetType === "trailer"}
        onClick={(e: any) => setAssetType("trailer")}
        size="medium"
      />
    </span>
  );
};

const OceanAssetTypes = ({ t, assetType, setAssetType }: any) => {
  return (
    <span className={styles.assetType}>
      <RadioButton
        label={t("Vessel #")}
        checked={assetType === "vessel"}
        onClick={(e: any) => setAssetType("vessel")}
        size="medium"
      />
      <RadioButton
        label={t("Container #")}
        checked={assetType === "container"}
        onClick={(e: any) => setAssetType("container")}
        size="medium"
      />
    </span>
  );
};

const MODE_LABELS = {
  otr: "OTR",
  ocean: "Ocean",
  rail: "Rail",
  air: "Air",
};

export default AddAssetsModal;
