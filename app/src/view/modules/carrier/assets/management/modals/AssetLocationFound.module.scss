@import "@fourkites/elemental-atoms/build/scss/colors/index";

.content {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  > label {
    font-weight: 600;
  }
}

.summary {
  display: flex;
  width: 100%;
  margin-bottom: 16px;

  > span {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;

    > label[id="title"] {
      color: $color-neutral-700;
      font-size: 16px;
      letter-spacing: 0;
      line-height: 24px;
    }

    > label[id="description"] {
      font-size: 16px;
      font-weight: 600;
      letter-spacing: 0;
      line-height: 24px;
    }
  }

  > button {
    display: flex;
    align-items: center;
    align-content: center;
  }
}

.mapContainer {
  width: 100%;
  height: 330px;

  > div {
    height: 340px;
  }
}
