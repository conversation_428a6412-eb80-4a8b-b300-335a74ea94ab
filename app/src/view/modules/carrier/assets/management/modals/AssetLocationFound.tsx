import { Icon, LatLngExpression } from "leaflet";
import { useTranslation } from "react-i18next";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>ay<PERSON> } from "react-leaflet";
import { Button, RefreshCcwIcon } from "@fourkites/elemental-atoms";

import markerIconPng from "leaflet/dist/images/marker-icon.png";
import { timePassedSinceDate } from "view/components/base/DateUtils";
import { fromKebabToCamelCase } from "view/components/base/StringUtils";

import styles from "./AssetLocationFound.module.scss";

const AssetLocationFound = ({ assetType, assetNumber, assetLocation, fetchAssetPingLocation }: any) => {
  const { t } = useTranslation();

  const { lat, lon, locatedAt, provider } = assetLocation;
  const providerName = provider ? fromKebabToCamelCase(provider) : "--";
  const position: LatLngExpression = assetLocation ? [lat, lon] : [0, 0];
  const address = `Lat: ${lat}, Lon: ${lon}`;
  const mapStyles = getCustomMapboxUrl();

  return (
    <div
      className={styles.content}
      data-test-id="assets-management-locate-modal-content"
    >
      <div className={styles.summary}>
        <span>
          <label id="title">{t("Current Location")}</label>
          <label id="description">{address}</label>
        </span>
        <span>
          <label id="title">{t("ELD/GPS")}</label>
          <label id="description">{providerName}</label>
        </span>
        <span>
          <label id="title">{t("Last Location Update")}</label>
          <label id="description">{timePassedSinceDate(locatedAt)}</label>
        </span>
        <Button onClick={() =>fetchAssetPingLocation(assetType, assetNumber)} size="large" theme="tertiary">
          <RefreshCcwIcon fill="#0e65e5" iconClass={"button-icon-left"} />
          {t("Locate Again")}
        </Button>
      </div>

      <div className={styles.mapContainer}>
        <MapContainer center={position} zoom={15} scrollWheelZoom={true}>
          <TileLayer
            attribution={mapStyles?.attribution}
            url={mapStyles?.tile}
          />

          <Marker
            position={position}
            icon={
              new Icon({
                iconUrl: markerIconPng,
                iconSize: [25, 41],
                iconAnchor: [12, 41],
              })
            }
          ></Marker>
        </MapContainer>
      </div>
    </div>
  );
};

// MAP STYLES
const MAPBOX_TILE_URL =
  "https://api.mapbox.com/styles/v1/fourkites-ux/cka209riz33k01ipc7hx28m95/tiles/{z}/{x}/{y}" +
  "?access_token=pk.eyJ1IjoiZm91cmtpdGVzLXV4IiwiYSI6ImNqZjhnODhwNzF0eGoyeHA0czh4enNpdjIifQ.0av6jP0C87TSje0-Ye4GPQ";

const MAPBOX_ATTRIBUTION =
  "<a href='https://www.here.com/' target='_blank'>HERE</a> |&copy; " +
  "<a href='https://www.maxar.com/' target='_blank'>Maxar</a> |&copy; " +
  "<a href='https://www.mapbox.com/about/maps/' target='_blank'>Mapbox</a> | &copy; " +
  "<a href='http://www.openstreetmap.org/copyright' target='_blank'>OpenStreetMap</a> " +
  "<strong><a href='https://www.mapbox.com/map-feedback/' target='_blank'>Improve this map</a></strong>";

export const getCustomMapboxUrl = () => {
  return {
    tile: MAPBOX_TILE_URL,
    attribution: MAPBOX_ATTRIBUTION,
  };
};
export default AssetLocationFound;
