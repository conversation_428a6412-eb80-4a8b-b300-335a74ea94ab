import { useTranslation } from "react-i18next";

import assetNotFound from "assets/img/assetNotFound.png";
import { fourkitesUrls } from "api/http/apiUtils";

import { Button, RefreshCcwIcon } from "@fourkites/elemental-atoms";

import styles from "./AssetLocationNotFound.module.scss";

const AssetLocationNotFound = ({ assetType, assetNumber, fetchAssetPingLocation }: any) => {
  const { t } = useTranslation();

  return (
    <div
      className={styles.content}
      data-test-id="assets-management-locate-modal-content"
    >
      <div className={styles.notFoundContainer}>
        <img className={styles.appStoreLogo} src={assetNotFound} />

        <>
          <h1 id="title">{t("Ooops! We couldn't locate your asset.")}</h1>
          <Button
            onClick={() =>fetchAssetPingLocation(assetType, assetNumber)}
            size="large"
            theme="tertiary"
          >
            <RefreshCcwIcon fill="#0e65e5" iconClass={"button-icon-left"} />
            {t("Try Again")}
          </Button>
        </>
        <p>
          <b>Troubleshooting:</b>
          <br />
          <li>
            <b>Invalid Asset Identifier:</b> Please make sure to ensure the
            asset identifier is valid for the ELD/GPS providers you have
            connected, and the format matches unit or asset ID numbers in your
            ELD/GPS provider. (i.e. Truck123 vs Truck_123 vs 123)
          </li>
          <li>
            <b>Initial Setup Time:</b> For some ELD/GPS providers, it may be
            necessary to wait about 20 minutes after connecting before we are
            able to ping an asset’s location.
          </li>
          <li>
            <b>OmniTracs:</b> We are unable to locate OmniTracs assets when
            unassigned to a FourKites load at this time.
          </li>
          <br />
          <b>Other reasons this asset can’t be located:</b>
          <li>The asset is in a dead zone without connectivity</li>
          <li>
            You don't have a GPS integration for this asset, and are tracking
            through other integration methods such as File / API
          </li>
          <li>The location provider unit is offline / is turned OFF</li>
          <li>
            The GPS credentials used are no longer valid and need to be updated
          </li>
          <br />
          For more information on other forms of data integrations such as File
          / API, please refer to our{" "}
          <a
            href={fourkitesUrls.shareTrackingDataKnowledgeBase}
            target="_blank"
          >
            {t("Knowledge Base")}
          </a>{" "}
          or submit a{" "}
          <a href={fourkitesUrls.contactSupport} target="_blank">
            {t("Support Ticket")}
          </a>{" "}
          for additional help.
        </p>
      </div>
    </div>
  );
};

export default AssetLocationNotFound;
