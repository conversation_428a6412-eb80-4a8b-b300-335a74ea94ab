import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { Spinner } from "@fourkites/elemental-loading-indicator";
import { Modal } from "@fourkites/elemental-modal";

import { useAppDispatch, useAppSelector } from "state/hooks";
import { CarrierFleetAssetsState } from "state/modules/carrier/CarrierFleetAssets";
import { UsersState } from "state/modules/Users";
import AssetLocationFound from "./AssetLocationFound";
import AssetLocationNotFound from "./AssetLocationNotFound";
import TrackingIntegrationAssetLocation from "../../../tracking/eld-gps/TrackingIntegrationAssetLocation";

import { startWithUpperCase } from "view/components/base/StringUtils";

import styles from "./LocateAssetModal.module.scss";

const LocateAssetModal = ({ show, asset, onClose }: any) => {
  const { t } = useTranslation();

  const dispatch = useAppDispatch();
  const carrierId: string = useAppSelector(UsersState.selectors.getCompanyId);

  // TODO: using jsut ping location for now
  /*
  const assetLocation = useAppSelector(
    CarrierFleetAssetsState.selectors.assetPingLocation()
  );
  const isLoading = useAppSelector(
    CarrierFleetAssetsState.selectors.isRetrievingAssetLocation()
  );
  */

  const assetPingLocation = useAppSelector(
    CarrierFleetAssetsState.selectors.assetPingLocation()
  );
  const isLoadingPingLocation = useAppSelector(
    CarrierFleetAssetsState.selectors.isRetrievingAssetPingLocation()
  );

  const shouldAskForAssetInfo = !asset;
  const [assetType, setAssetType] = useState<string>("truck");
  const [assetNumber, setAssetNumber] = useState<string>("");
  const [showAssetLocation, setShowAssetLocation] = useState<boolean>(false);

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  const fetchAssetPingLocation = async (assetType: string, assetId: string) => {
    dispatch(
      CarrierFleetAssetsState.actions.retrieveFleetAssetPingLocation({
        carrierId,
        assetType,
        assetId,
      })
    );
  };

  const fetchAndShowAssetLocation = (assetType: string, assetId: string) => {
    if (assetNumber.trim() !== "") {
      setShowAssetLocation(true);
      fetchAssetPingLocation(assetType, assetId);
    }
  };

  const handleModalClose = () => {
    setShowAssetLocation(false);
    setAssetNumber("");
    setAssetType("truck");
    onClose();
  };

  const getSubtitle = () => {
    let subtitle = "";

    if (!shouldAskForAssetInfo) {
      if (isLoadingPingLocation) {
        subtitle = t("Getting asset location...");
      } else if (assetLocationFound) {
        subtitle = t("See your asset location in the map");
      } else {
        subtitle = t("Asset not found");
      }
    }

    return subtitle;
  };

  /*
   * Pings asset for its latest location
   */
  useEffect(() => {
    if (!carrierId || !show || shouldAskForAssetInfo) {
      setShowAssetLocation(false);
      setAssetNumber("");
      return;
    }
    fetchAssetPingLocation(asset?.type, asset?.number);
    setShowAssetLocation(true);
  }, [carrierId, show, asset]);

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const assetLocationFound =
    !isLoadingPingLocation && assetPingLocation.lat != null;
  const assetLocationNotFound =
    !isLoadingPingLocation && assetPingLocation.lat == null;

  const title = shouldAskForAssetInfo
    ? t("Locate Asset")
    : t(`Locate ${startWithUpperCase(asset?.type)} ${asset?.number}`);
  const subtitle = shouldAskForAssetInfo
    ? ""
    : isLoadingPingLocation
    ? t("Getting asset location...")
    : assetLocationFound
    ? t("See your asset location in the map")
    : t("Asset not found");

  return (
    <Modal
      size="medium"
      title={title}
      subtitle={getSubtitle()}
      show={show}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: handleModalClose,
      }}
    >
      <div className={styles.content}>
        {shouldAskForAssetInfo && (
          <TrackingIntegrationAssetLocation
            assetType={assetType}
            setAssetType={setAssetType}
            assetNumber={assetNumber}
            setAssetNumber={setAssetNumber}
            fetchAndShowAssetLocation={fetchAndShowAssetLocation}
            showAssetLocation={showAssetLocation}
          />
        )}

        {isLoadingPingLocation && (
          <div
            className={styles.content}
            data-test-id="assets-management-locate-modal-content"
          >
            <div className={styles.loader}>
              <Spinner isLoading size="medium" />
            </div>
          </div>
        )}

        {showAssetLocation &&
          !isLoadingPingLocation &&
          (assetLocationFound ? (
            <AssetLocationFound
              assetType={asset?.type || assetType}
              assetNumber={asset?.number || assetNumber}
              assetLocation={assetPingLocation}
              fetchAssetPingLocation={fetchAssetPingLocation}
            />
          ) : (
            <AssetLocationNotFound
              assetType={asset?.type || assetType}
              assetNumber={asset?.number || assetNumber}
              fetchAssetPingLocation={fetchAssetPingLocation}
            />
          ))}
      </div>
    </Modal>
  );
};

export default LocateAssetModal;
