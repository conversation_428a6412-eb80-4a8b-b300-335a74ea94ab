@import "@fourkites/elemental-atoms/build/scss/colors/index";

.actionsIcon {
  height: 24px;
  width: 24px;
  cursor: pointer;

  &:hover {
    background-color: $color-neutral-300;
  }
}

.equipmentIcon {
  height: 24px;
  width: 24px;
  cursor: pointer;
}

.flyoutContent {
  display: flex;
  flex-direction: column;
  justify-content: left;
  padding-top: 16px;
  padding-bottom: 16px;
  position: relative;
  width: 100%;
  border-radius: 4px;
  box-shadow: 0 6px 14px 3px rgba(0, 0, 0, 0.1);
  background-color: white;
}

.loadStatusWrapper {
  > a {
    margin-left: 12px;
  }
}

.actionItem {
  display: flex;
  align-items: center;
  padding: 0;
  background-color: transparent;
  border: none;
  cursor: pointer;
  text-align: left;
  padding: 8px 16px;

  &:hover {
    background-color: $color-neutral-300;
  }
}

.tableContainer {
  width: 100%;
  height: calc(100vh + -300px);

  > div > div {
    overflow-x: auto;
    overflow-y: auto;

    > table {
      > tbody > tr > td {
        padding-top: 8px;
        padding-bottom: 8px;
      }
    }
  }
}
