import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { DIRECT_ASSIGNMENT_APP_URL } from "api/http/apiUtils";

import { Table } from "@fourkites/elemental-table";

import { useAppSelector } from "state/hooks";
import { CarrierFleetAssetsState } from "state/modules/carrier/CarrierFleetAssets";
import { UsersState } from "state/modules/Users";

import { fromKebabToCamelCase } from "view/components/base/StringUtils";

import { getColumns } from "./CarrierAssetsTableColumns";

import styles from "./CarrierAssetsTable.module.scss";

const CarrierAssetsTable = ({
  fleetMode,
  carrierAssets,
  fetchAssetsList,
  onEditAsset,
  onDeleteAsset,
  onLocateAsset,
}: any) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * REDUX
   ****************************************************************************/

  const companyName: string = useAppSelector(
    UsersState.selectors.getCompanyName
  );
  const directAssignmentInfo = useAppSelector(
    UsersState.selectors.getCompanyDirectAssignmentInfo
  );

  const fleetAssetsPagination = useAppSelector(
    CarrierFleetAssetsState.selectors.fleetAssetsPaginationByMode(fleetMode)
  );

  const directAssignmentUrl = `${DIRECT_ASSIGNMENT_APP_URL}/${
    directAssignmentInfo?.apiToken
  }/${btoa(companyName)}`;

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const onSelectDeleteAsset = (index: number) =>
    onDeleteAsset(carrierAssets[index]);

  const onSelectLocateAsset = (index: number) =>
    onLocateAsset(carrierAssets[index]);

  const onSelectEditAsset = (index: number) =>
    onEditAsset(carrierAssets[index]);

  /*****************************************************************************
   * DATA
   ****************************************************************************/

  // TODO: Create columns file
  const columns = useMemo(() => {
    return getColumns(
      t,
      fleetMode,
      directAssignmentUrl,
      onSelectDeleteAsset,
      onSelectLocateAsset
    );
  }, [
    fleetMode,
    directAssignmentUrl,
    onSelectEditAsset,
    onSelectDeleteAsset,
    onSelectLocateAsset,
  ]);

  const data = useMemo(() => {
    const getLocationProvider = (asset: any) =>
      asset?.location_provider_id
        ? fromKebabToCamelCase(asset?.location_provider_id)
        : "--";

    return carrierAssets?.map((asset: any) => {
      let locationProvider = getLocationProvider(asset);
      locationProvider =
        locationProvider === "Automatic_identification_system"
          ? "FourKites"
          : locationProvider;
      return {
        type: asset?.type,
        number: asset?.number,
        location_provider: locationProvider,
        tracking_status: asset?.tracking_status,
        load_status: asset?.load_status,
        loads: asset?.loads,
        last_tracked_at: asset?.last_tracked_at,
        modified_at: asset?.modified_at
          ? asset?.modified_at
          : asset?.created_at,
      };
    });
  }, [carrierAssets]);

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const paginationParams = {
    paginated: true,
    defaultPageSize: 25,
    fetchDataForPage: fetchAssetsList,
    pageCount: fleetAssetsPagination?.total,
    totalEntries: fleetAssetsPagination?.count,
  };

  return (
    <div
      className={styles.content}
      data-test-id="assets-management-table-content"
    >
      {data ? (
        <div className={styles.tableContainer}>
          <Table
            variant="flat-bordered"
            rowHeight="small"
            striped
            data={data}
            columns={columns}
            pagination={paginationParams}
          />
        </div>
      ) : (
        <label>{t("There are no assets for this carrier.")}</label>
      )}
    </div>
  );
};

export default CarrierAssetsTable;
