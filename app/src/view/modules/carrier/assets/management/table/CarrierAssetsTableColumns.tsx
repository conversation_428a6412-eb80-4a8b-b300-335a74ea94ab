import React from "react";
import { LinkButton } from "@fourkites/elemental-atoms";
import {
  //InfoIcon,
  //Button,
  MapIcon,
  ContainerHangedIcon,
  TruckEmptyIcon,
  MenuMeatballFillIcon,
  TrashFullIcon,
  ContainerStackedIcon,
  ShipIcon,
  MoveIcon,
} from "@fourkites/elemental-atoms";
import { Tooltip } from "@fourkites/elemental-tooltip";

import FlyoutMenu from "view/components/base/flyout-menu/FlyoutMenu";
import StatusTag from "view/components/base/status-indicators/StatusTag";
import { timePassedSinceDate } from "view/components/base/DateUtils";

import styles from "./CarrierAssetsTable.module.scss";

export const getColumns = (
  t: Function,
  fleetMode: string,
  directAssignmentUrl: string,
  onDeleteAsset: Function,
  onLocateAsset: Function
) => {
  return [
    {
      Header: "",
      accessor: "type",
      Cell: (cellProps: any) => {
        const assetType = cellProps.value;

        const assetIconsAndLabels = {
          trailer: {
            icon: (
              <ContainerHangedIcon
                iconClass={styles.equipmentIcon}
                fill="#21252a"
              />
            ),
            label: t("Trailer"),
          },
          truck: {
            icon: (
              <TruckEmptyIcon iconClass={styles.equipmentIcon} fill="#21252a" />
            ),
            label: t("Truck"),
          },
          container: {
            icon: (
              <ContainerStackedIcon
                iconClass={styles.equipmentIcon}
                fill="#21252a"
              />
            ),
            label: t("Container"),
          },
          vessel: {
            icon: <ShipIcon iconClass={styles.equipmentIcon} fill="#21252a" />,
            label: t("Vessel"),
          },
        };

        const assetValues =
          //@ts-ignore
          assetIconsAndLabels[assetType] || {
            icon: <MoveIcon iconClass={styles.equipmentIcon} fill="#21252a" />,
            label: t("Unkown"),
          };

        return (
          <Tooltip placement="bottom" text={assetValues?.label} theme="dark">
            <span>{assetValues?.icon}</span>
          </Tooltip>
        );
      },
    },
    {
      Header: "Asset Number",
      accessor: "number",
    },
    {
      Header: "Tracking Status",
      accessor: "tracking_status",
      Cell: (cellProps: any) => {
        // NOTE: we are considered null values as not tracking
        const trackingStatus =
          (cellProps.value as "tracking" | "not_tracking") || "not_tracking";

        const variant = trackingStatus === "not_tracking" ? "alert" : "ok";
        const labels = {
          tracking: t("Tracking"),
          not_tracking: t("Not Tracking"),
        };

        return (
          <span>
            <StatusTag label={labels[trackingStatus]} variant={variant} />
          </span>
        );
      },
    },
    {
      Header: "Location Provider",
      accessor: "location_provider",
    },
    {
      Header: "Load Status",
      accessor: "load_status",
      Cell: (cellProps: any) => {
        const loadStatus = cellProps.value as "loads_assigned" | "available";

        const assignButton = (
          <LinkButton
            href={directAssignmentUrl}
            theme="tertiary"
            target="_blank"
          >
            {t("ASSIGN NOW")}
          </LinkButton>
        );

        if (!loadStatus) {
          return (
            <span className={styles.loadStatusWrapper}>--{assignButton}</span>
          );
        }

        const variant = loadStatus === "available" ? "alert" : "ok";
        const labels = {
          loads_assigned: t("Assigned"),
          available: t("Needs Assignment"),
        };

        return (
          <span className={styles.loadStatusWrapper}>
            <StatusTag label={labels[loadStatus]} variant={variant} />

            {loadStatus === "available" && assignButton}
          </span>
        );
      },
    },
    /*{
      Header: "Loads",
      accessor: "loads",
      Cell: (cellProps: any) => {
        const loads = cellProps?.value;

        const parsedLoads =
          !loads || loads?.length === 0 ? (
            <LinkButton
              href={directAssignmentUrl}
              theme="tertiary"
              target="_blank"
            >
              {t("ASSIGN NOW")}
            </LinkButton>
          ) : loads.length > 1 ? (
            `${loads[0]} +${loads.length - 1} more`
          ) : (
            loads[0]
          );

        return <div data-test-id="assets-table-cell-loads">{parsedLoads}</div>;
      },
    },
    {
      Header: "Last Tracked",
      accessor: "last_tracked_at",
      Cell: (cellProps: any) => {
        //const lastTrackedAt = cellProps?.value
        //  ? timePassedSinceDate(cellProps?.value)
        //  : t("--");
        //return lastTrackedAt;

      },
    },*/
    {
      Header: "Updated At",
      accessor: "modified_at",
      Cell: (cellProps: any) => {
        // TODO: BE needs to return .000Z dates
        let date = cellProps?.value;
        date = date ? date?.split(".")[0] + ".000Z" : null;

        const lastUpdateTime = date ? timePassedSinceDate(date) : t("--");
        return lastUpdateTime;
      },
    },
    {
      Header: "",
      id: "actions",
      Cell: (cellProps: any) => {
        const index = cellProps.row.index;

        return (
          <div data-test-id="assets-table-cell-actions">
            <FlyoutMenu
              anchor={
                <MenuMeatballFillIcon
                  iconClass={styles.actionsIcon}
                  fill="#21252a"
                />
              }
            >
              <div className={styles.flyoutContent}>
                {/* For now, we are only allowing locate for OTR */}
                {fleetMode === "otr" && (
                  <button
                    className={styles.actionItem}
                    onClick={() => onLocateAsset(index)}
                  >
                    <MapIcon fill="#21252a" iconClass={"button-icon-left"} />
                    {t("Locate")}
                  </button>
                )}

                <button
                  className={styles.actionItem}
                  onClick={() => onDeleteAsset(index)}
                >
                  <TrashFullIcon
                    fill="#21252a"
                    iconClass={"button-icon-left"}
                  />
                  {t("Delete Asset")}
                </button>
              </div>
            </FlyoutMenu>
          </div>
        );
      },
    },
  ];
};
