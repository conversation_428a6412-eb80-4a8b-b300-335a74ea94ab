import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { useHistory } from "react-router-dom";
import { useAppSelector } from "state/hooks";
import { UsersState } from "state/modules/Users";

import {
  HierarchicalSidebar,
  SidebarSection,
} from "@fourkites/elemental-hierarchical-sidebar";

import CarrierCompanyRouter, {
  carrierCompanyRoutes,
  getCarrierCompanyRouteId,
} from "router/carrier/CarrierCompanyRouter";

import styles from "./CarrierCompanyPage.module.scss";

const CarrierCompanyPage: React.FC = () => {
  const { t } = useTranslation();
  const history = useHistory();

  /*****************************************************************************
   * REDUX
   ****************************************************************************/

  const isSuperAdmin = useAppSelector(UsersState.selectors.getIsSuperAdmin);

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [collapsed, setCollapsed] = useState<boolean>(false);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/
  const onChangeCollapse = (collapsed: boolean) => {
    setCollapsed(collapsed);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const sidebarSectionLinks = [
    {
      title: t("General"),
      id: carrierCompanyRoutes.general,
      to: carrierCompanyRoutes.general,
    },
    {
      title: t("Special Onboarding Instructions"),
      id: carrierCompanyRoutes.onboardingInstructions,
      to: carrierCompanyRoutes.onboardingInstructions,
    },
    isSuperAdmin && {
      title: t("FKC Configurations (SuperAdmin)"),
      id: "fkcConfigurationsId",
      sublinks: [
        {
          title: t("LTL Configurations"),
          id: carrierCompanyRoutes.ltlConfigurations,
          to: carrierCompanyRoutes.ltlConfigurations,
        },
        {
          title: t("Ocean + Freight Forwarders Configurations"),
          id: carrierCompanyRoutes.oceanConfigurations,
          to: carrierCompanyRoutes.oceanConfigurations,
        },
      ],
    },
  ].filter(Boolean);

  return (
    <div className={styles.pageContainer}>
      <div className={styles.container}>
        <div>
          <HierarchicalSidebar
            collapsed={collapsed}
            onChangeCollapse={onChangeCollapse}
          >
            {!collapsed && (
              <SidebarSection
                key={"general"}
                title={"Company Settings".toUpperCase()}
                links={sidebarSectionLinks}
                defaultActiveLinkId={getCarrierCompanyRouteId(history) || ""}
              />
            )}
          </HierarchicalSidebar>
        </div>

        <div className={styles.content}>
          <CarrierCompanyRouter />
        </div>
      </div>
    </div>
  );
};

export default CarrierCompanyPage;
