.container {
  display: flex;
  flex-direction: column;

  .formSwitch {
    margin-top: 10px;
  }

  .formContainer {
    margin: 10px 32px;
    padding-top: 10px;
  }
}

.formInput {
  margin-top: 20px;
  margin-left: 10px;
  width: 200px;
}

.formInputTextbox{
  flex: 1;
  margin-top: 20px;
  margin-bottom: 16px;

  > div {
    width: 20%;

    > div {
      display: flex;

      > input {
        width: 100%;
      }
    }
  }
}

.prerequisite {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
}

.textAreaClass {
  width: 25%;
  margin: 5px;
  min-height: 40px;
}
