import React from "react";
import { useTranslation } from "react-i18next";
import { Checkbox } from "@fourkites/elemental-checkbox";
import { Switch } from "@fourkites/elemental-switch";
import { Input } from "@fourkites/elemental-input";
import CredentialConfiguration from "./CredentialConfiguration";
import styles from "./AutoLoadCreationCapability.module.scss";

interface AutoLoadCreationCapabilityProps {
  capability: any;
  onUpdateCapabilities: any;
  mode?: string;
  disabled: boolean;
}

const AutoLoadCreationCapability: React.FC<AutoLoadCreationCapabilityProps> = ({
  capability,
  onUpdateCapabilities,
  mode,
  disabled,
}) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const onChange = (field: string, value: any) => {
    onUpdateCapabilities({
      ...capability,
      [field]: value,
    });
  };

  const handleLoadCreationBotsChange = (field: string, value: any) => {
    const updatedBotsValue = {
      ...capability.load_creation_bots,
      [field]: value,
    };

    onChange("load_creation_bots", updatedBotsValue);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div className={styles.container}>
      <div className={styles.formSwitch}>
      <Switch
          size="large"
          defaultLabel={t(`Enable ${capability.name}`)}
          checked={capability.enabled}
          onChange={() => {
            onChange("enabled", !capability.enabled);
          }}
          disabled={disabled}
        />
      </div>
      {capability.enabled && (
        <div className={styles.formContainer}>
          <div>
            <CredentialConfiguration
              capability={capability}
              onUpdateCapabilities={onUpdateCapabilities}
              disabled={disabled}
            />
          </div>
          {mode != "ocean" && (
          <div className={styles.formInputTextbox}>
            <Input
              size="medium"
              label="UI Scraper Bot Name"
              value={capability.ltl_carrier_bot_name}
              onChange={(e) => onChange("ltl_carrier_bot_name", e.target.value)}
              disabled={disabled}
            />
          </div> )}
          {mode != "ocean" && (
          <div className={styles.formInputTextbox}>
            <Input
              size="medium"
              label="Load Creation Mule Map"
              value={capability.mapping_configuration_name}
              onChange={(e) =>
                onChange("mapping_configuration_name", e.target.value)
              }
              disabled={disabled}
            />
          </div> )}
          {mode != "ocean" && (
          <div className={styles.formInputTextbox}>
            <Checkbox
              size="large"
              label={t("Enable Headrun UI Scraper Bot")}
              checked={capability?.load_creation_bots?.is_ui_scraping_enabled}
              onChange={() =>
                handleLoadCreationBotsChange(
                  "is_ui_scraping_enabled",
                  !capability?.load_creation_bots?.is_ui_scraping_enabled
                )
              }
              disabled={disabled}
            />
          </div>
        )}
          <div className={styles.prerequisite}>
            <label>{t("User Instructions for Auto Load Creation")}</label>
            <textarea
              className={styles.textAreaClass}
              placeholder={t(
                "Credentials are not required for this capability"
              )}
              value={capability.prerequisite}
              rows={5}
              onChange={(e) => onChange("prerequisite", e.target.value)}
              disabled={disabled}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default AutoLoadCreationCapability;
