import React from "react";
import { useTranslation } from "react-i18next";
import TrackingCapability from "./TrackingCapability";
import DocumentCapability from "./DocumentCapability";
import AutoLoadCreationCapability from "./AutoLoadCreationCapability";
import Capability from "./CarrierCapability.types";
import styles from "./CapabilitiesConfiguration.module.scss";

interface CapabilitiesConfigurationProps {
  capabilities: Capability[];
  onUpdateCapabilities: any;
  mode?: string;
  disabled: boolean;
}

const CapabilitiesConfiguration: React.FC<CapabilitiesConfigurationProps> = ({
  capabilities,
  onUpdateCapabilities,
  mode = "",
  disabled,
}) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div className={styles.container}>
      {capabilities?.map((cap: any) => (
        <div className={styles.formContainer} key={cap.id}>
          <label className={styles.boldLabel}>
            {t(`${cap.name} Capability`)}
          </label>
          <div className={styles.divider} />
          {getCapabilityComponent(cap, onUpdateCapabilities, mode, disabled)}
        </div>
      ))}
    </div>
  );
};

/*****************************************************************************
 * INTERNAL METHODS
 ****************************************************************************/

function getCapabilityComponent(
  cap: any,
  onUpdateCapabilities: any,
  mode:string,
  disabled: boolean
) {
  switch (cap.id) {
    case "tracking":
      return (
        <TrackingCapability
          capability={cap}
          onUpdateCapabilities={onUpdateCapabilities}
          disabled={disabled}
        />
      );
    case "document":
      return (
        <DocumentCapability
          capability={cap}
          onUpdateCapabilities={onUpdateCapabilities}
          mode={mode}
          disabled={disabled}
        />
      );
    case "loads":
      return (
        <AutoLoadCreationCapability
          capability={cap}
          onUpdateCapabilities={onUpdateCapabilities}
          mode={mode}
          disabled={disabled}
        />
      );

    default:
      return <div></div>;
  }
}

export default CapabilitiesConfiguration;
