import React, { useState} from "react";
import { useTranslation } from "react-i18next";
import { Checkbox } from "@fourkites/elemental-checkbox";
import styles from "./CredentialConfiguration.module.scss";
import { Label } from "@fourkites/elemental-atoms";

interface CredentialConfigurationProps {
  capability: any;
  onUpdateCapabilities: any;
  disabled: boolean;
}

const CredentialConfiguration: React.FC<CredentialConfigurationProps> = ({
  capability,
  onUpdateCapabilities,
  disabled,
}) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [credentials, setCredentials] = useState<any>(
    capability?.required_credentials || []
  );

  const credentialTypes = ["username", "password", "account_number", "api_key"];

  const credentialTypeToTranslationKey: { [key: string]: string } = {
    username: "Username",
    password: "Password",
    account_number: "Account Number",
    api_key: "Api Key",
  };

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const onChange = (type: string) => {
    const updatedCredentials = credentials.includes(type)
      ? credentials.filter((cred: string) => cred !== type)
      : [...credentials, type];
    setCredentials(updatedCredentials);
    onUpdateCapabilities({
      ...capability,
      ["required_credentials"]: updatedCredentials,
    });
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div className={styles.CredentialConfiguration}>
      <Label size="large">
        {t(`Select the required user credentials to enable ${capability.name}`)}
      </Label>
      {credentialTypes.map((type) => (
        <div className={styles.credential} key={type}>
          <Checkbox
            size="large"
            label={t(credentialTypeToTranslationKey[type])}
            checked={credentials.includes(type)}
            onChange={() => onChange(type)}
            disabled={disabled}
          />
        </div>
      ))}
    </div>
  );
};

export default CredentialConfiguration;
