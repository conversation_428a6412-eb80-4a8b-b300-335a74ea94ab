import React from "react";
import { useTranslation } from "react-i18next";
import { Switch } from "@fourkites/elemental-switch";
import CredentialConfiguration from "./CredentialConfiguration";
import LTLDocumentProvidersConstants from "./LTLDocumentProvidersConstants";
import styles from "./DocumentCapability.module.scss";
import { Select } from "@fourkites/elemental-select";

interface DocumentCapabilityProps {
  capability: any;
  onUpdateCapabilities: any;
  mode?:string;
  disabled: boolean;
}

const DocumentCapability: React.FC<DocumentCapabilityProps> = ({
  capability,
  onUpdateCapabilities,
  mode,
  disabled,
}) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const onChange = (field: string, value: any) => {
    onUpdateCapabilities({
      ...capability,
      [field]: value,
    });
  };

  const handleProviderChange = (providerName: string) => {
    const newSelectedProviderName = providerName;

    const selectedProvider = LTLDocumentProvidersConstants.find(
      (provider) => provider.name === newSelectedProviderName
    );

    if (selectedProvider) {
      onChange("ltl_document_provider", {
        value: selectedProvider.value,
        source: selectedProvider.providerSource || "",
      });
    }
  };

  const getFieldValue = (key: string) => {
    const matchingProvider = LTLDocumentProvidersConstants.find(
      (provider) => provider.value === key
    );

    if (matchingProvider) {
      return [matchingProvider.name];
    }
    return [""];
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div className={styles.container}>
      <div className={styles.formSwitch}>
        <Switch
          size="large"
          defaultLabel={t(`Enable ${capability.name}`)}
          checked={capability.enabled}
          onChange={() => {
            onChange("enabled", !capability.enabled);
          }}
          disabled={disabled}
        />
      </div>
      {capability.enabled && (
        <div className={styles.formContainer}>
          <div>
            <CredentialConfiguration
              capability={capability}
              onUpdateCapabilities={onUpdateCapabilities}
              disabled={disabled}
            />
          </div>

          {mode != 'ocean' && <div className={styles.formInput}>
            <Select
              size="medium"
              label={t("LTL Document Provider")}
              options={LTLDocumentProvidersConstants.map(
                (provider) => provider.name
              )}
              value={getFieldValue(capability.ltl_document_provider?.value)}
              onChange={(selectedOptions: any) => {
                handleProviderChange(selectedOptions[0]);
              }}
              disabled={disabled}
              required={true}
            />
          </div>}
          <div className={styles.prerequisite}>
            <label>{t("User Instructions for Document access")}</label>
            <textarea
              className={styles.textAreaClass}
              placeholder={t(
                "Credentials are not required for this capability"
              )}
              value={capability.prerequisite}
              rows={5}
              onChange={(e) => onChange("prerequisite", e.target.value)}
              disabled={disabled}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default DocumentCapability;
