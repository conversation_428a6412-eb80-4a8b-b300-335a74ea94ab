export const LTLDocumentProvidersConstants = [
  { name: "A Duie Pyle", value: "aduiepyle", providerSource: "Headrun" },
  {
    name: "Central Freight Lines Inc.",
    value: "centralfreight",
    providerSource: "Headrun",
  },
  {
    name: "Central Transport International",
    value: "centraltransportint",
    providerSource: "Headrun",
  },
  { name: "Ceva Logistics", value: "cevalogistics", providerSource: "Headrun" },
  { name: "DHE", value: "godependable", providerSource: "Headrun" },
  { name: "DSV", value: "mydsv", providerSource: "Headrun" },
  { name: "Fedex", value: "fedex", providerSource: "Headrun" },
  { name: "Hiway-9", value: "hiway", providerSource: "Headrun" },
  {
    name: "Midland Transport",
    value: "midlandtransport",
    providerSource: "Headrun",
  },
  {
    name: "Roadrunner Transportation Systems",
    value: "rrts",
    providerSource: "Headrun",
  },
  { name: "SEFL", value: "sefl", providerSource: "Headrun" },
  { name: "UPS", value: "ups", providerSource: "Headrun" },
  { name: "YRC", value: "yrc", providerSource: "Headrun" },
  { name: "Oceanex", value: "oceanex", providerSource: "Headrun" },
  {
    name: "Transport V A Inc.",
    value: "vatransport",
    providerSource: "Headrun",
  },
  {
    name: "Gardewine & Sons LTD",
    value: "gardewineweb",
    providerSource: "Headrun",
  },
  {
    name: "Speedy Transport Group Inc.",
    value: "speedy",
    providerSource: "Headrun",
  },
  {
    name: "Day and Ross",
    value: "day_and_ross",
    providerSource: "Direct Integration",
  },
  {
    name: "UPS Direct API",
    value: "ups_api",
    providerSource: "Direct Integration",
  },
  {
    name: "UPS Europe Direct API",
    value: "ups_europe_api",
    providerSource: "Direct Integration",
  },
  {
    name: "Fedex Direct API",
    value: "fedex_api",
    providerSource: "Direct Integration",
  },
  { name: "N & M", value: "nmtransfer", providerSource: "Direct Integration" },
  { name: "SMC3", value: "smc3", providerSource: "Other" },
  { name: "BOTS API", value: "bots_api", providerSource: "Other" },
];

export default LTLDocumentProvidersConstants;
