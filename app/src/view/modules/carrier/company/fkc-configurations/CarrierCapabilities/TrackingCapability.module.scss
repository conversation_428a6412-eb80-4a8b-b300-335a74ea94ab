.container {
  display: flex;
  flex-direction: column;

  .formSwitch {
    margin-top: 10px;
  }

  .formContainer {
    margin: 10px 32px;
    padding-top: 10px;

    .formInputFields {
      display: flex;
      padding: 20px;
      width: 40%;

      .column {
        flex: 1;
        margin-bottom: 16px;

        > div {
          width: 90%;
          > input {
            width: 100%;
          }
        }
      }
    }
  }
}

.formInput {
  margin-top: 20px;
  margin-left: 10px;
}

.radioButtonWrapper {
  display: flex;
  flex: 1;
  flex-direction: row;
  margin-top: 8px;
  margin-bottom: 8px;

  > div {
    margin-right: 16px;
  }
}
.prerequisite {
  display: flex;
  flex-direction: column;
}

.textAreaClass {
  width: 25%;
  margin: 5px;
  min-height: 40px;
}
