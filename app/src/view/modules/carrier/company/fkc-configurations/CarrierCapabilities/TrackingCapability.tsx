import React from "react";
import { useTranslation } from "react-i18next";
import { Switch } from "@fourkites/elemental-switch";
import { Input } from "@fourkites/elemental-input";
import CredentialConfiguration from "./CredentialConfiguration";
import styles from "./TrackingCapability.module.scss";
import { Label } from "@fourkites/elemental-atoms";

import { RadioButton } from "@fourkites/elemental-radio-button";

interface TrackingCapabilityProps {
  capability: any;
  onUpdateCapabilities: any;
  disabled: boolean;
}
const TrackingCapability: React.FC<TrackingCapabilityProps> = ({
  capability,
  onUpdateCapabilities,
  disabled,
}) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const onChange = (field: string, value: any) => {
    onUpdateCapabilities({
      ...capability,
      [field]: value,
    });
  };

  const handleGlobalCredentialsChange = (field: string, value: any) => {
    const updatedGlobalCredsValue = {
      ...capability.global_credentials,
      [field]: value,
    };

    onChange("global_credentials", updatedGlobalCredsValue);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div className={styles.container}>
      <div className={styles.formSwitch}>
        <Switch
          size="large"
          defaultLabel={t(`Enable ${capability.name}`)}
          checked={capability.enabled}
          onChange={() => {
            onChange("enabled", !capability.enabled);
          }}
          disabled={disabled}
        />
      </div>
      {capability.enabled && (
        <div className={styles.formContainer}>
          <div className={styles.radioButtonWrapper}>
          <RadioButton
              label={t("Customer Specific Credentials")}
              checked={!capability.global_credentials_enabled}
              onClick={() => onChange("global_credentials_enabled", false)}
              size="large"
              disabled={disabled}
            />
            <RadioButton
              label={t("Global Credentials")}
              checked={capability.global_credentials_enabled}
              onClick={() => onChange("global_credentials_enabled", true)}
              size="large"
              disabled={disabled}
            />
          </div>
          {capability.global_credentials_enabled && (
            <div className={styles.formInput}>
              <Label size="large">{t("Please enter global credentials")}</Label>
              <div className={styles.formInputFields}>
                <div className={styles.column}>
                  <Input
                    label={t("Username")}
                    value={capability?.global_credentials?.username}
                    onChange={(e) =>
                      handleGlobalCredentialsChange("username", e.target.value)
                    }
                    disabled={disabled}
                  />
                </div>
                <div className={styles.column}>
                  <Input
                    label={t("Password")}
                    value={capability?.global_credentials?.password}
                    onChange={(e) =>
                      handleGlobalCredentialsChange("password", e.target.value)
                    }
                    disabled={disabled}
                  />
                </div>
              </div>
              <div className={styles.formInputFields}>
                <div className={styles.column}>
                  <Input
                    label={t("Account Number")}
                    value={capability?.global_credentials?.account_number}
                    onChange={(e) =>
                      handleGlobalCredentialsChange(
                        "account_number",
                        e.target.value
                      )
                    }
                    disabled={disabled}
                  />
                </div>
                <div className={styles.column}>
                  <Input
                    label={t("API Key")}
                    value={capability?.global_credentials?.api_key}
                    onChange={(e) =>
                      handleGlobalCredentialsChange("api_key", e.target.value)
                    }
                    disabled={disabled}
                  />
                </div>
              </div>
            </div>
          )}

          {!capability.global_credentials_enabled && (
            <div>
              <CredentialConfiguration
                capability={capability}
                onUpdateCapabilities={onUpdateCapabilities}
                disabled={disabled}
              />
            </div>
          )}
          <div className={styles.prerequisite}>
            <label>{t("User Instructions for Tracking")}</label>
            <textarea
              className={styles.textAreaClass}
              placeholder={t(
                "Credentials are not required for this capability"
              )}
              value={capability.prerequisite}
              rows={5}
              onChange={(e) => onChange("prerequisite", e.target.value)}
              disabled={disabled}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default TrackingCapability;
