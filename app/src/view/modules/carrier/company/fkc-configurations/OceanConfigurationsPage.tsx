import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { Spinner } from "@fourkites/elemental-loading-indicator";
import { Switch } from "@fourkites/elemental-switch";
import { Input } from "@fourkites/elemental-input";

import { useAppSelector, useAppDispatch } from "state/hooks";
import { CarrierConfigurationsState } from "state/modules/carrier/CarrierConfigurations";
import { UsersState } from "state/modules/Users";

import BreadcrumbsHeader from "view/components/base/breadcrumbs-header/BreadcrumbsHeader";

import CompanySection from "view/components/base/editable-section/EditableSection";

import SubPagePanel from "view/components/base/containers/SubPagePanel";

import styles from "./LTLConfigurationsPage.module.scss";
import LTLConfigurationsCapabilities from "./CarrierCapabilities/CapabilitiesConfiguration";
import Capability from "./CarrierCapabilities/CarrierCapability.types";
import MultiInput from "view/components/base/multi-input/MultiInput";

import {
  MAX_SCACS,
  MAX_SCACS_LENGTH,
} from "view/components/self-service/company-management/CompanyManagementUtils";

const OceanConfigurationsPage = ({}: any) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  const carrierId: string = useAppSelector(UsersState.selectors.getCompanyId);

  const carrierConfigurations: any = useAppSelector(
    CarrierConfigurationsState.selectors.carrierConfigurations()
  );

  const isLoadingCarrierConfigurations: boolean = useAppSelector(
    CarrierConfigurationsState.selectors.isLoadingCarrierConfigurations()
  );
  const isEditingCarrierConfigurations: boolean = useAppSelector(
    CarrierConfigurationsState.selectors.isEditingCarrierConfigurations()
  );

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  useEffect(() => {
    dispatch(
      CarrierConfigurationsState.actions.retrieveCarrierConfigurations({
        carrierId,
        mode: 'ocean',
      })
    );
  }, [carrierId]);

  /*****************************************************************************
   * RENDER
   ****************************************************************************/
  const showHeaderLoader =
    carrierConfigurations != null &&
    (isLoadingCarrierConfigurations || isEditingCarrierConfigurations);

  const breadcrumbTitles = [t("Ocean + Freight Forwarders Configurations")];
  const headerItems = [
    showHeaderLoader ? (
      <div className={styles.headerLoader}>
        <label>{t("Loading details")}</label>
        <Spinner isLoading size="small" />
      </div>
    ) : null,
  ];

  return (
    <SubPagePanel>
      <div className={styles.titleContainer}>
        <BreadcrumbsHeader titles={breadcrumbTitles} children={headerItems} />
        <label className={styles.subHeading}>
          {t(
            "If you want to set up this company as an Ocean + Freight Forwarders Carrier in Fourkites Connect," +
              " please enable the configurations below."
          )}
        </label>
      </div>

      {carrierConfigurations && (
        <div className={styles.container}>
          <OceanConfigurations
            carrierConfigurations={carrierConfigurations}
            isLoadingCarrierConfigurations={isLoadingCarrierConfigurations}
            isEditingCarrierConfigurations={isEditingCarrierConfigurations}
          />
        </div>
      )}
    </SubPagePanel>
  );
};

export default OceanConfigurationsPage;

const OceanConfigurations: React.FC<{
  carrierConfigurations: any;
  isLoadingCarrierConfigurations: boolean;
  isEditingCarrierConfigurations: boolean;
}> = ({
  carrierConfigurations,
  isLoadingCarrierConfigurations,
  isEditingCarrierConfigurations,
}) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  const carrierId: string = useAppSelector(UsersState.selectors.getCompanyId);

  /*****************************************************************************
   * STATE
   ****************************************************************************/
  const [disabled, setDisabled] = useState<boolean>(true);
  const [oceanConfigurationsEnabled, setOceanConfigurationsEnabled] =
    useState<boolean>(false);

  const [oceanConfigurations, setOceanConfigurations] = useState<any>({
    is_ocean_carrier: false,
    mode: "ocean",
    current_integration: "",
    pro_number: "",
    scac: [],
    capabilities: [],
  });

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  useEffect(() => {
    if (carrierConfigurations) {
      resetLocalConfigurations();
    }
  }, [carrierConfigurations]);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const resetLocalConfigurations = () => {
    const isOceanCarrier = carrierConfigurations?.is_ocean_carrier;
    setOceanConfigurationsEnabled(isOceanCarrier);
    setOceanConfigurations({
      is_ocean_carrier: isOceanCarrier,
      mode: carrierConfigurations?.mode || "ocean",
      current_integration: carrierConfigurations?.current_integration || "",
      pro_number: carrierConfigurations?.pro_number || "",
      scac: carrierConfigurations?.scac,
      capabilities: carrierConfigurations?.capabilities,
    });
  };

  const onEdit = () => {
    setDisabled(false);
  };

  const onCancel = () => {
    resetLocalConfigurations();
    setDisabled(true);
  };

  const onSave = async () => {
    setDisabled(true);

    const payload = {
      is_ocean_carrier: oceanConfigurations.is_ocean_carrier,
      mode: oceanConfigurations.mode,
      pro_number: oceanConfigurations.pro_number,
      scac: oceanConfigurations.scac,
      capabilities: oceanConfigurations.capabilities,
    };

    await dispatch(
      CarrierConfigurationsState.actions.updateCarrierConfigurations({
        carrierId,
        ltlConfigurations: payload,
      })
    );
  };

  const onUpdateCapabilities = (updatedCapability: any) => {
    const capabilities = oceanConfigurations.capabilities;
    const updatedIndex = capabilities.findIndex(
      (cap: Capability) => cap.id === updatedCapability.id
    );

    if (updatedIndex !== -1) {
      const updatedCapabilities = [...capabilities];
      updatedCapabilities[updatedIndex] = updatedCapability;
      onChange("capabilities", updatedCapabilities);
    }
  };

  const onChange = (field: string, value: any) => {
    if (disabled) {
      return;
    }
    setOceanConfigurations({
      ...oceanConfigurations,
      [field]: value,
    });
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const showLoader =
    Object.keys(carrierConfigurations).length === 0 &&
    isLoadingCarrierConfigurations;

  const multiInputValidation = {
    maxNumberOfValues: MAX_SCACS,
    maxValueLength: MAX_SCACS_LENGTH,
  };

  return (
    <CompanySection
      title={t("Ocean + Freight Forwarders Configurations")}
      onEdit={onEdit}
      onSave={onSave}
      onCancel={onCancel}
      editable={!isEditingCarrierConfigurations}
    >
      {showLoader ? (
        <div>
          <Spinner isLoading size="medium" />
        </div>
      ) : (
        <div>
          {!isEditingCarrierConfigurations && (
            <div>
              <Switch
                size="large"
                defaultLabel={t("Enable Ocean + Freight Forwarders Configurations")}
                checked={oceanConfigurationsEnabled}
                disabled={disabled}
                onChange={() => {
                  onChange("is_ocean_carrier", !oceanConfigurationsEnabled);
                  setOceanConfigurationsEnabled(!oceanConfigurationsEnabled);
                }}
              />
            </div>
          )}
          {oceanConfigurationsEnabled && (
            <div className={styles.configContainer}>
              <div className={styles.formContainer}>
                <label className={styles.boldLabel}>
                  {t("General Configurations")}
                </label>
                <div className={styles.divider} />
                <div>
                  
                  {oceanConfigurations.current_integration === "BOTS API" && (
                    <div className={styles.formSection}>
                      <Input
                        label={t("Bots Active Pro Number")}
                        value={oceanConfigurations.pro_number}
                        onChange={(e) => onChange("pro_number", e.target.value)}
                        disabled={disabled}
                      />
                    </div>
                  )}
                  <div className={styles.formSection}>
                    <MultiInput
                      label={t("Default Scacs")}
                      defaultValues={[]}
                      values={oceanConfigurations.scac || []}
                      onAddValue={(scac: string) =>
                        onChange("scac", [
                          ...(oceanConfigurations.scac || []),
                          scac,
                        ])
                      }
                      onRemoveValue={(scac: string) =>
                        onChange(
                          "scac",
                          oceanConfigurations.scac.filter(
                            (item: string) => item !== scac
                          )
                        )
                      }
                      validation={multiInputValidation}
                      size="medium"
                      disabled={disabled}
                    />
                  </div>
                </div>
              </div>

              <label className={styles.boldLabel}>
                {t("Capabilities Configurations")}
              </label>
              <div className={styles.divider} />
              {carrierConfigurations && (
                <LTLConfigurationsCapabilities
                  capabilities={oceanConfigurations.capabilities}
                  onUpdateCapabilities={onUpdateCapabilities}
                  mode={oceanConfigurations.mode}
                  disabled={disabled}
                />
              )}
            </div>
          )}
        </div>
      )}
    </CompanySection>
  );
};
