import { useEffect } from "react";
import { useTranslation } from "react-i18next";

import { Spinner } from "@fourkites/elemental-loading-indicator";

import { useAppSelector, useAppDispatch } from "state/hooks";
import { CarrierCompanyDetailsState } from "state/modules/carrier/CarrierCompanyDetails";
import { UsersState } from "state/modules/Users";

import BreadcrumbsHeader from "view/components/base/breadcrumbs-header/BreadcrumbsHeader";

import SubPagePanel from "view/components/base/containers/SubPagePanel";

import CompanyProfile from "../../../../components/self-service/company-profile/CompanyProfile";
import CarrierCompanySettings from "./settings/CarrierCompanySettings";
//import CarrierCompanyRepresentatives from "./representatives/CarrierCompanyRepresentatives";

import styles from "./CarrierCompanyGeneralPage.module.scss";

const CarrierCompanyGeneralPage = ({}: any) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  // Carrier ID
  const carrierId: string = useAppSelector(UsersState.selectors.getCompanyId);

  const carrierCompanyDetails: any = useAppSelector(
    CarrierCompanyDetailsState.selectors.carrierCompanyDetails()
  );

  const isLoadingCarrierCompanyDetails: boolean = useAppSelector(
    CarrierCompanyDetailsState.selectors.isLoadingCarrierCompanyDetails()
  );
  const isEditingCarrierCompanyDetails: boolean = useAppSelector(
    CarrierCompanyDetailsState.selectors.isEditingCarrierCompanyDetails()
  );

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  useEffect(() => {
    dispatch(
      CarrierCompanyDetailsState.actions.retrieveCarrierCompanyDetails({
        carrierId,
      })
    );
  }, [carrierId]);

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const showLoader =
    Object.keys(carrierCompanyDetails).length === 0 &&
    isLoadingCarrierCompanyDetails;
  const showHeaderLoader =
    carrierCompanyDetails != null &&
    (isLoadingCarrierCompanyDetails || isEditingCarrierCompanyDetails);

  const breadcrumbTitles = [t("General Settings")];

  return (
    <SubPagePanel>
      <div
        className={styles.titleContainer}
        data-test-id="general-settings-title"
      >
        <BreadcrumbsHeader
          titles={breadcrumbTitles}
          children={[
            showHeaderLoader ? (
              <div className={styles.headerLoader}>
                <label>{t("Loading details")}</label>
                <Spinner isLoading size="small" />
              </div>
            ) : null,
          ]}
        />
      </div>

      {carrierCompanyDetails && (
        <div className={styles.container}>
          <CarrierCompanySettings
            showLoader={showLoader}
            carrierId={carrierId}
            settings={carrierCompanyDetails?.settings}
          />

          <CompanyProfile
            showLoader={showLoader}
            companyId={carrierId}
            companyDetails={carrierCompanyDetails}
          />

          {/*<CarrierCompanyRepresentatives />*/}
        </div>
      )}
    </SubPagePanel>
  );
};

export default CarrierCompanyGeneralPage;
