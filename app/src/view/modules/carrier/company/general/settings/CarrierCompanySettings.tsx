import { ChangeEvent, useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

import { Switch } from "@fourkites/elemental-switch";
import { Spinner } from "@fourkites/elemental-loading-indicator";

import { useAppDispatch, useAppSelector } from "state/hooks";
import { CarrierCompanyDetailsState } from "state/modules/carrier/CarrierCompanyDetails";

import CompanySection from "view/components/base/editable-section/EditableSection";

import styles from "./CarrierCompanySettings.module.scss";

const CarrierCompanySettings = ({ showLoader, carrierId, settings }: any) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  const isEditingCarrierCompanyDetails: boolean = useAppSelector(
    CarrierCompanyDetailsState.selectors.isEditingCarrierCompanyDetails()
  );

  /*****************************************************************************
   * STATE
   ****************************************************************************/
  const [autoAccept, setAutoAccept] = useState<boolean>(false);
  const [globallyVisible, setGloballyVisible] = useState<boolean>(false);
  const [disabled, setDisabled] = useState<boolean>(true);

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  const resetLocalSettings = () => {
    setGloballyVisible(settings?.onboarding?.globally_visible || false);
    setAutoAccept(settings?.onboarding?.auto_accept || false);
  };

  useEffect(() => {
    if (settings) {
      resetLocalSettings();
    }
  }, [settings]);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const onEdit = () => {
    setDisabled(false);
  };

  const onCancel = () => {
    resetLocalSettings();
    setDisabled(true);
  };

  const onSave = async () => {
    setDisabled(true);

    await dispatch(
      CarrierCompanyDetailsState.actions.updateCarrierCompanyDetails({
        carrierId,
        carrierCompanyDetails: {
          settings: {
            onboarding: {
              globally_visible: globallyVisible,
              auto_accept: autoAccept,
            },
          },
        },
      })
    );
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <CompanySection
      title={t("Network Requests")}
      onEdit={onEdit}
      onSave={onSave}
      onCancel={onCancel}
      editable={!isEditingCarrierCompanyDetails}
    >
      {showLoader ? (
        <div className={styles.settings}>
          <Spinner isLoading size="medium" />
        </div>
      ) : (
        settings && (
          <div className={styles.settings}>
            {/*
          <div className={disabled ? styles.disabledWrapper : ""}>
            <Switch
              size="large"
              defaultLabel={t("Visible to all FourKites Shippers/Brokers")}
              checked={globallyVisible}
              onChange={(e: ChangeEvent<HTMLInputElement>) => {
                if (disabled) {
                  return;
                }

                setGloballyVisible(!globallyVisible);
              }}
            />
            <label>
              {t("Any Shipper/Broker can search and add you to their Network.")}
            </label>
          </div>
          */}

            <div>
              <Switch
                size="large"
                defaultLabel={t("Auto Accept Shipper/Broker Connections")}
                checked={autoAccept}
                disabled={disabled}
                onChange={(e: ChangeEvent<HTMLInputElement>) => {
                  if (disabled) {
                    return;
                  }

                  setAutoAccept(!autoAccept);
                }}
              />
              <label>
                {t(
                  "You will be auto connected with Shippers/Brokers if they choose to connect with you."
                )}
              </label>
            </div>
          </div>
        )
      )}
    </CompanySection>
  );
};

export default CarrierCompanySettings;
