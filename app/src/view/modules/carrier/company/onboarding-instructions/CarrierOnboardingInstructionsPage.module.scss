@import "@fourkites/elemental-atoms/build/scss/colors/index";

.headerWrapper {
  padding: 32px;
  padding-top: 27px;
  background-color: $color-neutral-50;
}

.subHeading {
  font-size: 16px;
  color: $color-neutral-700;
}

.container {
  margin-top: 12px;
}

.settings {
  display: flex;
  flex-direction: column;

  > div {
    display: flex;
    flex-direction: column;
    margin-top: 12px;
    margin-bottom: 48px;

    &:last-child {
      margin-top: 0;
      margin-bottom: 0;
    }

    > label {
      color: $color-neutral-600;
      font-size: 16px;
      letter-spacing: 0;
      line-height: 24px;
      margin-top: 6px;
      margin-left: 57px;
    }
  }
}

.disabledWrapper {
  > div > span {
    opacity: 0.5;
  }
}

.loader {
  display: flex;
  align-items: left;
  align-content: center;
  justify-content: center;
  margin-top: 32px;
  margin-bottom: 32px;
}

.headerLoader {
  composes: loader;
  margin: 0;
  margin-right: 16px;

  > label {
    margin-right: 16px;
  }
}
