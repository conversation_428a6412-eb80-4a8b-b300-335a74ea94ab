import { ChangeEvent, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { Spinner } from "@fourkites/elemental-loading-indicator";
import { Switch } from "@fourkites/elemental-switch";

import { useAppSelector, useAppDispatch } from "state/hooks";
import { CarrierCompanyDetailsState } from "state/modules/carrier/CarrierCompanyDetails";
import { UsersState } from "state/modules/Users";

import BreadcrumbsHeader from "view/components/base/breadcrumbs-header/BreadcrumbsHeader";
import { showToast } from "view/components/base/toast/Toast";

import CompanySection from "view/components/base/editable-section/EditableSection";

import SubPagePanel from "view/components/base/containers/SubPagePanel";

import styles from "./CarrierOnboardingInstructionsPage.module.scss";

const CarrierOnboardingInstructionsPage = ({}: any) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  // Carrier ID
  const carrierId: string = useAppSelector(UsersState.selectors.getCompanyId);

  const carrierCompanyDetails: any = useAppSelector(
    CarrierCompanyDetailsState.selectors.carrierCompanyDetails()
  );

  const isLoadingCarrierCompanyDetails: boolean = useAppSelector(
    CarrierCompanyDetailsState.selectors.isLoadingCarrierCompanyDetails()
  );
  const isEditingCarrierCompanyDetails: boolean = useAppSelector(
    CarrierCompanyDetailsState.selectors.isEditingCarrierCompanyDetails()
  );

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  useEffect(() => {
    dispatch(
      CarrierCompanyDetailsState.actions.retrieveCarrierCompanyDetails({
        carrierId,
      })
    );
  }, [carrierId]);

  /*****************************************************************************
   * RENDER
   ****************************************************************************/
  const showHeaderLoader =
    carrierCompanyDetails != null &&
    (isLoadingCarrierCompanyDetails || isEditingCarrierCompanyDetails);

  const breadcrumbTitles = [t("Special Onboarding Instructions")];
  const headerItems = [
    showHeaderLoader ? (
      <div className={styles.headerLoader}>
        <label>{t("Loading details")}</label>
        <Spinner isLoading size="small" />
      </div>
    ) : null,
  ];

  return (
    <SubPagePanel>
      <div
        className={styles.titleContainer}
        data-test-id="onboarding-instructions-title"
      >
        <BreadcrumbsHeader titles={breadcrumbTitles} children={headerItems} />
        <label className={styles.subHeading}>
          {t(
            "If you require your customers to take actions outside of FourKites " +
              "in order to enable tracking, please enable this feature and " +
              "describe them here."
          )}
        </label>
      </div>

      {carrierCompanyDetails && (
        <div className={styles.container}>
          <OnboardingInstructions
            carrierCompanyDetails={carrierCompanyDetails}
            isLoadingCarrierCompanyDetails={isLoadingCarrierCompanyDetails}
            isEditingCarrierCompanyDetails={isEditingCarrierCompanyDetails}
          />
        </div>
      )}
    </SubPagePanel>
  );
};

export default CarrierOnboardingInstructionsPage;

const OnboardingInstructions: React.FC<{
  carrierCompanyDetails: any;
  isLoadingCarrierCompanyDetails: boolean;
  isEditingCarrierCompanyDetails: boolean;
}> = ({
  carrierCompanyDetails,
  isLoadingCarrierCompanyDetails,
  isEditingCarrierCompanyDetails,
}) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  // Carrier ID
  const carrierId: string = useAppSelector(UsersState.selectors.getCompanyId);

  /*****************************************************************************
   * STATE
   ****************************************************************************/
  const [disabled, setDisabled] = useState<boolean>(true);
  const [onboardingInstructionsEnabled, setOnboardingInstructionsEnabled] =
    useState<boolean>(false);
  const [onboardingInstructions, setOnboardingInstructions] =
    useState<string>("");

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  useEffect(() => {
    if (carrierCompanyDetails) {
      resetLocalInstructions();
    }
  }, [carrierCompanyDetails]);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const resetLocalInstructions = () => {
    const instructions = carrierCompanyDetails?.special_onboarding_instructions;

    setOnboardingInstructions(instructions?.description || "");
    setOnboardingInstructionsEnabled(instructions?.enabled);
  };

  const onEdit = () => {
    setDisabled(false);
  };

  const onCancel = () => {
    resetLocalInstructions();
    setDisabled(true);
  };

  const onSave = async () => {
    setDisabled(true);

    // If instructions are enabled, we need to have text
    if (onboardingInstructionsEnabled && !onboardingInstructions) {
      showToast(t("You need to add the instructions!"), t(""), "error");
      return;
    }

    await dispatch(
      CarrierCompanyDetailsState.actions.updateCarrierCompanyDetails({
        carrierId,
        carrierCompanyDetails: {
          special_onboarding_instructions: {
            description: onboardingInstructions ? onboardingInstructions : null,
            enabled: onboardingInstructionsEnabled,
          },
        },
      })
    );
  };

  const onChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (disabled) {
      return;
    }

    setOnboardingInstructionsEnabled(!onboardingInstructionsEnabled);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const showLoader =
    Object.keys(carrierCompanyDetails).length === 0 &&
    isLoadingCarrierCompanyDetails;

  return (
    <CompanySection
      title={t("Network Requests")}
      onEdit={onEdit}
      onSave={onSave}
      onCancel={onCancel}
      editable={!isEditingCarrierCompanyDetails}
    >
      {showLoader ? (
        <div className={styles.settings}>
          <Spinner isLoading size="medium" />
        </div>
      ) : (
        <div className={styles.settings}>
          <div>
            <Switch
              size="large"
              defaultLabel={t("Use Special Onboarding Instructions")}
              checked={onboardingInstructionsEnabled}
              disabled={disabled}
              onChange={onChange}
            />
            <label>
              {t(
                "Shippers requesting to connect with your company on FourKites " +
                  "will be shown the instructions you provide below. A " +
                  "representative of your company may still have to accept the " +
                  "sharing request, and/or add the bill-to-code for each shipper " +
                  "in FourKites Connect. "
              )}
            </label>
          </div>
          <div>
            <textarea
              id="asset-ids"
              placeholder={t("Enter special onboarding instructions")}
              rows={8}
              value={onboardingInstructions}
              onChange={(e: any) => setOnboardingInstructions(e.target.value)}
              disabled={disabled || !onboardingInstructionsEnabled}
            />
          </div>
        </div>
      )}
    </CompanySection>
  );
};
