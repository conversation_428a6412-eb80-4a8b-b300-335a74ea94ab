import { useEffect, useCallback, useState } from "react";

import { Spinner } from "@fourkites/elemental-loading-indicator";

import { PaginationType } from "state/BaseTypes";
import { useAppSelector, useAppDispatch } from "state/hooks";
import { ShippersNetworkState } from "state/modules/carrier/ShippersNetwork";

import CompanyManagement from "view/components/self-service/company-management/CompanyManagement";

import styles from "./CustomersNetworkPage.module.scss";

export const Customers = ({ mode, carrierId, managedCompanyType }: any) => {
  /*****************************************************************************
   * REDUX
   ****************************************************************************/

  const dispatch = useAppDispatch();

  // Carriers Data
  const shipperSelectors = ShippersNetworkState.selectors;

  // Pagination
  const pagination = useAppSelector(shipperSelectors.paginationByMode(mode));
  const { totalItems, totalPages } = pagination;

  const customers = useAppSelector(shipperSelectors.shippersByMode(mode));
  const isGettingCustomers = useAppSelector(
    shipperSelectors.isLoadingShippersByMode(mode)
  );
  const isEditingCustomers = useAppSelector(
    shipperSelectors.isEditingShippersByMode(mode)
  );

  // Sort Id Mappings
  const sortIdMapping: any = {
    identification: "target_company_name",
    services_provided: "services_provided",
    last_request_at: "last_request_at",
    action: "action",
    load_volume: "load_volume",
    my_assigned_percentage: "assignment_percentage",
    my_tracked_percentage: "tracking_percentage",
    connectivity_status: "onboarding_status",
  };

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [lastPageParams, setLastPageParams] = useState<any>({
    index: -1,
    size: -1,
  });

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  /*
   * Fetches customers when opening the component. The first fecth is done using
   * the effect to avoid table infinite loop when there's no data
   */
  useEffect(() => {
    if (!carrierId) {
      return;
    }

    dispatch(ShippersNetworkState.actions.getShippers({ carrierId, mode }));

    // eslint-disable-next-line
  }, [carrierId]);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  /*
   * Fetchs assets for fleet
   */
  const fetchShippers = useCallback(
    async ({ pageSize, pageIndex, sortBy }: PaginationType) => {
      // Don't fetch if we don't have needed parameters
      if (!carrierId) {
        return;
      }

      // Don't fetch if it's the same page
      if (
        customers.length === 0 &&
        pageIndex === lastPageParams.index &&
        pageSize === lastPageParams.size
      ) {
        return;
      }

      const sortId = sortBy[0]?.id;
      const isDesc = sortBy[0]?.desc;

      const sortByParam = {
        columnName: sortIdMapping[sortId],
        isDesc,
      };

      await dispatch(
        ShippersNetworkState.actions.getShippers({
          carrierId,
          mode,
          currentPage: pageIndex + 1,
          pageSize: pageSize,
          sortBy: sortByParam,
        })
      );

      setLastPageParams({ index: pageIndex, size: pageSize });
    },
    [customers, lastPageParams, setLastPageParams]
  );

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const isLoadingShippers =
    isGettingCustomers && !isEditingCustomers && !customers.length;

  if (isLoadingShippers) {
    <div className={styles.loading}>
      <Spinner isLoading size="medium" />
    </div>;
  }

  return (
    <CompanyManagement
      mode={mode}
      managerCompanyId={carrierId}
      managedCompanyType={managedCompanyType}
      companiesData={customers}
      fetchDataForPage={fetchShippers}
      pageCount={totalPages}
      totalEntries={totalItems}
    />
  );
};

export const FtlCustomers = ({ carrierId, managedCompanyType }: any) => (
  <Customers
    mode="ftl"
    carrierId={carrierId}
    managedCompanyType={managedCompanyType}
  />
);

export const LtlCustomers = ({ carrierId, managedCompanyType }: any) => (
  <Customers
    mode="ltl"
    carrierId={carrierId}
    managedCompanyType={managedCompanyType}
  />
);

export const ParcelCustomers = ({ carrierId, managedCompanyType }: any) => (
  <Customers
    mode="parcel"
    carrierId={carrierId}
    managedCompanyType={managedCompanyType}
  />
);

export const OceanCustomers = ({ carrierId, managedCompanyType }: any) => (
  <Customers
    mode="ocean"
    carrierId={carrierId}
    managedCompanyType={managedCompanyType}
  />
);
