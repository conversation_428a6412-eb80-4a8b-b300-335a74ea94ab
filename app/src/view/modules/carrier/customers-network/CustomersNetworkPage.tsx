import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import {
  BoxOpenedIcon,
  ShipIcon,
  Button,
  TruckIcon,
  UploadIcon,
  TruckLoadedIcon,
} from "@fourkites/elemental-atoms";

import { LoadsTrackingMode } from "state/BaseTypes";
import { useAppDispatch, useAppSelector } from "state/hooks";
import { UsersState } from "state/modules/Users";
import { showToast } from "view/components/base/toast/Toast";
import { ShippersNetworkDetailsState } from "state/modules/carrier/ShippersNetworkDetails";

import TabbedContainer from "view/components/base/tabbed-container/TabbedContainer";

import {
  FtlCustomers,
  LtlCustomers,
  OceanCustomers,
  ParcelCustomers,
} from "./CustomersComponents";

import styles from "./CustomersNetworkPage.module.scss";

const CustomersNetworkPage: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  const [mode, setMode] = useState<LoadsTrackingMode>("ftl");

  //  ID
  // For this context, the company is a carrier
  const carrierId: string = useAppSelector(UsersState.selectors.getCompanyId);
  // The company type that is being managed
  const managedCompanyType = "shipper";
  const managerCompanyId = carrierId;
  const [isExportDisabled, setIsExportDisabled] = useState<boolean>(false);

  
    useEffect(() => {
        if (carrierId) {
          getExportInfo();
        }
      }, [carrierId,isExportDisabled]);
    
  const getExportInfo = async () => {
      let response = await dispatch(
        ShippersNetworkDetailsState.actions.getExportInfo({
          carrierId: managerCompanyId,
        })
      );
  
      const isDisabled =
        response.payload === "multiple_export_within_threshold_time";
      setIsExportDisabled(isDisabled);
    };


    const exportShipperList = async () => {
        const response = await dispatch(
          ShippersNetworkDetailsState.actions.exportShipperList({
            carrierId: managerCompanyId,
          })
        );
        if ("error" in response) {
          showToast(
            t("Error"),
            t("Couldn't initiate export for carriers list."),
            "error"
          );
          return;
        }
        setIsExportDisabled(true);
        showToast(
          t("Export customers list"),
          t("You will get the results in your email shortly."),
          "ok"
        );
      };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const tabs = [
    {
      title: "FTL",
      id: "ftl",
      icon: <TruckIcon />,
      tabComponent: (
        <FtlCustomers
          carrierId={carrierId}
          managedCompanyType={managedCompanyType}
        />
      ),
    },
    {
      title: "LTL",
      id: "ltl",
      icon: <TruckLoadedIcon />,
      tabComponent: (
        <LtlCustomers
          carrierId={carrierId}
          managedCompanyType={managedCompanyType}
        />
      ),
    },
    {
      title: "Parcel",
      id: "parcel",
      icon: <BoxOpenedIcon />,
      tabComponent: (
        <ParcelCustomers
          carrierId={carrierId}
          managedCompanyType={managedCompanyType}
        />
      ),
    },
    {
      title: "Ocean + Freight Forwarders",
      id: "ocean",
      icon: <ShipIcon />,
      tabComponent: (
        <OceanCustomers
          carrierId={carrierId}
          managedCompanyType={managedCompanyType}
        />
      ),
    },
  ];

  return (
    <div className={styles.pageContainer}>
      <div className={styles.container}>
        <div className={styles.content}>

        <div className={styles.exportButton}>
            <Button
              theme="secondary"
              size={"medium"}
              onClick={exportShipperList}
              disabled={isExportDisabled}
              data-testid="export-carrier-list-button"
            >
              <UploadIcon fill="#000" iconClass={"button-icon-left"} />
              {t("Export Customers List")}
            </Button>
          </div>
          <TabbedContainer
            tabs={tabs}
            selectedTab={mode}
            onSelectTab={(tabId: LoadsTrackingMode) => setMode(tabId)}
          />
        </div>
      </div>
    </div>
  );
};

export default CustomersNetworkPage;
