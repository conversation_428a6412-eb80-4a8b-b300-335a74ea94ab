@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container {
  display: flex;
  flex-direction: column;
  background-color: $color-neutral-50;
  width: 84%;
  padding-left: 8%;
  padding-right: 8%;
  height: calc(100vh + -113px);
  overflow-y:scroll;
}

.titleContainer {
  display: block;
  padding-top: 36px;
  padding-bottom: 36px;
}

.title {
  color: $color-neutral-700;
  font-size: 20px;
  font-weight: 600;
  letter-spacing: 0;
  line-height: 24px;
}

.content {
  display:flex;
  align-content: center;
  justify-content: center;

  > div  {
    display: flex;
    margin-right: 24px;
    width: 100%;

    &:last-child {
      margin-right: 0;
    }
  }
}

.contentColumn {
  display:flex;
  flex: 1;
  height: fit-content;
  padding-top: 24px;

  > div  {
    display: flex;
    width: 100%;
    max-width: 490px;

    &:last-child {
      margin-right: 0;
    }
  }
}

.contentColumnBig {
  composes: contentColumn;

  > div {
    max-width: none;
  }
}

.cardContent {
  display: flex;
  flex-direction: column;
}

.divider {
  display: block;
  height: 1px;
  width: 100%;
  background-color: $color-neutral-300;
}

.carrierIndicator {
  display: flex;
  align-content: center;
  align-items: center;

  > label {
    margin-left: 8px;
  }
}

.connectionErrors {
  display: flex;
  flex-direction: column;
  padding: 24px;
}




.trackingCard {
  background-color: $color-primary-500 !important;
  color: white;

  > div {
    display:flex;
    flex-direction: column;
    align-content: center;
    padding: 32px 24px;

    > h4 {
      margin: 0;
      font-size: 28px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 42px;
      margin-bottom: 16px;
    }

    > label {
      font-size: 20px;
      letter-spacing: 0;
      line-height: 30px;
    }

    > div[id=footer] {
      margin-top: 56px;
      display: flex;
    }
  }
}


.loader {
  display: flex;
  align-items: left;
  align-content: center;
  justify-content: center;
  margin-top: 32px;
  margin-bottom: 32px;
}
