import React, { useState } from "react";

import { useAppSelector } from "state/hooks";
import UsersState from "state/modules/Users";

//import BreadcrumbsHeader from "view/components/base/breadcrumbs-header/BreadcrumbsHeader";

import CarrierLinkModal from "view/components/self-service/location-data-integrations/mobile/CarrierLinkModal";

import CarrierOverview from "view/components/self-service/overview/carrier/CarrierOverview";
import ShipperOverview from "view/components/self-service/overview/shipper/ShipperOverview";
//import InviteUsers from "view/components/self-service/overview/invite-users/InviteUsers";

import styles from "./CarrierOverviewPage.module.scss";

const CarrierOverviewPage: React.FC = () => {
  /*****************************************************************************
   * REDUX
   ****************************************************************************/

  const companyType = useAppSelector(UsersState.selectors.getCompanyType);

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [showModal, setShowModal] = useState<boolean>(false);

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const isBroker = companyType?.includes("broker");
  const isShipper = companyType?.includes("shipper");

  return (
    <div className={styles.container}>
      {(isBroker || isShipper) && <ShipperOverview />}

      <CarrierOverview />

      {/*
      <div className={styles.titleContainer}>
        <BreadcrumbsHeader titles={[t("Organization")]} />
      </div>
      <div className={styles.content}>
        <InviteUsers />

        <div />
      </div>
      */}

      <CarrierLinkModal show={showModal} onClose={() => setShowModal(false)} />
    </div>
  );
};

export default CarrierOverviewPage;
