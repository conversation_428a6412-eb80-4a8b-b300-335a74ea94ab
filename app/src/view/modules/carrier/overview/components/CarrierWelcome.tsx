import React from "react";
import { useTranslation } from "react-i18next";

import styles from "./CarrierWelcome.module.scss";

import Card from "view/components/base/card/Card";

const CarrierWelcome: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className={styles.pageContainer}>
      <h5 className={styles.contentTitle}>
        {t("Welcome to FourKites LTL Carrier Module!")}
      </h5>
      <Card customClass={styles.welcomeCard}>ASDF</Card>
    </div>
  );
};

export default CarrierWelcome;
