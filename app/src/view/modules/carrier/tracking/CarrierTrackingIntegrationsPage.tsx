import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { useHistory } from "react-router-dom";
import { useAppSelector } from "state/hooks";
import UsersState from "state/modules/Users";

import {
  HierarchicalSidebar,
  SidebarSection,
} from "@fourkites/elemental-hierarchical-sidebar";

import CarrierLinkModal from "view/components/self-service/location-data-integrations/mobile/CarrierLinkModal";

import styles from "./CarrierTrackingIntegrationsPage.module.scss";

import CarrierTrackingIntegrationsRouter, {
  carrierTrackingIntegrationsRoutes,
  getCarrierTrackingIntegrationsRouteId,
} from "router/carrier/CarrierTrackingIntegrationsRouter";

const CarrierTrackingIntegrationsPage: React.FC = () => {
  const { t } = useTranslation();
  const history = useHistory();

  const isSuperAdmin = useAppSelector(UsersState.selectors.getIsSuperAdmin);
  const isCompanyAdmin = useAppSelector(UsersState.selectors.getIsCompanyAdmin);

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [showModal, setShowModal] = useState<boolean>(false);
  const [collapsed, setCollapsed] = useState<boolean>(false);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const onChangeCollapse = (collapsed: boolean) => {
    setCollapsed(collapsed);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const sidebarSections = [];

  if(isSuperAdmin || isCompanyAdmin) {
    sidebarSections.push({
      title: t("Tracking Integrations").toUpperCase(),
      links: [
        {
          title: t("ELD / GPS"),
          id: carrierTrackingIntegrationsRoutes.eldGps,
          to: carrierTrackingIntegrationsRoutes.eldGps,
        },
        {
          title: t("Load Update Logs (Beta)"),
          id: carrierTrackingIntegrationsRoutes.dispatcherLogs,
          to: carrierTrackingIntegrationsRoutes.dispatcherLogs,
        }
        /*
        {
          title: t("EDI / Location files"),
          id: carrierTrackingIntegrationsRoutes.ediLocation,
          to: carrierTrackingIntegrationsRoutes.ediLocation,
        },*/
      ],
    });
  }

  return (
    <div
      className={styles.pageContainer}
      data-test-id="tracking-integrations-page"
    >
      <div className={styles.container}>
        <div>
          <HierarchicalSidebar
            collapsed={collapsed}
            onChangeCollapse={onChangeCollapse}
          >
            {!collapsed && (
              <div>
                {sidebarSections &&
                  sidebarSections.map((section, index) => {
                    return (
                      <SidebarSection
                        key={index}
                        title={section.title}
                        links={section.links}
                        defaultActiveLinkId={
                          getCarrierTrackingIntegrationsRouteId(history) || ""
                        }
                      />
                    );
                  })}
              </div>
            )}
          </HierarchicalSidebar>
        </div>

        <div className={styles.content}>
          <CarrierTrackingIntegrationsRouter />
        </div>
      </div>

      <CarrierLinkModal show={showModal} onClose={() => setShowModal(false)} />
    </div>
  );
};

export default CarrierTrackingIntegrationsPage;
