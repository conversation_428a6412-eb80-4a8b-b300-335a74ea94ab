import React, { useEffect, useState, useCallback } from "react";
import { Spinner } from "@fourkites/elemental-loading-indicator";
import { Button, RotateCwIcon, InfoIcon } from "@fourkites/elemental-atoms";
import { useTranslation } from "react-i18next";
import SubPagePanel from "view/components/base/containers/SubPagePanel";
import BreadcrumbsHeader from "view/components/base/breadcrumbs-header/BreadcrumbsHeader";
import LogsTable from "./DispatcherTable";
import { useAppSelector, useAppDispatch } from "state/hooks";
import { UsersState } from "state/modules/Users";
import { Input } from "@fourkites/elemental-input";
import styles from "./DispatcherLogsPage.module.scss";
import { DispatcherLogsState } from "state/modules/carrier/dispatcherLogsData";
import { PaginationType } from "state/BaseTypes";
import { convertDateToUtcDate } from "view/components/base/DateUtils";
import { DateTime } from "luxon";
import { TimeframeFilter } from "./TimeframeFilter";

export interface Log {
  handleFetchData: Function;
  totalHits?: number;
  hits: {
    _source: {
      billToCode?: string;
      identifierKeys?: {
        identifier: string;
      }[];
      billOfLading: string;
      extra_info: {
        statusCode?: string;
        Timestamp: string;
        carrierPermalink: string;
      };
      loadIdentifier: string;
      sender: string;
      shipper: string;
      sqs_publisher: string;
      timestamp: number;
    };
  }[];
}

const getInitialDates = () => {
  const DEFAULT_HOUR_LIMIT = 2;
  const DATE_TIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
  let startDate = new Date();
  startDate.setHours(startDate.getHours() - DEFAULT_HOUR_LIMIT);
  const startDateInUtc = convertDateToUtcDate(startDate);
  const endDate = new Date();
  const endDateInUtc = convertDateToUtcDate(endDate);
  return {
    DATE_TIME_FORMAT,
    startDateInUtc,
    endDateInUtc,
  };
};

const getMinMaxDefaultDates = () => {
  const MAX_RANGE_ALLOWED = 15;
  let minStartDate = new Date();
  minStartDate.setDate(minStartDate.getDate() - MAX_RANGE_ALLOWED);
  minStartDate.setHours(0, 0, 0, 0);
  let maxEndDate = new Date();
  maxEndDate.setHours(23, 59, 59, 999);
  return { minStartDate, maxEndDate };
};

const {
  DATE_TIME_FORMAT,
  startDateInUtc,
  endDateInUtc,
} = getInitialDates();



const DispatcherApiLogsPage: React.FC = () => {
  const { t } = useTranslation();
  const breadcrumbTitles = [t("Load Update Logs (Beta)")];


/*****************************************************************************
 * STATES
 ****************************************************************************/
const [processedTimeframe, setProcessedTimeframe] = useState<any>({
  start: { type: "calendar", dateTime: startDateInUtc },
  end: { type: "calendar", dateTime: endDateInUtc },
});
const [selectedRowId, setSelectedRowId] = useState<number | null>(null);
const [logs, setLogs] = useState({ totalHits: 0, hits: [] });
const [carrierId] = useState(
  useAppSelector(UsersState.selectors.getCompanyId)
);
const [loadIdentifier, setLoadIdentifier] = useState("");
const [shipper, setShipper] = useState("");

/*****************************************************************************
 * APP selectors
 ****************************************************************************/
const pagination = useAppSelector(
  DispatcherLogsState.selectors.paginationDetails()
);
const isRetrieving = useAppSelector(
  DispatcherLogsState.selectors.isRetrievingDispatcherLogs()
);

const DEFAULT_PAGINATION_PARAMETERS = {
  pageSize: pagination?.pageSize || 10,
  pageIndex: 0,
};
const dispatch = useAppDispatch();
  useEffect(() => {
    const fetchData = async () => {
      await fetchInitialData(DEFAULT_PAGINATION_PARAMETERS);
    };
    fetchData();
  }, []);

  const fetchInitialData = useCallback(
    async ({ pageSize, pageIndex }: PaginationType) => {
      await fetchDispatcherLogs(pageSize, pageIndex);
    },
    [carrierId, loadIdentifier, shipper, processedTimeframe]
  );

  const fetchDispatcherLogsData = useCallback(
    async ({ pageSize, pageIndex }: PaginationType) => {
      await fetchDispatcherLogs(pageSize, pageIndex);
    },
    [carrierId, loadIdentifier, shipper, processedTimeframe]
  );

  const fetchDispatcherLogs = async (pageSize: number, pageIndex: number) => {
    const processedFrom = DateTime.fromJSDate(
      processedTimeframe?.start?.dateTime
    ).toFormat(DATE_TIME_FORMAT);

    const processedTo = DateTime.fromJSDate(
      processedTimeframe?.end?.dateTime
    ).toFormat(DATE_TIME_FORMAT);
    try {
      setSelectedRowId(null);
      const data = await dispatch(
        DispatcherLogsState.actions.retrieveDispatcherLogs({
          fromTimestamp: processedFrom,
          toTimestamp: processedTo,
          carrierId,
          loadIdentifier,
          shipper,
          pageSize,
          currentPage: pageIndex + 1,
        })
      );
      setLogs(data.payload);
    } catch (error) {
      // Handle error
      console.error("Error fetching data:", error);
    }
  };

  const dispatcherFilters = (
    <>
      <div className={styles.filter}>
        <Input
          label={t("Shipper ID")}
          type="text"
          value={shipper}
          onChange={(e) => setShipper(e.target.value)}
        />
      </div>
      <div className={styles.filter}>
        <Input
          label={t("Load Identifier")}
          type="text"
          value={loadIdentifier}
          onChange={(e) => setLoadIdentifier(e.target.value)}
        />
      </div>
    </>
  );

  const fetchDataButton = (
    <div className={styles.filterButton}>
      <Button
        size={"large"}
        variant="outline"
        onClick={() =>
          fetchDispatcherLogsData(DEFAULT_PAGINATION_PARAMETERS).then(() => {})
        }
      >
        <RotateCwIcon fill="#0E65E5" iconClass={styles.buttonIcon} />
        {t("Fetch Data")}
      </Button>
    </div>
  );

  const itemContent = (
    <div className={styles.logsContaianer}>
      <LogsTable
        selectedRowId={selectedRowId}
        setSelectedRowId={setSelectedRowId}
        fetchDispatcherLogsData={fetchDispatcherLogsData}
        hits={logs?.hits || []}
      />
      {isRetrieving && (
        <div className={styles.loading}>
          <Spinner isLoading size="medium" />
        </div>
      )}
    </div>
  );

  return (
    <div
      className={styles.containerHeader}
      data-test-id="dispatchers-logs-errors-page-container"
    >
      <SubPagePanel>
        <div className={styles.headerWrapper}>
          <BreadcrumbsHeader titles={breadcrumbTitles} children={[]} />
          <label className={`${styles.subHeading} multi-line-label`}>
            {t(
              "View your load updates and API logs\nSome file integration data may appear here in addition to logs from the Fourkites Dispatcher API"
            )}
          </label>
        </div>
        <div className={styles.filtersWrapper}>
          <div className={styles.container}>
            <div className={styles.content}>
              {dispatcherFilters}
              <TimeframeFilter
                processedTimeframe={processedTimeframe}
                setProcessedTimeframe={setProcessedTimeframe}
                getMinMaxDefaultDates={getMinMaxDefaultDates}
              />
              {fetchDataButton}
            </div>
          </div>
        </div>
        {itemContent}
      </SubPagePanel>
    </div>
  );
};

export default DispatcherApiLogsPage;
