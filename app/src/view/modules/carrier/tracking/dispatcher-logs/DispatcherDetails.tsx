import { useTranslation } from "react-i18next";
import copyT<PERSON><PERSON>lipboard from "copy-to-clipboard";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "react-json-pretty";

import {
  CopyIcon,
  XCircleInvertedColoredIcon,
  XIcon,
} from "@fourkites/elemental-atoms";

import { formatDate } from "view/components/base/DateUtils";
import { showToast } from "view/components/base/toast/Toast";
import NewStatusTag from "view/components/base/new-status-indicators/NewStatusTag";

import styles from "./DispatcherDetails.module.scss";

const DispatcherDetails = ({
  dispatcherId,
  dispatcherData,
  onClosePanel,
  fetchDispatcherStatusDetails,
}: any) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * RENDER
   ****************************************************************************/
  // hardcoded this to 200, since we are not storing any logs or errors currently.
  // in the next cycle of development, we are planning to capture logs and errors, store it and display it here.
  const dispatcherStatusDetail = fetchDispatcherStatusDetails(
    JSON.parse("200")
  );

  const iSuccessStatusCode =
    200 ||
    (dispatcherData?._source?.statusCode >= 200 &&
      dispatcherData?._source?.statusCode <= 299);

  return (
    <div key={dispatcherId} className={styles.container}>
      <div
        data-test-id="dispatcher-details-wrapper"
        key={dispatcherId}
        className={styles.wrapper}
      >
        <DispatcherDetailsHeader
          dispatcherData={dispatcherData}
          onClosePanel={onClosePanel}
        />

        <DispatcherDestinationUrl dispatcherData={dispatcherData} />

        {!iSuccessStatusCode && (
          <DispatcherErrorReason dispatcherData={dispatcherData} />
        )}

        <DispatcherJsonPayload
          dispatcherData={dispatcherData}
          dispatcherStatusDetail={dispatcherStatusDetail}
        />
      </div>
    </div>
  );
};

// DispatcherDetailsHeader
const DispatcherDetailsHeader = ({ dispatcherData, onClosePanel }: any) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/
  /*
   * Get dispatcher response
   */
  const getDispatcherResponse = (statusCode: any) => {
    const iSuccessStatusCodes = statusCode >= 200 && statusCode <= 299;
    const validationErrorStatus = statusCode === 0;
    const dispatcherResponseMessage = iSuccessStatusCodes
      ? t("Success")
      : validationErrorStatus
      ? t("Validation Error")
      : t("Error");
    return dispatcherResponseMessage;
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const identifiers = [
    dispatcherData?._source?.billOfLading,
    dispatcherData?._source?.loadIdentifier,
    dispatcherData?._source?.identifierKeys
      ?.map((key: any) => key.identifier)
      .join(", "),
  ]
    .filter(Boolean)
    .join(", ");

  return (
    <div className={styles.headerContainer}>
      <div className={styles.dispatcherDetails}>
        <div className={styles.loadDetails}>
          <span>{t("Identifiers")}</span>
          <h5>{identifiers}</h5>
        </div>
        <div className={styles.detail}>
          <span className={styles.detailTitle}>{t("Received")}</span>
          <span className={styles.createdOnContent}>
            {formatDate(dispatcherData?._source?.extra_info?.Timestamp)}
          </span>
        </div>
        <div className={styles.headerRight}>
          <button className={styles.closeButtonWrapper} onClick={onClosePanel}>
            <XIcon iconClass={styles.closeIcon} />
          </button>
        </div>
      </div>
      <div className={styles.dispatcherDetails}>
        <div className={styles.detail}>
          <span className={styles.detailTitle}>{t("Response Message")}</span>
          <span className={styles.detailContent}>
            {getDispatcherResponse(JSON.parse("202"))}
          </span>
        </div>
      </div>
    </div>
  );
};

// DispatcherDestinationUrl
const DispatcherDestinationUrl = ({ dispatcherData }: any) => {
  const { t } = useTranslation();
  const identifierKeys = dispatcherData?._source?.identifierKeys;
  const appEnvironment = window?.appConfig?.app_environment;
  let destinationURL = "";

  if (appEnvironment === "prod") {
    if (identifierKeys) {
      destinationURL =
        "https://api.fourkites.com/load/update/dispatcher-api/async";
    } else {
      destinationURL =
        "https://api.fourkites.com/api/v1/tracking/dispatcher_updates";
    }
  } else if (appEnvironment === "staging") {
    if (identifierKeys) {
      destinationURL =
        "https://api-staging.fourkites.com/load/update/dispatcher-api/async";
    } else {
      destinationURL =
        "https://tracking-api-staging.fourkites.com/api/v1/tracking/dispatcher_updates";
    }
  } else {
    if (identifierKeys) {
      destinationURL =
        "http://load-update-async-api-service-qat.fourkites.internal/load/update/dispatcher-api/async";
    } else {
      destinationURL =
        "https://api-qat.fourkites.com/api/v1/tracking/dispatcher_updates";
    }
  }

  return (
    <div className={styles.dispatcherDestinationUrl}>
      <div className={styles.dispatcherDetails}>
        <div className={styles.detail}>
          <span className={styles.detailTitle}>{t("Destination URL")}</span>
          <span className={styles.detailContent}>{destinationURL}</span>
        </div>
      </div>
    </div>
  );
};

// DispatcherErrorReason
const DispatcherErrorReason = ({ dispatcherData }: any) => {
  const { t } = useTranslation();

  return (
    <div className={styles.dispatcherErrorReason}>
      <div className={styles.dispatcherDetails}>
        <div className={styles.detail}>
          <span className={styles.detailTitle}>
            <span>
              <XCircleInvertedColoredIcon
                iconClass={styles.errorReasonIcon}
                size="22px"
              />
            </span>
            {t("Error!")}
          </span>
          <span className={styles.detailContent}>{dispatcherData?.reason}</span>
        </div>
      </div>
    </div>
  );
};

// DispatcherJsonPayload
const DispatcherJsonPayload = ({
  dispatcherData,
  dispatcherStatusDetail,
}: any) => {
  const { t } = useTranslation();
  const payload = dispatcherData;
  const { variant, statusCode, icon, statusText } = dispatcherStatusDetail;

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  /*
   * On copy payload
   */
  const onCopyPayload = async () => {
    await copyToClipboard(JSON.stringify(payload, null, 2));
    showToast(t("Payload copied to clipboard."), t(""), "ok");
  };

  /*
   * Get payload details
   */
  const getPayloadDetails = (dispatcherData: any) => {
    const computedPayload = payload ? (
      <div className={styles.jsonDataWrapper}>
        <JSONPretty className={styles.jsonData} data={payload} />
      </div>
    ) : (
      "--"
    );
    return computedPayload;
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div className={styles.dispatcherDestinationUrl}>
      <div className={styles.dispatcherDetails}>
        <div className={styles.payloadDetail}>
          <span className={styles.detailTitle}>{t("Payload")}</span>
          <span className={styles.detailContent}>
            {getPayloadDetails(dispatcherData)}
          </span>
        </div>
        <div className={styles.payload}>
          <span className={styles.detailTitle}>
            <NewStatusTag
              variant={variant}
              statusCode={statusCode}
              statusIcon={icon}
              statusText={statusText}
            />
            {payload && (
              <span
                className={styles.copyPayload}
                data-testid="copy-payload"
                onClick={onCopyPayload}
              >
                <CopyIcon iconClass={"button-icon-right"} size="18px" />
              </span>
            )}
          </span>
        </div>
      </div>
    </div>
  );
};

export default DispatcherDetails;
