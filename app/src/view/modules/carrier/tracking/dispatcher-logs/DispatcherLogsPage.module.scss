@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container {
  width: 100%;
  display: flex;

  .content {
    display: flex;
    flex-direction: row;
    width: 100%;

    .dateRangeFilter {
      display: flex;
      position: relative;
      top: 1px;
      width: 370px;

      > div > div > div > div {
        height: 37px;
      }

      span {
        font-size: 13px;
      }

      .timeframeInfo {
        position: relative;
        left: -255px;
        top: 3px;
        font-size: 14px;
      }
    }

    .filter {
      display: flex;
      margin-right: 2%;
      flex-direction: column;
      width: 15%;
    }

    .filterButton {
      display: flex;
      margin-right: 0;
      flex-direction: column-reverse;

      .buttonIcon {
        margin-right: 8px;
        position: relative;
        top: 2px;
      }
    }

    .eventsFilter {
      width: 29%;
      display: flex;
      margin-right: 2%;
      flex-direction: column;
    }
  }
}

.containerHeader {
  > div > div {
    height: calc(100vh + -113px);
    padding: 0;
    background-color: white;
  }
}

.subHeading {
  font-size: 16px;
  color: $color-neutral-700;
  white-space: pre-line;
}

.loader {
  width: 100%;
  display: flex;
  align-content: center;
  justify-content: center;
  margin-top: 32px;
  margin-bottom: 32px;
}

.headerWrapper {
  padding: 27px 32px 32px 32px;
  background-color: $color-neutral-50;
}

.headerButton {
  display: flex;
  > button {
    display: flex;
    align-items: center;
    align-content: center;
    min-width: fit-content;
    margin-left: 16px;
  }

  > a {
    margin-left: 16px;
  }
}

.headerLoader {
  composes: loader;
  width: fit-content;
  margin: 0;
  margin-right: 16px;

  > label {
    margin-right: 16px;
  }
}

.filtersWrapper {
  padding: 0 32px 32px 32px;
  background-color: $color-neutral-50;
}

.loading {
  display: flex;
  width: 100%;
  margin-top: 32px;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  background-color: rgba(
    255,
    255,
    255,
    0.8
  ); /* Adjust the background color and opacity */
  align-items: center;
  z-index: 9999;
}

.emptyDataContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 10%;
  height: calc(100vh - 195px);

  img {
    object-fit: contain;
    height: 150px;
    width: 150px;
    margin-right: 16px;
  }

  h2 {
    display: flex;
    align-items: center;
    font-size: 28px;
    font-weight: 300;
    letter-spacing: 0;
    text-align: center;
    margin-bottom: 5px;
  }

  h6 {
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    text-align: center;
    color: #868e96;
    margin-top: 0;
    margin-bottom: 30px;
  }

  .createConfigurationIcon {
    position: relative;
    top: 2px;
    margin-right: 16px;
  }
}
