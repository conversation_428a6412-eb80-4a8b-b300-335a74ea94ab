import { useMemo } from "react";
import { Table } from "@fourkites/elemental-table";
import { AlertTriangleIcon, CheckIcon } from "@fourkites/elemental-atoms";
import TableWithSidePanel from "view/components/base/containers/TableWithSidePanel";
import DispatcherDetails from "./DispatcherDetails";
import { useTranslation } from "react-i18next";
import NewStatusTag from "view/components/base/new-status-indicators/NewStatusTag";
import styles from "./DispatcherTableCells.module.scss";
import { useAppSelector } from "state/hooks";
import { DispatcherLogsState } from "state/modules/carrier/dispatcherLogsData";
import passwordError from "assets/img/passwordError.png";
import { formatDate } from "view/components/base/DateUtils";

export const StatusCell = ({ code, fetchDispatcherStatusDetails }: any) => {
  const { t } = useTranslation();

  const dispatcherStatusDetail = fetchDispatcherStatusDetails(code);
  const { variant, statusCode, icon, dispatcherStatusTooltip, statusText } =
    dispatcherStatusDetail;

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div className={styles.dispatcherStatusWrapper}>
      <div className={styles.dispatcherStatus}>
        <NewStatusTag
          variant={variant}
          statusCode={statusCode}
          statusIcon={icon}
          tooltipText={dispatcherStatusTooltip}
          statusText={statusText}
        />
      </div>
    </div>
  );
};

const EmptyDispatcherLogs = () => {
  const { t } = useTranslation();

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div className={styles.container}>
      <img className={styles.emptyStateLogo} src={passwordError} />
      <h2>{t("No data found!")}</h2>
      <h6>{t("Update your filters or timeframe to view results")}</h6>
    </div>
  );
};

const LogsTable = ({
  selectedRowId,
  setSelectedRowId,
  hits,
  fetchDispatcherLogsData,
}: any) => {
  const handleViewDetails = (rowId: number) => {
    setSelectedRowId(selectedRowId === rowId ? null : rowId);
  };

  const { t } = useTranslation();

  // Pagination
  const pagination = useAppSelector(
    DispatcherLogsState.selectors.paginationDetails()
  );
  const { totalItems, totalPages } = pagination;

  const columns = useMemo(
    () => [
      {
        Header: t("Shipper ID"),
        accessor: "_source.shipper",
        disableSortBy: true,
        Cell: ({ row }: { row: any }) => {
          const shipperId =
            row.original._source.shipper || row.original._source.billToCode;
          return shipperId ? shipperId : "";
        },
      },
      {
        Header: t("Load Identifiers"),
        disableSortBy: true,
        accessor: (row: any) => {
          const identifiers = [
            row._source.billOfLading,
            row._source.loadIdentifier,
            row._source.identifierKeys
              ?.map((key: any) => key.identifier)
              .join(", "),
          ]
            .filter(Boolean)
            .join(", ");

          return identifiers ? identifiers : "";
        },
      },
      {
        Header: t("Received"),
        accessor: (row: any) => formatDate(row._source.extra_info.Timestamp),
        disableSortBy: true,
      },
      // hardcoded status to 202, since we are not storing any logs or errors currently.
      // in the next cycle of development, we are planning to capture logs and errors, store it and display it here.
      {
        id: "status",
        Header: t("Status"),
        accessor: "statusCode",
        disableSortBy: true,
        Cell: (cellProps: any) => {
          const statusCode = 202;
          return (
            <StatusCell
              code={statusCode}
              fetchDispatcherStatusDetails={fetchDispatcherStatusDetails}
            />
          );
        },
      },
      {
        Header: t("Payload"),
        Cell: ({ row }: { row: any }) => (
          <a className="container" onClick={() => handleViewDetails(row.id)}>
            View
          </a>
        ),
      },
    ],
    []
  );

  const data = useMemo(
    () => hits.map((log: any, index: number) => ({ ...log, id: index })),
    [hits]
  );

  const onClosePanel = () => {
    setSelectedRowId(null);
  };

  /*
   * Fetch dispatcher status text
   */
  const getStatusText = (
    statusCode: any,
    successStatusCodes: boolean,
    validationErrorStatus: boolean
  ) => {
    if (statusCode == "202") {
      return t("Accepted");
    }
    if (successStatusCodes) {
      return t("Success");
    }

    if (validationErrorStatus) {
      return t("Validation Error");
    } else {
      return statusCodesMapping[statusCode] || t("Bad Request");
    }
  };

  // Status code mapping
  const statusCodesMapping: any = {
    404: t("Resource not found"),
    401: t("Unauthorised"),
    500: t("Internal Server error"),
    400: t("Bad Request"),
    410: t("Resource not found"),
    429: t("Too many requests"),
    503: t("Service Unavailable"),
  };

  const fetchDispatcherStatusDetails = (statusCode: any) => {
    statusCode = 202;
    const successStatusCodes = statusCode >= 200 && statusCode <= 299;
    const validationErrorStatus = statusCode === 0;
    const variant = successStatusCodes
      ? t("ok")
      : validationErrorStatus
      ? t("alert")
      : t("error");
    const icon = successStatusCodes ? <CheckIcon /> : <AlertTriangleIcon />;
    const dispatcherStatusTooltip = successStatusCodes
      ? t("Message fits required payload format")
      : t("Error");
    const statusText = getStatusText(
      statusCode,
      successStatusCodes,
      validationErrorStatus
    );

    return { variant, statusCode, icon, dispatcherStatusTooltip, statusText };
  };
  const sidePanel =
    selectedRowId !== null ? (
      <DispatcherDetails
        dispatcherId={selectedRowId}
        dispatcherData={hits[selectedRowId]}
        onClosePanel={onClosePanel}
        fetchDispatcherStatusDetails={fetchDispatcherStatusDetails}
      />
    ) : null;

  const paginationParams = {
    defaultPageSize: 100,
    pageCount: totalPages,
    fetchDataForPage: fetchDispatcherLogsData,
    totalEntries: totalItems,
    enableManualSort: false,
    striped: true,
    paginated: true,
  };

  return (
    <div className={styles.dispatcherContent}>
      {data.length ? (
        <TableWithSidePanel
          table={
            <div className={styles.tableContainer}>
              <Table
                variant="flat-bordered"
                data={data}
                columns={columns}
                onRowClicked={(rowInfo: any) =>
                  handleViewDetails(rowInfo?.row?.id)
                }
                preselectedRowIds={selectedRowId != null ? [selectedRowId] : []}
                pagination={paginationParams}
              />
            </div>
          }
          sidePanel={sidePanel}
        />
      ) : (
        <EmptyDispatcherLogs />
      )}
    </div>
  );
};

export default LogsTable;
