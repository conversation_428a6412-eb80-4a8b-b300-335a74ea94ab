import { DateRangePicker } from "@fourkites/elemental-datepicker";
import { Tooltip } from "@fourkites/elemental-tooltip";
import { InfoIcon } from "@fourkites/elemental-atoms";
import { useTranslation } from "react-i18next";
import { Modal } from "@fourkites/elemental-modal";
import { useState } from "react";

import styles from "./DispatcherLogsPage.module.scss";

export const TimeframeFilter = ({
  processedTimeframe,
  setProcessedTimeframe,
  getMinMaxDefaultDates,
}: any) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * STATE
   ****************************************************************************/
  const [showInvalidTimeframeAlertModal, setShowInvalidTimeframeAlertModal] =
    useState<boolean>(false);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  /*
   * On date range changed
   */
  const onTimeframeChanged = (range: any) => {
    const isInvalidRangeSelected = checkIsInvalidTimeframe(range);

    if (isInvalidRangeSelected) {
      setShowInvalidTimeframeAlertModal(true);
      return;
    }

    setProcessedTimeframe(range);
  };

  /*
   * Validate date range selected
   */
  const checkIsInvalidTimeframe = (range: any) => {
    const startDate = range?.start?.dateTime;
    const endDate = range?.end?.dateTime;
    const MAX_DAYS_ALLOWED = 15;
    let isTimeframeInvalid = false;

    if (!startDate || !endDate) {
      isTimeframeInvalid = true;
      return isTimeframeInvalid;
    }

    let selectedStartDateString: any = new Date(startDate).toDateString();
    selectedStartDateString = new Date(selectedStartDateString);
    let selectedEndDateString: any = new Date(endDate).toDateString();
    let maxRange = new Date(endDate);
    maxRange.setDate(maxRange.getDate() - MAX_DAYS_ALLOWED);
    maxRange.setHours(0, 0, 0, 0);
    selectedEndDateString = new Date(selectedEndDateString);

    if (
      selectedStartDateString < MIN_START_DATE ||
      selectedStartDateString > MAX_END_DATE ||
      selectedEndDateString < MIN_START_DATE ||
      selectedEndDateString > MAX_END_DATE ||
      selectedStartDateString < maxRange
    ) {
      isTimeframeInvalid = true;
      return isTimeframeInvalid;
    }

    return isTimeframeInvalid;
  };

  /*
   * Close timeframe alert
   */
  const closeTimeframeAlert = () => {
    setShowInvalidTimeframeAlertModal(false);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const { minStartDate, maxEndDate } = getMinMaxDefaultDates();
  const MIN_START_DATE = minStartDate,
    MAX_END_DATE = maxEndDate;

  return (
    <>
      <div className={styles.dateRangeFilter}>
        <DateRangePicker
          label={"Timeframe"}
          relativeRangeButtons={[]}
          cancelButtonLabel={"Cancel"}
          saveButtonLabel={"Apply"}
          currentRange={processedTimeframe}
          onRangeChange={(range: any) => onTimeframeChanged(range)}
          timeEnabled={true}
          singleDayRange={false}
          dateFormat={"yyyy-LL-dd"}
          timeFormat={"HH:mm"}
          calendarPosition={{ top: "250px", left: "420px" }}
          calendarZIndex={1000}
          className={"timeframe"}
        />
        <Tooltip
          contentComponent={
            <div>
              {t("Load Update Logs (Beta) support data for last 15 days only")}
              <p>
                {t("All Timestamps shown below are converted to UTC timezone.")}
              </p>
            </div>
          }
        >
          <span className={styles.timeframeInfo}>
            <InfoIcon fill="#0e65e5" iconClass={"button-icon-left"} />
          </span>
        </Tooltip>
      </div>
      <InvalidTimeframeAlertModal
        showInvalidTimeframeAlertModal={showInvalidTimeframeAlertModal}
        closeTimeframeAlert={closeTimeframeAlert}
      />
    </>
  );
};

// InvalidTimeframeAlertModal
const InvalidTimeframeAlertModal = ({
  showInvalidTimeframeAlertModal,
  closeTimeframeAlert,
}: any) => {
  const { t } = useTranslation();

  return (
    <Modal
      size="small"
      title={t("Timeframe not supported")}
      show={showInvalidTimeframeAlertModal}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: closeTimeframeAlert,
      }}
      saveButtonProps={{
        label: t("Ok"),
        onClick: closeTimeframeAlert,
      }}
    >
      <div
        className={styles.modalContainer}
        data-test-id="webhooks-timeframe-alert"
      >
        <label>
          <Tooltip
            text={t(
              "This console  supports logs / errors only for last 15 days"
            )}
          >
            <span className={styles.infoMessage}>
              <InfoIcon fill="#0e65e5" iconClass={"button-icon-left"} />
            </span>
          </Tooltip>
          {t("Load Update Logs (Beta) support  data for last 15 days only")}
        </label>
      </div>
    </Modal>
  );
};
