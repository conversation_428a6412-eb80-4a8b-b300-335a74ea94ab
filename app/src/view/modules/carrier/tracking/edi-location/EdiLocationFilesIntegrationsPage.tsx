import React from "react";
import { useTranslation } from "react-i18next";

import SubPagePanel from "view/components/base/containers/SubPagePanel";
import BreadcrumbsHeader from "view/components/base/breadcrumbs-header/BreadcrumbsHeader";
import IntegrationCard from "view/components/base/integration-card/IntegrationCard";

import styles from "./EdiLocationFilesIntegrationsPage.module.scss";

const EdiLocationFilesIntegrationsPage = (props: any) => {
  const { t } = useTranslation();

  const breadcrumbTitles = [t("EDI / Location Files Integrations")];

  return (
    <SubPagePanel>
      <BreadcrumbsHeader titles={breadcrumbTitles} />

      <div className={styles.content}>
        <IntegrationCard />
      </div>
    </SubPagePanel>
  );
};

export default EdiLocationFilesIntegrationsPage;
