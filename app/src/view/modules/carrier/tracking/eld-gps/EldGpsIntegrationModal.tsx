import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { Button, UploadIcon, XIcon } from "@fourkites/elemental-atoms";
import { Modal } from "@fourkites/elemental-modal";
import { Spinner } from "@fourkites/elemental-loading-indicator";

import { useAppSelector, useAppDispatch } from "state/hooks";
import { EldGpsIntegrationsState } from "state/modules/carrier/EldGpsIntegrations";
import { UsersState } from "state/modules/Users";

import WizardFooter from "view/components/self-service/wizard/footer/WizardFooter";
import EldGpsIntegrationAddition from "view/components/self-service/location-data-integrations/eld-gps/EldGpsIntegrationAddition";
import ProviderCredentials from "view/components/self-service/location-data-integrations/eld-gps/credentials/ProviderCredentials";

import styles from "./EldGpsIntegrationModal.module.scss";

const EldGpsIntegrationModal = ({
  isCreating,
  integrationId,
  show,
  onClose,
  onComplete,
  onProviderNotFound,
}: any) => {
  const { t } = useTranslation();

  const dispatch = useAppDispatch();

  /*****************************************************************************
   * REDUX
   ****************************************************************************/

  const carrierId: string = useAppSelector(UsersState.selectors.getCompanyId);
  const integrationDetails = useAppSelector(
    EldGpsIntegrationsState.selectors.eldGpsIntegrationDetails()
  );
  const isLoading = useAppSelector(
    EldGpsIntegrationsState.selectors.isRetrieving()
  );

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [step, setStep] = useState<number>(1);

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  useEffect(() => {
    if (isCreating || !integrationId) {
      return;
    }
    // Get integration details
    dispatch(
      EldGpsIntegrationsState.actions.retrieveIntegrationDetails({
        carrierId,
        integrationId,
      })
    );
  }, [isCreating, integrationId]);

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const title = isCreating
    ? t("Add Location Providers")
    : t("Edit Location Providers");
  const subtitle = isCreating
    ? t("Add your Location Provider")
    : t("Edit the credentials for your Location Provider.");

  //

  return (
    <div
      className={styles.modalWrapper}
      data-testid="eld-gps-integration-modal"
    >
      <Modal
        size="full"
        title={title}
        subtitle={subtitle}
        show={show}
        customHeader={
          <CustomHeader isProviderSelected={step === 2} onClose={onClose} />
        }
        customFooter={
          <WizardFooter>
            <Button
              className={styles.cancelButton}
              onClick={onClose}
              size="medium"
              theme="secondary"
              variant="solid"
            >
              {t("Cancel")}
            </Button>
          </WizardFooter>
        }
      >
        {isLoading && (
          <div className={styles.integrationWrapper}>
            <div className={styles.loader}>
              <Spinner isLoading size="medium" />
            </div>
          </div>
        )}

        {!isLoading && (
          <div className={styles.integrationWrapper}>
            {isCreating ? (
              <EldGpsIntegrationAddition
                isModal={true}
                isExternallyUsed={false}
                onProviderChanged={(provider: any) => setStep(provider ? 2 : 1)}
                onBack={onClose}
                onProviderNotFound={onProviderNotFound}
                onComplete={onComplete}
              />
            ) : (
              <ProviderCredentials
                isModal={true}
                isCreating={false}
                isExternallyUsed={false}
                provider={integrationDetails?.provider || null}
                integrationId={integrationId}
                onCredentialsUpdated={onComplete}
              />
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

const CustomHeader = ({ isProviderSelected, onClose }: any) => {
  const { t } = useTranslation();

  return (
    <div data-test-id="eld-gps-integration-modal-header">
      <div className={styles.header}>
        <div
          data-test-id="carrier-additions-bulk-upload-title-container"
          id="title-container"
        >
          <h5 data-test-id="carrier-additions-bulk-upload-title">
            {t("Add your ELD/GPS device")}
          </h5>
        </div>

        <div id="right">
          <button
            id="close"
            data-test-id="carrier-additions-bulk-upload-cancel-button"
            onClick={onClose}
          >
            <XIcon iconClass={styles.closeIcon} />
          </button>
        </div>
      </div>

      <div className={styles.subHeader}>
        <div>
          <div id="content">
            <div>
              <span
                className={
                  isProviderSelected ? styles.oval : styles.ovalSelected
                }
              >
                1
              </span>
              <label
                className={
                  isProviderSelected
                    ? styles.headerDescription
                    : styles.headerDescriptionSelected
                }
              >
                {t("CHOOSE LOCATION PROVIDER")}
              </label>
            </div>
            <div id="content-divider" />
            <span
              className={
                !isProviderSelected ? styles.oval : styles.ovalSelected
              }
            >
              2
            </span>
            <label
              className={
                !isProviderSelected
                  ? styles.headerDescription
                  : styles.headerDescriptionSelected
              }
            >
              {t("AUTHENTICATE")}
            </label>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EldGpsIntegrationModal;
