@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container {
  display: flex;
  > div > div {
    height: calc(100vh + -113px);
    padding: 0;
    background-color: white;
  }
}

.headerWrapper {
  padding: 32px;
  padding-top: 27px;
  background-color: $color-neutral-50;
}

.headerButton {
  > button {
    display: flex;
    align-items: center;
    align-content: center;
  }
}

.locateButton {
  > button {
    display: flex;
    align-items: center;
    align-content: center;
    margin-right: 20px;
  }
}

.loader {
  display: flex;
  align-items: left;
  align-content: center;
  justify-content: center;
  margin-top: 32px;
  margin-bottom: 32px;
}

.headerLoader {
  composes: loader;
  margin: 0;
  margin-right: 16px;

  > label {
    margin-right: 16px;
  }
}
