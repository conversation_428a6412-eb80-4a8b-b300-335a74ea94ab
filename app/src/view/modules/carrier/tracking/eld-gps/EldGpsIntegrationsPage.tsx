import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { LocateIcon, PlusIcon, Button } from "@fourkites/elemental-atoms";
import { Spinner } from "@fourkites/elemental-loading-indicator";

import {
  useAppSelector,
  useAppDispatch,
  useAutoRefreshEldIntegrations,
} from "state/hooks";
import { EldGpsIntegrationsState } from "state/modules/carrier/EldGpsIntegrations";
import { UsersState } from "state/modules/Users";
import { LocationProvidersState } from "state/modules/carrier/LocationProviders";

import BreadcrumbsHeader from "view/components/base/breadcrumbs-header/BreadcrumbsHeader";
import SubPagePanel from "view/components/base/containers/SubPagePanel";
import { showToast } from "view/components/base/toast/Toast";
import EldGpsDeleteIntegrationModal from "view/components/self-service/location-data-integrations/eld-gps/details/EldGpsDeleteIntegrationModal";

import EldGpsIntegrationsTable from "./EldGpsIntegrationsTable";
import EldGpsIntegrationModal from "./EldGpsIntegrationModal";
import LocateAssetModal from "../../assets/management/modals/LocateAssetModal";

import styles from "./EldGpsIntegrationsPage.module.scss";

/**
 * Code contains the below
 * Add integration
 * Edit integration
 * Complete integration (peoplenet case, provider not in FK and in FK)
 * Delete integration
 */

const EldGpsIntegrationsPage = () => {
  const { t } = useTranslation();

  const dispatch = useAppDispatch();
  const currentUser = useAppSelector(UsersState.selectors.getCurrentUser);

  /*****************************************************************************
   * REDUX
   ****************************************************************************/
  const carrierId: string = useAppSelector(UsersState.selectors.getCompanyId);
  const isLoading = useAppSelector(
    EldGpsIntegrationsState.selectors.isRetrieving()
  );
  const eldGpsIntegrations = useAppSelector(
    EldGpsIntegrationsState.selectors.eldGpsIntegrations()
  );

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [showIntegrationModal, setShowIntegrationModal] =
    useState<boolean>(false);
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const [isAddingCredential, setIsAddingCredential] = useState<boolean>(false);
  const [integrationId, setIntegrationId] = useState<any>(null);
  const [integration, setIntegration] = useState<any>(null);
  const [showLocateAssetModal, setShowLocateAssetModal] =
    useState<boolean>(false);

  /*****************************************************************************
   * EFFECTS AND HOOKS
   ****************************************************************************/

  /**
   * Handler for retrieving the list of all integrations
   */
  const fetchIntegrations = () => {
    dispatch(
      EldGpsIntegrationsState.actions.retrieveIntegrations({
        carrierId,
        modes: ["ltl", "ftl"],
      })
    );
  };

  /**
   * Fetches list of integrations when carrier Id changes
   */
  useEffect(() => {
    fetchIntegrations();
  }, [carrierId]);

  /**
   * Auto refresh GPS Integrations. Pooling only occurs if any integration has
   * a validation in progress status
   */
  const { setAutoRefreshGPSInterval, clearAutoRefreshGPSInterval } =
    useAutoRefreshEldIntegrations(eldGpsIntegrations, fetchIntegrations);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  /**
   * When provider is not found, fetch integrations again and close modal
   */
  const onProviderNotFound = () => {
    // Fetches credentials again
    fetchIntegrations();

    onClose();
  };

  /**
   * Handler for open modal to add a new integration
   */
  const onAddIntegration = () => {
    // Stop pooling of list of integratons
    clearAutoRefreshGPSInterval();

    // Show modal
    setIsAddingCredential(true);
    setShowIntegrationModal(true);
  };
  /**
   * Handler for open modal to edit credential
   */
  const onEditIntegration = (integration: any) => {
    // Stops pooling of list of integratons and set integration to edit
    setIntegrationId(integration?.id);
    clearAutoRefreshGPSInterval();

    // Show modal
    setIsAddingCredential(false);
    setShowIntegrationModal(true);
  };

  /**
   * Handler for closing integration add/edit modal
   */
  const onClose = () => {
    setIsAddingCredential(false);
    setShowIntegrationModal(false);

    // Start pooling of lsit of integrations
    setAutoRefreshGPSInterval();
  };

  /**
   * Handler for completing update of an integration
   */
  const onCompleteIntegrationUpdate = () => {
    onClose();
    // Fetch credentials again
    fetchIntegrations();
  };

  /**
   * Link the provider
   */
  const linkProvider = async (providerId: any, integration: any) => {
    await dispatch(
      EldGpsIntegrationsState.actions.updateIntegration({
        carrierId: carrierId,
        userId: currentUser?.userId,
        modes: ["ltl", "ftl"],
        integrationId: integration?.id,
        provider: {
          id: providerId,
          type: "truck",
          alias: "", //TODO,
          credentials: {},
        },
      })
    );
  };

  /**
   * Verify the provider created by Ops else show error message to wait
   */
  const getProviderCreatedOrShowError = async (integration: any) => {
    const response = await dispatch(
      LocationProvidersState.actions.getLocationProviders({
        showPopular: false,
        query: integration?.provider?.name,
      })
    );

    if (response?.payload?.length > 0) {
      // To link with correct provider, checking name too
      // BE Api check provider name in contains perspective
      const providerId =
        response.payload[0]?.name === integration?.provider?.name
          ? response.payload[0]?.id
          : null;
      return providerId;
    }
    showToast(
      t("The requested provider is not yet created"),
      t("Please reach out to Product or Support team"),
      "error"
    );
    fetchIntegrations();
  };

  /**
   * For manual interventions, make PATCH call to make the status to connected (SELF-2223)
   */
  const connectProviderManually = async (integration: any) => {
    await dispatch(
      EldGpsIntegrationsState.actions.updateIntegration({
        carrierId: carrierId,
        userId: currentUser?.userId,
        modes: ["ltl", "ftl"],
        integrationId: integration?.id,
        provider: {
          id: integration?.provider?.id,
          type: "truck",
          alias: "",
          credentials: {},
        },
        completeIntegrationManually: true,
      })
    );
    fetchIntegrations();
  };

  /**
   * Complete pending gps integrations to connected
   */

  /**
   * This is done as a hotfix under SELF-1671
   * Since we are recieving different status description from CS and SS for pending integration
   * out-network gps connection -> status description = New FourKites integration
   * normal pending gps connection -> status description = new integration
   */

  const onCompleteIntegration = async (integration: any) => {
    const statusValue = integration.status?.value;
    const statusDescription = integration.status?.description;

    const isNewProviderIntegration =
      statusValue === "pending" &&
      (statusDescription === "New FourKites integration" ||
        statusDescription === "new integration");

    const isManualProcessInvolved =
      statusValue === "pending" &&
      statusDescription === "Awaiting FourKites Support" &&
      integration?.provider?.manualProcessInvolved;

    const isPeoplenetCreatedViaLegacy =
      statusValue === "pending" && integration?.provider?.id === "peoplenet";

    if (isManualProcessInvolved || isPeoplenetCreatedViaLegacy) {
      // Providers like peoplenet (SELF-2223)
      connectProviderManually(integration);
      return;
    } else if (isNewProviderIntegration && !integration?.provider?.id) {
      // Providers not in FK. Needs to be created via support
      const providerId = await getProviderCreatedOrShowError(integration);
      if (providerId) {
        await linkProvider(providerId, integration);
        onEditIntegration(integration);
        return;
      }
    } else {
      // Providers in FK or needs some help from support team
      onEditIntegration(integration);
      return;
    }
  };

  /*
   * Show confirmation modal when user wants to delete integration
   */
  const onDeleteIntegration = async (integration: any) => {
    clearAutoRefreshGPSInterval();
    setIntegrationId(integration?.id);
    setIntegration(integration);

    setShowDeleteModal(true);
  };

  /*
   * Handler for deleting integration when user confirms it
   */
  const onConfirmIntegrationDelete = async () => {
    // Hides delete modal
    setShowDeleteModal(false);

    const response = await dispatch(
      EldGpsIntegrationsState.actions.deleteIntegration({
        carrierId,
        integrationId: integrationId,
        modes: ["ltl", "ftl"],
      })
    );

    // Show error if error
    if ("error" in response) {
      showToast(
        t("Error"),
        t("We could not delete your integration. Please try again shortly."),
        "error"
      );
    }

    // Confirms succss and fetches credentials again
    showToast(
      t("Success"),
      t("Your integration was successfully deleted."),
      "ok"
    );
    fetchIntegrations();
    setAutoRefreshGPSInterval();
  };

  /**
   * On Closing delete modal
   */
  const onCloseDeleteModal = () => {
    setShowDeleteModal(false);
    setAutoRefreshGPSInterval();
  };

  const onLocateAsset = () => {
    setShowLocateAssetModal(true);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const showHeaderLoader = isLoading && eldGpsIntegrations.length > 0;
  const showTableLoader = isLoading && eldGpsIntegrations.length === 0;

  const breadcrumbTitles = [t("ELD / GPS Integrations")];

  const headerItems = [
    showHeaderLoader ? (
      <div className={styles.headerLoader}>
        <label data-testid="loading-integrations-label">
          {t("Loading Integrations")}
        </label>
        <Spinner isLoading size="small" />
      </div>
    ) : null,
    <div className={styles.locateButton}>
      <Button
        size={"large"}
        onClick={onLocateAsset}
        theme="primary"
        variant="outline"
      >
        <LocateIcon fill="#0e65e5" iconClass={"button-icon-left"} />
        {t("Locate Asset")}
      </Button>
    </div>,
    <div className={styles.headerButton}>
      <Button
        size={"large"}
        onClick={onAddIntegration}
        data-testid="add-location-provider-button"
      >
        <PlusIcon fill="#fff" iconClass={"button-icon-left"} />
        {t("Add Location Providers")}
      </Button>
    </div>,
  ];

  return (
    <div
      className={styles.container}
      data-testid="eld-gps-tracking-integrations-page-container"
    >
      <SubPagePanel>
        <div className={styles.headerWrapper} data-testid="gps-header">
          <BreadcrumbsHeader titles={breadcrumbTitles} children={headerItems} />
        </div>

        {showTableLoader && (
          <div className={styles.loader}>
            <Spinner isLoading size="medium" />
          </div>
        )}

        {!showTableLoader && (
          <EldGpsIntegrationsTable
            eldGpsIntegrations={eldGpsIntegrations}
            onEditIntegration={onEditIntegration}
            onCompleteIntegration={onCompleteIntegration}
            onDeleteIntegration={onDeleteIntegration}
          />
        )}
      </SubPagePanel>

      <EldGpsIntegrationModal
        integrationId={integrationId}
        isCreating={isAddingCredential}
        show={showIntegrationModal}
        onClose={onClose}
        onProviderNotFound={onProviderNotFound}
        onComplete={onCompleteIntegrationUpdate}
      />

      <EldGpsDeleteIntegrationModal
        providerName={integration?.provider?.name}
        show={showDeleteModal}
        onClose={onCloseDeleteModal}
        onDelete={onConfirmIntegrationDelete}
      />

      <LocateAssetModal
        show={showLocateAssetModal}
        onClose={() => setShowLocateAssetModal(false)}
      />
    </div>
  );
};

export default EldGpsIntegrationsPage;
