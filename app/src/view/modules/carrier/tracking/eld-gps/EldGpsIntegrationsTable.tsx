import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { Table } from "@fourkites/elemental-table";

import { useAppSelector } from "state/hooks";
import UsersState from "state/modules/Users";

import { getUserLocaleDate } from "view/components/base/DateUtils";
import { startWithUpperCase } from "view/components/base/StringUtils";

import { getColumns } from "./EldGpsIntegrationsTableColumns";

import styles from "./EldGpsIntegrationsTable.module.scss";

const EldGpsIntegrationsTable = ({
  eldGpsIntegrations,
  onEditIntegration,
  onCompleteIntegration,
  onDeleteIntegration,
}: any) => {
  const { t } = useTranslation();
  const superAdmin = useAppSelector(UsersState.selectors.getIsSuperAdmin);

  /*****************************************************************************
   * DATA
   ****************************************************************************/

  const columns = useMemo(() => {
    return getColumns(
      t,
      superAdmin,
      onEditIntegration,
      onCompleteIntegration,
      onDeleteIntegration,
      eldGpsIntegrations
    );
  }, [
    superAdmin,
    onEditIntegration,
    onCompleteIntegration,
    onDeleteIntegration,
    eldGpsIntegrations,
  ]);

  const data = useMemo(() => {
    return eldGpsIntegrations?.map((integration: any) => {
      const provider = integration?.provider;

      return {
        provider_name: provider?.display_name
          ? provider?.display_name
          : provider?.name,
        type: provider?.type
          ? `${startWithUpperCase(integration?.provider?.type)} GPS`
          : "-",
        status: { ...integration?.status, provider_id: provider?.id },
        created_by: integration?.created_by,
        created_at: getUserLocaleDate(integration?.created_at),
      };
    });
  }, [eldGpsIntegrations]);

  /*****************************************************************************
   * RENDER
   ****************************************************************************/
  const paginationParams = {
    paginated: true,
  };

  return (
    <div
      className={styles.content}
      data-testid="eld-gps-tracking-integrations-table"
    >
      {data ? (
        <div
          className={styles.tableContainer}
          data-testid="eld-gps-integration-table-container"
        >
          <Table
            data={data}
            columns={columns}
            rowHeight="small"
            striped
            pagination={paginationParams}
          />
        </div>
      ) : (
        <label data-testid="no-tracking-integration-label">
          {t("There are no integrations for this carrier.")}
        </label>
      )}
    </div>
  );
};

export default EldGpsIntegrationsTable;
