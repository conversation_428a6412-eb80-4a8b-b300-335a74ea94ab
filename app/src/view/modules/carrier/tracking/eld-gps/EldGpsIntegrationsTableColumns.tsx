import React from "react";

import { Button } from "@fourkites/elemental-atoms";

import Contact from "view/components/base/contact/Contact";

import { IntegrationActions } from "view/components/self-service/location-data-integrations/eld-gps/details/IntegrationActions";
import { IntegrationStatus } from "view/components/self-service/location-data-integrations/eld-gps/details/IntegrationStatus";

import styles from "./EldGpsIntegrationsTable.module.scss";

export const getColumns = (
  t: Function,
  superAdmin: boolean,
  onEditIntegration: Function,
  onCompleteIntegration: Function,
  onDeleteIntegration: Function,
  eldGpsIntegrations: any[]
) => {
  return [
    {
      Header: "Provider",
      accessor: "provider_name",
    },
    {
      Header: "Status",
      accessor: "status",
      Cell: (cellProps: any) => {
        const status = cellProps?.value;

        if (!status) {
          return null;
        }

        // Defaulting status to connected for old integrations (SELF-1607)
        const statusValue = status?.value ? status?.value : "connected";
        const statusDescription = status?.description;
        const statusDetail = status?.statusDetail;

        return (
          <div
            className={styles.statusWrapper}
            data-testid="tracking-integration-table-cell-status"
          >
            <IntegrationStatus
              statusValue={statusValue}
              statusDescription={statusDescription}
              statusDetail={statusDetail}
            />

            {statusValue === "error" && (
              <div className={styles.statusInfoWrapper}>
                <Button
                  data-testid="error-gps-fix-issue-button"
                  variant="flat"
                  size="small"
                  onClick={() =>
                    onEditIntegration(eldGpsIntegrations[cellProps.row.index])
                  }
                >
                  {t("Fix Issue")}
                </Button>
              </div>
            )}
          </div>
        );
      },
    },
    {
      Header: "Type",
      accessor: "type",
    },
    {
      Header: "Added By",
      accessor: "created_by",
      Cell: (cellProps: any) => {
        const c = cellProps.value;

        if (c?.first_name == null) {
          return "FourKites Admin";
        }

        return (
          <Contact
            contact={{
              avatar: c.avatar,
              firstName: c.first_name,
              lastName: c.last_name,
              position: c.position,
              email: c.email,
              secondaryEmails: c.secondary_emails || [],
              phones: c.phones || [],
              messaging: c.messaging,
            }}
            contactInline
          />
        );
      },
    },
    {
      Header: "Added On",
      accessor: "created_at",
    },
    {
      Header: "",
      accessor: "status",
      id: "actions",
      Cell: (cellProps: any) => {
        const status = cellProps?.value;

        const statusValue = status && status?.value;
        const providerId = status && status?.provider_id;
        const integration = eldGpsIntegrations[cellProps.row.index];
        const showCompleteIntegration =
          superAdmin && statusValue === "pending" && integration?.provider?.id;
        const showEditIntegration = integration?.provider?.id;

        return (
          <IntegrationActions
            onEditIntegration={
              showEditIntegration ? () => onEditIntegration(integration) : null
            }
            onCompleteIntegration={
              showCompleteIntegration
                ? () => onCompleteIntegration(integration)
                : null
            }
            onDeleteIntegration={() => onDeleteIntegration(integration)}
          />
        );
      },
    },
  ];
};
