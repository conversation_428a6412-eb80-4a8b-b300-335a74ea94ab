import { useTranslation } from "react-i18next";
import { LocateI<PERSON>, Button } from "@fourkites/elemental-atoms";
import { RadioButton } from "@fourkites/elemental-radio-button";
import { Input } from "@fourkites/elemental-input";
import locationPin from "assets/img/locationPin.png";

import styles from "./TrackingIntegrationAssetLocation.module.scss";

const TrackingIntegrationAssetLocation = ({
  assetType,
  setAssetType,
  assetNumber,
  setAssetNumber,
  fetchAndShowAssetLocation,
  showAssetLocation,
}: any) => {
  const { t } = useTranslation();

  return (
    <div className={styles.content}>
      <label>
        {t(
          "Enter asset type and an asset ID to execute a ping test and fetch location details"
        )}
      </label>

      <div className={styles.inputFields}>
        <label>{t("Asset Type")}</label>
        <span>
          <RadioButton
            label="Truck #"
            checked={assetType === "truck"}
            onClick={() => setAssetType("truck")}
            size="medium"
          />
          <RadioButton
            label="Trailer #"
            checked={assetType === "trailer"}
            onClick={() => setAssetType("trailer")}
            size="medium"
          />
        </span>
      </div>

      <div className={styles.assetIdContainer}>
        <Input
          label={`${t(`Asset ID`)}`}
          value={assetNumber}
          onChange={(e) => setAssetNumber(e.target.value)}
          errorLabel={t("Field is required")}
          required
        />

        <div className={styles.fetchButtonContainer}>
          <Button
            onClick={() => fetchAndShowAssetLocation(assetType, assetNumber)}
            size="large"
            theme="primary"
            disabled={assetNumber.trim() === ""}
          >
            <LocateIcon fill="#fff" iconClass={"button-icon-left"} />
            {t("Fetch Location")}
          </Button>
        </div>
      </div>

      {!showAssetLocation && <AssetLocationInstruction />}
    </div>
  );
};

const AssetLocationInstruction = () => {
  const { t } = useTranslation();

  return (
    <div className={styles.locationInstructionContainer}>
      <img className={styles.appStoreLogo} src={locationPin} />
      <h1 id="title">
        {t("Enter a valid asset ID and fetch location to see details here")}
      </h1>
    </div>
  );
};

export default TrackingIntegrationAssetLocation;
