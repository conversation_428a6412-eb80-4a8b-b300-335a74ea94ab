import React from "react";
import { cleanup, fireEvent } from "@testing-library/react";

import store from "state/store";

import EldGpsIntegrationsPage from "../EldGpsIntegrationsPage";
import EldGpsIntegrationsTable from "../EldGpsIntegrationsTable";

import {
  mockedUseSelector,
  renderWithRedux,
  changeState,
  toBeValidAssertion,
  toHaveTextContentAssertion,
} from "tests/TestUtils";

import {
  mockAppState,
  mockAppStateWithIntegration,
  mockAppStateWhileRetrieivngIntegrations,
  mockAppStateWithIntegrationDetails,
} from "../tests/MockAppState";

// TODO : figure out how to move the mocks to an utils file

jest.mock("react-i18next", () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

const mockDispatch = jest.fn();

jest.mock("react-redux", () => ({
  ...jest.requireActual("react-redux"),
  useSelector: jest.fn(),
  useDispatch: () => mockDispatch,
}));

jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
}));

describe("EldGpsIntegrationsPage", () => {
  let eldGpsIntegrationsTableProps: any;
  const clickHandler = jest.fn();

  beforeEach(() => {
    eldGpsIntegrationsTableProps = {
      eldGpsIntegrations: mockAppStateWithIntegration.eldGpsIntegrations.data,
      onEditIntegration: clickHandler,
      onCompleteIntegration: clickHandler,
      onDeleteIntegration: clickHandler,
    };

    mockedUseSelector.mockImplementation((callback) => {
      return callback(mockAppState);
    });
  });

  afterEach(() => {
    cleanup();
    mockedUseSelector.mockClear();
    jest.restoreAllMocks();
  });

  const renderComponent = () =>
    renderWithRedux(<EldGpsIntegrationsPage />, store);

  const renderEldGpsIntegrationsTableComponent = () =>
    renderWithRedux(
      <EldGpsIntegrationsTable {...eldGpsIntegrationsTableProps} />,
      store
    );

  const selectFlyOutMenu = (dataRow: any) => {
    return dataRow[5].childNodes[0].childNodes[0].childNodes[0];
  };

  it("should render the EldGpsIntegrationsPage component", () => {
    const { getByTestId } = renderComponent();

    const testComponent = getByTestId(
      `eld-gps-tracking-integrations-page-container`
    );
    toBeValidAssertion(testComponent);
  });

  // Test Cases Completed :-
  // contents of table
  // add location provider button open a modal on click
  // loading of integrations
  // table columns
  // edit integration should open modal
  // delete inetgration should open modal

  it("should render EldGpsIntegrationsPage Header with ELD / GPS Integrations title and add location providers button", () => {
    const { getByTestId } = renderComponent();

    const gpsHeaderComponent = getByTestId(`gps-header`);
    const gpsBreadcrumbsHeader = getByTestId(`bread-crumbs-header-title`);
    const locationProvidersButton = getByTestId(`add-location-provider-button`);

    toBeValidAssertion(gpsHeaderComponent);

    toBeValidAssertion(gpsBreadcrumbsHeader);
    toHaveTextContentAssertion(gpsBreadcrumbsHeader, "ELD / GPS Integrations");

    toBeValidAssertion(locationProvidersButton);
    toHaveTextContentAssertion(
      locationProvidersButton,
      "Add Location Providers"
    );
  });

  it("should render loading integrations label when retrieving key is true", () => {
    changeState(mockAppStateWhileRetrieivngIntegrations);
    const { getByTestId } = renderComponent();

    const loadingIntegrationsLabel = getByTestId(`loading-integrations-label`);

    toBeValidAssertion(loadingIntegrationsLabel);
    toHaveTextContentAssertion(
      loadingIntegrationsLabel,
      "Loading Integrations"
    );
  });

  it("should render gps integrations table component with integrations and check column names of table", () => {
    changeState(mockAppStateWithIntegration);
    const { getByTestId } = renderEldGpsIntegrationsTableComponent();

    const eldGpsTrackingIntegrationsTable = getByTestId(
      `eld-gps-tracking-integrations-table`
    );
    const eldGpsTrackingIntegrationsTableHeader = getByTestId(
      "elemental-table-header"
    );
    const headings =
      eldGpsTrackingIntegrationsTableHeader.getElementsByClassName(
        "elemental-table-header-cell"
      );

    toBeValidAssertion(eldGpsTrackingIntegrationsTable);

    toBeValidAssertion(eldGpsTrackingIntegrationsTableHeader);

    expect(headings.length).toBe(6);
    toHaveTextContentAssertion(headings[0], "Provider");
    toHaveTextContentAssertion(headings[1], "Status");
    toHaveTextContentAssertion(headings[2], "Type");
    toHaveTextContentAssertion(headings[3], "Added By");
    toHaveTextContentAssertion(headings[4], "Added On");
  });

  it("should check for row data of eld gps integrations table with error config", () => {
    changeState(mockAppStateWithIntegration);
    const { getByTestId } = renderEldGpsIntegrationsTableComponent();

    const eldGpsTrackingIntegrationsTableBody = getByTestId(
      "elemental-table-body"
    );
    const dataRow = eldGpsTrackingIntegrationsTableBody.getElementsByClassName(
      "elemental-table-row-data elemental-table-row-data--small"
    );
    const fixIssueButton = getByTestId("error-gps-fix-issue-button");
    const statusTag = getByTestId("status-tag");
    const avatarInitials = getByTestId("avatar-initials");

    expect(dataRow.length).toBe(6);
    toHaveTextContentAssertion(dataRow[0], "Eroad");
    toHaveTextContentAssertion(statusTag, "Error");
    toHaveTextContentAssertion(fixIssueButton, "Fix Issue");
    toHaveTextContentAssertion(dataRow[2], "Truck GPS");
    toHaveTextContentAssertion(avatarInitials, "A");
    toHaveTextContentAssertion(dataRow[4], "Jul 24, 2022, 02:50 PM");
  });

  it("should check details of the modal when add Location Provider is clicked and providers have loaded", async () => {
    const { getByTestId } = renderComponent();

    const locationProvidersButton = getByTestId(`add-location-provider-button`);
    await fireEvent.click(locationProvidersButton);

    const modalHeader = getByTestId("elemental-modal-header");
    const modalHeadingTitle = modalHeader.getElementsByClassName(
      "elemental-modal-header-title modal-full"
    );
    const modalHeadingSubTitle = modalHeader.getElementsByClassName(
      "elemental-modal-header-subtitle modal-full"
    );
    const modalHeaderCancelButton = modalHeader.getElementsByClassName(
      "elemental-modal-close-icon-container modal-full"
    );

    toBeValidAssertion(modalHeader);
    toHaveTextContentAssertion(modalHeadingTitle[0], "Add Location Providers");
    toHaveTextContentAssertion(
      modalHeadingSubTitle[0],
      "Add your Location Provider"
    );

    expect(modalHeaderCancelButton).toBeDefined();
  });

  it("should check details of the modal when edit Location Provider is clicked", async () => {
    changeState(mockAppStateWithIntegrationDetails);
    const { getByTestId, getAllByTestId } = renderComponent();

    const eldGpsTrackingIntegrationsTableBody = getByTestId(
      "elemental-table-body"
    );
    const dataRow = eldGpsTrackingIntegrationsTableBody.getElementsByClassName(
      "elemental-table-row-data elemental-table-row-data--small"
    );

    fireEvent.click(selectFlyOutMenu(dataRow));

    const editIntegrationButton = getByTestId("edit-integration-button");
    await fireEvent.click(editIntegrationButton);

    const modalHeader = getByTestId("elemental-modal-header");
    const modalHeadingTitle = modalHeader.getElementsByClassName(
      "elemental-modal-header-title modal-full"
    );
    const modalHeadingSubTitle = modalHeader.getElementsByClassName(
      "elemental-modal-header-subtitle modal-full"
    );
    const modalHeaderCancelButton = modalHeader.getElementsByClassName(
      "elemental-modal-close-icon-container modal-full"
    );

    const providerCredentialsFormLabel = getByTestId(
      "provider-credentials-form-label"
    );

    const assetInput = getByTestId("input-component");
    const saveAndConnectButton = getAllByTestId("button-component")[0];
    const formLInk = getByTestId("provider-credentials-form-link");

    toBeValidAssertion(modalHeader);
    toHaveTextContentAssertion(modalHeadingTitle[0], "Edit Location Providers");
    toHaveTextContentAssertion(
      modalHeadingSubTitle[0],
      "Edit the credentials for your Location Provider."
    );
    expect(modalHeaderCancelButton).toBeDefined();
    toHaveTextContentAssertion(
      providerCredentialsFormLabel,
      "Share your Eroad login credentials with FourKites."
    );
    toHaveTextContentAssertion(
      assetInput,
      "Enter any Asset ID that uses Eroad equipment *"
    );
    toBeValidAssertion(saveAndConnectButton);
    toBeValidAssertion(formLInk);
    toHaveTextContentAssertion(
      formLInk,
      "I need some support. Have FourKites reach out to me"
    );
  });

  it("should delete the provider when delete Location Provider is clicked", async () => {
    changeState(mockAppStateWithIntegrationDetails);
    const { getByTestId, getAllByTestId } = renderComponent();

    const eldGpsTrackingIntegrationsTableBody = getByTestId(
      "elemental-table-body"
    );
    const dataRow = eldGpsTrackingIntegrationsTableBody.getElementsByClassName(
      "elemental-table-row-data elemental-table-row-data--small"
    );

    fireEvent.click(selectFlyOutMenu(dataRow));

    const deleteIntegrationButton = getByTestId("delete-integration-button");
    await fireEvent.click(deleteIntegrationButton);

    const modalHeader = getByTestId("elemental-modal-header");
    const modalHeadingTitle = modalHeader.getElementsByClassName(
      "elemental-modal-header-title modal-small"
    );
    const modalHeaderCancelButton = modalHeader.getElementsByClassName(
      "elemental-modal-close-icon-container modal-small"
    );
    const modalFooter = getByTestId("elemental-modal-footer");
    const modalContent = getByTestId("delete-gps-modal-content");

    toBeValidAssertion(modalHeader);
    toBeValidAssertion(modalFooter);
    toHaveTextContentAssertion(
      modalHeadingTitle[0],
      "Are you sure you want to delete this integration?"
    );
    expect(modalHeaderCancelButton).toBeDefined();
    toHaveTextContentAssertion(
      modalContent,
      `"Eroad" Integration will be permanently deleted and cannot be recovered.`
    );
  });
});
