import { getCarriersMockAppState } from "tests/CarriersMockAppState";

export const mockAppState = getCarriersMockAppState();

export const mockAppStateWithIntegration = {
  ...mockAppState,
  locationProviders: {
    locationProviders: {
      data: [
        {
          id: "eroad",
          name: "Eroad",
          display_name: "Eroad test",
          logo: "",
          credentials: [
            {
              name: "Datastore",
              id: "dataStore",
              value: null,
              default_value: null,
            },
          ],
          requires_asset: true,
          instructions_url:
            "https://s3.amazonaws.com/dev-files-backup/location_providers/eroad/assets/eroad_instructions.html",
          instructions_url_html:
            "https://s3.amazonaws.com/dev-files-backup/location_providers/eroad/assets/eroad_instructions.html",
        },
        {
          id: "geotab",
          name: "Geotab",
          display_name: "Geotab",
          logo: "https://s3.amazonaws.com/dev-files-backup/location_providers/geotab/assets/geotab_logo",
          credentials: [
            {
              name: "Userna<PERSON>",
              id: "username",
              value: null,
              default_value: null,
            },
            {
              name: "Password",
              id: "password",
              value: null,
              default_value: null,
            },
            {
              name: "Provider Code",
              id: "providerCode",
              value: null,
              default_value: null,
            },
          ],
          requires_asset: true,
          instructions_url:
            "https://s3.amazonaws.com/dev-files-backup/location_providers/geotab/assets/geotab_instructions.html,https://s3.amazonaws.com/dev-files-backup/location_providers/geotab/assets/geotab_instructions.pdf",
          instructions_url_html:
            "https://s3.amazonaws.com/dev-files-backup/location_providers/geotab/assets/geotab_instructions.html",
          instructions_url_pdf:
            "https://s3.amazonaws.com/dev-files-backup/location_providers/geotab/assets/geotab_instructions.pdf",
        },
        {
          id: "keeptruckinv2",
          name: "Keep Truckin v2",
          display_name: "Keep Truckin v21",
          logo: "",
          credentials: [],
          requires_asset: false,
          instructions_url: "",
        },
        {
          id: "peoplenet",
          name: "PeopleNet",
          display_name: "PeopleNet",
          logo: "https://s3.amazonaws.com/dev-files-backup/location_providers/peoplenet/assets/peoplenet_logo",
          credentials: [],
          requires_asset: false,
          instructions_url: "",
        },
        {
          id: "randmcnally",
          name: "RandMcNally",
          display_name: "RandMcNally",
          logo: "https://s3.amazonaws.com/dev-files-backup/location_providers/randmcnally/assets/randmcnally_logo",
          credentials: [],
          requires_asset: false,
          instructions_url: "",
        },
        {
          id: "samsara",
          name: "samsara",
          display_name: "samsara",
          logo: "",
          credentials: [
            {
              name: "Username",
              id: "username",
              value: null,
              default_value: null,
            },
            {
              name: "Password",
              id: "password",
              value: null,
              default_value: null,
            },
          ],
          requires_asset: true,
          instructions_url:
            "https://s3.amazonaws.com/dev-files-backup/location_providers/samsara/assets/samsara_instructions.html",
          instructions_url_html:
            "https://s3.amazonaws.com/dev-files-backup/location_providers/samsara/assets/samsara_instructions.html",
        },
      ],
      loading: false,
      creating: false,
      error: false,
    },
  },
  eldGpsIntegrations: {
    data: [
      {
        id: "a0a658d2-08e4-4e70-b381-3ed37daf80e4",
        modes: ["ftl"],
        provider: {
          id: "eroad",
          name: "Eroad",
          logo: "",
          type: "truck",
          requires_asset: true,
          instructions_url:
            "https://s3.amazonaws.com/dev-files-backup/location_providers/eroad/assets/eroad_instructions.html",
          instructions_url_html:
            "https://s3.amazonaws.com/dev-files-backup/location_providers/eroad/assets/eroad_instructions.html",
        },
        status: { value: "error", description: "Credentials are Invalid" },
        created_by: {
          id: "arpitsabharwal",
          first_name: "arpit.sabharwal",
          last_name: null,
          email: "<EMAIL>",
        },
        created_at: "2022-07-24T09:20:56.390689Z",
      },
    ],
    details: {},
    retrieving: false,
    creating: false,
    updating: false,
    deleting: false,
    error: false,
  },
};

export const mockAppStateWhileRetrieivngIntegrations = {
  ...mockAppState,
  eldGpsIntegrations: {
    data: [
      {
        id: "a0a658d2-08e4-4e70-b381-3ed37daf80e4",
        modes: ["ftl"],
        provider: {
          id: "eroad",
          name: "Eroad",
          logo: "",
          type: "truck",
          requires_asset: true,
          instructions_url:
            "https://s3.amazonaws.com/dev-files-backup/location_providers/eroad/assets/eroad_instructions.html",
          instructions_url_html:
            "https://s3.amazonaws.com/dev-files-backup/location_providers/eroad/assets/eroad_instructions.html",
        },
        status: { value: "error", description: "Credentials are Invalid" },
        created_by: {
          id: "arpitsabharwal",
          first_name: "arpit.sabharwal",
          last_name: null,
          email: "<EMAIL>",
        },
        created_at: "2022-07-24T09:20:56.390689Z",
      },
    ],
    details: { id: "a0a658d2-08e4-4e70-b381-3ed37daf80e4" },
    retrieving: true,
    creating: false,
    updating: false,
    deleting: false,
    error: false,
  },
};

export const mockAppStateWithIntegrationDetails = {
  ...mockAppState,
  eldGpsIntegrations: {
    data: [
      {
        id: "a0a658d2-08e4-4e70-b381-3ed37daf80e4",
        modes: ["ftl"],
        provider: {
          id: "eroad",
          name: "Eroad",
          logo: "",
          type: "truck",
          requires_asset: true,          
          instructions_url:
            "https://s3.amazonaws.com/dev-files-backup/location_providers/eroad/assets/eroad_instructions.html",
          instructions_url_html:
            "https://s3.amazonaws.com/dev-files-backup/location_providers/eroad/assets/eroad_instructions.html",
        },
        status: { value: "error", description: "Credentials are Invalid" },
        created_by: {
          id: "arpitsabharwal",
          first_name: "arpit.sabharwal",
          last_name: null,
          email: "<EMAIL>",
        },
        created_at: "2022-07-24T09:20:56.390689Z",
      },
    ],
    details: {
      id: "a0a658d2-08e4-4e70-b381-3ed37daf80e4",
      modes: ["ftl"],
      provider: {
          id: "eroad",
          name: "Eroad",
          logo: "",
          type: "truck",
          requires_asset: true,
          instructions_url:
            "https://s3.amazonaws.com/dev-files-backup/location_providers/eroad/assets/eroad_instructions.html",
          instructions_url_html:
            "https://s3.amazonaws.com/dev-files-backup/location_providers/eroad/assets/eroad_instructions.html",
      },
      status: { value: "error", description: "Credentials are Invalid" },
       created_by: {
          id: "arpitsabharwal",
          first_name: "arpit.sabharwal",
          last_name: null,
          email: "<EMAIL>",
        },
        created_at: "2022-07-24T09:20:56.390689Z",

    },
    retrieving: false,
    creating: false,
    updating: false,
    deleting: false,
    error: false,
  },
};
