import React, { useState } from "react";
import { useHistory } from "react-router-dom";

//import { carrierWizardRoutes } from "router/carrier/CarrierWizardRouter";

import { carrierRoutes } from "router/carrier/CarrierRouter";
import { useAppSelector } from "state/hooks";
import { UsersState } from "state/modules/Users";

import EldGpsIntegrationAddition from "view/components/self-service/location-data-integrations/eld-gps/EldGpsIntegrationAddition";

import WizardFooter from "view/components/self-service/wizard/footer/WizardFooter";
import StepsHeader from "view/components/self-service/wizard/header/StepsHeader";

import styles from "./CarrierWizardEldGpsTrackingPage.module.scss";

const CarrierWizardEldGpsTrackingPage: React.FC = () => {
  const history = useHistory();

  const [step, setStep] = useState<number>(1);

  const isSuperAdmin = useAppSelector(UsersState.selectors.getIsSuperAdmin);
  const isCompanyAdmin = useAppSelector(UsersState.selectors.getIsCompanyAdmin);

  const goToTrackingIntegrationsPage = () => {
    // TODO: show toast message here on integrations page
    if (isSuperAdmin || isCompanyAdmin)
      history.push(`${carrierRoutes.tracking}/eld-gps`);
  };

  const onBack = () => {
    //TODO: removing welcome page for now
    //history.push(carrierWizardRoutes.start);
    history.push(carrierRoutes.overview);
  };

  return (
    <div className={styles.container}>
      <StepsHeader step={step} />

      <div className={styles.content}>
        <EldGpsIntegrationAddition
          isModal={false}
          isExternallyUsed={false}
          onProviderChanged={(provider: any) => setStep(provider ? 2 : 1)}
          onProviderNotFound={goToTrackingIntegrationsPage}
          onComplete={goToTrackingIntegrationsPage}
          onBack={onBack}
        />
      </div>

      <WizardFooter />
    </div>
  );
};

export default CarrierWizardEldGpsTrackingPage;
