import React from "react";
import { useTranslation } from "react-i18next";
import { <PERSON> } from "react-router-dom";

import {
  LinkButton,
  ArrowLeftIcon,
  ArrowRightIcon,
} from "@fourkites/elemental-atoms";
import { carrierWizardRoutes } from "router/carrier/CarrierWizardRouter";
import { carrierRoutes } from "router/carrier/CarrierRouter";

import CarrierLink from "view/components/self-service/location-data-integrations/mobile/CarrierLink";

import styles from "./CarrierWizardMobileTrackingPage.module.scss";

import WizardFooter from "view/components/self-service/wizard/footer/WizardFooter";

const CarrierWizardMobileTrackingPage: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <h1>
          <Link className={styles.cardLink} to={carrierWizardRoutes.start}>
            <ArrowLeftIcon />
          </Link>
          {t("Mobile App")}
        </h1>

        <CarrierLink />

        <h3>{t("How It Works?")}</h3>

        <label>
          {t(
            "CarrierLink is a free, easy to use and time-saving app for truck " +
              "drivers to communicate about assigned loads without having to make " +
              "check-in calls. Using the app, drivers can send automatic location " +
              "updates which let your customers always know where the load is. " +
              "Avoid all the manual check-in calls to make your driving " +
              "distraction-free and safer."
          )}
          <br /> <br />
          {t(
            "Drivers can also upload documents, communicate about truck " +
              "availability, and access Truck Specific Turn-By-Turn Navigation " +
              "with fuel cost details across various route options. The app will " +
              "only track location only upon the driver’s consent and only when " +
              "there is an active load being assigned.."
          )}
          <br /> <br />
          {t("Please find a quick ")}
          <a href="https://vimeo.com/392790800" target="_blank">
            {t("introductory video ")}
          </a>{" "}
          {t("and find more information in our ")}
          {t("Please visit the ")}
          <a
            href="https://support-fourkites.force.com/publicKB/s/"
            target="_blank"
          >
            {t("knowledge base")}
          </a>
        </label>
      </div>

      <WizardFooter>
        <LinkButton
          theme="primary"
          variant="solid"
          href={carrierRoutes.overview}
        >
          <span className={"button-content"}>
            {t("Done")}
            <ArrowRightIcon fill="white" iconClass={"button-icon-right"} />
          </span>
        </LinkButton>
      </WizardFooter>
    </div>
  );
};

export default CarrierWizardMobileTrackingPage;
