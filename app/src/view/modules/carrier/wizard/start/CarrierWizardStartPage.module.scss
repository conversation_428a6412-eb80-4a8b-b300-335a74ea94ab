@import "@fourkites/elemental-atoms/build/scss/colors/index";

.pageContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  height: calc(100vh + -112px);
  overflow-y: auto;
}

.contentContainer {
  padding-left: 96px;
  padding-right: 96px;
  padding-top: 100px;
}

.contentTitle {
  margin: 0;
  color: $color-neutral-900;
  font-size: 34px;
  font-weight: bold;
  letter-spacing: 0;
  line-height: 51px;
}

.contentSubtitle {
  color: $color-neutral-900;  font-size: 24px;
  letter-spacing: 0;
  line-height: 36px;
}


.eldGpsTrackingCard {
  background-color: $color-primary-500 !important;
  color: white;

  > div {
    display:flex;
    flex-direction: column;
    width: 608px;
    height: 286px;
    align-content: center;
    padding-top: 44px;
    padding-left: 40px;

    > h4 {
      margin: 0;
      font-size: 28px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 42px;
      margin-bottom: 16px;
    }

    > label {
      font-size: 20px;
      letter-spacing: 0;
      line-height: 30px;
    }

    > div[id=footer] {
      margin-top: 56px;
      display: flex;
    }
  }
}

.eldGpsTrackingCardFooter {
  margin-top: 56px;
  display: flex;
}



.cardsContainer {
  display:flex;
  width: 100%;
  margin-top: 112px;
}

.cardContent {
  display:flex;
  flex-direction: column;
  width: 608px;
  height: 286px;
  align-content: center;
  padding-top: 44px;
  padding-left: 40px;
}

.cardTitle {
  margin: 0;
  font-size: 28px;
  font-weight: bold;
  letter-spacing: 0;
  line-height: 42px;
  margin-bottom: 16px;
}

.cardDescritpion {
  font-size: 20px;
  letter-spacing: 0;
  line-height: 30px;
}


.cardLink {
  text-decoration: none;

  &:active {
    color: white;
  }

  &:hover,
  &:visited {
    color: white;
  }

  &:focus {
    outline: none;
  }
}

.mobileTrackingCard {
  color: $color-neutral-900;
  margin-left: 40px;
}

.mobileTrackingCardFooter {
  margin-top: 24px;
  display: flex;
}



// TODO: we need to have the white outline btton
.buttonContainer {
  width: 180px;
}
