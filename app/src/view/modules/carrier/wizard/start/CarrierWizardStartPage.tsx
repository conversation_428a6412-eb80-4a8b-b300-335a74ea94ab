import React from "react";
import { Link, useHistory } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useAppSelector } from "state/hooks";
import UsersState from "state/modules/Users";

import {
  LinkButton,
  Button,
  ArrowRightIcon,
  ChevronsRightIcon,
  PlayCircleIcon,
} from "@fourkites/elemental-atoms";

import { fourkitesUrls } from "api/http/apiUtils";

import { carrierWizardRoutes } from "router/carrier/CarrierWizardRouter";
import { carrierRoutes } from "router/carrier/CarrierRouter";

import Card from "view/components/base/card/Card";
import WizardFooter from "view/components/self-service/wizard/footer/WizardFooter";

import styles from "./CarrierWizardStartPage.module.scss";

const CarrierWizardStartPage: React.FC = () => {
  const { t } = useTranslation();
  const history = useHistory();

  const isSuperAdmin = useAppSelector(UsersState.selectors.getIsSuperAdmin);
  const isCompanyAdmin = useAppSelector(UsersState.selectors.getIsCompanyAdmin);

  return (
    <div className={styles.pageContainer}>
      <div className={styles.contentContainer}>
        <h4 className={styles.contentTitle}>{t("Welcome to FourKites")}</h4>
        <span className={styles.contentSubtitle}>
          {t("You can go live with shipment tracking in minutes!")}
        </span>

        {
          (isSuperAdmin || isCompanyAdmin) && (
            <div className={styles.cardsContainer}>
              <EldGpsTrackingCard />
              <MobileTrackingCard />
            </div>
            )
        }
      </div>

      <WizardFooter>
        <Button
          theme="tertiary"
          variant="solid"
          onClick={() => history.push(carrierRoutes.overview)}
        >
          <span className={"button-content"}>
            <ChevronsRightIcon fill="#0e65e5" iconClass={"button-icon-left"} />
            {t("Do it later")}
          </span>
        </Button>
      </WizardFooter>
    </div>
  );
};

const EldGpsTrackingCard = (props: any) => {
  const { t } = useTranslation();

  return (
    <Card customClass={styles.eldGpsTrackingCard}>
      <div>
        <h4>{t("ELD / GPS")}</h4>
        <label>
          {t("If you have ELD/GPS installed on your trucks/trailers.")}
        </label>

        <div id="footer">
          <Link
            className={styles.cardLink}
            to={carrierWizardRoutes.eldGpsTracking}
          >
            <div className={styles.buttonContainer}>
              <Button theme="primary" variant="outline">
                <span className={"button-content"}>
                  {t("Get Started")}
                  <ArrowRightIcon
                    fill="#0e65e5"
                    iconClass={"button-icon-right"}
                  />
                </span>
              </Button>
            </div>
          </Link>

          <div className={styles.buttonContainer}>
            <LinkButton
              theme="primary"
              variant="solid"
              target="_blank"
              href={fourkitesUrls.help}
            >
              <span className={"button-content"}>
                <PlayCircleIcon fill="white" iconClass={"button-icon-left"} />
                {t("Watch Demo")}
              </span>
            </LinkButton>
          </div>
        </div>
      </div>
    </Card>
  );
};

const MobileTrackingCard = (props: any) => {
  const { t } = useTranslation();

  return (
    <Card customClass={styles.mobileTrackingCard}>
      <div className={styles.cardContent}>
        <h4 className={styles.cardTitle}>{t("Mobile App")}</h4>
        <span className={styles.cardDescritpion}>
          {t(
            "Use the FourKites CarrierLink installed in your driver’s phones."
          )}
        </span>

        <div className={styles.eldGpsTrackingCardFooter}>
          <Link
            className={styles.cardLink}
            to={carrierWizardRoutes.mobileTracking}
          >
            <div className={styles.buttonContainer}>
              <Button theme="primary" variant="outline">
                <span className={"button-content"}>
                  {t("Get Started")}
                  <ArrowRightIcon
                    fill="#0e65e5"
                    iconClass={"button-icon-right"}
                  />
                </span>
              </Button>
            </div>
          </Link>
        </div>
      </div>
    </Card>
  );
};

export default CarrierWizardStartPage;
