import React from "react";
import { useTranslation } from "react-i18next";
import { useHistory } from "react-router-dom";

import { Spinner } from "@fourkites/elemental-loading-indicator";

import { COMPANY_API_DOMAIN, fourkitesUrls } from "api/http/apiUtils";

import { useAppSelector, useAppDispatch } from "state/hooks";
import CompanySwitcher from "view/components/self-service/component/CompanySwitcher"

import {
  GlobalHeader as Header,
  ProductId,
} from "@fourkites/elemental-global-header";

import { appRoutes } from "router/AppRouter";
import {
  onNewWindow,
  onNav,
  onHelpClick,
  onCommunitySelect,
  setCookie,
} from "router/navigationUtils";

import UsersState, { getCookies } from "state/modules/Users";

import styles from "./CompanySelectionPage.module.scss";
const CompanySelectionPage: React.FC = () => {
  const { t } = useTranslation();
  const history = useHistory();

  const dispatch = useAppDispatch();

  const currentUser = useAppSelector(UsersState.selectors.getCurrentUser);
  const superAdmin = useAppSelector(UsersState.selectors.getIsSuperAdmin);
  const isCompanyAdmin = useAppSelector(UsersState.selectors.getIsCompanyAdmin);
  const isLoadingUserData = useAppSelector(
    UsersState.selectors.getLoadingStatus
  );

  const { authToken, userId, deviceId } = getCookies();

  const onUserLogoutClick = () => {
    dispatch(
      UsersState.actionCreators.userLogout(() => history.push(appRoutes.login))
    );
  };

  const onSelectCompany = (selectedCompany: any) => {
    if (!selectedCompany || !selectedCompany?.id) {
      return;
    }

    dispatch(UsersState.actionCreators.setSelectedCompany(selectedCompany?.id));
  };

  const currentUserObject = {
    authToken,
    userId,
    deviceId,
    superAdmin: superAdmin ? "true" : "false",
    name: {
      firstName: currentUser?.name,
      lastName: currentUser?.lastName,
    },
    role: currentUser?.role,
    company: {
      defaultTypes: currentUser?.defaultCompanyType,
      defaultId: currentUser?.defaultCompanyID,
      context: currentUser?.companyContext,
    },
    displayCompanySwitcher: false,
  };

  const domains = {
    company: COMPANY_API_DOMAIN,
    user: "",
  }

  const onWorkspaceSelect = currentUser?.hasWorkspaceEnabled
  ? () => onNav(fourkitesUrls.myWorkspace)
  : undefined; 

  const onLogoClick = () => {
    currentUser?.hasWorkspaceEnabled ?
    onNav(fourkitesUrls.myWorkspace) : onNav(fourkitesUrls.dashboard)
 }
  const onConnectivitySelect = () => onNav(fourkitesUrls.connectivity);

  const initialSelectedProduct = ProductId.FourKitesConnect;
  const currentUserData = sessionStorage.getItem("fkcUserDetails") || localStorage.getItem("tfcurrent-user");
  const userObject = currentUserData ? JSON.parse(currentUserData) : null;

  const onDeveloperPortalSelect = async () => {
    setCookie();
    const archbeeJwt = await UsersState.helpers.getArchbeeJwt();
    const jwtAddedUrl = `${fourkitesUrls.developerPortalDoc}?jwt=${archbeeJwt}`;
    userObject?.superAdmin || userObject?.companyAdmin
      ? onNewWindow(fourkitesUrls.developerPortal)
      : onNewWindow(jwtAddedUrl)
  };

  const onAppointmentsManagerSelect = userObject?.modules?.includes(
    "appointment-manager"
  )
    ? () => onNav(fourkitesUrls.appointmentsManager)
    : undefined;

  const onDynamicYardSelect = userObject?.modules?.includes("dynamic-yard")
    ? () => onNav(fourkitesUrls.dynamicYard)
    : undefined; 

  const onDynamicYardPlusSelect = userObject?.modules?.includes(
    "dynamic-yard-plus"
  )
    ? () => onNav(fourkitesUrls.dynamicYardPlus)
    : undefined;

  const onPrivateFleetTrackingSelect = userObject?.modules?.includes(
    "nic-private-fleet"
  )
    ? () => onNav(fourkitesUrls.privateFleetTracking)
    : undefined;

  const onRailFleetSelect = userObject?.modules?.includes("nic-rail-fleet")
    ? () => onNav(fourkitesUrls.railFleet)
    : undefined;
  const onDigitalWorkforceSelect = () => onNav(fourkitesUrls.digitalWorkforce);
  return (
    <div className={styles.pageContainer}>
      <CompanySwitcher/>
      {!isLoadingUserData && (
        <Header
          currentUser={currentUserObject}
          initialSelectedItemId={initialSelectedProduct}
          initialSelectedProductId={initialSelectedProduct}
          onWorkspaceSelect={onWorkspaceSelect}
          onLogoClick={onLogoClick}
          // Product switcher
          onVisibilitySelect={() => onNav(fourkitesUrls.visibility)}
          onAppointmentsManagerSelect={onAppointmentsManagerSelect}
          onDynamicYardSelect={onDynamicYardSelect}
          onCommunitySelect={onCommunitySelect}
          onDeveloperPortalSelect={onDeveloperPortalSelect}
          onFourKitesConnectSelect={onConnectivitySelect}
          onDynamicYardPlusSelect={onDynamicYardPlusSelect}
          onPrivateFleetTrackingSelect={onPrivateFleetTrackingSelect}
          onRailFleetSelect={onRailFleetSelect}
          onDigitalWorkforceSelect={onDigitalWorkforceSelect}

          // Right menu
          onNotificationsClick={() => onNewWindow(fourkitesUrls.notifications)}
          onHelpClick={onHelpClick}
          onSettingsClick={() => onNav(fourkitesUrls.companySettings)}
          // Avatar menu
          onUserLogoutClick={onUserLogoutClick}
          domains={domains}
          setSelectedCompany={onSelectCompany}
        />
      )}

      <div id="content">
        {isLoadingUserData ? (
          <Spinner isLoading size="medium" />
        ) : (
          <label>
            {superAdmin
              ? t(
                  "Please select a valid company in the top right field to access its settings."
                )
              : t(
                  "Please login either as a shipper, a carrier, a broker, or a super admin to access this app."
                )}
          </label>
        )}
      </div>
    </div>
  );
};

export default CompanySelectionPage;
