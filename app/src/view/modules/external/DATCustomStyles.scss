$dat-grey-100: #e9ecf1;
$dat-grey-200: #c7cfd6;
$dat-grey-500: #636d79;
$dat-grey-900: #192129;
$dat-blue-50: #e9effe;
$dat-blue-500: #0046e0;
$dat-blue-700: #0032a8;
$dat-white: #fff;
​ #external-self-service-page-container {
  /* First set of selectors */
  font-family: "Sequel Sans", Helvetica, Arial, sans-serif;
  align-items: center;
}

#external-self-service-page-container *::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  margin-left: 6px;
}

#external-self-service-page-container *::-webkit-scrollbar-thumb {
  background: #c7cfd6;
  border-radius: 10px;
}

#external-self-service-page-container h1 {
  color: #192129;
  font-size: 24px;
  line-height: 28px;
  font-family: inherit;
  font-weight: 900;
}

#external-self-service-page-container button.elemental-button {
  border-radius: 100px;
  height: 36px;
  text-transform: uppercase;
  font-weight: normal;
  font-size: 12px;
  line-height: 14px;
  letter-spacing: 1px;
  padding: 10px 24px;
}

#external-self-service-page-container button.elemental-button:focus {
  box-shadow: none;
}

#external-self-service-page-container
  button.elemental-button.btn-solid-primary {
  background-color: #0046e0;
}

#external-self-service-page-container
  button.elemental-button.btn-solid-primary:hover {
  background-color: #0032a8;
}

#external-self-service-page-container
  button.elemental-button.btn-solid-tertiary {
  background-color: #ffffff;
  color: #0046e0;
  border: 1px solid #0046e0;
}

#external-self-service-page-container
  button.elemental-button.btn-solid-tertiary:hover {
  background-color: #e9effe;
}

#external-self-service-page-container div#company-summary-container {
  display: grid;
  grid-auto-flow: column;
  justify-content: start;
  align-items: center;
  border: 1px solid #e9ecf1;
  box-shadow: 5px 10px 15px 2px #e9ecf1;
}

#external-self-service-page-container div#company-summary-container img {
  border: none;
  content: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAA2CAYAAACBWxqaAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAKrSURBVHgB7ZrZjpswFIYPZsvWbKNUbS960171ru//Hr1qK3WR0iqaTDbWAFMOnYWAMRjjSYj4JCIFTgi/7fN7wcp9DLQYDQRYrTfg+UeQjWkYsJhPqNcICLBa76QLwPv/vd0UXheqAWQxn8J4NABZ7A42fP+1BMvxYNg3c9eFauAlsWwXHC9f260RgOzj2vCD4ORcqwQg250NYRQ9fU9yQCQRPd+PP+XlQBZ0/c3WgulkCCoh/wV8+7l8eBB+NFWl/gmtdyFE4YopAmsARcyno2cX+vD+XWNusrdccCkJ9/pmzIzBEr2ZjaAKKMKOnal1OZBFuB+gYegaKIrCHaOUt54cUgT0TD05RGOqcN1NCBMtC1a9aUipuFown8Rx89ZK4oZaJiCM7iFKdTaP6JrKFVMFKUWJ45YyG6XF8NjoI0wBtJuROlYhEaYALJE6YBMr+y0tRqlROFKakGno8SEeU4XrttFNPHTN0lQH1BRMAf4xyJ2rYnP+MaRaZFo4LUapYNFZpOSA6/lUG00LoMVgUptGgzaa9u1LRdJgzgBd17hjyKWMRg0d80QVjqnCddvo7d0hd27QN6Dfa6AHagimgJBihVFUvhaMqxxBkP/tcGAyY9BGsYB4kJIDnh9QbfRUQEC10UYFdDbKGdPZaBvpbJQFz6IxTnDqwD2lJByZ9uXrj0oi8OE/f/oIdeCe1PPyZjGHt/FRxHK1hrvtHuoifYVKUwmzeeB1EVrvQp2Ac9MJODedgHPz1A/8/rNKdoU0SRiGsN4ekslLEZbjxnFR8qq36B4sFNwvhBsq8JWl7XpwiZBkqtnLncfNH0p6wxOOW/aWwzVgOyco4CQHsMufTUZA1PakRu5JcQQ6Gw9bI4L6lMm7qrgm6o7RX5LCYsY1msmrAQwuaPZF4x/1SQ3Bl2JQDAAAAABJRU5ErkJggg==");
}

#external-self-service-page-container
  div#company-summary-container
  div#company-ids {
  font-size: 12px;
  line-height: 16px;
}

#external-self-service-page-container a {
  text-decoration: none;
  font-size: 12px;
  font-family: inherit;
  line-height: 16px;
  color: #0046e0;
}

#external-self-service-page-container
  div#external-self-service-page-container
  .elemental-Input {
  line-height: 16px;
}

#external-self-service-page-container
  div#external-self-service-page-container
  .elemental-Input
  input {
  line-height: 16px;
}

#external-self-service-page-container .elemental-Label {
  font-size: 12px;
  font-family: inherit;
  letter-spacing: 0.2px;
  color: #636d79;
}

#external-self-service-page-container div#integrations {
  max-height: 100%;
  height: auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 240px));
  row-gap: 12px;
  column-gap: 12px;
  padding: 0 10px;
  justify-content: center;
  margin-bottom: 14px;
}

#external-self-service-page-container div#integration-card {
  display: grid;
  height: auto;
  grid-template-rows: auto 1fr;
  border: 1px solid #e9ecf1;
  border-radius: 3px;
  margin-bottom: 0;
}

#external-self-service-page-container
  label#carrier-onboarding-progress-header-label {
  color: #0046e0;
  font-size: 10px;
  line-height: 16px;
  letter-spacing: 1.5px;
  text-transform: uppercase;
}

#external-self-service-page-container
  label#carrier-onboarding-account-creation-subtitle {
  font-size: 10px;
  line-height: 16px;
  letter-spacing: 1.5px;
  text-transform: uppercase;
  color: #636d79;
}

#external-self-service-page-container
  div#external-carrier-wizard-page-container {
  width: 100%;
  display: grid;
  grid-template-rows: 72px minmax(0, 1fr) 0;
  height: 100%;
  justify-items: center;
}

#external-self-service-page-container div#external-carrier-wizard-page-content {
  height: 100%;
  margin: 0;
}

#external-self-service-page-container div#external-eld-integrations-container {
  height: 100%;
  align-items: stretch;
  margin: 0;
  max-width: 800px;
}

#external-self-service-page-container
  div#external-eld-integrations-container
  label {
  letter-spacing: 1.5px;
  text-transform: uppercase;
  color: #636d79;
  font-size: 10px;
  line-height: 16px;
  margin-top: 10px;
}

#external-self-service-page-container
  #external-eld-integrations-container
  > div {
  height: 100%;
  justify-content: start;
}

#external-self-service-page-container button.flyout-menu-anchor svg:hover {
  background: none;
}

#external-self-service-page-container div#external-eld-integrations-right {
  background: none;
  display: grid;
  grid-template-columns: 1fr auto;
  column-gap: 12px;
}

#external-self-service-page-container
  div#external-eld-integrations-status-container {
  justify-content: space-between;
}

#external-self-service-page-container
  div#external-eld-integrations-status-container
  label#provider-name {
  color: #636d79;
  font-size: 12px;
  line-height: 16px;
  margin-bottom: 6px;
  margin-top: 6px;
  font-weight: normal;
}

#external-self-service-page-container
  div#external-eld-integrations-status-container
  .elemental-status-tag {
  text-transform: uppercase;
  font-size: 10px;
  line-height: 16px;
  border-radius: 5px;
  letter-spacing: 1px;
}

#external-self-service-page-container
  div#external-eld-integrations-status-container
  button {
  display: none;
}

#external-self-service-page-container
  div#eld-gps-integration-addition-container {
  height: 100%;
}

#external-self-service-page-container
  div#eld-gps-integration-addition-container
  div#provider-container {
  overflow-y: auto;
  display: unset;
}

#external-self-service-page-container
  div#eld-gps-integration-addition-container
  div#provider-container
  div#provider-credentials-form {
  max-height: unset;
  align-items: center;
  height: auto;
  margin-bottom: 10px;
}

#external-self-service-page-container
  div#eld-gps-integration-addition-container
  div#provider-container
  div#provider-credentials-form
  div#security-alert {
  width: auto;
  font-size: 14px;
  line-height: 20px;
}

#external-self-service-page-container
  div#eld-gps-integration-addition-container
  div#provider-container
  div#provider-credentials-form
  label#provider-credentials-form-label {
  font-size: 14px;
  line-height: 18px;
}

#external-self-service-page-container
  div#eld-gps-integration-addition-container
  h3#provider-selection-list-header {
  font-size: 10px;
  line-height: 16px;
  letter-spacing: 1.5px;
  text-transform: uppercase;
  font-weight: 600;
  align-self: start;
  padding-left: 10px;
}

#external-self-service-page-container
  div#eld-gps-integration-addition-container
  div#provider-instructions {
  width: 100%;
  max-height: unset;
}

#external-self-service-page-container div#providers-wrapper {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 240px));
  column-gap: 24px;
  align-items: stretch;
  padding: 0 10px;
  box-sizing: border-box;
  justify-content: center;
}

#external-self-service-page-container div#provider-selection {
  overflow: auto;
  padding-bottom: 0;
  justify-content: start;
}

#external-self-service-page-container div#provider-not-found {
  overflow: auto;
  padding-bottom: 0;
  justify-content: start;
}

#external-self-service-page-container
  div#provider-selection-provider-container {
  width: auto;
  height: auto;
  margin: 0;
  max-width: unset;
  position: relative;
  margin-bottom: 24px;
  cursor: pointer;
  border-color: #e9ecf1;
}

#external-self-service-page-container
  div#provider-selection-provider-container
  label {
  font-size: 12px;
  line-height: 16px;
  font-weight: normal;
  color: #636d79;
}

#external-self-service-page-container
  div#provider-selection-provider-container
  button {
  position: absolute;
  bottom: -20px;
  font-weight: normal;
  font-size: 12px;
  line-height: 14px;
  letter-spacing: 1px;
  text-transform: uppercase;
  height: 20px;
  background-color: #0046e0;
  box-sizing: border-box;
}

#external-self-service-page-container
  div#provider-selection-provider-container:hover {
  border: 1px solid #0046e0;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

#external-self-service-page-container label#location-provider-type-label {
  font-size: 14px;
}

.elemental-modal .elemental-modal-footer .elemental-button {
  border-radius: 100px;
  height: 36px;
  text-transform: uppercase;
  font-weight: normal;
  font-size: 12px;
  line-height: 14px;
  letter-spacing: 1px;
  padding: 10px 24px;
  align-self: stretch;
  justify-content: center;
}

.elemental-modal .elemental-modal-footer .elemental-button:focus {
  border-radius: 100px;
  box-shadow: none;
}

.elemental-modal .elemental-modal-footer .elemental-button.btn-solid-primary {
  background-color: #0046e0;
}

.elemental-modal
  .elemental-modal-footer
  .elemental-button.btn-solid-primary:hover {
  background-color: #0032a8;
}

.elemental-modal .elemental-modal-footer .elemental-button.btn-solid-secondary {
  background-color: #ffffff;
  color: #0046e0;
  border: 1px solid #0046e0;
}

.elemental-modal
  .elemental-modal-footer
  .elemental-button.btn-solid-secondary:hover {
  background-color: #e9effe;
}
