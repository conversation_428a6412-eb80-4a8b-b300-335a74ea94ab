import React, { useEffect } from "react";

import ExternalSelfServiceRouter from "router/external/ExternalSelfServiceRouter";

import styles from "./ExternalSelfServicePage.module.scss";

const ExternalSelfServicePage: React.FC = () => {
  const query = new URLSearchParams(window.location.search);
  // The customStyleName query param is to add the DAT custom styles to the page
  const customStyleName = query.get("customStyleName");
  const externalPageId = customStyleName
    ? "external-self-service-page-container"
    : "";

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/
  /*
   * Check query string param customStyleName and if yes
   * load DAT custom styles CSS
   */
  useEffect(() => {
    if (!customStyleName || customStyleName === "") {
      return;
    } else {
      require("./DATCustomStyles.scss");
    }
  }, []);

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div className={styles.pageContainer} id={externalPageId}>
      <ExternalSelfServiceRouter />
    </div>
  );
};

export default ExternalSelfServicePage;
