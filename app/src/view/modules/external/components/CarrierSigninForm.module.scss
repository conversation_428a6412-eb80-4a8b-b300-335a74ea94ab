@import "@fourkites/elemental-atoms/build/scss/colors/index";

.titleContainer {
  display: flex;
  flex:1;
  width: 100%;
  flex-direction: column;
  align-items: center;

  > h1 {
    display: flex;
    align-items: center;
    font-size: 28px;
    font-weight: 300;
    letter-spacing: 0;
    line-height: 36px;
    text-align: center;
  }

  > label {
    max-width: 520px;
    font-size: 18px;
    letter-spacing: 0;
    line-height: 32px;
    text-align: center;
  }
}

.accountFormContainer {
  justify-content: center;
  align-items: center;
  display: flex;
  flex-direction: column;

  > button {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-right: 16px;
    background-color: transparent;
    border: none;
    border-radius: 4px;

    &:hover {
      background-color: $color-neutral-100;
    }
  }
}

.accountForm {
  display: flex;
  width: 100%;
  flex-direction: column;
  margin-top: 24px;
  margin-bottom: 36px;
}

.accountFormRow {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
}

.accountFormInput {
  flex: 1;
  margin-top: 8px;
  width: 100%;

  > div {
    width: 100%;

    > div {
      display: flex;
      > input {
        width: 100%;
      }
    }
  }
}

.link {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;

  > a {
    cursor: pointer;
    text-decoration: underline;

    &:visited {
      color: $color-primary-500;
    }
  }
}
