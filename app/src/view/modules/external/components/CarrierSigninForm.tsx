import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import { Button, EyeIcon } from "@fourkites/elemental-atoms";
import { Input } from "@fourkites/elemental-input";

import { isFieldInvalid, isEmailInvalid } from "view/components/base/FormUtils";

import CompanySummary from "view/components/self-service/company-summary/CompanySummary";

import styles from "./CarrierSigninForm.module.scss";

const CarrierSigninForm = ({
  form,
  setForm,
  setIsNewUser,
  onLogin,
  setShowForgotPassword,
}: any) => {
  const { t } = useTranslation();

  return (
    <div>
      <div className={styles.titleContainer}>
        <h1>{t("Sign In to your FourKites Account")}</h1>
      </div>

      <div className={styles.summaryContainer}>
        <CompanySummary data={form.companyDetails} />
      </div>

      <SigninForm
        form={form}
        setForm={setForm}
        setIsNewUser={setIsNewUser}
        setShowForgotPassword={setShowForgotPassword}
        onLogin={onLogin}
      />
    </div>
  );
};

const SigninForm = ({
  form,
  setForm,
  setIsNewUser,
  onLogin,
  setShowForgotPassword,
}: any) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [password, setPassword] = useState<string>("");
  const [confirmed, setConfirmed] = useState<boolean>(false);
  const [revealPassword, setRevealPassword] = useState<boolean>(false);

  const isFormInvalid = isEmailInvalid(form?.email) || isFieldInvalid(password);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const onConfirm = () => {
    setConfirmed(true);
    if (isFormInvalid) {
      return;
    }

    onLogin(form?.email, password);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div className={styles.accountFormContainer}>
      <div className={styles.accountForm}>
        <div className={styles.accountFormRow}>
          <div className={styles.accountFormInput}>
            <Input
              label={`${t("Business Email")}`}
              errorLabel={t("Please add a valid email")}
              value={form?.email}
              invalid={confirmed && isEmailInvalid(form?.email)}
              onChange={(e: any) => setForm("email", e.target.value)}
              required
            />
          </div>
        </div>

        {/*
          NOTE: we are removing this for INFOSEC concerns but not completely,
          since this flow is still under evaluation. This code can be delted
          after external onboarding is in production usage.

        <span className={styles.link}>
          <a onClick={() => setIsNewUser(true)}>
            {t("Not You? Create New Account")}
          </a>
        </span>
        */}

        <div className={styles.accountFormRow}>
          <div className={styles.accountFormInput}>
            <Input
              icon={<EyeIcon />}
              label={`${t("Password")}`}
              errorLabel={"Field is required"}
              type={revealPassword ? "text" : "password"}
              value={password}
              onChange={(e: any) => setPassword(e.target.value)}
              onIconClick={() => setRevealPassword(!revealPassword)}
              invalid={confirmed && isFieldInvalid(password)}
              required
            />
          </div>
        </div>

        <span className={styles.link}>
          <a onClick={() => setShowForgotPassword(true)}>
            {t("Forgot your Password?")}
          </a>
        </span>
      </div>

      <Button size="large" onClick={onConfirm}>
        {t("Sign In")}
      </Button>
    </div>
  );
};

export default CarrierSigninForm;
