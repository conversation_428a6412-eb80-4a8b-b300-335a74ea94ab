import { useEffect } from "react";

import {
  isFieldInvalid,
  isEmailInvalid,
  isUrlInvalid,
} from "view/components/base/FormUtils";

/*******************************************************************************
 * ERROR HANDLING CONTANTS AND FUNCTIONS
 ******************************************************************************/

// 5 seconds
const REDIRECT_TIMEOUT = 2000;

// 15 minutes
export const ERROR_TIMEOUT = 15 * 60 * 60 * 1000;

const ERROR_TYPES = {
  MISSING_PARAMETERS: "MISSING_PARAMETERS",
  INVALID_REDIRECT_URL: "INVALID_REDIRECT_URL",
  CARRIER_NOT_FOUND: "CARRIER_NOT_FOUND",
  INVALID_EMAIL: "INVALID_EMAIL",
  INVALID_TOKEN: "INVALID_TOKEN",
  SERVER_ERROR: "SERVER_ERROR",
  PROCESS_NOT_FINISHED_TIMEOUT: "PROCESS_NOT_FINISHED_TIMEOUT",
};

export const ERROR_TITLES = {
  MISSING_PARAMETERS: "Incomplete Request",
  INVALID_REDIRECT_URL: "Invalid Redirect",
  CARRIER_NOT_FOUND: "Carrier DOT/MC Number Not Found in FMCSA Database",
  INVALID_EMAIL: "Invalid Email",
  INVALID_TOKEN: "Invalid or Expired Token",
  SERVER_ERROR: "Server Error",
  PROCESS_NOT_FINISHED_TIMEOUT: "User Onboarding Timed Out",
};

export const ERROR_MESSAGES = {
  INVALID_REDIRECT_URL: "You need to pass a valid redirect URL",
  MISSING_PARAMETERS:
    "You need to provide all parameters required for this page, which are: " +
    "token, carrierUsdot or carrierMcnumber, userEmail, redirectUrl, redirectUrlError",
  CARRIER_NOT_FOUND:
    "The carrierUsdot or carrierMcnumber you passed don't match any carrier",
  INVALID_EMAIL: "You need to provide a valid userEmail",
  INVALID_TOKEN: "Your token is invalid, or has expired",
  SERVER_ERROR: "A server error has occurred, please try again later",
  PROCESS_NOT_FINISHED_TIMEOUT:
    "User took more than 15 minutes to complete process",
};

/*
 * Notifies external app that an error has occurred process
 */
function sendErrorToParent(error: string, message: string) {
  const query = new URLSearchParams(window.location.search);
  const redirectUrlError = query.get("redirectUrlError");

  if (!redirectUrlError || redirectUrlError === "") {
    return;
  }

  window.setTimeout(function () {
    const queryParamIndex = hasQueryParam(redirectUrlError);
    queryParamIndex !== -1
      ? window.location.replace(
          `${redirectUrlError}&error=${escape(error)}&message=${escape(
            message
          )}`
        )
      : window.location.replace(
          `${redirectUrlError}?error=${escape(error)}&message=${escape(
            message
          )}`
        );
  }, REDIRECT_TIMEOUT);
}

/*
 * Function to check url contains query params
 */
function hasQueryParam(url: string) {
  return url.indexOf("?");
}

/*
 * Notifies external app that user has finished process
 */
export function sendSuccessMessageToParent() {
  const query = new URLSearchParams(window.location.search);
  const redirectUrl = query.get("redirectUrl");

  if (!redirectUrl || redirectUrl === "") {
    return;
  }

  window.location.replace(
    `${redirectUrl}?event=${escape("SUCCESS")}&message=${escape(
      "User finished adding integrations"
    )}`
  );
}

/*******************************************************************************
 * ERROR HANDLING HOOKS
 ******************************************************************************/

/*
 * Validates all parameters and get user details if parameters are valid
 */
export const useTimeoutError = (callback?: any) => {
  useEffect(() => {
    let errorTimer = setTimeout(() => {
      if (callback) {
        callback();
      }

      sendErrorToParent(
        ERROR_TYPES.PROCESS_NOT_FINISHED_TIMEOUT,
        ERROR_MESSAGES.PROCESS_NOT_FINISHED_TIMEOUT
      );
    }, ERROR_TIMEOUT);

    // this will clear the timeout when component unmount like in
    // willComponentUnmount
    return () => {
      clearTimeout(errorTimer);
    };
  }, []);
};

/*******************************************************************************
 * ERROR HANDLING FUNCTIONS
 ******************************************************************************/

export const validateTokenAndServerResponseError = (serverResponse: any) => {
  if (serverResponse?.error?.message?.includes("401")) {
    const hasError = true;
    sendErrorToParent(ERROR_TYPES.INVALID_TOKEN, ERROR_MESSAGES.INVALID_TOKEN);
    return {
      hasError: hasError,
      title: ERROR_TITLES.INVALID_TOKEN,
      message: ERROR_MESSAGES.INVALID_TOKEN,
    };
  }

  sendErrorToParent(ERROR_TYPES.SERVER_ERROR, ERROR_MESSAGES.SERVER_ERROR);
  return {
    hasError: true,
    title: ERROR_TITLES.SERVER_ERROR,
    message: ERROR_MESSAGES.SERVER_ERROR,
  };
};

export const validateCarrierAndSendError = () => {
  const hasError = true;
  sendErrorToParent(
    ERROR_TYPES.CARRIER_NOT_FOUND,
    ERROR_MESSAGES.CARRIER_NOT_FOUND
  );

  return {
    hasError: hasError,
    title: ERROR_TITLES.CARRIER_NOT_FOUND,
    message: ERROR_MESSAGES.CARRIER_NOT_FOUND,
  };
};

export const validateOnboardingParametersAndSendError = (
  token: string,
  userEmail: string,
  carrierUsdot: string,
  carrierMcnumber: string,
  redirectUrl: string
) => {
  let errorParams = {
    hasError: false,
    title: "",
    message: "",
  };

  switch (true) {
    // Invalid URL error
    case isUrlInvalid(redirectUrl || ""):
      sendErrorToParent(
        ERROR_TYPES.INVALID_REDIRECT_URL,
        ERROR_MESSAGES.INVALID_REDIRECT_URL
      );
      errorParams = setExternalErrorAttributes(
        true,
        ERROR_TITLES.INVALID_REDIRECT_URL,
        ERROR_MESSAGES.INVALID_REDIRECT_URL
      );
      break;
    // Invalid email error
    case isEmailInvalid(userEmail || ""):
      sendErrorToParent(
        ERROR_TYPES.INVALID_EMAIL,
        ERROR_MESSAGES.INVALID_EMAIL
      );
      errorParams = setExternalErrorAttributes(
        true,
        ERROR_TITLES.INVALID_EMAIL,
        ERROR_MESSAGES.INVALID_EMAIL
      );
      break;
    // Carrier dot or  MC number error
    case isFieldInvalid(carrierUsdot) && isFieldInvalid(carrierMcnumber):
      errorParams = setExternalErrorAttributes(
        true,
        ERROR_TITLES.CARRIER_NOT_FOUND,
        ERROR_MESSAGES.CARRIER_NOT_FOUND
      );
      sendErrorToParent(
        ERROR_TYPES.CARRIER_NOT_FOUND,
        ERROR_MESSAGES.CARRIER_NOT_FOUND
      );
      break;
    // Missing parameters error
    case isFieldInvalid(token) ||
      isEmailInvalid(userEmail) ||
      (isFieldInvalid(carrierUsdot) && isFieldInvalid(carrierMcnumber)) ||
      isFieldInvalid(redirectUrl):
      errorParams = setExternalErrorAttributes(
        true,
        ERROR_TITLES.MISSING_PARAMETERS,
        ERROR_MESSAGES.MISSING_PARAMETERS
      );
      sendErrorToParent(
        ERROR_TYPES.MISSING_PARAMETERS,
        ERROR_MESSAGES.MISSING_PARAMETERS
      );
      break;
  }

  return errorParams;
};

export const validateWizardParametersAndSendError = (
  token: string,
  redirectUrl: string
) => {
  let errorParams = {
    hasError: false,
    title: "",
    message: "",
  };

  // Invalid URL error
  if (isUrlInvalid(redirectUrl || "")) {
    sendErrorToParent(
      ERROR_TYPES.INVALID_REDIRECT_URL,
      ERROR_MESSAGES.INVALID_REDIRECT_URL
    );
    errorParams = setExternalErrorAttributes(
      true,
      ERROR_TITLES.INVALID_REDIRECT_URL,
      ERROR_MESSAGES.INVALID_REDIRECT_URL
    );

    return errorParams;
  }

  // Invalid Token error
  if (isFieldInvalid(token)) {
    sendErrorToParent(ERROR_TYPES.INVALID_TOKEN, ERROR_MESSAGES.INVALID_TOKEN);
    errorParams = setExternalErrorAttributes(
      true,
      ERROR_TITLES.INVALID_TOKEN,
      ERROR_MESSAGES.INVALID_TOKEN
    );

    return errorParams;
  }

  // Missing parameters error
  const missingParameters =
    isFieldInvalid(token) || isFieldInvalid(redirectUrl);

  if (missingParameters) {
    sendErrorToParent(
      ERROR_TYPES.MISSING_PARAMETERS,
      ERROR_MESSAGES.MISSING_PARAMETERS
    );
    errorParams = setExternalErrorAttributes(
      true,
      ERROR_TITLES.MISSING_PARAMETERS,
      ERROR_MESSAGES.MISSING_PARAMETERS
    );

    return errorParams;
  }

  return errorParams;
};

const setExternalErrorAttributes = (
  hasError: boolean,
  errorTitle: string,
  errorMessage: string
) => {
  return {
    hasError: hasError,
    title: errorTitle,
    message: errorMessage,
  };
};
