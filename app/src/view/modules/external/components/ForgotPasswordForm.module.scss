@import "@fourkites/elemental-atoms/build/scss/colors/index";

.content {
  display: flex;
  flex: 1 1;
  width: 100%;
  flex-direction: column;
  align-items: center;

  .heading {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    h1 {
      font-size: 24px;
      font-weight: 300;
      letter-spacing: 0;
      text-align: center;
      font-weight: 600;
      margin: 0;
    }

    span {
      color: $color-neutral-700;
      font-size: 14px;
      margin: 10px;
      flex-wrap: wrap;
      justify-content: center;
    }
  }

  .forgotPasswordFormContainer {
    justify-content: center;
    align-items: center;
    display: flex;
    flex-direction: column;

    .forgotPasswordForm {
      display: flex;
      width: 100%;
      flex-direction: column;
      margin-top: 24px;
      margin-bottom: 36px;

      .forgotPasswordFormRow {
        display: flex;
        width: 100%;
        flex-wrap: wrap;

        .forgotPasswordFormInput {
          margin-top: 8px;
          width: 100%;
          display: flex;
          justify-content: center;

          > div {
            width: 100%;
            > div {
              display: flex;
              > input {
                width: 100%;
              }
            }
          }

          img {
            width: 150px;
          }
        }
      }
    }

    .resetButton {
      width: 100%;
    }

    .signInButton {
      width: 70%;
    }
  }
}

.content {
  display: flex;
  align-items: center;
  flex-direction: column;
  margin-top: 20px;

  .instruction {
    color: $color-neutral-800;
    font-size: 16px;
    display: flex;
    flex-wrap: wrap;
    margin-top: 5px;
    text-align: center;
  }
}
