import React from "react";
import { useTranslation } from "react-i18next";

import { Input } from "@fourkites/elemental-input";
import { Button } from "@fourkites/elemental-atoms";

import passwordSuccess from "assets/img/passwordSuccess.png";
import passwordError from "assets/img/passwordError.png";

import { useAppDispatch } from "state/hooks";
import UsersState from "state/modules/Users";

import { isEmailInvalid } from "view/components/base/FormUtils";

import styles from "./ForgotPasswordForm.module.scss";

const ForgotPasswordForm = ({
  showForgotPassword,
  showResetPassword,
  forgotPasswordError,
  setShowForgotPassword,
  setShowResetPassword,
  setForgotPasswordError,
  form,
  setForm,
}: any) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const isFormInvalid = isEmailInvalid(form?.email);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/
  const resetsPassword = async () => {
    if (isFormInvalid) {
      return;
    }

    const status = await dispatch(
      UsersState.actionCreators.passwordReset(form?.email)
    );

    setShowForgotPassword(false);
    if (status !== 200) {
      setForgotPasswordError(true);
    } else {
      setShowResetPassword(true);
    }
  };

  const backToSignIn = () => {
    setShowResetPassword(false);
    setForgotPasswordError(false);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div className={styles.content}>
      <div>
        <div className={styles.heading}>
          <h1>{t("Forgot Your Password?")}</h1>
        </div>

        {showForgotPassword && (
          <ForgotPasswordContent
            form={form}
            setForm={setForm}
            resetsPassword={resetsPassword}
          />
        )}

        {showResetPassword && (
          <ResetPasswordContent backToSignIn={backToSignIn} />
        )}

        {forgotPasswordError && (
          <ForgotPasswordInvalidEmail backToSignIn={backToSignIn} />
        )}
      </div>
    </div>
  );
};

export default ForgotPasswordForm;

const ForgotPasswordContent = ({ form, setForm, resetsPassword }: any) => {
  const { t } = useTranslation();

  return (
    <>
      <div className={styles.heading}>
        <span>
          {t("We will fix this for you. Please provide your email address.")}
        </span>
      </div>
      <div className={styles.forgotPasswordFormContainer}>
        <div className={styles.forgotPasswordForm}>
          <div className={styles.forgotPasswordFormRow}>
            <div className={styles.forgotPasswordFormInput}>
              <Input
                label={`${t("Email")}`}
                errorLabel={t("Please add a valid email")}
                value={form?.email}
                invalid={isEmailInvalid(form?.email)}
                onChange={(e: any) => setForm("email", e.target.value)}
                required
              />
            </div>
          </div>
        </div>
        <ResetButton resetsPassword={resetsPassword} />
      </div>
    </>
  );
};

const ResetPasswordContent = ({ backToSignIn }: any) => {
  const { t } = useTranslation();

  return (
    <>
      <div className={styles.forgotPasswordFormContainer}>
        <div className={styles.forgotPasswordForm}>
          <div className={styles.forgotPasswordFormRow}>
            <div className={styles.forgotPasswordFormInput}>
              <img
                className={styles.logo}
                src={passwordSuccess}
                alt="Password Reset Success"
              />
            </div>
            <div className={styles.content}>
              <label className={styles.instruction}>
                {t(
                  "An email to reset your password has been sent to your email address."
                )}
                <br />
                {t(
                  "Please follow the instructions in the email to reset your password, "
                )}
                <br />
                {t("return to this screen, and press ‘Return to Sign In’.")}
              </label>
            </div>
          </div>
        </div>
        <SignInButton backToSignIn={backToSignIn} />
      </div>
    </>
  );
};

const ForgotPasswordInvalidEmail = ({ backToSignIn }: any) => {
  const { t } = useTranslation();

  return (
    <>
      <div className={styles.forgotPasswordFormContainer}>
        <div className={styles.forgotPasswordForm}>
          <div className={styles.forgotPasswordFormRow}>
            <div className={styles.forgotPasswordFormInput}>
              <img
                className={styles.logo}
                src={passwordError}
                alt="Password Reset Error"
              />
            </div>
            <div className={styles.content}>
              <label className={styles.instruction}>
                {t(
                  "The email address you provided is not registered with FourKites."
                )}
                <br />
                {t(
                  "Please input a new email address, or go back to create a new account."
                )}
              </label>
            </div>
          </div>
        </div>
        <SignInButton backToSignIn={backToSignIn} />
      </div>
    </>
  );
};

const SignInButton = ({ backToSignIn }: any) => {
  const { t } = useTranslation();

  return (
    <Button size="large" className={styles.signInButton} onClick={backToSignIn}>
      {t("Return to Sign In")}
    </Button>
  );
};

const ResetButton = ({ resetsPassword }: any) => {
  const { t } = useTranslation();

  return (
    <Button
      size="large"
      className={styles.resetButton}
      onClick={resetsPassword}
    >
      {t("Reset")}
    </Button>
  );
};
