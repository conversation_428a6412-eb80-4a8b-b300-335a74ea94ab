import React from "react";
import { useTranslation } from "react-i18next";

import { ColoredFKLogo } from "@fourkites/elemental-atoms";

import { fourkitesUrls } from "api/http/apiUtils";

import styles from "./InvalidParameters.module.scss";

export const InvalidParameters = ({}: any) => {
  const { t } = useTranslation();
  return (
    <div className={styles.invalidContainer}>
      <ColoredFKLogo />
      <h1>{t("Invalid company")}</h1>
      <label>
        {t(
          "Seems like your company details are invalid or we couldn't find the company you are looking for."
        )}
        <br />
        {t("You need a valid email and a valid USDOT# or MC#.")}
        <br />
        {t("Please ")}{" "}
        <a href={fourkitesUrls.help} target="_blank">
          {t("contact support.")}
        </a>
      </label>
    </div>
  );
};

export const ExternalErrorMessage = ({ isSignupError = false }: any) => {
  const { t } = useTranslation();

  const errorTitle = isSignupError
    ? t("Oops! Signup can't be completed")
    : t("Oops! Something went wrong!");

  const errorMessage = isSignupError
    ? t(
        "For some reason the signup process cannot be completed. " +
          "We have informed our support team to look into it and " +
          "we will reach back to you soon. " +
          "Meanwhile, please explore more about offerings from FourKites "
      )
    : t(
        "For some reason the request cannot be completed. " +
          "We have informed our support team to look into it and " +
          "we will reach back to you soon. " +
          "Meanwhile, please explore more about offerings from FourKites "
      );

  return (
    <div className={styles.invalidContainer}>
      <ColoredFKLogo />
      <h1>{errorTitle}</h1>
      <label>
        {errorMessage}
        <a href={fourkitesUrls.carrierOfferings} target="_blank">
          {t("here.")}
        </a>
      </label>
    </div>
  );
};

export const ExternalErrorDisplay = ({ errorTitle, errorMessage }: any) => {
  const { t } = useTranslation();

  return (
    <div className={styles.invalidContainer}>
      <ColoredFKLogo />
      <h1>{errorTitle}</h1>
      <label>{errorMessage}</label>
    </div>
  );
};
