@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  padding: 16px;
}

.content {
  display: flex;
  flex:1;
  width: 100%;
  flex-direction: column;
  align-items: center;

  > div[id="header"] {
    display: flex;
    width: 100%;
    flex-direction: column;
    align-items: center;

    > div {
      display: flex;
      align-items: center;
      margin-top: 24px;
    }
  }

  > div[id="invalid"] {
    display: flex;
    width: 100%;
    flex-direction: column;
    align-items: center;
    margin-bottom:24px;

    > h1 {
      font-size: 28px;
      font-weight: 300;
      letter-spacing: 0;
      line-height: 42px;
      text-align: center;
      font-weight: 600;
    }

    > label {
      max-width: 520px;
      font-size: 18px;
      letter-spacing: 0;
      line-height: 32px;
      text-align: center;
    }

  }
}

.loader{
  display: flex;
  height: 100%;
  width: 100%;
  align-items: center;
  justify-content: center;
}

.headerStep {
  margin-left: 11px;
  margin-right: 11px;
  height: 8px;
  width: 8px;
  border-radius: 4px;
  background-color: $color-neutral-300;
}

.headerStepSelected {
  composes: headerStep;
  height: 12px;
  width: 12px;
  border-radius: 6px;
  background-color: $color-primary-500;
}

.signupWrapper {
  margin-bottom: 16px;

  > div > div {

    > div > div[id="company-summary-container"] {
      border: 1px solid $color-neutral-400;
      border-radius: 4px;
      padding: 12px;
      width: 396px;

      > img {
        height: 48px;
        width: 48px;
        margin-right: 12px;
      }

      > div > div[id="company-name"] {

        > h5 {
          font-size: 16px;
        }

      }

    }

  }
}

.signupWrapperMobile {
  composes: signupWrapper;

  > div > div {

    > h1 {
      font-size: 24px;
      line-height: 24px;
    }

    > div[id="password-alert"] {
      padding-left: 16px;
      padding-right: 16px;
      width: calc(100% + -32px);
    }

    > div {
      width: 100%;

      > div[id="company-summary-container"] {
        display: flex;
        padding: 6px;
        width: calc(100% + -12px);

        > img {
          height: 32px;
          width: 32px;
          margin-right: 6px;
        }

      }
    }

    // Password helper
    > div > div > div > div[id="password-validation-menu"] {
      margin-top: -12px;
      position: relative !important;
      top: 0 !important;
      left: 0 !important;
      width: 100%;
      border-radius: 0;
      box-shadow: none;
    }

  }

}

.signinWrapper {
  margin-bottom: 16px;

  > div > div {

    > div[id="company-summary-container"] {
      border: 1px solid $color-neutral-400;
      border-radius: 4px;
      padding: 12px;
      width: 396px;
      margin-top: 12px;

      > img {
        height: 48px;
        width: 48px;
        margin-right: 12px;
      }

      > div > div[id="company-name"] {
        > h5 {
          font-size: 16px;
        }
      }

    }
  }
}

.signinWrapperMobile {
  composes: signinWrapper;

  > div > div {

    > h1 {
      font-size: 24px;
      line-height: 24px;
    }

    > div[id="company-summary-container"] {
      padding: 6px;
      width: calc(100% + -12px);

      > img {
        height: 32px;
        width: 32px;
        margin-right: 6px;
      }

    }

  }
}
