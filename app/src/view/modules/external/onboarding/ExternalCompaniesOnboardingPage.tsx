import React, { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useMediaQuery } from "react-responsive";

import { Spinner } from "@fourkites/elemental-loading-indicator";

import { useAppSelector, useAppDispatch, useAnalytics } from "state/hooks";
import {
  ExternalOnboardingState,
  signupUser,
  isUserAuthenticated,
  loginUser,
} from "state/modules/external/ExternalOnboarding";

import { showToast } from "view/components/base/toast/Toast";

import CarrierOnboardingProgressHeader from "view/components/self-service/onboarding/headers/CarrierOnboardingProgressHeader";
import CarrierOnboardingAccountCreation from "view/components/self-service/onboarding/account-creation/CarrierOnboardingAccountCreation";
import {
  isFieldInvalid,
  isEmailInvalid,
  getFirstName,
  getLastName,
} from "view/components/base/FormUtils";

import CarrierSigninForm from "../components/CarrierSigninForm";
import {
  InvalidParameters,
  ExternalErrorMessage,
  ExternalErrorDisplay,
} from "../components/InvalidParameters";
import {
  useTimeoutError,
  validateTokenAndServerResponseError,
  validateOnboardingParametersAndSendError,
  validateCarrierAndSendError,
  ERROR_TITLES,
  ERROR_MESSAGES,
} from "../components/ErrorHandling";
import ForgotPasswordForm from "../components/ForgotPasswordForm";

import styles from "./ExternalCompaniesOnboardingPage.module.scss";

const ExternalCompaniesOnboardingPage: React.FC = () => {
  const { t } = useTranslation();

  const location = useLocation();
  const dispatch = useAppDispatch();

  const isMobile = useMediaQuery({ maxWidth: 720 });

  // Initializing pendo
  useAnalytics({ id: "" });

  // User will have a set amount of time to perform all his operations
  useTimeoutError(() => {
    if (!hasError) {
      updateErrorParameters(
        true,
        ERROR_TITLES.PROCESS_NOT_FINISHED_TIMEOUT,
        ERROR_MESSAGES.PROCESS_NOT_FINISHED_TIMEOUT
      );
    }
  });

  /*****************************************************************************
   * REDUX
   ****************************************************************************/

  const selectors = ExternalOnboardingState.selectors;
  const carrierDetails = useAppSelector(selectors.carrierDetails());
  const carrierIsLoading = useAppSelector(selectors.carrierIsLoading());
  const carrierIsInvalid = useAppSelector(selectors.carrierIsInvalid());

  const userDetails = useAppSelector(selectors.userDetails());
  const userIsLoading = useAppSelector(selectors.userIsLoading());

  const isLoading = carrierIsLoading || userIsLoading;
  // If user exists, shows signing page; if not, shows signup page
  const userExists = userDetails != null;

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [isNewUser, setIsNewUser] = useState<boolean>(false);
  const [showForgotPassword, setShowForgotPassword] = useState<boolean>(false);
  const [showResetPassword, setShowResetPassword] = useState<boolean>(false);
  const [forgotPasswordError, setForgotPasswordError] =
    useState<boolean>(false);
  const [hasSignupError, setHasSignupError] = useState<boolean>(false);
  const [hasGeneralError, setHasGeneralError] = useState<boolean>(false);
  const [hasError, setError] = useState<boolean>(false);
  const [errorTitle, setErrorTitle] = useState<string>("");
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [form, setForm] = useState<any>({
    type: "carrier",
    region: "US & Canada",
    companyDetails: null,
    email: "",
  });

  /*****************************************************************************
   * URL PARAMETERS AND VALIDATION
   ****************************************************************************/

  // Get external parameters from URL
  const query = new URLSearchParams(location.search);
  const token = query.get("token");
  const carrierUsdot = query.get("carrierUsdot");
  const carrierMcnumber = query.get("carrierMcnumber");
  const userEmail = query.get("userEmail");
  const redirectUrl = query.get("redirectUrl");

  const invalidParameters =
    isFieldInvalid(token || "") ||
    isEmailInvalid(userEmail || "") ||
    (isFieldInvalid(carrierUsdot || "") &&
      isFieldInvalid(carrierMcnumber || "")) ||
    isFieldInvalid(redirectUrl || "");

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  const checkIfCarrierUserExists = async (carrierId: string) => {
    const response = await dispatch(
      ExternalOnboardingState.actions.getUserDetails({
        m2mToken: token || "",
        carrierId: carrierId,
        userEmail: userEmail || "",
      })
    );
  };

  const [isCheckingAuthentication, setIsCheckingAuthentication] =
    useState<boolean>(true);

  /*
   * Checks whether user is already authenticated or not. If authenticated,
   * redirects to page
   */
  useEffect(() => {
    async function checkAuthentication() {
      if (isCheckingAuthentication) {
        const authenticated = await isUserAuthenticated();

        // If alreay auhtenticated, redirects to DAT page
        if (authenticated) {
          // If authenticated, logs user in
          loginUser(redirectUrl || "#");
        } else {
          // If not, remove loader and display signup page
          setIsCheckingAuthentication(false);
        }
      }
    }

    checkAuthentication();
  }, []);

  /*
   * Validates all parameters and get user details if parameters are valid
   */
  useEffect(() => {
    if (invalidParameters) {
      const { hasError, title, message } =
        validateOnboardingParametersAndSendError(
          token || "",
          userEmail || "",
          carrierUsdot || "",
          carrierMcnumber || "",
          redirectUrl || ""
        );
      updateErrorParameters(hasError, title, message);
      return;
    }

    dispatch(
      ExternalOnboardingState.actions.getCarrierDetails({
        m2mToken: token || "",
        usdot: carrierUsdot || undefined,
        mcNumber: carrierMcnumber || undefined,
      })
    ).then((response) => {
      if ("error" in response) {
        setHasGeneralError(true);

        const statusError = response?.error?.message;

        // Carrier not found error
        if (statusError?.includes("404")) {
          const { hasError, title, message } = validateCarrierAndSendError();
          updateErrorParameters(hasError, title, message);
          return;
        }

        const { hasError, title, message } =
          validateTokenAndServerResponseError(response);
        updateErrorParameters(hasError, title, message);
        return;
      }

      // TODO: change when the method is not a list anymore
      const carrierId = response?.payload[0].permalink;
      if (!carrierId) {
        return;
      }

      checkIfCarrierUserExists(carrierId);
    });
  }, [
    invalidParameters,
    token,
    carrierUsdot,
    carrierMcnumber,
    userEmail,
    redirectUrl,
  ]);

  /*
   * Updates carrier form when URL parameters change
   */
  useEffect(() => {
    setForm({
      ...form,
      email: userEmail,
      companyDetails: {
        permalink: carrierDetails?.permalink,
        name: carrierDetails?.name,
        identifications: carrierDetails?.identifications,
        address: carrierDetails?.address,
      },
    });
  }, [location, carrierDetails]);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  /*
   * Method to check and set the error params
   */
  const updateErrorParameters = (
    hasError: boolean,
    title: string,
    message: string
  ) => {
    if (hasError) {
      setError(true);
      setErrorTitle(title);
      setErrorMessage(message);
    } else {
      setError(false);
      setErrorTitle("");
      setErrorMessage("");
    }
  };

  /*
   * Helper to control form
   */
  const onChangeFormField = async (fieldName: string, fieldValue: string) => {
    setForm({ ...form, [fieldName]: fieldValue });
  };

  /*
   * Handler to complete onboarding, creating carrier and user depending on
   * proper conditions
   */
  const onCompleteOnboarding = async () => {
    const companyDetails = form?.companyDetails;
    let carrierPermalink = companyDetails?.permalink;
    const shouldCreateCompany = carrierPermalink == null;

    // STEP 1 - Creates company in case it's not onboarded and don't have admin
    if (shouldCreateCompany) {
      const createCarrierResponse = await dispatch(
        ExternalOnboardingState.actions.createCarrier({
          m2mToken: token || "",
          carrier: {
            //TODO: add modes in the future
            modes: ["ftl"],
            types: ["carrier"],
            region: "US & Canada",
            name: companyDetails?.name,
            identifications: companyDetails?.identifications,
            address: companyDetails?.address || null,
          },
        })
      );

      if ("error" in createCarrierResponse) {
        setHasGeneralError(true);
        const { hasError, title, message } =
          validateTokenAndServerResponseError(createCarrierResponse);
        updateErrorParameters(hasError, title, message);
        return;
      }

      // Updates permalink from created company
      carrierPermalink = createCarrierResponse?.payload?.permalink;
    }

    // STEP 2 - Creates user in case it will be admin
    const createUserResponse = await dispatch(
      ExternalOnboardingState.actions.createCarrierUser({
        m2mToken: token || "",
        carrierId: carrierPermalink,
        user: {
          firstName: getFirstName(form?.fullName),
          lastName: getLastName(form?.fullName, ""),
          email: form?.email,
        },
        password: form?.password,
        passwordConfirmation: form?.passwordConfirmation,
        role: "admin",
      })
    );

    if ("error" in createUserResponse) {
      // Handle edge case where email already exists for user, when the error is
      // an user conflict
      if (createUserResponse?.error?.message?.includes("409")) {
        setIsNewUser(false);

        showToast(
          t("User already exists"),
          t(
            "Looks like you already have an account with FourKites. Please Sign In."
          ),
          "error"
        );

        return;
      }

      // If not, handler error case normally
      setHasSignupError(true);
      const { hasError, title, message } =
        validateTokenAndServerResponseError(createUserResponse);
      updateErrorParameters(hasError, title, message);
      return;
    }

    // STEP 3 - REDIRECTS TO KEYCLOAK TO SIGN UP AND GET USER GRANTED TOKEN
    await signupUser(redirectUrl || "", form?.email, form?.password, () => {});
  };

  /*
   * Handler to complete login if user already exists
   */
  const onLogin = async (email: string, password: string) => {
    await signupUser(redirectUrl || "", email, password, () => {});
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const signupWrapperClass = isMobile
    ? styles.signupWrapperMobile
    : styles.signupWrapper;
  const signinWrapperClass = isMobile
    ? styles.signinWrapperMobile
    : styles.signinWrapper;

  if (isLoading || isCheckingAuthentication) {
    return (
      <div className={styles.loader}>
        <Spinner isLoading size="medium" />
      </div>
    );
  }

  if (hasError || carrierIsInvalid) {
    return (
      <ExternalErrorDisplay
        errorTitle={errorTitle}
        errorMessage={errorMessage}
      />
    );
  }

  // Show invalid parameters if not keycloack
  if (invalidParameters) {
    //TODO: different types of error
    return <InvalidParameters />;
  }

  // Onboarding Error
  if (hasSignupError || hasGeneralError) {
    return (
      <ExternalErrorMessage
        isSignupError={hasSignupError && !hasGeneralError}
      />
    );
  }

  if (showForgotPassword || showResetPassword || forgotPasswordError) {
    return (
      <div className={styles.container}>
        <ForgotPasswordForm
          showForgotPassword={showForgotPassword}
          showResetPassword={showResetPassword}
          forgotPasswordError={forgotPasswordError}
          setShowForgotPassword={setShowForgotPassword}
          setShowResetPassword={setShowResetPassword}
          setForgotPasswordError={setForgotPasswordError}
          form={form}
          setForm={onChangeFormField}
        />
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <CarrierOnboardingProgressHeader
        onboardingStep={1}
        showLogo={false}
        showLabel={true}
      />
      <div className={styles.content}>
        {userExists && !isNewUser ? (
          <div className={signinWrapperClass}>
            <CarrierSigninForm
              form={form}
              setIsNewUser={setIsNewUser}
              setForm={onChangeFormField}
              setShowForgotPassword={setShowForgotPassword}
              onLogin={onLogin}
            />
          </div>
        ) : (
          <div className={signupWrapperClass}>
            <CarrierOnboardingAccountCreation
              form={form}
              setForm={onChangeFormField}
              onConfirm={onCompleteOnboarding}
              isExternallyUsed
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default ExternalCompaniesOnboardingPage;
