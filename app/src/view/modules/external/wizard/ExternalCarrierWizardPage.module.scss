@import "@fourkites/elemental-atoms/build/scss/colors/index";

////////////////////////////////////////////////////////////////////////////////
// DESKTOP STYLES
////////////////////////////////////////////////////////////////////////////////

.container {
  display: flex;
  width: 100%;
  height: calc(100vh);
  align-items: center;
  flex-direction: column;

  > div[id="invalid"] {
    display: flex;
    width: 100%;
    flex-direction: column;
    align-items: center;
    margin-bottom: 24px;
    margin-top: 32px;

    > h1 {
      font-size: 28px;
      font-weight: 300;
      letter-spacing: 0;
      line-height: 42px;
      text-align: center;
    }

    > label {
      max-width: 520px;
      font-size: 18px;
      letter-spacing: 0;
      line-height: 32px;
      text-align: center;
    }
  }
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  margin: 16px 448px 0 448px;
  height: calc(100% + -88px);

  > div {
    height: calc(100% + -88px);
    align-items: center;

    > div[id="back"] {
      display: flex;
      width: 100%;
      align-items: center;
      align-content: center;
      justify-content: center;

      > button {
        display: flex;
        align-items: center;
        cursor: pointer;
        width: 32px;
        height: 32px;
        background-color: transparent;
        border: none;
        border-radius: 24px;

        &:hover {
          background-color: $color-neutral-100;
        }
      }

      > h1 {
        font-weight: bold;
        padding-left: 16px;
        font-size: 30px;
      }
    }

    > h1 {
      font-size: 30px;
    }

    > div[id="provider-selection"] {
      width: 100%;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      > div[id="providers-search"] {
        > label[id="subtitle"] {
          text-align: center;
        }

        > div {
          max-height: none !important;
          width: 100%;
          align-items: center;
          justify-content: center;

          > div[class="IndefiniteSpinner--medium"] {
            width: 48px !important;
            max-width: 48px !important;
          }

          > div[class="elemental-Input"] {
            width: 100% !important;
          }

          > div {
            align-items: center;
            justify-content: center;
            width: 30%;
            max-width: 40vw;
          }
        }
      }
    }

    > div[id="setup-explanation"] {
      width: 100%;
      align-items: center;
      justify-content: center;
    }
  }

  > div > div[id="provider-container"] {
    width: 100%;
    height: 100%;
    display: flex;

    > div[id="provider-credentials-form"] {
      flex: none !important;
      height: 100%;
    }

    > div[id="provider-instructions"] {
      padding: 0;
      display: flex;
      min-width: 500px;
      max-width: 576px;
    }
  }
}

////////////////////////////////////////////////////////////////////////////////
// MOBILE SPECIFIC STYLES
////////////////////////////////////////////////////////////////////////////////

.contentMobile {
  height: calc(100% + -72px);

  > div {
    height: 100%;

    > div[id="back"] {
      display: flex;
      width: 100%;
      align-items: center;
      align-content: center;
      justify-content: center;

      > button {
        display: flex;
        align-items: center;
        cursor: pointer;
        width: 32px;
        height: 32px;
        background-color: transparent;
        border: none;
        border-radius: 24px;

        &:hover {
          background-color: $color-neutral-100;
        }
      }

      > h1 {
        font-weight: bold;
        text-align: left;
        font-size: 20px;
        line-height: 22px;
      }
    }

    > h1 {
      width: 100%;
      width: calc(100% + -32px);
      justify-content: center;
      font-weight: bold;
      text-align: left;
      font-size: 20px;
      line-height: 22px;
      margin-left: 16px;

      > button {
        margin-right: 8px;
      }
    }

    /***************************************************************************
    * SCREEN FOR SELECTION OF PROVIDERS
    ***************************************************************************/
    > div[id="provider-selection"] {
      height: 100%;
      padding-right: 16px;
      padding-left: 16px;
      flex-direction: column;
      height: calc(100% + -96px);

      > h3 {
        font-size: 16px;
        line-height: 22px;
      }

      // Wrapper for integration cards
      > div[id="providers-search"] {
        > div[id="providers-wrapper"] {
          max-height: none !important;

          > div[class="IndefiniteSpinner--medium"] {
            width: 48px !important;
            max-width: 48px !important;
          }

          > div[class="elemental-Input"] {
            width: 100% !important;
          }

          // Cards container
          > div {
            align-items: center;
            justify-content: center;
            width: 100%;
            max-width: none;
            margin-right: 0;

            display: flex;
            height: 90px;
            flex-direction: row-reverse;

            > button {
              display: none;
            }

            > label {
              margin: 0;
              margin-left: 16px;
              text-align: left;
              font-size: 22px;
              line-height: 22px;
              width: 70%;
            }

            > img {
              margin: 2px;
              object-fit: contain;
              height: 70px;
              width: 70px;
              background-color: white;
              padding: 8px;
              margin-top: 0;
            }
          }
        }
      }

      // Container for provider not found
      > div[id="provider-not-found"] {
        margin-top: 0;

        > img {
          height: 80px;
          width: 80px;
        }

        > h1 {
          font-weight: bold;
          padding-right: 16px;
          padding-left: 16px;
          font-size: 18px;
          line-height: 22px;
        }

        > label {
          font-size: 16px;
          line-height: 22px;
        }

        > div[id="form"] {
          width: 100% !important;

          > div {
            margin-right: 0;
          }

          > button {
            width: 100%;
          }
        }
      }
    }

    > div[id="setup-explanation"] {
      width: 100%;
      align-items: center;
      justify-content: center;

      > div[id="slide-container"] {
        width: 100%;
      }
    }

    /***************************************************************************
    * SCREEN FOR SELECTION PROVIDER CREDENTIALS
    ***************************************************************************/
    > div[id="provider-container"] {
      flex-wrap: wrap;
      flex-direction: column;
      padding-right: 16px;
      padding-left: 16px;

      > div[id="provider-credentials-form"] {
        display: contents;

        > div[id="security-alert"] {
          width: calc(100% + -32px);

          > label {
            width: 100%;
          }
        }

        > div[id="location-provider-type"] {
          width: 100% !important;

          > div > div {
            width: 100% !important;
          }
        }

        > div[id="credentials-wrapper"] {
          width: 100% !important;

          > div > div {
            width: 100% !important;
          }

          > button {
            width: 100% !important;
          }
        }

        > a {
          margin-bottom: 16px;
        }
      }

      > div[id="provider-instructions"] {
        width: 100% !important;
        height: 100% !important;
        display: flex;

        > div > iframe {
          height: 1000px;
        }

        > div > div > div {
          width: 100% !important;
          > canvas {
            width: 100% !important;
          }
        }
      }
    }
  }
}

.loader {
  display: flex;
  height: 100%;
  width: 100%;
  align-items: center;
  justify-content: center;
}
