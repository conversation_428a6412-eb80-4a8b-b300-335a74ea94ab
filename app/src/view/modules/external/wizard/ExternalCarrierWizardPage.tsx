import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useMediaQuery } from "react-responsive";
import { useLocation } from "react-router-dom";

import { ArrowLeftIcon } from "@fourkites/elemental-atoms";
import { Spinner } from "@fourkites/elemental-loading-indicator";

import {
  useAnalytics,
  useAppDispatch,
  useAppSelector,
  useAutoRefreshEldIntegrations,
} from "state/hooks";
import { EldGpsIntegrationsState } from "state/modules/carrier/EldGpsIntegrations";
import { ExternalParametersState } from "state/modules/external/ExternalParameters";
import { UsersState } from "state/modules/Users";

import ProviderCredentials from "view/components/self-service/location-data-integrations/eld-gps/credentials/ProviderCredentials";
import EldGpsDeleteIntegrationModal from "view/components/self-service/location-data-integrations/eld-gps/details/EldGpsDeleteIntegrationModal";
import EldGpsIntegrationAddition from "view/components/self-service/location-data-integrations/eld-gps/EldGpsIntegrationAddition";

import CarrierOnboardingProgressHeader from "view/components/self-service/onboarding/headers/CarrierOnboardingProgressHeader";

import { isFieldInvalid } from "view/components/base/FormUtils";

import {
  ERROR_MESSAGES,
  ERROR_TITLES,
  sendSuccessMessageToParent,
  useTimeoutError,
  validateTokenAndServerResponseError,
  validateWizardParametersAndSendError,
} from "../components/ErrorHandling";
import {
  ExternalErrorDisplay,
  ExternalErrorMessage,
  InvalidParameters,
} from "../components/InvalidParameters";

import ExternalEldIntegrations from "./ExternalEldIntegrations";

import styles from "./ExternalCarrierWizardPage.module.scss";

const ExternalCarrierWizardPage: React.FC = () => {
  const { t } = useTranslation();

  const dispatch = useAppDispatch();
  const location = useLocation();

  const isMobile = useMediaQuery({ maxWidth: 720 });

  // Initializing pendo
  useAnalytics({ id: "" });

  // User will have a set amount of time to perform all his operations
  useTimeoutError(() => {
    if (!hasError) {
      updateErrorParameters(
        true,
        ERROR_TITLES.PROCESS_NOT_FINISHED_TIMEOUT,
        ERROR_MESSAGES.PROCESS_NOT_FINISHED_TIMEOUT
      );
    }
  });

  /*****************************************************************************
   * REDUX
   ****************************************************************************/

  const isLoading = useAppSelector(
    EldGpsIntegrationsState.selectors.isRetrieving()
  );
  const eldGpsIntegrations = useAppSelector(
    EldGpsIntegrationsState.selectors.eldGpsIntegrations()
  );
  const integrationDetails = useAppSelector(
    EldGpsIntegrationsState.selectors.eldGpsIntegrationDetails()
  );

  const hasEldIntegrations = eldGpsIntegrations?.length > 0;

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [step, setStep] = useState<number>(3);
  const [showIntegrationModal, setShowIntegrationModal] =
    useState<boolean>(false);
  const [hasGeneralError, setHasGeneralError] = useState<boolean>(false);
  const [isAddingCredential, setIsAddingCredential] = useState<boolean>(false);
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const [integration, setIntegration] = useState<any>(null);
  const [hasError, setError] = useState<boolean>(false);
  const [errorTitle, setErrorTitle] = useState<string>("");
  const [errorMessage, setErrorMessage] = useState<string>("");

  /*****************************************************************************
   * URL PARAMETERS AND VALIDATION
   ****************************************************************************/

  // Get external parameters from URL
  const query = new URLSearchParams(location.search);
  const redirectUrl = query.get("redirectUrl");
  const externalAccessToken = query.get("externalAccessToken");

  const parsedToken = parseJwt(externalAccessToken || "");

  const invalidParameters =
    isFieldInvalid(externalAccessToken || "") ||
    isFieldInvalid(redirectUrl || "");

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  /**
   * Validate parametrs and set data
   */
  useEffect(() => {
    if (invalidParameters) {
      const { hasError, title, message } = validateWizardParametersAndSendError(
        externalAccessToken || "",
        redirectUrl || ""
      );

      updateErrorParameters(hasError, title, message);
      return;
    }

    setExternalToken();

    fetchIntegrations();
  }, [externalAccessToken, redirectUrl, invalidParameters]);

  /**
   * Show page depending if carrier has ELD integrations or not
   */
  useEffect(() => {
    if (!isLoading && !hasEldIntegrations) {
      onAddCredential();
    } else {
      setStep(3);
      setShowIntegrationModal(false);
    }
  }, [hasEldIntegrations, isLoading]);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  /*
   * Method to check and set the error params
   */
  const updateErrorParameters = (
    hasError: boolean,
    title: string,
    message: string
  ) => {
    if (hasError) {
      setError(true);
      setErrorTitle(title);
      setErrorMessage(message);
    } else {
      setError(false);
      setErrorTitle("");
      setErrorMessage("");
    }
  };

  /**
   * Save external token for API usage
   */
  const setExternalToken = () => {
    dispatch(
      UsersState.actionCreators.setExternalCompany({
        companyId: parsedToken?.companyPermalink,
        companyType: "carrier",
      })
    );

    dispatch(
      ExternalParametersState.actions.setExternalAccessTokenAndUser({
        externalAccessToken: externalAccessToken,
        externalUser: {
          userId: parsedToken?.userId,
          email: parsedToken?.emailAddress,
          firstName: parsedToken?.name,
        },
      })
    );
  };

  /**
   * Fetch carrier integrations
   */
  const fetchIntegrations = async () => {
    // Get integrations
    const response = await dispatch(
      EldGpsIntegrationsState.actions.retrieveIntegrations({
        carrierId: parsedToken?.companyPermalink,
        modes: ["ltl", "ftl"],
      })
    );

    if ("error" in response) {
      setHasGeneralError(true);
      const { hasError, title, message } =
        validateTokenAndServerResponseError(response);
      updateErrorParameters(hasError, title, message);
      return;
    }
  };

  /**
   * Retrieves details of a given integration
   */
  const fetchIntegrationDetails = async (integrationId: string) => {
    // Gets the integration details
    const response = await dispatch(
      EldGpsIntegrationsState.actions.retrieveIntegrationDetails({
        carrierId: parsedToken?.companyPermalink,
        integrationId: integrationId,
      })
    );

    if ("error" in response) {
      setHasGeneralError(true);
      const { hasError, title, message } =
        validateTokenAndServerResponseError(response);
      updateErrorParameters(hasError, title, message);
      return;
    }
  };

  /**
   * Auto refresh GPS Integrations
   */
  const { setAutoRefreshGPSInterval, clearAutoRefreshGPSInterval } =
    useAutoRefreshEldIntegrations(eldGpsIntegrations, fetchIntegrations);

  /**
   * Handler for when user chooses to add credential
   */
  const onAddCredential = () => {
    setIsAddingCredential(true);
    setStep(2);
    setShowIntegrationModal(true);
    clearAutoRefreshGPSInterval();
  };

  /**
   * Handler for when user chooses to edit credential
   */
  const onEditIntegration = async (integration: any) => {
    await fetchIntegrationDetails(integration?.id);

    // Show integrations details page
    setStep(3);
    setIntegration(integration);
    setIsAddingCredential(false);
    setShowIntegrationModal(true);
    clearAutoRefreshGPSInterval();
  };

  /**
   * Handler for when user finishes adding or updating credentials
   */
  const onFinishUpdatingIntegrations = () => {
    setStep(3);
    setShowIntegrationModal(false);
    fetchIntegrations();
    setAutoRefreshGPSInterval();
  };

  /**
   * Handler for when user chooses to delete credential
   */
  const onDeleteIntegration = async (integration: any) => {
    setIntegration(integration);
    setShowDeleteModal(true);
    clearAutoRefreshGPSInterval();
  };

  /**
   * Handler for when user finishes deleting credential
   */
  const onConfirmIntegrationDelete = async () => {
    // Hides delete modal
    setShowDeleteModal(false);

    const response = await dispatch(
      EldGpsIntegrationsState.actions.deleteIntegration({
        carrierId: parsedToken?.companyPermalink,
        integrationId: integration?.id,
        modes: ["ltl", "ftl"],
      })
    );
    if ("error" in response) {
      setHasGeneralError(true);
      const { hasError, title, message } =
        validateTokenAndServerResponseError(response);
      updateErrorParameters(hasError, title, message);
      return;
    }

    // Fetches integrations again
    fetchIntegrations();
    setAutoRefreshGPSInterval();
  };

  /**
   * Notifies external app that user has finished process
   */
  const onComplete = () => {
    sendSuccessMessageToParent();
    clearAutoRefreshGPSInterval();
  };

  /**
   * Handler for wizard back
   */
  const onWizardBack = hasEldIntegrations
    ? () => {
        setStep(3);
        setShowIntegrationModal(false);
        setAutoRefreshGPSInterval();
      }
    : undefined;

  /**
   * On Closing delete modal
   */
  const onCloseDeleteModal = () => {
    setShowDeleteModal(false);
    setAutoRefreshGPSInterval();
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  if (hasError) {
    return (
      <ExternalErrorDisplay
        errorTitle={errorTitle}
        errorMessage={errorMessage}
      />
    );
  }

  if (invalidParameters) {
    return (
      <div className={styles.container}>
        <InvalidParameters />
      </div>
    );
  }

  if (hasGeneralError) {
    return (
      <div className={styles.container}>
        <ExternalErrorMessage />
      </div>
    );
  }

  if (isLoading) {
    return (
      <span className={styles.loader}>
        <Spinner isLoading size="medium" />
      </span>
    );
  }

  const contentClass = isMobile ? styles.contentMobile : styles.content;

  return (
    <div
      className={styles.container}
      id="external-carrier-wizard-page-container"
    >
      <CarrierOnboardingProgressHeader
        onboardingStep={step}
        showLogo={false}
        showLabel={true}
      />

      <div className={contentClass} id="external-carrier-wizard-page-content">
        {hasEldIntegrations && !showIntegrationModal && (
          <ExternalEldIntegrations
            integrations={eldGpsIntegrations}
            onAddIntegration={onAddCredential}
            onEditIntegration={onEditIntegration}
            onDeleteIntegration={onDeleteIntegration}
            onContinue={onComplete}
          />
        )}

        {showIntegrationModal && (
          <>
            {isAddingCredential ? (
              <EldGpsIntegrationAddition
                isModal={false}
                isExternallyUsed={true}
                onProviderChanged={(provider: any) => setStep(provider ? 3 : 2)}
                onBack={onWizardBack}
                onProviderNotFound={onFinishUpdatingIntegrations}
                onComplete={onFinishUpdatingIntegrations}
              />
            ) : (
              <div>
                <div id="back">
                  <button
                    onClick={() => {
                      setStep(3);
                      setShowIntegrationModal(false);
                      setAutoRefreshGPSInterval();
                    }}
                  >
                    <ArrowLeftIcon size={"24px"} />
                  </button>
                  <h1>{t("Authenticate")}</h1>
                </div>
                <ProviderCredentials
                  isModal={false}
                  isCreating={false}
                  isExternallyUsed={true}
                  provider={integrationDetails?.provider}
                  integrationId={integration?.id}
                  onCredentialsUpdated={onFinishUpdatingIntegrations}
                />
              </div>
            )}
          </>
        )}
      </div>

      <EldGpsDeleteIntegrationModal
        providerName={integration?.provider?.name}
        show={showDeleteModal}
        onClose={onCloseDeleteModal}
        onDelete={onConfirmIntegrationDelete}
      />
    </div>
  );
};

function parseJwt(token: string) {
  if (!token) {
    return;
  }

  var base64Url = token.split(".")[1];
  var base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
  var jsonPayload = decodeURIComponent(
    atob(base64)
      .split("")
      .map(function (c) {
        return "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2);
      })
      .join("")
  );

  return JSON.parse(jsonPayload);
}

export default ExternalCarrierWizardPage;
