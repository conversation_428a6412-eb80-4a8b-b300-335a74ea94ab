@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  .content {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 0 16px;

    > h1 {
      font-size: 24px;
      font-weight: 300;
      letter-spacing: 0;
      text-align: center;
      font-weight: 600;
      margin: 0;
    }

    > label {
      font-size: 18px;
      letter-spacing: 0;
      line-height: 32px;
      text-align: center;
    }

    > div[id="integrations"] {
      display: flex;
      width: 100%;
      margin-top: 16px;
      height: calc(100vh - 296px);
      align-items: center;
      flex-direction: column;
      overflow-y: auto;
    }

    > div[id="buttons"] {
      display: flex;
      width: 100%;
      flex-wrap: wrap;
      align-items: center;
      align-content: center;
      justify-content: center;
      margin-top: 12px;

      > button {
        display: flex;
        align-items: center;
        align-content: center;
        justify-content: center;
        margin-right: 16px;
        margin-bottom: 16px;
        min-width: 226px;
      }
    }
  }
}

.integrationCard {
  width: 100%;
  height: 115px;
  box-sizing: border-box;
  border: 1px solid $color-neutral-300;
  border-radius: 4px;
  background-color: white;
  margin-bottom: 12px;
  display: flex;

  .left {
    display: flex;
    flex: 1;
    align-items: center;
    align-content: center;
    justify-content: center;
  }

  .right {
    display: flex;
    justify-content: space-between;
    padding: 6px 12px 6px 24px;
    flex: 3.5;
    background-color: $color-neutral-200;

    .statusContainer {
      display: flex;
      flex-direction: column;

      > label {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 12px;
      }
    }
  }

  .logo {
    object-fit: contain;
    height: 72px;
    width: 72px;
    background-color: white;
    padding: 8px;
  }

  .fixIssueWrapper {
    button {
      background: none !important;
      text-decoration: underline;
      padding: 0;
      margin-top: 5px;
    }
  }
}
