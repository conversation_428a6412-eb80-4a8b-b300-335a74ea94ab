import React from "react";
import { useTranslation } from "react-i18next";

import genericGpsProvider from "assets/img/genericGpsProvider.png";

import { Button, PlusIcon, ArrowRightIcon } from "@fourkites/elemental-atoms";

import { IntegrationActions } from "view/components/self-service/location-data-integrations/eld-gps/details/IntegrationActions";
import { IntegrationStatus } from "view/components/self-service/location-data-integrations/eld-gps/details/IntegrationStatus";

import styles from "./ExternalEldIntegrations.module.scss";

const ExternalEldIntegrations = ({
  integrations,
  onAddIntegration,
  onEditIntegration,
  onDeleteIntegration,
  onContinue,
}: any) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div className={styles.container} id="external-eld-integrations-container">
      <div className={styles.content}>
        <h1>{t("Your ELD/GPS Providers")}</h1>
        <label>
          {t(
            `You have ${integrations?.length} location providers already added`
          )}
        </label>

        <div id="integrations">
          {integrations?.map((integration: any, index: number) => (
            <IntegrationCard
              key={index}
              integration={integration}
              onEditIntegration={onEditIntegration}
              onDeleteIntegration={onDeleteIntegration}
            />
          ))}
        </div>

        <div id="buttons">
          <Button size={"medium"} theme="tertiary" onClick={onAddIntegration}>
            <PlusIcon fill="#0e65e5" iconClass={"button-icon-left"} />
            {t("Add another ELD/GPS")}
          </Button>
          <Button size={"medium"} onClick={onContinue}>
            {t("Looks good, continue")}
            <ArrowRightIcon fill="#fff" iconClass={"button-icon-right"} />
          </Button>
        </div>
      </div>
    </div>
  );
};

const IntegrationCard = ({
  integration,
  onEditIntegration,
  onDeleteIntegration,
}: any) => {
  const provider = integration?.provider;
  const providerName = provider?.display_name
    ? provider?.display_name
    : provider?.name;

  const { t } = useTranslation();
  const statusValue = integration?.status?.value;
  const statusDescription = integration?.status?.description;
  const statusDetail = integration?.status?.statusDetail;

  return (
    <div
      className={styles.integrationCard}
      id="integration-card"
      data-test-id="integration-card"
    >
      <div className={styles.left}>
        <img
          className={styles.logo}
          src={provider?.logo ? provider?.logo : genericGpsProvider}
          alt="Provider logo"
        />
      </div>
      <div className={styles.right} id="external-eld-integrations-right">
        <div
          className={styles.statusContainer}
          id="external-eld-integrations-status-container"
        >
          <label id="provider-name">{providerName}</label>
          <div>
            <IntegrationStatus
              statusValue={statusValue}
              statusDescription={statusDescription}
              statusDetail={statusDetail}
            />
          </div>
          {statusValue === "error" && (
            <div className={styles.fixIssueWrapper}>
              <Button
                variant="flat"
                size="small"
                onClick={() => onEditIntegration(integration)}
              >
                {t("Fix Issue")}
              </Button>
            </div>
          )}
        </div>

        <IntegrationActions
          onEditIntegration={() => onEditIntegration(integration)}
          onDeleteIntegration={() => onDeleteIntegration(integration)}
        />
      </div>
    </div>
  );
};

export default ExternalEldIntegrations;
