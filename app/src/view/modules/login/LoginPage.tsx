import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import { fourkitesUrls } from "api/http/apiUtils";

import { Button, EyeIcon } from "@fourkites/elemental-atoms";
import { Input } from "@fourkites/elemental-input";

import { useAppDispatch } from "state/hooks";

import UsersState from "state/modules/Users";

import styles from "./LoginPage.module.scss";

const LoginPage: React.FC = () => {
  const { t } = useTranslation();

  const dispatch = useAppDispatch();
  //const carriersData = useAppSelector(selectCarriers);

  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [revealPassword, setRevealPassword] = useState<boolean>(false);

  const onChangeUsername = (e: any) => setUsername(e.target.value);
  const onChangePassword = (e: any) => setPassword(e.target.value);

  const onLogin = () => {
    dispatch(UsersState.actionCreators.userLogin(username, password));
  };

  // TODO: improve
  // Redirect to signup screen if not running locally
  const isRunninLocally = window.location.href.includes("localhost");
  if (!isRunninLocally) {
    window.location.href = fourkitesUrls.login;

    return null;
  }

  return (
    <div className={styles.pageContainer}>
      <div className={styles.loginForm}>
        <div className={styles.inputWrapper}>
          <Input
            label={`${t("Username")}`}
            value={username}
            onChange={onChangeUsername}
            required
          />
        </div>
        <div className={styles.inputWrapper}>
          <Input
            value={password}
            onChange={onChangePassword}
            label={`${t("Password")}`}
            type={revealPassword ? "text" : "password"}
            icon={<EyeIcon />}
            onIconClick={() => setRevealPassword(!revealPassword)}
            required
          />
        </div>
        <Button onClick={onLogin}>Login</Button>
      </div>
    </div>
  );
};

export default LoginPage;
