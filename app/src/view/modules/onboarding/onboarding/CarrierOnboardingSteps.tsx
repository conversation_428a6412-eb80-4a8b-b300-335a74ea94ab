import React from "react";

import CarrierOnboardingProgressHeader from "view/components/self-service/onboarding/headers/CarrierOnboardingProgressHeader";
import CarrierOnboardingBusinessVerification from "view/components/self-service/onboarding/business-verification/CarrierOnboardingBusinessVerification";
import CarrierOnboardingAccountCreation from "view/components/self-service/onboarding/account-creation/CarrierOnboardingAccountCreation";

const CarrierOnboardingSteps = ({
  invitationToken,
  invitationDetails,
  form,
  companyHasAdmin,
  shouldCreateCompany,
  onboardingStep,
  setOnboardingStep,
  onHelpRequested,
  onChangeFormField,
  onCompleteOnboarding,
}: any) => {
  const showFirstStep =
    !companyHasAdmin && onboardingStep === ONBOARDING_STEPS.ACCOUNT_CREATION;

  const showSecondStep =
    onboardingStep === ONBOARDING_STEPS.BUSINESS_VERIFICATION;

  return (
    <>
      <CarrierOnboardingProgressHeader
        onboardingStep={onboardingStep}
        skipFirstStep={companyHasAdmin}
      />

      {showFirstStep && (
        <CarrierOnboardingAccountCreation
          inviterName={invitationDetails?.inviter?.name}
          form={form}
          setForm={onChangeFormField}
          onConfirm={() =>
            setOnboardingStep(ONBOARDING_STEPS.BUSINESS_VERIFICATION)
          }
        />
      )}

      {showSecondStep && (
        <CarrierOnboardingBusinessVerification
          invitationToken={invitationToken}
          companyHasAdmin={companyHasAdmin}
          companyDetails={form?.companyDetails}
          form={form}
          onChangeFormField={onChangeFormField}
          setCompanyDetails={(value: any) => {
            onChangeFormField("companyDetails", value);
          }}
          onHelpRequested={onHelpRequested}
          onBack={() => setOnboardingStep(ONBOARDING_STEPS.ACCOUNT_CREATION)}
          onConfirm={onCompleteOnboarding}
        />
      )}
    </>
  );
};

export const ONBOARDING_STEPS = {
  ACCOUNT_CREATION: 1,
  BUSINESS_VERIFICATION: 2,
};

export default CarrierOnboardingSteps;
