import React, { useState, useEffect } from "react";
import { useMediaQuery } from "react-responsive";
import { useParams, useHistory } from "react-router-dom";

import { Spinner } from "@fourkites/elemental-loading-indicator";

import { carrierWizardRoutes } from "router/carrier/CarrierWizardRouter";

import { useAppSelector, useAppDispatch, useAnalytics } from "state/hooks";
import { CompaniesOnboardingState } from "state/modules/CompaniesOnboarding";
import UsersState from "state/modules/Users";

import { getFirstName, getLastName } from "view/components/base/FormUtils";

import OnboardingSidePanel from "view/components/self-service/onboarding/side-panel/OnboardingSidePanel";
import OnboardingHeader from "view/components/self-service/onboarding/headers/OnboardingHeader";
import OnboardingFooter from "view/components/self-service/onboarding/footer/OnboardingFooter";

import CarrierOnboardingSteps, {
  ONBOARDING_STEPS,
} from "./CarrierOnboardingSteps";

import { OnboardingMessage } from "./UserFeedbackMessages";

import styles from "./CompaniesOnboardingPage.module.scss";

const CompaniesOnboardingPage: React.FC = () => {
  const history = useHistory();

  const { invitationToken }: any = useParams();

  const isMobile = useMediaQuery({ maxWidth: 720 });

  /*****************************************************************************
   * REDUX
   ****************************************************************************/

  const dispatch = useAppDispatch();

  //Users data
  const isLoadingUserData = useAppSelector(
    UsersState.selectors.getLoadingStatus
  );

  // Onboarding data
  const selectors = CompaniesOnboardingState.selectors;
  const invitationDetails = useAppSelector(selectors.onboardingInvitation());
  const isLoadingOnboardingInvitation = useAppSelector(selectors.isLoading());
  const isCreating = useAppSelector(selectors.isCreating());
  const onboardingInvitationError = useAppSelector(selectors.isInvalid());

  const companyHasAdmin = invitationDetails?.carrier?.has_admin;

  // We only create company if it doesn't have permalink
  const shouldCreateCompany =
    !companyHasAdmin && !invitationDetails?.carrier?.permalink;

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [onboardingStep, setOnboardingStep] = useState<number>(
    ONBOARDING_STEPS.ACCOUNT_CREATION
  );
  const [form, setForm] = useState<any>({
    type: "carrier",
    region: "US & Canada",
    email: "<EMAIL>",
    companyDetails: null,
  });
  const [helpRequested, setHelpRequested] = useState<boolean>(false);
  const [hasSignupError, setHasSignupError] = useState<boolean>(false);
  const [hasGeneralError, setHasGeneralError] = useState<boolean>(false);

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  /*
   * Fetches invitation details bases on token
   */
  useEffect(() => {
    if (!invitationToken) {
      return;
    }

    dispatch(
      CompaniesOnboardingState.actions.getOnboardingInvitation({
        invitationToken,
      })
    );
  }, [invitationToken]);

  /*
   * Update state details based on invitation details return
   */
  useEffect(() => {
    if (!invitationDetails) {
      return;
    }

    // If company has admin, show only last step
    if (companyHasAdmin) {
      setOnboardingStep(ONBOARDING_STEPS.BUSINESS_VERIFICATION);
    }

    // Updates company details
    setForm({
      ...form,
      fullName: `${invitationDetails?.contact?.first_name || ""} ${
        invitationDetails?.contact?.last_name || ""
      }`,
      email: invitationDetails?.contact?.email,
      companyDetails: invitationDetails?.carrier,
    });
  }, [invitationDetails, companyHasAdmin]);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  /*
   * Helper for updating form fields
   */
  const onChangeFormField = (fieldName: string, fieldValue: string) => {
    setForm({ ...form, [fieldName]: fieldValue });
  };

  /*
   * Helper for creating carrier
   */
  const doCreateCarrier = async () => {
    const companyDetails = form?.companyDetails;

    const createCarrierResponse = await dispatch(
      CompaniesOnboardingState.actions.createCarrier({
        invitationToken,
        carrier: {
          //TODO: add modes in the future
          modes: ["ftl"],
          types: form?.type === "both" ? ["broker", "carrier"] : [form?.type],
          region: form?.region,
          name: companyDetails?.name,
          identifications: companyDetails?.identifications,
          address: companyDetails?.address || null,
        },
      })
    );

    return createCarrierResponse;
  };

  /*
   * Helper for creating user associated with carrier
   */
  const doCreateCarrierUser = async (carrierPermalink: string) => {
    const createUserResponse = await dispatch(
      CompaniesOnboardingState.actions.createCarrierUser({
        invitationToken,
        carrierId: carrierPermalink,
        user: {
          firstName: getFirstName(form?.fullName),
          lastName: getLastName(form?.fullName, ""),
          email: form?.email,
        },
        password: form?.password,
        // NOTE: on 2022-06-13, to simplify signup, it was asked to remove
        // passowrd confirmation
        passwordConfirmation: form?.password,
        role: "admin",
      })
    );

    return createUserResponse;
  };

  /*
   * Completes onboarding by creating company in case it doesn't exists yet,
   * and after that creating user
   */
  const onCompleteOnboarding = async () => {
    let carrierPermalink = form?.companyDetails?.permalink;

    // STEP 1 - Creates company in case it's not onboarded and don't have admin
    if (shouldCreateCompany) {
      const createCarrierResponse = await doCreateCarrier();

      // NOTE: how to handle this?
      if ("error" in createCarrierResponse) {
        // We need the permalink, but if company exists, how to handle?
        // TODO: unsolved business flow case
      }

      // Updates permalink from created company
      carrierPermalink = createCarrierResponse?.payload?.permalink;
    }

    // STEP 2 - Creates user in case it will be admin
    const createUserResponse = await doCreateCarrierUser(carrierPermalink);
    if ("error" in createUserResponse) {
      setHasSignupError(true);
      return;
    }

    // STEP 3 - LOGS IN AND REDIRECTS TO MAIN APP
    await dispatch(
      UsersState.actionCreators.userLogin(form?.email, form?.password)
    );
    // After user creation is complete, push carrier into GPS wizard
    history.push(carrierWizardRoutes.eldGpsTracking);
    // NOTE: originally this redirected to wizard landing page, but that was
    // removed for now. Turn route back on if the landing page ever comes back
  };

  /*
   * Creates a help request for this user
   */
  const onHelpRequested = async (
    issue: string,
    wrongInformation?: string[]
  ) => {
    const createHelpRequestResponse = await dispatch(
      CompaniesOnboardingState.actions.createHelpRequest({
        invitationToken,
        issue,
        wrongInformation,
      })
    );
    if ("error" in createHelpRequestResponse) {
      setHasGeneralError(true);
      return;
    }

    setHelpRequested(true);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  // Initializing pendo
  useAnalytics({ id: "" });

  const isLoading =
    isLoadingOnboardingInvitation || isCreating || isLoadingUserData;

  const hasInvalidInvitation =
    invitationToken == null || onboardingInvitationError;

  // Invalid Invitation
  if (hasInvalidInvitation) {
    return (
      <OnboardingMessage
        invitationDetails={invitationDetails}
        messageType="invalid_invitation"
      />
    );
  }

  // Help Requested
  if (helpRequested) {
    return (
      <OnboardingMessage
        invitationDetails={invitationDetails}
        messageType="help_requested"
      />
    );
  }

  // Onboarding Error
  if (hasSignupError || hasGeneralError) {
    return (
      <OnboardingMessage
        invitationDetails={invitationDetails}
        messageType="onboarding_error"
        isSignupError={hasSignupError && !hasGeneralError}
      />
    );
  }

  // Normal onboarding content
  return (
    <div className={styles.container}>
      {!isMobile && (
        <OnboardingSidePanel invitationDetails={invitationDetails} />
      )}

      <div id="onboarding-form">
        <OnboardingHeader />

        <div className={styles.onboardingFormContent}>
          {isLoading ? (
            <div className={styles.loader}>
              <Spinner isLoading size="medium" />
            </div>
          ) : (
            <CarrierOnboardingSteps
              invitationToken={invitationToken}
              invitationDetails={invitationDetails}
              form={form}
              companyHasAdmin={companyHasAdmin}
              shouldCreateCompany={shouldCreateCompany}
              onboardingStep={onboardingStep}
              setOnboardingStep={setOnboardingStep}
              onHelpRequested={onHelpRequested}
              onChangeFormField={onChangeFormField}
              onCompleteOnboarding={onCompleteOnboarding}
            />
          )}
        </div>

        <OnboardingFooter />
      </div>
    </div>
  );
};

export default CompaniesOnboardingPage;
