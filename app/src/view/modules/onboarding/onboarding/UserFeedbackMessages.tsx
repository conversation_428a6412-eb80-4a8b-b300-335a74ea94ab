import React from "react";
import { useTranslation } from "react-i18next";

import { ColoredFKLogo } from "@fourkites/elemental-atoms";
import { useMediaQuery } from "react-responsive";

import { fourkitesUrls } from "api/http/apiUtils";

import OnboardingSidePanel from "view/components/self-service/onboarding/side-panel/OnboardingSidePanel";

import styles from "./CompaniesOnboardingPage.module.scss";

export const OnboardingMessage = ({
  invitationDetails,
  messageType,
  isSignupError = false,
}: any) => {
  const { t } = useTranslation();
  const isMobile = useMediaQuery({ maxWidth: 720 });
  let errorDetails = getErrorDetails(t, messageType, isSignupError);

  return (
    <div className={styles.container}>
      {!isMobile && (
        <OnboardingSidePanel invitationDetails={invitationDetails} />
      )}
      <div className={styles.onboardingFormContent}>
        <div id="invalid">
          <ColoredFKLogo />
          <h1>{errorDetails.title}</h1>
          <label>
            {errorDetails.description}
            {errorDetails.link}
          </label>
        </div>
      </div>
    </div>
  );
};

export function getErrorDetails(
  t: Function,
  errorType: string,
  isSignupError = false
) {
  let errorDetails = {
    title: "",
    description: "",
    link: <></>,
  };

  switch (errorType) {
    case "invalid_invitation":
      errorDetails.title = t("Oops! Invalid Invitation!");
      errorDetails.description = t(
        "Seems like your invitation is invalid. It has " +
          "either expired or you have already " +
          "used this link to create an account. " +
          "Please reach out to the company that " +
          "invited you to FourKites if you have " +
          "questions."
      );
      break;

    case "help_requested":
      errorDetails.title = t("Help is on the way!");
      errorDetails.description = t(
        "We have informed our teams to review our data. " +
          "We will reach back to you soon. " +
          "Meanwhile, please explore more about offerings from FourKites "
      );
      errorDetails.link = (
        <a href={fourkitesUrls.carrierOfferings} target="_blank">
          {t("here.")}
        </a>
      );
      break;

    case "onboarding_error":
      errorDetails.title = isSignupError
        ? t("Oops! Signup can't be completed")
        : t("Oops! Something went wrong!");

      errorDetails.description = isSignupError
        ? t(
            "For some reason the signup process cannot be completed. " +
              "We have informed our support team to look into it and " +
              "we will reach back to you soon. " +
              "Meanwhile, please explore more about offerings from FourKites "
          )
        : t(
            "For some reason the request cannot be completed. " +
              "We have informed our support team to look into it and " +
              "we will reach back to you soon. " +
              "Meanwhile, please explore more about offerings from FourKites "
          );
      errorDetails.link = (
        <a href={fourkitesUrls.carrierOfferings} target="_blank">
          {t("here.")}
        </a>
      );
      break;
    default:
      break;
  }
  return errorDetails;
}
