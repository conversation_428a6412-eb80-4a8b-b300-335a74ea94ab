import React from "react";
import { useTranslation } from "react-i18next";

import { ColoredFKLogo } from "@fourkites/elemental-atoms";

import { fourkitesUrls } from "api/http/apiUtils";

import OnboardingSidePanel from "view/components/self-service/onboarding/side-panel/OnboardingSidePanel";

import styles from "./SignupPageMessages.module.scss";

export const WelcomeMessage = () => {
  const { t } = useTranslation();

  return (
    <div className={styles.messageContainer}>
      <ColoredFKLogo />
      <h1>{t("Carrier Onboarding Survey")}</h1>
      <label>
        {t(
          "Welcome! You've been invited to join a shipper's carrier network " +
            "for real-time shipment visibility through FourKites."
        )}
        <br />
        <br />
        {t(
          "Please provide your USDOT number and business email address to begin the onboarding survey."
        )}
      </label>
    </div>
  );
};

export const SuccessMessage = (form: { email: string }) => {
  const { t } = useTranslation();
  return (
    <div className={styles.container}>
      <OnboardingSidePanel invitationDetails={null} />

      <div id="onboarding-form">
        <div className={styles.messageContainer}>
          <ColoredFKLogo />
          <h1>{t("Thank you!")}</h1>
          <label>
            {t("We have received your inputs.")}
            <br />
            {t("We will send an email regarding the next steps to  ")}
            <b>{form?.email}</b>
            <br />
            {t("We are looking forward to seeing you on FourKites!")}
            <br />
            {t(
              "Meanwhile, please explore more about offerings from FourKites "
            )}
            <a href={fourkitesUrls.carrierOfferings} target="_blank">
              {t("here.")}
            </a>
          </label>
        </div>
      </div>
    </div>
  );
};

export const InvalidInviteToken = () => {
  const { t } = useTranslation();
  return (
    <div className={styles.container}>
      <OnboardingSidePanel invitationDetails={null} />

      <div id="onboarding-form">
        <div className={styles.messageContainer}>
          <ColoredFKLogo />
          <h1>{t("Oops! Invalid Invitation!")}</h1>
          <label>
            {t("We are unable to validate your invitation.")}
            <br />
            {t("This could be due to one of the following reasons:")}
            <br />
            <ul style={{ textAlign: 'left', paddingLeft: '20px' }}>
              <li>{t("The invitation link is invalid or corrupted")}</li>
              <li>{t("The invitation has expired (invitations are valid for 21 days)")}</li>
              <li>{t("The data entered does not match our records")}</li>
            </ul>
            <br />
            {t("Please check your invitation link and try again, or contact your administrator for a new invitation.")}
          </label>
        </div>
      </div>
    </div>
  );
};