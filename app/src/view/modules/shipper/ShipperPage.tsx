import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useHistory } from "react-router-dom";

import { ExternalLinkIcon } from "@fourkites/elemental-atoms";
import {
  GlobalHeader as Header,
  ProductId,
} from "@fourkites/elemental-global-header";
import { Spinner } from "@fourkites/elemental-loading-indicator";

import { COMPANY_API_DOMAIN, fourkitesUrls } from "api/http/apiUtils";

import { appRoutes } from "router/AppRouter";
import {
  onCommunitySelect,
  onHelpClick,
  onNav,
  onNewWindow,
  setCookie,
} from "router/navigationUtils";
import ShipperRouter, {
  getShipperRouteId,
  shipperRoutes,
} from "router/shipper/ShipperRouter";

import { useAppDispatch, useAppSelector } from "state/hooks";
import UsersState, { getCookies } from "state/modules/Users";

import { startWithUpperCase } from "view/components/base/StringUtils";
import TermsAndConditionsModal from "view/components/self-service/terms-and-conditions/TermsAndConditions";
import CompanySwitcher from "view/components/self-service/component/CompanySwitcher"

import styles from "./ShipperPage.module.scss";

const ShipperPage: React.FC = () => {
  const { t } = useTranslation();
  const history = useHistory();

  const dispatch = useAppDispatch();

  const isLoadingUserData = useAppSelector(
    UsersState.selectors.getLoadingStatus
  );
  const currentUser = useAppSelector(UsersState.selectors.getCurrentUser);
  const superAdmin = useAppSelector(UsersState.selectors.getIsSuperAdmin);
  const isCompanyAdmin = useAppSelector(UsersState.selectors.getIsCompanyAdmin);
  const termsAgreed = useAppSelector(UsersState.selectors.getTermsAgreed);
  const { authToken, userId, deviceId } = getCookies();

  const currentUserObject = {
    authToken,
    userId,
    deviceId,
    superAdmin: superAdmin ? "true" : "false",
    name: {
      firstName: startWithUpperCase(currentUser?.firstName),
      lastName: startWithUpperCase(currentUser?.lastName),
    },
    isCompanyAdmin: isCompanyAdmin ? "true" : "false",
    role: currentUser?.role,
    company: {
      defaultTypes: currentUser?.defaultCompanyType,
      defaultId: currentUser?.defaultCompanyID,
      context: currentUser?.companyContext,
    },
    displayCompanySwitcher: false,
  };

  const domains = {
    company: COMPANY_API_DOMAIN,
    user: "",
  }

  const onWorkspaceSelect = currentUser?.hasWorkspaceEnabled
  ? () => onNav(fourkitesUrls.myWorkspace)
  : undefined; 

  const onLogoClick = () => {
    currentUser?.hasWorkspaceEnabled ?
    onNav(fourkitesUrls.myWorkspace) : onNav(fourkitesUrls.dashboard)
 }
 const currentUserData = sessionStorage.getItem("fkcUserDetails") || localStorage.getItem("tfcurrent-user");
 const userObject = currentUserData ? JSON.parse(currentUserData) : null;

 const onDeveloperPortalSelect = async () => {
    setCookie();
    const archbeeJwt = await UsersState.helpers.getArchbeeJwt();
    const jwtAddedUrl = `${fourkitesUrls.developerPortalDoc}?jwt=${archbeeJwt}`;
    userObject?.superAdmin || userObject?.companyAdmin
      ? onNewWindow(fourkitesUrls.developerPortal)
      : onNewWindow(jwtAddedUrl)
  };

  const onAppointmentsManagerSelect = userObject?.modules?.includes(
    "appointment-manager"
  )
    ? () => onNav(fourkitesUrls.appointmentsManager)
    : undefined;

  const onDynamicYardSelect = userObject?.modules?.includes("dynamic-yard")
    ? () => onNav(fourkitesUrls.dynamicYard)
    : undefined; 

 const onDynamicYardPlusSelect = userObject?.modules?.includes(
   "dynamic-yard-plus"
 )
   ? () => onNav(fourkitesUrls.dynamicYardPlus)
   : undefined;

 const onPrivateFleetTrackingSelect = userObject?.modules?.includes(
   "nic-private-fleet"
 )
   ? () => onNav(fourkitesUrls.privateFleetTracking)
   : undefined;

 const onRailFleetSelect = userObject?.modules?.includes("nic-rail-fleet")
   ? () => onNav(fourkitesUrls.railFleet)
   : undefined;
  const onDigitalWorkforceSelect = () => onNav(fourkitesUrls.digitalWorkforce);
  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [showTermsAndConditionsModal, setShowTermsAndConditionsModal] =
    useState<boolean>(false);

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  useEffect(() => {
    // await sleep(1000);
    setShowTermsAndConditionsModal(!termsAgreed);
  }, [termsAgreed]);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const onUserLogoutClick = () => {
    dispatch(
      UsersState.actionCreators.userLogout(() => {
        const isRunninLocally = window.location.href.includes("localhost");

        if (isRunninLocally) {
          history.push(appRoutes.login);
        } else {
          onNav(fourkitesUrls.login);
        }
      })
    );
  };

  const onSelectCompany = (selectedCompany: any) => {
    if (!selectedCompany || !selectedCompany?.id) {
      return;
    }

    // TODO: needs to clear state
    dispatch(UsersState.actionCreators.setSelectedCompany(selectedCompany?.id));
  };

  const onAgreedConditions = async () => {
    try {
      await dispatch(UsersState.actionCreators.setTermsAgreed());
      // Close modal
      setShowTermsAndConditionsModal(false);
    } catch (error) {
      onDeniedConditions();
    }
  };

  const onDeniedConditions = () => {
    setShowTermsAndConditionsModal(false);
    onNav(fourkitesUrls.visibility);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const subHeaderItems = [
    {
      label: t("Overview"),
      id: shipperRoutes.overview,
      pagePath: shipperRoutes.overview,
      headerType: "app_route",
    },
    {
      label: t("Carriers"),
      id: shipperRoutes.carriers,
      pagePath: shipperRoutes.carriers,
      headerType: "app_route",
    },
    ...((superAdmin || isCompanyAdmin) ? [
        {
      label: t("Data Integrations"),
      id: shipperRoutes.dataIntegrations,
      pagePath: shipperRoutes.dataIntegrations,
      headerType: "app_route",
    },
    {
      label: t("Company"),
      id: shipperRoutes.company,
      pagePath: shipperRoutes.company,
      headerType: "app_route",
    }
      ]: []),
    {
      label: t("Address Manager"),
      id: fourkitesUrls.addressManager,
      pagePath: fourkitesUrls.addressManager,
      headerType: "externalLink",
      icon: <ExternalLinkIcon className="sub-header-icon" />,
    },
  ];

  const onConnectivitySelect = () => onNav(fourkitesUrls.connectivity);

  const initialSelectedProduct = ProductId.FourKitesConnect;

  const shouldShowTcModal = isCompanyAdmin && !termsAgreed;

  return (
    <div className={styles.pageContainer}>
      <CompanySwitcher/>
      <Header
        currentUser={currentUserObject}
        subHeaderItems={subHeaderItems}
        initialSelectedItemId={getShipperRouteId(history)}
        initialSelectedProductId={initialSelectedProduct}
        onWorkspaceSelect={onWorkspaceSelect}
        onLogoClick={onLogoClick}
        // Product switcher
        onVisibilitySelect={() => onNav(fourkitesUrls.visibility)}
        onAppointmentsManagerSelect={onAppointmentsManagerSelect}
        onDynamicYardSelect={onDynamicYardSelect}
        onCommunitySelect={onCommunitySelect}
        onDeveloperPortalSelect={onDeveloperPortalSelect}
        onFourKitesConnectSelect={onConnectivitySelect}
        onDynamicYardPlusSelect={onDynamicYardPlusSelect}
        onPrivateFleetTrackingSelect={onPrivateFleetTrackingSelect}
        onRailFleetSelect={onRailFleetSelect} 
        onDigitalWorkforceSelect={onDigitalWorkforceSelect}
        // Right menu
        onNotificationsClick={() => onNewWindow(fourkitesUrls.notifications)}
        onHelpClick={onHelpClick}
        onSettingsClick={() => onNav(fourkitesUrls.companySettings)}
        // Avatar menu
        onUserLogoutClick={onUserLogoutClick}
        domains={domains}
        setSelectedCompany={onSelectCompany}
      />

      <div id="content">
        {isLoadingUserData ? (
          <Spinner isLoading size="medium" />
        ) : (
          <>
            <ShipperRouter />

            {shouldShowTcModal && (
              <TermsAndConditionsModal
                show={showTermsAndConditionsModal}
                onClose={onDeniedConditions}
                onAgreed={onAgreedConditions}
              />
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default ShipperPage;
