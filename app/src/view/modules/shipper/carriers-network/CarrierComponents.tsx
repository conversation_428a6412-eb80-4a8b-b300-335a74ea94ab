import { useEffect, useCallback, useState } from "react";

import { Spinner } from "@fourkites/elemental-loading-indicator";

import { PaginationType } from "state/BaseTypes";
import { useAppSelector, useAppDispatch } from "state/hooks";
import { CarriersNetworkState } from "state/modules/shipper/CarriersNetwork";
import { ShipperIndicatorsState } from "state/modules/shipper/ShipperIndicators";

import CompanyManagement from "view/components/self-service/company-management/CompanyManagement";
import GetConnectedPanel from "view/components/self-service/carrier-additions/get-connected/GetConnectedPanel";

import styles from "./CarriersNetworkPage.module.scss";

/*******************************************************************************
 * MODES WITH INVITES
 ******************************************************************************/

export const FtlCarriers = ({
  managerCompanyName,
  shipperId,
  managedCompanyType,
  onShowManualCarrierAdditions,
  onDownloadBulkTemplate,
  onShowBulkCarrierAdditions,
  onShowCarrierInviteTemplate,
}: any) => {
  const dispatch = useAppDispatch();

  /*****************************************************************************
   * REDUX
   ****************************************************************************/

  const mode = "ftl";
  const carriersNetworkSelectors = CarriersNetworkState.selectors;

  // Pagination
  const pagination = useAppSelector(
    carriersNetworkSelectors.paginationByMode(mode)
  );
  const { totalItems, totalPages } = pagination;

  // Data
  const carriers = useAppSelector(
    carriersNetworkSelectors.carriersByMode(mode)
  );
  const isGettingCarriers = useAppSelector(
    carriersNetworkSelectors.isLoadingCarriersByMode(mode)
  );
  const isEditingCarriers = useAppSelector(
    carriersNetworkSelectors.isEditingCarriersByMode(mode)
  );

  // Indicators
  const shipperIndicators = useAppSelector(
    ShipperIndicatorsState.selectors.shipperIndicators()
  );
  const isLoadingShipperIndicators = useAppSelector(
    ShipperIndicatorsState.selectors.isLoadingShipperIndicators()
  );

  // Sort Id Mappings
  const sortIdMapping: any = {
    identification: "target_company_name",
    services_provided: "services_provided",
    last_request_at: "last_request_at",
    action: "action",
    tracking_method: "tracking_method",
    load_volume: "load_volume",
    my_assigned_percentage: "assignment_percentage",
    my_tracked_percentage: "tracking_percentage",
    connectivity_status: "onboarding_status",
  };

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [lastPageParams, setLastPageParams] = useState<any>({
    index: -1,
    size: -1,
  });

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  /*
   * Fetches carriers when opening the component. The first fecth is done using
   * the effect to avoid table infinite loop when there's no data
   */
  useEffect(() => {
    if (!shipperId) {
      return;
    }

    dispatch(CarriersNetworkState.actions.getCarriers({ shipperId, mode }));
  }, [shipperId, mode]);

  /*
   * We need to fetch indicators to be able to tell if we have carriers or not
   */
  useEffect(() => {
    const modesWithIndicators = ["ltl", "ftl"];
    for (let mode of modesWithIndicators) {
      dispatch(
        ShipperIndicatorsState.actions.getShipperIndicators({
          shipperId,
          //@ts-ignore
          mode,
        })
      );
    }
  }, [shipperId, mode]);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  /*
   * Fetchs assets for fleet
   */
  const fetchCarriers = useCallback(
    async ({ pageSize, pageIndex, sortBy }: PaginationType) => {
      // Don't fetch if doesn't have shipper id
      if (!shipperId) {
        return;
      }

      // Don't fetch if it's the same page
      if (
        carriers.length === 0 &&
        pageIndex === lastPageParams.index &&
        pageSize === lastPageParams.size
      ) {
        return;
      }

      const sortId = sortBy[0]?.id;
      const isDesc = sortBy[0]?.desc;

      const sortByParam = {
        columnName: sortIdMapping[sortId],
        isDesc,
      };

      await dispatch(
        CarriersNetworkState.actions.getCarriers({
          shipperId,
          mode,
          currentPage: pageIndex + 1,
          pageSize: pageSize,
          sortBy: sortByParam,
        })
      );

      setLastPageParams({ index: pageIndex, size: pageSize });
    },
    [carriers, lastPageParams, setLastPageParams]
  );

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const isLoadingFtlCarriers =
    isGettingCarriers && !isEditingCarriers && !carriers?.length;

  // No indicators for carriers
  const noIndicatorCarriers =
    !isLoadingShipperIndicators &&
    shipperIndicators?.carriers?.total === 0 &&
    shipperIndicators?.invitations?.total === 0;

  // No carriers connected
  const noNetworkCarriers = !isLoadingFtlCarriers && carriers?.length === 0;

  // Show get connected
  const shouldShowGetConnectedOnFtl = noIndicatorCarriers && noNetworkCarriers;

  if (isLoadingFtlCarriers) {
    return (
      <div className={styles.loading}>
        <Spinner isLoading size="medium" />
      </div>
    );
  }

  if (shouldShowGetConnectedOnFtl) {
    return (
      <GetConnectedPanel
        mode={mode}
        managerCompanyName={managerCompanyName}
        managerCompanyId={shipperId}
        managedCompanyType={managedCompanyType}
        onShowManualCarrierAdditions={onShowManualCarrierAdditions}
        onDownloadBulkTemplate={onDownloadBulkTemplate}
        onShowBulkCarrierAdditions={onShowBulkCarrierAdditions}
      />
    );
  }

  return (
    <CompanyManagement
      mode={mode}
      managerCompanyId={shipperId}
      managedCompanyType="carrier"
      companiesData={carriers}
      fetchDataForPage={fetchCarriers}
      pageCount={totalPages}
      totalEntries={totalItems}
      onShowManualCarrierAdditions={() => onShowManualCarrierAdditions(null)}
      onShowBulkCarrierAdditions={onShowBulkCarrierAdditions}
      onShowCarrierInviteTemplate={onShowCarrierInviteTemplate}
    />
  );
};

/*******************************************************************************
 * MODES WITHOUT INVITES
 ******************************************************************************/

export const NotInvitableCarriers = ({
  mode,
  shipperId,
  managedCompanyType,
  onShowManualCarrierAdditions,
  onShowBulkCarrierAdditions,
}: any) => {
  const dispatch = useAppDispatch();

  /*****************************************************************************
   * REDUX
   ****************************************************************************/

  // Filters
  const carriersNetworkSelectors = CarriersNetworkState.selectors;
  // Pagination
  const pagination = useAppSelector(
    CarriersNetworkState.selectors.paginationByMode(mode)
  );
  const { totalItems, totalPages } = pagination;

  // Carriers Data
  const carriers = useAppSelector(
    carriersNetworkSelectors.carriersByMode(mode)
  );
  const isGettingCarriers = useAppSelector(
    carriersNetworkSelectors.isLoadingCarriersByMode(mode)
  );
  const isEditingCarriers = useAppSelector(
    carriersNetworkSelectors.isEditingCarriersByMode(mode)
  );

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  /*
   * Fetches carriers when opening the component. The first fecth is done using
   * the effect to avoid table infinite loop when there's no data
   */
  useEffect(() => {
    if (!shipperId) {
      return;
    }

    dispatch(CarriersNetworkState.actions.getCarriers({ shipperId, mode }));
  }, [shipperId]);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  /*
   * Fetchs assets for fleet
   */
  const fetchCarriers = useCallback(
    async ({ pageSize, pageIndex, sortBy }: PaginationType) => {
      if (!shipperId || carriers.length === 0) {
        return;
      }

      const sortByParam = {
        columnName:
          sortBy[0]?.id === "name" ? "target_company_name" : sortBy[0]?.id,
        isDesc: sortBy[0]?.desc,
      };

      await dispatch(
        CarriersNetworkState.actions.getCarriers({
          shipperId,
          mode,
          currentPage: pageIndex + 1,
          pageSize: pageSize,
          sortBy: sortByParam,
        })
      );
    },
    [carriers]
  );

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const isLoadingCarriers =
    isGettingCarriers && !isEditingCarriers && !carriers?.length;

  if (isLoadingCarriers) {
    return (
      <div className={styles.loading}>
        <Spinner isLoading size="medium" />
      </div>
    );
  }

  return (
    <CompanyManagement
      mode={mode}
      managerCompanyId={shipperId}
      managedCompanyType={managedCompanyType}
      companiesData={carriers}
      fetchDataForPage={fetchCarriers}
      pageCount={totalPages}
      totalEntries={totalItems}
      onShowManualCarrierAdditions={() => onShowManualCarrierAdditions(null)}
      onShowBulkCarrierAdditions={onShowBulkCarrierAdditions}
    />
  );
};

export const LtlCarriers = ({
  shipperId,
  managedCompanyType,
  onShowManualCarrierAdditions,
  onShowBulkCarrierAdditions,
}: any) => (
  <NotInvitableCarriers
    mode={"ltl"}
    shipperId={shipperId}
    managedCompanyType={managedCompanyType}
    onShowManualCarrierAdditions={onShowManualCarrierAdditions}
    onShowBulkCarrierAdditions={onShowBulkCarrierAdditions}
  />
);

export const ParcelCarriers = ({
  shipperId,
  managedCompanyType,
  onShowManualCarrierAdditions,
  onShowBulkCarrierAdditions,
}: any) => (
  <NotInvitableCarriers
    mode={"parcel"}
    shipperId={shipperId}
    managedCompanyType={managedCompanyType}
    onShowManualCarrierAdditions={onShowManualCarrierAdditions}
    onShowBulkCarrierAdditions={onShowBulkCarrierAdditions}
  />
);

export const OceanCarriers = ({
  shipperId,
  managedCompanyType,
  onShowManualCarrierAdditions,
  onShowBulkCarrierAdditions,
}: any) => (
  <NotInvitableCarriers
    mode={"ocean"}
    shipperId={shipperId}
    managedCompanyType={managedCompanyType}
    onShowManualCarrierAdditions={onShowManualCarrierAdditions}
    onShowBulkCarrierAdditions={onShowBulkCarrierAdditions}
  />
);

export const AirCarriers = ({
  shipperId,
  managedCompanyType,
  onShowManualCarrierAdditions,
  onShowBulkCarrierAdditions,
}: any) => (
  <NotInvitableCarriers
    mode={"air"}
    shipperId={shipperId}
    managedCompanyType={managedCompanyType}
    onShowManualCarrierAdditions={onShowManualCarrierAdditions}
    onShowBulkCarrierAdditions={onShowBulkCarrierAdditions}
  />
);
