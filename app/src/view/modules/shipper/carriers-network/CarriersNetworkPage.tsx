import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import {
  BoxOpenedIcon,
  Button,
  ShipIcon,
  TruckIcon,
  TruckLoadedIcon,
  UploadIcon,
  Flight3Icon
} from "@fourkites/elemental-atoms";

import { LoadsTrackingMode } from "state/BaseTypes";
import { useAppDispatch, useAppSelector } from "state/hooks";
import { CarrierAdditionsState } from "state/modules/shipper/CarrierAdditions";
import { CarriersNetworkDetailsState } from "state/modules/shipper/CarriersNetworkDetails";
import { UsersState } from "state/modules/Users";
import { showToast } from "view/components/base/toast/Toast";
import { FOURKITES_APP_URL } from "api/http/apiUtils";


import { downloadCsv } from "view/components/base/file-upload/FileUtils";
import TabbedContainer from "view/components/base/tabbed-container/TabbedContainer";

import BulkCarrierAdditions from "view/components/self-service/carrier-additions/bulk/BulkCarrierAdditions";
import CarrierInvite from "view/components/self-service/carrier-additions/invite/CarrierInvite";
import ManualCarrierAdditions from "view/components/self-service/carrier-additions/manual/ManualCarrierAdditions";
import CarrierOnboardingDownload from 'view/components/self-service/carrier-onboarding-survey/CarrierOnboardingDownload';
import { CarrierInvitationsState } from "state/modules/shipper/CarrierInvitations";
import {
  FtlCarriers,
  LtlCarriers,
  OceanCarriers,
  ParcelCarriers,
  AirCarriers,
} from "./CarrierComponents";

import styles from "./CarriersNetworkPage.module.scss";

const CarriersNetworkPage: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  const inviterToken = useAppSelector(
    CarrierInvitationsState.selectors.inviterToken()
  );

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const inviterTokenUrl = `${FOURKITES_APP_URL}self-service/signup?inviter=${inviterToken}`;
    

  /*****************************************************************************
   * STATE
   ****************************************************************************/
  const [mode, setMode] = useState<LoadsTrackingMode>("ftl");
  const [initialAddition, setinitialAddition] = useState<any>(null);
  const [isExportDisabled, setIsExportDisabled] = useState<boolean>(false);
  const [showTemplate, setShowTemplate] = useState<boolean>(false);
  const [showManualCarrierAdditions, setShowManualCarrierAdditions] =
    useState<boolean>(false);
  const [showBulkCarrierAdditions, setShowBulkCarrierAdditions] =
    useState<boolean>(false);

  // Shipper ID
  const shipperId: string = useAppSelector(UsersState.selectors.getCompanyId);
  // For this context, the manager company is a shipper
  const managerCompanyName: string =
    useAppSelector(UsersState.selectors.getCompanyName) || "";
  const managerCompanyId = shipperId;
  // The company type that is being managed
  const managedCompanyType = "carrier";

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/
  useEffect(() => {
    if (shipperId) {
      dispatch(
        CarrierInvitationsState.actions.getInviterToken({
          shipperId: managerCompanyId,
        })
      );
      getExportInfo();
    }
  }, [shipperId]);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const onShowManualCarrierAdditions = (addition: any) => {
    setShowBulkCarrierAdditions(false);
    setShowManualCarrierAdditions(true);
    setinitialAddition(addition);
  };

  const onCloseManualConnect = () => {
    setinitialAddition(null);
    setShowManualCarrierAdditions(false);
  };

  const onShowBulkCarrierAdditions = () => {
    setShowManualCarrierAdditions(false);
    setShowBulkCarrierAdditions(true);
  };

  const onShowCarrierInviteTemplate = () => {
    setShowTemplate(true);
  };

  const onCloseBulkConnect = () => {
    setShowBulkCarrierAdditions(false);
  };

  const onCloseCarrierInvite = () => {
    setShowTemplate(false);
  };

  const onDownloadBulkTemplate = () => {
    dispatch(CarrierAdditionsState.actions.getBulkListTemplate({ mode })).then(
      (template) => {
        downloadCsv(template?.payload?.data, "carriers-list");
      }
    );
  };

  const getExportInfo = async () => {
    let response = await dispatch(
      CarriersNetworkDetailsState.actions.getExportInfo({
        shipperId: managerCompanyId,
      })
    );

    const isDisabled =
      response.payload === "multiple_export_within_threshold_time";
    setIsExportDisabled(isDisabled);
  };

  const exportCarriersList = async () => {
    const response = await dispatch(
      CarriersNetworkDetailsState.actions.exportCarrierList({
        shipperId: managerCompanyId,
      })
    );
    if ("error" in response) {
      showToast(
        t("Error"),
        t("Couldn't initiate export for carriers list."),
        "error"
      );
      return;
    }
    setIsExportDisabled(true);
    showToast(
      t("Export carrier list"),
      t("You will get the results in your email shortly."),
      "ok"
    );
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const tabs = [
    {
      title: "FTL",
      id: "ftl",
      icon: <TruckIcon />,
      tabComponent: (
        <FtlCarriers
          managerCompanyName={managerCompanyName}
          shipperId={shipperId}
          managedCompanyType={managedCompanyType}
          onShowManualCarrierAdditions={onShowManualCarrierAdditions}
          onDownloadBulkTemplate={onDownloadBulkTemplate}
          onShowBulkCarrierAdditions={onShowBulkCarrierAdditions}
          onShowCarrierInviteTemplate={onShowCarrierInviteTemplate}
        />
      ),
    },
    {
      title: "LTL",
      id: "ltl",
      icon: <TruckLoadedIcon />,
      tabComponent: (
        <LtlCarriers
          shipperId={shipperId}
          managedCompanyType={managedCompanyType}
          onShowManualCarrierAdditions={onShowManualCarrierAdditions}
          onShowBulkCarrierAdditions={onShowBulkCarrierAdditions}
        />
      ),
    },
    {
      title: "Parcel",
      id: "parcel",
      icon: <BoxOpenedIcon />,
      tabComponent: (
        <ParcelCarriers
          shipperId={shipperId}
          managedCompanyType={managedCompanyType}
          onShowManualCarrierAdditions={onShowManualCarrierAdditions}
          onShowBulkCarrierAdditions={onShowBulkCarrierAdditions}
        />
      ),
    },
    {
      title: "Ocean + Freight Forwarders",
      id: "ocean",
      icon: <ShipIcon />,
      tabComponent: (
        <OceanCarriers
          shipperId={shipperId}
          managedCompanyType={managedCompanyType}
          onShowManualCarrierAdditions={onShowManualCarrierAdditions}
          onShowBulkCarrierAdditions={onShowBulkCarrierAdditions}
        />
      ),
    },
    {
      title: "Air + Freight Forwarders",
      id: "air",
      icon: <Flight3Icon />,
      tabComponent: (
        <AirCarriers
          shipperId={shipperId}
          managedCompanyType={managedCompanyType}
          onShowManualCarrierAdditions={onShowManualCarrierAdditions}
          onShowBulkCarrierAdditions={onShowBulkCarrierAdditions}
        />
      ),
    },
  ];

  return (
    <div
      className={styles.pageContainer}
      data-testid="carriers-network-page-container"
    >
      <div className={styles.container}>
        <div className={styles.content}>
          <div className={styles.exportButtons}>
          <Button
            theme="secondary"
            size={"medium"}
            onClick={exportCarriersList}
            disabled={isExportDisabled}
            data-testid="export-carrier-list-button"
          >
            <UploadIcon fill="#000" iconClass={"button-icon-left"} />
            {t("Export Carrier List")}
          </Button>
          
          <CarrierOnboardingDownload
            shipperPermalink={shipperId}
            buttonText={t("Download Onboarding Data")}
          />
        </div>
          <TabbedContainer
            tabs={tabs}
            selectedTab={mode}
            onSelectTab={(tabId: LoadsTrackingMode) => setMode(tabId)}
          />
        </div>
      </div>

      <ManualCarrierAdditions
        managerCompanyName={managerCompanyName}
        managerCompanyId={managerCompanyId}
        managedCompanyType={managedCompanyType}
        mode={mode}
        initialAddition={initialAddition}
        show={showManualCarrierAdditions}
        onShowBulkCarrierAdditions={onShowBulkCarrierAdditions}
        onClose={onCloseManualConnect}
      />

      <BulkCarrierAdditions
        mode={mode}
        managerCompanyId={managerCompanyId}
        onDownloadBulkTemplate={onDownloadBulkTemplate}
        show={showBulkCarrierAdditions}
        onClose={onCloseBulkConnect}
      />

      <CarrierInvite
        show={showTemplate}
        onClose={onCloseCarrierInvite}
        surveyLink={inviterTokenUrl}
      />
    </div>
  );
};

export default CarriersNetworkPage;
