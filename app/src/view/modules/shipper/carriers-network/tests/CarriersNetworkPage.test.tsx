import { cleanup, fireEvent } from "@testing-library/react";
import copyToClipboard from "copy-to-clipboard";

import store from "state/store";

import CarriersNetworkPage from "../CarriersNetworkPage";
import { FtlCarriers } from "../CarrierComponents";

import {
  mockedUseSelector,
  renderWithRedux,
  changeState,
  toBeValidAssertion,
  toHaveTextContentAssertion,
  mockUseLocation,
} from "tests/TestUtils";

import { inviteCodeUrl } from "tests/TestConstants";

import {
  mockAppState,
  mockAppStateAfterCompaniesSearch,
  mockAppStateWithInviteToken,
  mockAppStateWithoutCarriers,
} from "./MockAppState";

import { toKebabCase } from "view/components/base/StringUtils";

mockUseLocation("/self-service/shipper/carriers", "", "", "6lelpr");
// TODO: These should be moved to the utils file as functions
// TODO: create mockTranslation()
jest.mock("react-i18next", () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

// TODO: create mockUseSelector()
jest.mock("react-redux", () => ({
  ...jest.requireActual("react-redux"),
  useSelector: jest.fn(),
}));

// TODO: create mockCopyToClipboard()
jest.mock("copy-to-clipboard", () => {
  return jest.fn();
});

/*
 * CarriersNetworkPage Test Suite
 */

describe("CarriersNetworkPage", () => {
  let ftlModeProps: any;
  const clickHandler = jest.fn();

  const renderComponent = () => renderWithRedux(<CarriersNetworkPage />, store);

  beforeEach(() => {
    ftlModeProps = {
      managerCompanyName: "Ashish shipper",
      shipperId: "ashish-shipper",
      managedCompanyType: "carrier",
      onShowManualCarrierAdditions: clickHandler,
      onDownloadBulkTemplate: clickHandler,
      onShowBulkCarrierAdditions: clickHandler,
    };

    mockedUseSelector.mockImplementation((callback) => {
      return callback(mockAppState);
    });
  });

  afterEach(() => {
    cleanup();
    mockedUseSelector.mockClear();
  });

  const renderFTLComponent = () =>
    renderWithRedux(<FtlCarriers {...ftlModeProps} />, store);

  it("should render the CarriersNetworkPage component", () => {
    const { getByTestId } = renderComponent();
    const testComponent = getByTestId(`carriers-network-page-container`);
    toBeValidAssertion(testComponent);
  });

  it("should have the respective modes", () => {
    const { getByTestId } = renderComponent();

    const ftlModeComponent = getByTestId(`ftl`);
    const ltlModeComponent = getByTestId(`ltl`);
    const parcelModeComponent = getByTestId(`parcel`);
    const oceanModeComponent = getByTestId(`ocean`);

    toBeValidAssertion(ftlModeComponent);
    toHaveTextContentAssertion(ftlModeComponent, "FTL");

    toBeValidAssertion(ltlModeComponent);
    toHaveTextContentAssertion(ltlModeComponent, "LTL");

    toBeValidAssertion(parcelModeComponent);
    toHaveTextContentAssertion(parcelModeComponent, "Parcel");

    toBeValidAssertion(oceanModeComponent);
    toHaveTextContentAssertion(oceanModeComponent, "Ocean");
  });

  it("should have export carrier list button", () => {
    const { getByTestId } = renderComponent();

    const exportCarrierListButtonComponent = getByTestId(
      `export-carrier-list-button`
    );

    toBeValidAssertion(exportCarrierListButtonComponent);
    toHaveTextContentAssertion(
      exportCarrierListButtonComponent,
      "Export Carrier List"
    );
  });

  it("should have add or invite carrier button and its svgs", () => {
    const { getByTestId } = renderFTLComponent();

    const addOrInviteCarrierButtonComponent = getByTestId(
      `add-or-invite-carrier`
    );

    toBeValidAssertion(addOrInviteCarrierButtonComponent);
    toHaveTextContentAssertion(
      addOrInviteCarrierButtonComponent,
      "Add / Invite Carrier"
    );

    expect(addOrInviteCarrierButtonComponent.children.length).toBe(2);
  });

  it("should have 2 submenus under add or invite carrier button if inviter token not available", () => {
    const { getByTestId } = renderFTLComponent();

    const addOrInviteCarrierButton = getByTestId(`add-or-invite-carrier`);

    fireEvent.click(addOrInviteCarrierButton);

    const flyoutItemsComponent = getByTestId(`flyout-items`);
    expect(flyoutItemsComponent.children.length).toBe(2);

    const searchAndAddCarrierMenu = getByTestId(`search-and-add-carrier`);
    const bulkUploadCarriersMenu = getByTestId(`bulk-upload-carriers`);

    expect(searchAndAddCarrierMenu).toBeDefined();
    toHaveTextContentAssertion(searchAndAddCarrierMenu, "Search & Add");

    expect(bulkUploadCarriersMenu).toBeDefined();
    toHaveTextContentAssertion(bulkUploadCarriersMenu, "Bulk Upload");
  });

  it("should have 3 submenus under add or invite carrier button if inviter token available", async () => {
    changeState(mockAppStateWithInviteToken);
    const { getByTestId } = renderFTLComponent();

    const addOrInviteCarrierButton = getByTestId(`add-or-invite-carrier`);
    fireEvent.click(addOrInviteCarrierButton);

    const flyoutItemsComponent = getByTestId(`flyout-items`);
    expect(flyoutItemsComponent.children.length).toBe(3);

    const copyInviteCodeMenu = getByTestId(`copy-invite-code`);
    expect(copyInviteCodeMenu).toBeDefined();

    toHaveTextContentAssertion(copyInviteCodeMenu, "Copy Invite Link");
    await fireEvent.click(copyInviteCodeMenu);

    expect(copyToClipboard).toHaveBeenCalledTimes(1);
    expect(copyToClipboard).toBeCalledWith(inviteCodeUrl);
  });

  it("should have Invited, In Progress, Connected and Disconnected button groups for FTL", () => {
    const { getByTestId, getByText } = renderFTLComponent();
    // not taking other data test ids - button group and buttons inside button group
    const networkStatusButtonGroup = getByTestId(`button-group-component`);

    expect(networkStatusButtonGroup).toBeDefined();
    expect(networkStatusButtonGroup.children.length).toEqual(4);

    expect(getByText(/Invited/i)).toBeDefined();
    expect(getByText(/In Progress/i)).toBeDefined();
    // TODO: this is failing and needs to be fixed
    //expect(getByText(/Connected/i)).toBeDefined();
    expect(getByText(/Disconnected/i)).toBeDefined();
  });

  it("should have right columns in the carriers table for connected", () => {
    const { getByTestId } = renderFTLComponent();

    const carrierTableHeader = getByTestId(
      `company-name-cell-header-container`
    );

    const trackingMethodTableHeader = getByTestId(
      toKebabCase("Tracking Method")
    );
    const loadVolumeTableHeader = getByTestId(toKebabCase("Load Volume"));
    const trackingPercentTableHeader = getByTestId(toKebabCase("Tracking %"));

    const loadStatusTableHeader = getByTestId(`status`);

    expect(carrierTableHeader).toBeDefined();
    toHaveTextContentAssertion(carrierTableHeader, "Carrier");

    expect(trackingMethodTableHeader).toBeDefined();
    toHaveTextContentAssertion(trackingMethodTableHeader, "Tracking Method");

    expect(loadVolumeTableHeader).toBeDefined();
    toHaveTextContentAssertion(loadVolumeTableHeader, "Load Volume");

    expect(trackingPercentTableHeader).toBeDefined();
    toHaveTextContentAssertion(trackingPercentTableHeader, "Tracking %");

    expect(loadStatusTableHeader).toBeDefined();
    toHaveTextContentAssertion(loadStatusTableHeader, "Status");
  });

  it("should show Get connected panel component when shipper have no carriers", () => {
    changeState(mockAppStateWithoutCarriers);

    const { getByTestId, getByText } = renderFTLComponent();

    const floatingChipButton = getByTestId(`floating-chip-button`);
    const greetMessageLabel = getByTestId(`greet-msg`);
    const userAssociatedCompany = getByTestId(`user-associated-company`);
    const uploadCarrierListButton = getByTestId(`upload-carrier-list-button`);
    const downloadCsvTemplateButton = getByTestId(
      `download-csv-template-button`
    );
    const copyInvitationCodeButton = getByTestId(`copy-invitation-code-button`);
    const companySuggestionsInput = getByTestId(`input-component-input`);

    expect(floatingChipButton).toBeDefined();
    toHaveTextContentAssertion(
      floatingChipButton,
      "Choose an option to connect"
    );

    expect(greetMessageLabel).toBeDefined();
    toHaveTextContentAssertion(
      greetMessageLabel,
      "Hello ramya shanmugasundaram!"
    );

    expect(userAssociatedCompany).toBeDefined();
    toHaveTextContentAssertion(
      userAssociatedCompany,
      "Welcome The Ashish shipper! Let's get started & connect with your Carriers."
    );

    expect(getByText(/Upload all your Carriers/i)).toBeDefined();
    expect(getByText(/Search & Add Manually/i)).toBeDefined();
    expect(getByText(/Invite your Carriers at Scale/i)).toBeDefined();

    expect(uploadCarrierListButton).toBeDefined();
    toHaveTextContentAssertion(uploadCarrierListButton, "Upload Carrier List");

    expect(downloadCsvTemplateButton).toBeDefined();
    toHaveTextContentAssertion(
      downloadCsvTemplateButton,
      "Download CSV Template"
    );

    expect(copyInvitationCodeButton).toBeDefined();
    toHaveTextContentAssertion(copyInvitationCodeButton, "Copy Invite Link");

    expect(companySuggestionsInput).toBeDefined();
    expect(companySuggestionsInput.getAttribute("placeholder")).toEqual(
      "Search for Carrier Name, USDOT#, MC# or SCAC"
    );

    fireEvent.click(copyInvitationCodeButton);
    expect(copyToClipboard).toBeCalledWith(inviteCodeUrl);

    expect(companySuggestionsInput.getAttribute("value")).toEqual("");
    fireEvent.change(companySuggestionsInput, { target: { value: "ashish" } });
    expect(companySuggestionsInput.getAttribute("value")).toEqual("ashish");
  });

  it("should check company search results after typing in the search and add box for no carriers present", () => {
    changeState(mockAppStateAfterCompaniesSearch);

    const { getByTestId } = renderFTLComponent();
    const companySuggestionsInput = getByTestId(`input-component-input`);

    fireEvent.change(companySuggestionsInput, {
      target: { value: "ashish" },
    });

    const suggestions = getByTestId("suggestions");
    const companySummary = getByTestId("company-summary-container");
    const companyLogo = getByTestId("company-logo");
    const companyNameHeader = getByTestId("company-name-header");
    const companyUsDot = getByTestId("company-usdot");

    expect(suggestions).toBeDefined();
    expect(companySummary).toBeDefined();
    expect(companyLogo).toBeDefined();
    toHaveTextContentAssertion(
      companyNameHeader,
      "ESCARPITA CONSTRUCTION COMPANY INC"
    );
    toHaveTextContentAssertion(companyUsDot, "2374312");
  });

  // TODO
  // 1. Test Other Modes - LTL, Parcel, Ocean
  // 2. Checks for different status columns -> invited, in progress, disconnected
  // 3. Check details side panels
});
