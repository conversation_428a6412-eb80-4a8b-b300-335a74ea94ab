import { getShippersMockAppState } from "tests/ShippersMockAppState";
import { inviteCode } from "tests/TestConstants";
export const mockAppState = getShippersMockAppState();

export const mockAppStateWithInviteToken = {
  ...mockAppState,
  carrierInvitations: {
    inviterToken: inviteCode,
  },
};

export const mockAppStateWithoutCarriers = {
  ...mockAppState,
  carriersNetwork: {
    carriers: {
      ftl: {
        data: [],
        filters: {
          query: null,
          networkStatus: "connected",
          filterBy: [],
        },
        loading: false,
        editing: false,
        error: false,
        pagination: {
          currentPage: 0,
          pageSize: 0,
          totalItems: 0,
          totalPages: 0,
        },
      },
    },
  },
  shipperIndicators: {
    ltl: { data: {}, loading: false, error: false },
    ftl: {
      data: {},
      loading: false,
      error: false,
    },
    parcel: { data: {}, loading: false, error: false },
    ocean: { data: {}, loading: false, error: false },
  },
  shippersNetwork: {
    shippers: {
      ltl: {},
      ftl: {
        data: [],
        filters: {
          query: null,
          networkStatus: "connected",
          filterBy: [],
        },
        sortBy: [],
        pagination: {
          currentPage: 0,
          pageSize: 0,
          totalItems: 0,
          totalPages: 0,
        },
        loading: true,
        editing: false,
        error: false,
      },
      parcel: {},
      ocean: {},
    },
  },
  carrierInvitations: {
    inviterToken: inviteCode,
  },
  users: {
    currentUser: {
      name: "ramya shanmugasundaram",
      userId: "ramya-shanmugasundaram",
    },
  },
};

export const mockAppStateAfterCompaniesSearch = {
  ...mockAppStateWithoutCarriers,
  companiesSearch: {
    searchedCompanies: {
      data: [
        {
          identifiers: [{ type: "usdot", value: "2374312" }],
          permalink: null,
          name: "ESCARPITA CONSTRUCTION COMPANY INC",
          id: null,
          logo: null,
          identifications: [
            { type: "usdot", value: "2374312" },
            { type: "mc", value: null },
            { type: "scac", value: null },
          ],
          type: "carrier",
          address: { country: "United States", country_code: "US" },
          status: { data: null, network: null },
        },
      ],
      searching: false,
      error: false,
    },
  },
};
