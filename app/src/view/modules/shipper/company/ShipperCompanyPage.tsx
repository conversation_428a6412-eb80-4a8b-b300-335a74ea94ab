import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import {
  HierarchicalSidebar,
  SidebarSection,
} from "@fourkites/elemental-hierarchical-sidebar";

import ShipperCompanyRouter from "router/shipper/ShipperCompanyRouter";

import styles from "./ShipperCompanyPage.module.scss";

const ShipperCompanyPage: React.FC = () => {
  const { t } = useTranslation();

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [collapsed, setCollapsed] = useState<boolean>(false);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/
  const onChangeCollapse = (collapsed: boolean) => {
    setCollapsed(collapsed);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const sidebarSectionLinks = [
    {
      title: t("General"),
      id: "general",
    },
  ];

  return (
    <div className={styles.pageContainer}>
      <div className={styles.container}>
        <div>
          <HierarchicalSidebar
            collapsed={collapsed}
            onChangeCollapse={onChangeCollapse}
          >
            {!collapsed && (
              <SidebarSection
                key={"general"}
                title={"Company Settings".toUpperCase()}
                links={sidebarSectionLinks}
                defaultActiveLinkId={"general"}
              />
            )}
          </HierarchicalSidebar>
        </div>

        <div className={styles.content}>
          <ShipperCompanyRouter />
        </div>
      </div>
    </div>
  );
};

export default ShipperCompanyPage;
