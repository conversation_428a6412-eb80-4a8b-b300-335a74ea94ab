import { useEffect } from "react";
import { useTranslation } from "react-i18next";

import { Spinner } from "@fourkites/elemental-loading-indicator";

import { useAppSelector, useAppDispatch } from "state/hooks";
import { ShipperCompanyDetailsState } from "state/modules/shipper/ShipperCompanyDetails";
import { UsersState } from "state/modules/Users";

import BreadcrumbsHeader from "view/components/base/breadcrumbs-header/BreadcrumbsHeader";
import SubPagePanel from "view/components/base/containers/SubPagePanel";
import CompanyProfile from "view/components/self-service/company-profile/CompanyProfile";

import styles from "./ShipperCompanyGeneralPage.module.scss";

const ShipperCompanyGeneralPage = ({}: any) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  // Shipper ID
  const shipperId: string = useAppSelector(UsersState.selectors.getCompanyId);

  const shipperCompanyDetails: any = useAppSelector(
    ShipperCompanyDetailsState.selectors.shipperCompanyDetails()
  );

  const isLoadingShipperCompanyDetails: boolean = useAppSelector(
    ShipperCompanyDetailsState.selectors.isLoadingShipperCompanyDetails()
  );
  const isEditingShipperCompanyDetails: boolean = useAppSelector(
    ShipperCompanyDetailsState.selectors.isEditingShipperCompanyDetails()
  );

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  useEffect(() => {
    dispatch(
      ShipperCompanyDetailsState.actions.retrieveShipperCompanyDetails({
        shipperId,
      })
    );
  }, [shipperId]);

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const showLoader =
    Object.keys(shipperCompanyDetails).length === 0 &&
    isLoadingShipperCompanyDetails;
  const showHeaderLoader =
    shipperCompanyDetails != null &&
    (isLoadingShipperCompanyDetails || isEditingShipperCompanyDetails);

  const breadcrumbTitles = [t("General Settings")];

  return (
    <SubPagePanel>
      <div
        className={styles.titleContainer}
        data-test-id="general-settings-title"
      >
        <BreadcrumbsHeader
          titles={breadcrumbTitles}
          children={[
            showHeaderLoader ? (
              <div className={styles.headerLoader}>
                <label>{t("Loading details")}</label>
                <Spinner isLoading size="small" />
              </div>
            ) : null,
          ]}
        />
      </div>

      {shipperCompanyDetails && (
        <div className={styles.container}>
          <CompanyProfile
            showLoader={showLoader}
            companyId={shipperId}
            companyDetails={shipperCompanyDetails}
          />
        </div>
      )}
    </SubPagePanel>
  );
};

export default ShipperCompanyGeneralPage;
