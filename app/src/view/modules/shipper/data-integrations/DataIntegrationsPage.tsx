import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { useHistory } from "react-router-dom";

import {
  HierarchicalSidebar,
  SidebarSection,
} from "@fourkites/elemental-hierarchical-sidebar";

import ShipperDataIntegrationsRouter, {
  dataIntegrationRoutes,
  getShipperDataIntegrationsRouteId,
} from "router/shipper/ShipperDataIntegrationsRouter";

import { useAppSelector } from "state/hooks";
import UsersState from "state/modules/Users";

import { LockFillIcon } from "@fourkites/elemental-atoms";

import styles from "./DataIntegrationsPage.module.scss";

const DataIntegrationsPage: React.FC = () => {
  const { t } = useTranslation();
  const history = useHistory();

  /*****************************************************************************
   * REDUX
   ****************************************************************************/
  const companyLicenses: string = useAppSelector(
    UsersState.selectors.getCompanyLicenses
  );
  const hasCallbacksLicense = companyLicenses.indexOf("callbacks") !== -1;
  const superAdmin = useAppSelector(UsersState.selectors.getIsSuperAdmin);

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [collapsed, setCollapsed] = useState<boolean>(false);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const onChangeCollapse = (collapsed: boolean) => {
    setCollapsed(collapsed);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  let sidebarSections = [
    {
      title: t("Shipments").toUpperCase(),
      links: [
        {
          title: t("Sample Reference Numbers"),
          id: dataIntegrationRoutes.loadMatching,
          to: dataIntegrationRoutes.loadMatching,
        },
        {
          title: t("Manual File Uploads"),
          id: dataIntegrationRoutes.batchUploads,
          to: dataIntegrationRoutes.batchUploads,
        },
        {
          title: t("Email Integrations"),
          id: dataIntegrationRoutes.email,
          to: dataIntegrationRoutes.email,
        },
        {
          title: "Advanced Integrations",
          sublinks: [
            {
              title: t("FourKites API"),
              id: dataIntegrationRoutes.platformApi,
              to: dataIntegrationRoutes.platformApi,
            },
          ],
        },
      ],
    },
    {
      title: t("Validations").toUpperCase(),
      links: [
        {
          title: t("Loads Creation File"),
          id: dataIntegrationRoutes.fileValidations,
          to: dataIntegrationRoutes.fileValidations,
        },
      ],
    },
  ];

  const webhooksSection: any = hasCallbacksLicense
    ? {
        title: t("Webhooks"),
        id: dataIntegrationRoutes.webhooks,
        sublinks: [
          {
            title: t("Configurations"),
            id: dataIntegrationRoutes.webhooks,
            to: dataIntegrationRoutes.webhooks,
          },
          {
            title: t("Logs & Errors"),
            id: dataIntegrationRoutes.webhookLogs,
            to: dataIntegrationRoutes.webhookLogs,
          },
          {
            title: t("Webhook Schedule Maintenance"),
            id: dataIntegrationRoutes.webhookMaintenance,
            to: dataIntegrationRoutes.webhookMaintenance,
          },
        ],
      }
    : {
        title: t("Webhooks"),
        id: dataIntegrationRoutes.webhooks,
        to: dataIntegrationRoutes.webhooks,
        icon: LockFillIcon,
      };

  sidebarSections[0].links.push(webhooksSection);

  return (
    <div className={styles.pageContainer} data-test-id="data-integrations-page">
      <div className={styles.container}>
        <div>
          <HierarchicalSidebar
            collapsed={collapsed}
            onChangeCollapse={onChangeCollapse}
          >
            {!collapsed && (
              <div>
                {sidebarSections?.map((section: any, index: number) => {
                  return (
                    <SidebarSection
                      key={index}
                      title={section.title}
                      links={section.links}
                      defaultActiveLinkId={
                        getShipperDataIntegrationsRouteId(history) || ""
                      }
                    />
                  );
                })}
              </div>
            )}
          </HierarchicalSidebar>
        </div>

        <div className={styles.content}>
          <ShipperDataIntegrationsRouter />
        </div>
      </div>
    </div>
  );
};

export default DataIntegrationsPage;
