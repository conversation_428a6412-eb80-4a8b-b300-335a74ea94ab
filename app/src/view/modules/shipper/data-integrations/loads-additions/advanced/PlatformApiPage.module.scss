@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container {
  > div > div {
    height: calc(100vh + -113px);
  }
}

.content {
  display:flex;
  align-content: center;
  justify-content: center;
  padding-top: 32px;

  > div {
    display: flex;
    margin-right: 24px;
    width: 100%;

    &:last-child {
      margin-right: 0;
    }
  }
}

.cardContent {
  display:flex;
  width: 100%;
  flex-direction: column;
  align-content: center;
  align-items: center;
  padding: 32px;
}

.cardTitle {
  color: $color-primary-500;
  font-size: 24px;
  font-weight: bold;
  letter-spacing: 0;
  line-height: 28px;
  text-align: center;
  margin-bottom: 24px;
}

.cardDescription {
  color: $color-neutral-700;
  font-size: 20px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 28px;
  text-align: center;
  margin-bottom: 42px;
 }

.cardImg {
  width: 196px;
  height: 196px;
  margin-bottom: 40px;
}

.contentTitle {
  padding: 10px;
}
