import React from "react";
import { useTranslation } from "react-i18next";

import { LinkButton } from "@fourkites/elemental-atoms";

/*
import {
  HierarchicalSidebar,
  SidebarSection,
} from "@fourkites/elemental-hierarchical-sidebar";
*/

import developerPortal from "assets/img/developerPortal.png";
//import tmsIntegration from "assets/img/tmsIntegration.png";

import { fourkitesUrls } from "api/http/apiUtils";

import BreadcrumbsHeader from "view/components/base/breadcrumbs-header/BreadcrumbsHeader";
import Card from "view/components/base/card/Card";

import styles from "./PlatformApiPage.module.scss";

import SubPagePanel from "view/components/base/containers/SubPagePanel";

const PlatformApiPage: React.FC = () => {
  const { t } = useTranslation();

  const breadcrumbTitles = [
    t("Data Integrations"),
    t("Shipments"),
    t("FourKites API"),
  ];

  return (
    <div
      className={styles.container}
      data-test-id="platform-api-page-container"
    >
      <SubPagePanel>
        <div className={styles.titleContainer}>
          <BreadcrumbsHeader titles={breadcrumbTitles} />
        </div>

        <div className={styles.content}>
          {/*
        // TODO: removing data integrations page while it's not ready
        <Card>
          <div className={styles.cardContent}>
            <img
              alt="Contact FourKites"
              className={styles.cardImg}
              src={tmsIntegration}
            />
            <span className={styles.cardTitle}>
              {t("Want to automate integration with your TMS?")}
            </span>
            <span className={styles.cardDescription}>
              {t(
                "Our FourKites Implementation team can help you with advanced FTP or API Integrations."
              )}
            </span>

            <LinkButton size="large" href={fourkitesUrls.help}>
              {t("Contact FourKites")}
            </LinkButton>
          </div>
        </Card>
        */}

          <Card>
            <div className={styles.cardContent}>
              <img
                alt="API integration"
                className={styles.cardImg}
                src={developerPortal}
              />
              <span className={styles.cardTitle}>
                {t("Create shipments using FourKites API")}
              </span>
              <span className={styles.cardDescription}>
                {t("Use FourKites APIs to create, update or delete shipments.")}
              </span>

              <LinkButton size="large" href={fourkitesUrls.developerPortal}>
                {t("Open FourKites Developer Portal")}
              </LinkButton>
            </div>
          </Card>

          <div className={styles.cardContent} />
        </div>
      </SubPagePanel>
    </div>
  );
};

export default PlatformApiPage;
