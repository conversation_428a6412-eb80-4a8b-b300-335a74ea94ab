@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container {
  > div > div {
    height: calc(100vh + -113px);
    padding: 0;
    background-color: white;
  }
}

.loader {
  width: 100%;
  display: flex;
  align-content: center;
  justify-content: center;
  margin-top: 32px;
  margin-bottom: 32px;
}

.headerWrapper {
  padding: 32px;
  padding-top: 27px;
  background-color: $color-neutral-50;
}

.headerButton {
  > button {
    display: flex;
    align-items: center;
    align-content: center;
    min-width: fit-content;
  }
}

.headerLoader {
  composes: loader;
  width: fit-content;
  margin: 0;
  margin-right: 16px;

  > label {
    margin-right: 16px;
  }
}
