import { useEffect, useState, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { useLocation } from "react-router-dom";

import { PlusIcon, Button } from "@fourkites/elemental-atoms";
import { Spinner } from "@fourkites/elemental-loading-indicator";

import { PaginationType } from "state/BaseTypes";
import { useAppSelector, useAppDispatch } from "state/hooks";
import { UsersState } from "state/modules/Users";
import { BatchListsLoadsAdditionsState } from "state/modules/shipper/BatchListsLoadsAdditions";

import { showToast } from "view/components/base/toast/Toast";
import BreadcrumbsHeader from "view/components/base/breadcrumbs-header/BreadcrumbsHeader";
import SubPagePanel from "view/components/base/containers/SubPagePanel";

import BatchUploadsLoadsAdditionsTable from "./table/BatchUploadsLoadsAdditionsTable";
import BatchUploadsLoadsAdditionsModal from "./modals/BatchUploadsLoadsAdditionsModal";

import styles from "./BatchUploadsLoadsAdditionsPage.module.scss";

const BatchUploadsLoadsAdditionsPage = () => {
  const { t } = useTranslation();
  /*****************************************************************************
   * URL HANDLING
   ****************************************************************************/
  const location = useLocation();

  // Get external parameters from URL
  const query = new URLSearchParams(location.search);
  const showModalUrlParam = query.get("show-additions-modal");
  const modeUrlParam = query.get("mode");

  /*****************************************************************************
   * REDUX
   ****************************************************************************/

  const dispatch = useAppDispatch();

  const shipperId: string = useAppSelector(UsersState.selectors.getCompanyId);

  const batchLists = useAppSelector(
    BatchListsLoadsAdditionsState.selectors.batchLists()
  );
  const isLoading = useAppSelector(
    BatchListsLoadsAdditionsState.selectors.isRetrieving()
  );

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [showBatchUploadsModal, setShowBatchUploadsModal] =
    useState<boolean>(false);
  const [presetMode, setPresetMode] = useState<string | null>(null);
  const [lastPageFetched, setLastPageFetched] = useState<number>(-1);
  const [lastPageParams, setLastPageParams] = useState<any>({
    index: -1,
    size: -1,
  });

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  /*
   * Fetches lists based on shipper id
   */
  useEffect(() => {
    fetchBatchLists({ ...DEFAULT_PAGINATION_PARAMETERS }, true);
  }, [shipperId]);

  /*
   * Shows modal depending on url parameters
   */
  useEffect(() => {
    // Return if modal already open
    if (showBatchUploadsModal) {
      return;
    }

    if (showModalUrlParam) {
      setShowBatchUploadsModal(true);
      setPresetMode(modeUrlParam);
    }
  }, [showModalUrlParam, modeUrlParam]);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  /*
   * Fetchs assets for fleet
   */
  const fetchBatchLists = useCallback(
    async ({ pageSize, pageIndex }: PaginationType, forceReload?: boolean) => {
      // Don't fetch if we don't have shipper id
      if (!shipperId) {
        return;
      }

      // Don't fetch if it's the same page
      if (
        !forceReload &&
        pageIndex === lastPageParams.index &&
        pageSize === lastPageParams.size
      ) {
        return;
      }

      await dispatch(
        BatchListsLoadsAdditionsState.actions.retrieveBatchLists({
          shipperId,
          modes: [],
          pageIndex: pageIndex + 1,
          pageSize: pageSize,
        })
      );

      setLastPageParams({ index: pageIndex, size: pageSize });
    },
    [shipperId, lastPageFetched, lastPageParams, setLastPageParams]
  );

  const onAddBatchList = () => {
    setShowBatchUploadsModal(true);
  };

  const onClose = () => {
    setShowBatchUploadsModal(false);
  };

  const onSaveBatchList = () => {
    setShowBatchUploadsModal(false);

    // Fetch lists of integrations again
    fetchBatchLists({ ...DEFAULT_PAGINATION_PARAMETERS }, true);
  };

  const onCreateExportableBatchList = async (batchListId: string | number) => {
    const response = await dispatch(
      BatchListsLoadsAdditionsState.actions.createExportableBatchList({
        shipperId,
        batchListId,
      })
    );

    if ("error" in response) {
      showToast(
        t("Error"),
        t("There was an error while exporting the file."),
        "error"
      );
      return;
    }

    showToast(
      t("Success"),
      t("Your report was created and you will receive it by email soon."),
      "ok"
    );
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/
  const showHeaderLoader = isLoading && batchLists.length > 0;
  const showTableLoader = isLoading && batchLists.length === 0;

  const breadcrumbTitles = [
    t("Data Integrations"),
    t("Shipments"),
    t("Manual File Uploads"),
  ];

  const headerItems = [
    showHeaderLoader ? (
      <div className={styles.headerLoader}>
        <label>{t("Loading Integrations")}</label>
        <Spinner isLoading size="small" />
      </div>
    ) : null,
    <div className={styles.headerButton}>
      <Button size={"large"} onClick={onAddBatchList}>
        <PlusIcon fill="#fff" iconClass={"button-icon-left"} />
        {t("Upload file")}
      </Button>
    </div>,
  ];

  return (
    <div
      className={styles.container}
      data-test-id="batch-uploads-loads-additions-page-container"
    >
      <SubPagePanel>
        <div className={styles.headerWrapper}>
          <BreadcrumbsHeader titles={breadcrumbTitles} children={headerItems} />
        </div>

        {showTableLoader ? (
          <div className={styles.loader}>
            <Spinner isLoading size="medium" />
          </div>
        ) : (
          <BatchUploadsLoadsAdditionsTable
            batchLists={batchLists}
            fetchBatchLists={fetchBatchLists}
            onCreateExportableBatchList={onCreateExportableBatchList}
          />
        )}
      </SubPagePanel>

      <BatchUploadsLoadsAdditionsModal
        show={showBatchUploadsModal}
        shipperId={shipperId}
        presetMode={presetMode}
        onClose={onClose}
        onSave={onSaveBatchList}
      />
    </div>
  );
};

const DEFAULT_PAGINATION_PARAMETERS = { pageSize: 10, pageIndex: 0 };

export default BatchUploadsLoadsAdditionsPage;
