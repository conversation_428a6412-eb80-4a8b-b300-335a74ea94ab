import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { Table } from "@fourkites/elemental-table";

import styles from "./BatchUploadsFilePreview.module.scss";

const BatchUploadsFilePreview = ({ fileName, filePreview }: any) => {
  const { t } = useTranslation();

  /*
   * Maps each header as a col
   */
  const columns = useMemo(() => {
    const cols = filePreview.length > 0 ? Object.keys(filePreview[0]) : [];
    return cols?.map((col: any, index: number) => ({
      Header: col,
      accessor: col || index,
    }));
  }, [filePreview]);

  /*
   * Memoizing data
   */
  const data = useMemo(() => {
    return filePreview || [];
  }, [filePreview]);

  /*****************************************************************************
   * RENDER
   ****************************************************************************/
  const paginationParams = {
    defaultPageSize: 25,
  };

  return (
    <div
      className={styles.previewWrapper}
      data-test-id="batch-uploads-file-preview-container"
    >
      <div id="preview-summary">
        <label>
          {t("Preview")} {fileName}
        </label>
        <label>{t("Showing first 10 results")}</label>
      </div>

      <Table
        data={data}
        columns={columns}
        rowHeight="small"
        variant="rounded"
        striped
        pagination={paginationParams}
      />
    </div>
  );
};

export default BatchUploadsFilePreview;
