@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container {
  display: flex;
  flex-direction: column;
  width:100%;
}

.form {
  display: flex;
  flex-direction: column;

  > label {
    font-weight: bold;
    margin-bottom: 24px;
  }

  > div[id="input-row"] {
    display: flex;
    flex-wrap: wrap;
    width: fit-content;

    > label {
      margin-top: 28px;
      font-size: 16px;
      letter-spacing: 0;
      line-height: 24px;
      color: $color-neutral-700;
    }
  }
}

.inputWrapper  {
  flex: 1;
  margin-right: 16px;
  margin-bottom: 12px;
  width: 330px;

  > div {
    width: 330px;

    > div {
      display: flex;

      > input {
        width: 100%;
      }
    }
  }
}

.linkWrapper{
  margin-bottom: 12px;

  > button {
    padding-left: 0;
    display: flex;
    align-items: center;
    align-content: center;
  }
}


.buttonWrapper{
  margin-top: 23px;

  > button {
    display: flex;
    align-items: center;
    align-content: center;
  }
}

.previewWrapper {
  display: flex;
  flex-direction: column;
  width: 100%;

  > div[id="preview-summary"] {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    max-width: 1200px;

    > label {
      color: $color-neutral-900;
      font-size: 18px;
      letter-spacing: 0;
      line-height: 27px;
    }
  }
}

.checkboxWrapper  {
  flex: 1;
  margin-top: 8px;
  margin-bottom: 8px;
}

.modeIcon{
  margin-right: 8px;
  padding-top: 2px;
}
