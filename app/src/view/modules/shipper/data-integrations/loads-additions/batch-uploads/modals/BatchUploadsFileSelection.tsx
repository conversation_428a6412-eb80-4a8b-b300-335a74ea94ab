import { useEffect } from "react";
import { useTranslation } from "react-i18next";

import { DownloadIcon, LinkButton } from "@fourkites/elemental-atoms";
import { Select } from "@fourkites/elemental-select";

import FileFormatSelector, {
  getFileFormatKeyFromValue,
  getFileFormatValue,
} from "view/components/base/field-selectors/file-format-selector/FileFormatSelector";
import ModeSelector, {
  getModeValue,
  getModeKeyFromValue,
} from "view/components/base/field-selectors/mode-selector/ModeSelector";
import FileUpload from "view/components/base/file-upload/FileUpload";

import { isFieldInvalid } from "view/components/base/FormUtils";

import { useLoadsAdditionsTemplates } from "../../useLoadsAdditionsTemplates";

import styles from "./BatchUploadsFileSelection.module.scss";

const BatchUploadsFileSelection = ({
  shipperId,
  confirmed,
  form,
  fileName,
  onChangeFormField,
  onConfirmFile,
}: any) => {
  const { t } = useTranslation();

  const {
    nonEmptyModes,
    nonEmptyFileFormatsForMode,
    templateNames,
    templateUrl,
  } = useLoadsAdditionsTemplates(
    shipperId,
    form?.mode,
    form?.file_format,
    form?.template
  );

  /*
   * When the possible template options change, we need to reset the value
   */
  useEffect(() => {
    // Reset selection of preset template
    onChangeFormField("template", null);
  }, [templateNames]);

  const shouldDisableDownloadTemplateButton = !form?.template || !templateUrl;

  return (
    <div
      className={styles.container}
      data-test-id="batch-uploads-loads-additions-modal-file-selection-container"
    >
      {/* Commented as part of SELF-2204. Enable once KB is ready}
      {/* <div className={styles.linkWrapper}>
        <LinkButton variant="flat" href={fourkitesUrls.community} size="large">
          <InfoIcon className="button-icon-left" fill="#0e65e5" />
          {t("Learn how to export from TMS")}
        </LinkButton>
      </div> */}

      <div className={styles.form}>
        <div id="input-row">
          <div className={styles.inputWrapper} data-test-id="input-mode">
            <ModeSelector
              errorLabel="Field is required"
              enabledModes={nonEmptyModes}
              value={[getModeValue(form?.mode)]}
              showError={confirmed && isFieldInvalid(form?.mode)}
              onChange={(selectedOptions: any) =>
                onChangeFormField(
                  "mode",
                  getModeKeyFromValue(selectedOptions[0])
                )
              }
              required
            />
          </div>

          <div className={styles.inputWrapper} data-test-id="input-file-format">
            <FileFormatSelector
              enabledFormats={nonEmptyFileFormatsForMode}
              errorLabel="Field is required"
              value={[getFileFormatValue(form?.file_format)]}
              showError={confirmed && isFieldInvalid(form?.file_format)}
              onChange={(selectedOptions: any) =>
                onChangeFormField(
                  "file_format",
                  getFileFormatKeyFromValue(selectedOptions[0])
                )
              }
              required
            />
          </div>

          <div
            className={styles.inputWrapper}
            data-test-id="input-file-template"
          >
            <Select
              label={t("Template")}
              options={templateNames}
              value={[form?.template]}
              showError={confirmed && isFieldInvalid(form?.template)}
              onChange={(selectedOptions: any) =>
                onChangeFormField("template", selectedOptions[0])
              }
              required
            />
          </div>

          <div
            className={styles.buttonWrapper}
            data-test-id="button-download-template"
          >
            <LinkButton
              variant="flat"
              href={templateUrl}
              target="_blank"
              disabled={shouldDisableDownloadTemplateButton}
              style={
                shouldDisableDownloadTemplateButton
                  ? { pointerEvents: "none" }
                  : {}
              }
            >
              <DownloadIcon className="button-icon-left" fill="#0e65e5" />
              {t("Download template")}
            </LinkButton>
          </div>
        </div>
      </div>

      <div>{fileName}</div>

      {/* AllowedFileFormats includes windows excel formats */}
      <FileUpload
        allowedFileFormats={[
          // CSV
          "csv",
          "text/csv",
          "application/vnd.ms-excel",
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          // XML
          "xml",
          "text/xml",
          // EDI
          "edi",
        ]}
        onConfirmFile={onConfirmFile}
      />
    </div>
  );
};

export default BatchUploadsFileSelection;
