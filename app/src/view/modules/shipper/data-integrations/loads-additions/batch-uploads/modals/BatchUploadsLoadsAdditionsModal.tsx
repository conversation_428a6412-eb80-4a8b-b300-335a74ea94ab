import React, { useState, useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";

import { Modal } from "@fourkites/elemental-modal";

import { useAppDispatch, useAppSelector } from "state/hooks";
import { BatchListsLoadsAdditionsState } from "state/modules/shipper/BatchListsLoadsAdditions";
import { UsersState } from "state/modules/Users";

import { showToast } from "view/components/base/toast/Toast";
import { csvToArray } from "view/components/base/file-upload/FileUtils";

import { isFieldInvalid } from "view/components/base/FormUtils";

import BatchUploadsModalHeader from "./BatchUploadsModalHeader";
import BatchUploadsFilePreview from "./BatchUploadsFilePreview";
import BatchUploadsFileSelection from "./BatchUploadsFileSelection";

import BatchUploadsLoadsAdditionsModalProps from "./BatchUploadsLoadsAdditionsModal.types";

const BatchUploadsLoadsAdditionsModal = ({
  shipperId,
  presetMode,
  show,
  onClose,
  onSave,
}: BatchUploadsLoadsAdditionsModalProps) => {
  const { t } = useTranslation();

  const dispatch = useAppDispatch();
  const currentUser = useAppSelector(UsersState.selectors.getCurrentUser);

  const [form, setForm] = useState<any>({});
  const [confirmed, setConfirmed] = useState<boolean>(false);
  const [fileName, setFileName] = useState<string>("");
  const [file, setFile] = useState<any>(null);
  const [filePreview, setFilePreview] = useState<any[]>([]);

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  const presetTemplateMap: any = useMemo(
    () => ({
      ltl: "Easy Load Creation (Pro#)",
      parcel: "Easy Load Creation (Tracking#)",
      ocean: "Easy Load Creation (Simple)",
    }),
    []
  );

  /*
   * Hanldes preset mode
   */
  useEffect(() => {
    // When mode is preset, preselect other fields also
    if (presetMode && !form?.template) {
      setForm({
        ...form,
        mode: presetMode,
        file_format: "csv",
        template: presetTemplateMap[presetMode || "generic"],
      });
      return;
    }
  }, [presetMode]);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  /*
   * Verifies if template field is preset
   */
  const isTemplatePreset = () => {
    const isLtlPreset =
      form?.mode === "ltl" && form?.template === presetTemplateMap["ltl"];
    const isParcelPreset =
      form?.mode === "parcel" && form?.template === presetTemplateMap["parcel"];
    const isOceanPreset =
      form?.mode === "ocean" && form?.template === presetTemplateMap["ocean"];

    if (isLtlPreset || isParcelPreset || isOceanPreset) {
      return true;
    }
  };

  /*
   * Helper to control form
   */
  const onChangeFormField = async (
    fieldName: string,
    fieldValue: string | null
  ) => {
    // Don't allow reseting the preset template field
    if (isTemplatePreset() && fieldName === "template" && !fieldValue) {
      return;
    }

    setForm({ ...form, [fieldName]: fieldValue });
  };

  const createIntegration = async () => {
    const response = await dispatch(
      BatchListsLoadsAdditionsState.actions.createBatchList({
        shipperId,
        userId: currentUser?.userId,
        mode: form?.mode,
        fileFormat: form?.file_format,
        template: form?.template,
        batchList: file,
      })
    );

    return response;
  };

  const resetState = () => {
    setFileName("");
    setForm({});
    setConfirmed(false);
  };

  const onCloseModal = () => {
    resetState();
    onClose();
  };

  const onSaveModal = async () => {
    const response = await createIntegration();

    if ("error" in response) {
      showToast(
        t("Error"),
        t("There was an error while creating the loads."),
        "error"
      );
      onCloseModal();
      return;
    }

    showToast(
      t("File Processing..."),
      t("You will receive an email when the process is complete."),
      "ok"
    );

    resetState();
    onSave();
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const areFieldsOk =
    !isFieldInvalid(form?.file_format) &&
    !isFieldInvalid(form?.template) &&
    fileName;

  return (
    <Modal
      size="full"
      title={t("Add new loads through a file")}
      show={show}
      customHeader={
        <BatchUploadsModalHeader
          fileName={fileName}
          onChangeFile={() => setFileName("")}
          isDataProcessed={areFieldsOk}
          onClose={onCloseModal}
        />
      }
      closeButtonProps={{
        label: t("Cancel"),
        onClick: onCloseModal,
      }}
      saveButtonProps={{
        label: t("Save & Continue"),
        onClick: onSaveModal,
        disabled: !areFieldsOk,
      }}
    >
      {areFieldsOk ? (
        <BatchUploadsFilePreview
          fileName={fileName}
          filePreview={filePreview}
        />
      ) : (
        <BatchUploadsFileSelection
          shipperId={shipperId}
          confirmed={confirmed}
          form={form}
          fileName={fileName}
          onChangeFormField={onChangeFormField}
          onConfirmFile={async (files: File[], data: any) => {
            setConfirmed(true);
            setFileName(files[0]?.name);
            setFile(files[0]);

            // Read data in tabular format
            const response = await fetch(data);
            const textData = await response.text();
            setFilePreview(csvToArray(textData));
          }}
        />
      )}
    </Modal>
  );
};

export default BatchUploadsLoadsAdditionsModal;
