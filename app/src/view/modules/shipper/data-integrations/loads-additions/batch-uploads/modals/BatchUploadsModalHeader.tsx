import React from "react";
import { useTranslation } from "react-i18next";

import { <PERSON>ton, XIcon, RefreshCcwIcon } from "@fourkites/elemental-atoms";

import styles from "./BatchUploadsModalHeader.module.scss";

const BatchUploadsModalHeader = ({
  fileName,
  onChangeFile,
  isDataProcessed,
  onClose,
}: any) => {
  const { t } = useTranslation();

  return (
    <div data-test-id="batch-uploads-loads-additions-modal-header">
      <div className={styles.header}>
        <div id="title-container">
          <h5>{t("Add new loads through a file")}</h5>
          <span>{t("Choose a file format and select a file to upload.")}</span>
        </div>

        <div id="right">
          <button id="close" onClick={onClose}>
            <XIcon iconClass={styles.closeIcon} />
          </button>
        </div>
      </div>

      <div className={styles.subHeader}>
        <div>
          <div id="content">
            <div>
              <span
                className={isDataProcessed ? styles.oval : styles.ovalSelected}
              >
                1
              </span>
              <label
                className={
                  isDataProcessed
                    ? styles.headerDescription
                    : styles.headerDescriptionSelected
                }
              >
                {t("CHOOSE FILE")}
              </label>
            </div>
            <div id="content-divider" />
            <span
              className={!isDataProcessed ? styles.oval : styles.ovalSelected}
            >
              2
            </span>
            <label
              className={
                !isDataProcessed
                  ? styles.headerDescription
                  : styles.headerDescriptionSelected
              }
            >
              {t("PREVIEW & SUBMIT")}
            </label>
          </div>

          {isDataProcessed && (
            <div id="file">
              <label id="file-label">{t("CSV source file")}</label>

              <label id="file-name">{fileName}</label>

              <Button
                theme="secondary"
                variant="flat"
                onClick={onChangeFile}
                disabled={!fileName}
              >
                <span className={"button-content"}>
                  <RefreshCcwIcon
                    fill="#21252a"
                    iconClass={"button-icon-left"}
                  />
                  {t("Change File")}
                </span>
              </Button>
            </div>
          )}
        </div>
        <div className={styles.divider} />
      </div>
    </div>
  );
};

export default BatchUploadsModalHeader;
