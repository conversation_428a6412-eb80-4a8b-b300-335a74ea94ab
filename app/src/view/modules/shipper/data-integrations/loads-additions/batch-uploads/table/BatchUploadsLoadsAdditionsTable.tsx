import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { Table } from "@fourkites/elemental-table";

import { useAppSelector } from "state/hooks";
import { BatchListsLoadsAdditionsState } from "state/modules/shipper/BatchListsLoadsAdditions";

import { getColumns } from "./BatchUploadsLoadsAdditionsTableColumns";

import styles from "./BatchUploadsLoadsAdditionsTable.module.scss";

const BatchUploadsLoadsAdditionsTable = ({
  batchLists,
  fetchBatchLists,
  onCreateExportableBatchList,
}: any) => {
  const { t } = useTranslation();

  const batchListsPagination = useAppSelector(
    BatchListsLoadsAdditionsState.selectors.batchListsPagination()
  );

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const doCreateExportableBatchList = (index: number) => {
    onCreateExportableBatchList(batchLists[index]?.id);
  };

  /*****************************************************************************
   * DATA
   ****************************************************************************/

  const columns = useMemo(() => {
    return getColumns(t, doCreateExportableBatchList);
  }, [doCreateExportableBatchList]);

  const data = useMemo(() => {
    return batchLists?.map((batchList: any) => {
      const fileFormat =
        batchList?.file_format == "xml20"
          ? "XML 2.0"
          : batchList?.file_format?.toUpperCase();

      return {
        id: batchList?.id,
        mode: batchList?.mode || "--",
        name: batchList?.file_name || "--",
        file_format: fileFormat || "--",
        created_at: batchList?.created_at || "--",
        created_by: batchList?.created_by || "--",
        processed_at: batchList?.processed_at || "--",
        status: batchList?.status,
      };
    });
  }, [batchLists]);

  /*****************************************************************************
   * RENDER
   ****************************************************************************/
  const paginationParams = {
    paginated: true,
    defaultPageSize: 10,
    fetchDataForPage: fetchBatchLists,
    pageCount: batchListsPagination?.totalPages,
    totalEntries: batchListsPagination?.totalItems,
  };

  return (
    <>
      {data ? (
        <div
          className={styles.tableContainer}
          data-test-id="batch-uploads-loads-additions-table-container"
        >
          <Table
            data={data}
            columns={columns}
            rowHeight="small"
            variant="flat-bordered"
            striped
            pagination={paginationParams}
          />
        </div>
      ) : (
        <label>{t("There are no file uploads.")}</label>
      )}
    </>
  );
};

export default BatchUploadsLoadsAdditionsTable;
