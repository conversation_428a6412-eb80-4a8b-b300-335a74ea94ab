import React from "react";

import {
  SuccessInvertedColoredIcon,
  DownloadIcon,
  InfoInvertedColoredIcon,
  XCircleInvertedColoredIcon,
} from "@fourkites/elemental-atoms";

import { Tooltip } from "@fourkites/elemental-tooltip";

import Contact from "view/components/base/contact/Contact";

import { getUserLocaleDate } from "view/components/base/DateUtils";
import { getIconForMode } from "view/components/base/ModeUtils";
import { startWithUpperCase } from "view/components/base/StringUtils";

import styles from "./BatchUploadsLoadsAdditionsTable.module.scss";

export const getColumns = (
  t: Function,
  onCreateExportableBatchList: (index: number) => void
) => {
  return [
    {
      Header: "File Name",
      accessor: "name",
    },
    {
      Header: "Shipment Mode",
      accessor: "mode",
      Cell: (cellProps: any) => {
        const mode = cellProps?.value;
        const Icon = getIconForMode(mode);

        return (
          <Tooltip
            placement="bottom"
            text={startWithUpperCase(mode)}
            theme="dark"
          >
            <span>
              <Icon size={"20px"} />
            </span>
          </Tooltip>
        );
      },
    },
    {
      Header: "File Format",
      accessor: "file_format",
    },
    {
      Header: "Uploaded By",
      accessor: "created_by",
      Cell: (cellProps: any) => {
        //XXX: BE returns user id as the first name
        const c = cellProps?.value;
        return c?.first_name || "--";
      },
    },
    {
      Header: "Uploaded At",
      accessor: "created_at",
      Cell: (cellProps: any) => {
        const createdAt = cellProps?.value
          ? getUserLocaleDate(cellProps?.value)
          : t("--");
        return createdAt;
      },
    },
    {
      Header: "Processed At",
      accessor: "processed_at",
      Cell: (cellProps: any) => {
        const processedAt = cellProps?.value
          ? getUserLocaleDate(cellProps?.value)
          : t("--");
        return processedAt;
      },
    },
    {
      Header: "Status",
      accessor: "status",
      Cell: (cellProps: any) => (
        <BatchUploadStatusCell
          t={t}
          status={cellProps?.value}
          description={cellProps?.row?.original?.description}
        />
      ),
    },
    {
      Header: "Actions",
      accessor: "action",
      Cell: (cellProps: any) => (
        <button
          className={styles.actionButton}
          onClick={() => onCreateExportableBatchList(cellProps?.row?.index)}
        >
          <DownloadIcon size="24px" fill="#0e65e5" />
        </button>
      ),
    },
  ];
};

const BatchUploadStatusCell = ({ t, status, description }: any) => {
  const icons: any = {
    completed: <SuccessInvertedColoredIcon size="24px" />,
    initialized: <InfoInvertedColoredIcon size="24px" />,
    failed: <XCircleInvertedColoredIcon size="24px" />,
  };

  const labels: any = {
    completed: t("Completed"),
    initialized: t("Processing..."),
    failed: t("Error"),
  };

  const defaultDescriptions: any = {
    completed: t("Everything went ok!"),
    initialized: t("Wait a bit, your loads are being created."),
    failed: t("An error has occurred while creatig your loads."),
  };

  const tooltipText = description ? description : defaultDescriptions[status];

  return (
    <Tooltip placement="bottom" text={tooltipText} theme="dark">
      <span className={styles.statusWrapper}>
        {icons[status]}
        <label>{labels[status]}</label>
      </span>
    </Tooltip>
  );
};
