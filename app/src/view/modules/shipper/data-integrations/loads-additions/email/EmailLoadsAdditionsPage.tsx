import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { PlusIcon, Button } from "@fourkites/elemental-atoms";
import { Spinner } from "@fourkites/elemental-loading-indicator";

import { useAppSelector, useAppDispatch } from "state/hooks";
import { UsersState } from "state/modules/Users";
import { EmailLoadsAdditionsIntegrationsState } from "state/modules/shipper/EmailLoadsAdditionsIntegrations";

import BreadcrumbsHeader from "view/components/base/breadcrumbs-header/BreadcrumbsHeader";
import SubPagePanel from "view/components/base/containers/SubPagePanel";
import TableWithSidePanel from "view/components/base/containers/TableWithSidePanel";

import EmailLoadsAdditionsTable from "./table/EmailLoadsAdditionsTable";
import EmailLoadsAdditionsDetails from "./details/EmailLoadsAdditionsDetails";
import EmailLoadsAdditionsModal from "./modals/EmailLoadsAdditionsModal";
import EmailLoadsAdditionsDeleteIntegrationModal from "./modals/EmailLoadsAdditionsDeleteIntegrationModal";

import styles from "./EmailLoadsAdditionsPage.module.scss";

const EmailLoadsAdditionsPage = () => {
  const { t } = useTranslation();

  const dispatch = useAppDispatch();

  /*****************************************************************************
   * REDUX
   ****************************************************************************/
  const shipperId: string = useAppSelector(UsersState.selectors.getCompanyId);

  const emailIntegrations = useAppSelector(
    EmailLoadsAdditionsIntegrationsState.selectors.emailIntegrations()
  );
  const isLoading = useAppSelector(
    EmailLoadsAdditionsIntegrationsState.selectors.isRetrieving()
  );

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [integration, setIntegration] = useState<any>(null);
  const [integrationId, setIntegrationId] = useState<any>(null);
  const [isAddingIntegration, setIsAddingIntegration] =
    useState<boolean>(false);
  const [showIntegrationModal, setShowIntegrationModal] =
    useState<boolean>(false);
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  useEffect(() => {
    fetchEmailLoadsAdditionsIntegrations();
  }, [shipperId]);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const fetchEmailLoadsAdditionsIntegrations = async () => {
    await dispatch(
      EmailLoadsAdditionsIntegrationsState.actions.retrieveIntegrations({
        shipperId,
      })
    );
  };

  // ADD AND EDIT INTEGRATIONS

  const onAddEmailLoadsCreation = () => {
    setIntegrationId(null);
    setIntegration(null);

    setIsAddingIntegration(true);
    setShowIntegrationModal(true);
  };

  const onEditEmailLoadsCreation = (integration: any) => {
    setIntegrationId(integration?.id);
    setIntegration(integration);

    setIsAddingIntegration(false);
    setShowIntegrationModal(true);
  };

  const onCompleteEmailLoadsCreation = () => {
    setIntegrationId(null);
    setIntegration(null);

    onCloseEmailLoadsAdditionsModal();
    // Fetch credentials again
    fetchEmailLoadsAdditionsIntegrations();
  };

  // DELETE INTEGRATIONS

  const onDeleteIntegration = (integration: any) => {
    setIntegrationId(integration?.id);
    setIntegration(integration?.name);

    setShowDeleteModal(true);
  };

  const onConfirmIntegrationDelete = async () => {
    setIntegrationId(null);
    setIntegration(null);

    // Hides delete modal
    setShowDeleteModal(false);

    // Fetches credentials again
    fetchEmailLoadsAdditionsIntegrations();
  };

  /*
   * Closes modal
   */
  const onCloseEmailLoadsAdditionsModal = async () => {
    setIntegrationId(null);
    setIntegration(null);

    setShowIntegrationModal(false);
    setIsAddingIntegration(false);

    await fetchEmailLoadsAdditionsIntegrations();
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/
  const showHeaderLoader = isLoading && emailIntegrations.length > 0;
  const showTableLoader = isLoading && emailIntegrations.length === 0;

  const breadcrumbTitles = [
    t("Data Integrations"),
    t("Shipments"),
    t("Email Integrations"),
  ];

  const headerItems = [
    showHeaderLoader ? (
      <div className={styles.headerLoader}>
        <label>{t("Loading Integrations")}</label>
        <Spinner isLoading size="small" />
      </div>
    ) : null,
    <div className={styles.headerButton}>
      <Button size={"large"} onClick={onAddEmailLoadsCreation}>
        <PlusIcon fill="#fff" iconClass={"button-icon-left"} />
        {t("Add Email Integration")}
      </Button>
    </div>,
  ];

  return (
    <div
      className={styles.container}
      data-test-id="email-loads-additions-page-container"
    >
      <SubPagePanel>
        <div className={styles.headerWrapper}>
          <BreadcrumbsHeader titles={breadcrumbTitles} children={headerItems} />
          <span className={styles.fileInfo}>
            {t("Send your files as email to ")}
            <a href="mailto:<EMAIL>">
              <EMAIL>
            </a>
          </span>
        </div>

        <TableWithSidePanel
          table={
            showTableLoader ? (
              <div className={styles.loader}>
                <Spinner isLoading size="medium" />
              </div>
            ) : (
              <EmailLoadsAdditionsTable
                setIntegrationId={setIntegrationId}
                onDeleteIntegration={onDeleteIntegration}
                emailIntegrations={emailIntegrations}
              />
            )
          }
          sidePanel={
            integrationId ? (
              <EmailLoadsAdditionsDetails
                onEditIntegration={onEditEmailLoadsCreation}
                onDeleteIntegration={onDeleteIntegration}
                shipperId={shipperId}
                integrationId={integrationId}
              />
            ) : null
          }
        />
      </SubPagePanel>

      <EmailLoadsAdditionsModal
        show={showIntegrationModal}
        shipperId={shipperId}
        isAddingIntegration={isAddingIntegration}
        integrationId={integrationId}
        integration={integration}
        onClose={onCloseEmailLoadsAdditionsModal}
        onSave={onCompleteEmailLoadsCreation}
      />

      <EmailLoadsAdditionsDeleteIntegrationModal
        shipperId={shipperId}
        integrationId={integrationId}
        integrationName={integration?.name}
        show={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onDelete={onConfirmIntegrationDelete}
      />
    </div>
  );
};

export default EmailLoadsAdditionsPage;
