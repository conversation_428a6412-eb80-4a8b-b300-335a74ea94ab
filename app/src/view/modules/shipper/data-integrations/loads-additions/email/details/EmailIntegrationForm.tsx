import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

import { Input } from "@fourkites/elemental-input";

import FileFormatSelector, {
  getFileFormatValue,
} from "view/components/base/field-selectors/file-format-selector/FileFormatSelector";
import ModeSelector, {
  getModeValue,
} from "view/components/base/field-selectors/mode-selector/ModeSelector";

import styles from "./EmailIntegrationForm.module.scss";

const EmailIntegrationForm = ({ integration }: any) => {
  const { t } = useTranslation();

  const [form, setForm] = useState<any>({});

  /*
   * Populates form when we change integration for edit
   */
  useEffect(() => {
    setForm(integration);
  }, [integration]);

  /*
   * Helper to control form
   */
  const onChangeFormField = async (fieldName: string, fieldValue: string) => {
    setForm({ ...form, [fieldName]: fieldValue });
  };

  const presetTemplate = form?.template?.preset;
  const ediSchema = form?.template?.custom?.mapping_configuration_name;
  const mappinConfiguration = form?.template?.custom?.edi_schema_source;

  return (
    <div
      className={styles.container}
      data-test-id="email-loads-additions-details-form-container"
    >
      <h2>{t("Email Configuration")}</h2>

      <div className={styles.form} data-test-id="input-sender-email">
        <div id="input-row">
          <div className={styles.inputWrapper}>
            <Input
              label={`${t("Sender Email")}`}
              value={form?.sender_email}
              onChange={(e: any) =>
                onChangeFormField("sender_email", e.target.value)
              }
              disabled
            />
          </div>
        </div>

        <div id="input-row">
          <div className={styles.inputWrapper} data-test-id="input-mode">
            <ModeSelector
              errorLabel="Field is required"
              value={[getModeValue(form?.mode)]}
              required
              disabled
            />
          </div>
        </div>

        <div id="input-row">
          <div className={styles.inputWrapper} data-test-id="input-file-format">
            <FileFormatSelector
              mode={form?.mode}
              errorLabel="Field is required"
              value={[getFileFormatValue(form?.file_format)]}
              disabled
            />
          </div>
        </div>

        {presetTemplate && (
          <div id="input-row">
            <div
              className={styles.inputWrapper}
              data-test-id="input-preset-template"
            >
              <Input
                label={`${t("Preset Template")}`}
                value={presetTemplate}
                onChange={(e: any) =>
                  onChangeFormField("template.preset", e.target.value)
                }
                disabled
              />
            </div>
          </div>
        )}

        {mappinConfiguration && (
          <div id="input-row">
            <div
              className={styles.inputWrapper}
              data-test-id="input-mapping-configuration"
            >
              <Input
                label={`${t("Mapping Configuration")}`}
                value={mappinConfiguration}
                onChange={(e: any) =>
                  onChangeFormField(
                    "template.custom.mapping_configuration_name",
                    e.target.value
                  )
                }
                disabled
              />
            </div>
          </div>
        )}

        {ediSchema && (
          <div id="input-row">
            <div
              className={styles.inputWrapper}
              data-test-id="input-source-edi-schema"
            >
              <Input
                label={`${t("Source EDI Schema")}`}
                value={ediSchema}
                onChange={(e: any) =>
                  onChangeFormField(
                    "template.custom.edi_schema",
                    e.target.value
                  )
                }
                disabled
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EmailIntegrationForm;
