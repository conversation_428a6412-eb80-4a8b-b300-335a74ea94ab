import React from "react";
import { useTranslation } from "react-i18next";

import Contact from "view/components/base/contact/Contact";

import { getUserLocaleDate } from "view/components/base/DateUtils";

import styles from "./EmailIntegrationRuleDetails.module.scss";

const EmailIntegrationRuleDetails = ({ integration }: any) => {
  const { t } = useTranslation();

  return (
    <div
      className={styles.container}
      data-test-id="email-loads-additions-details-rules-details-container"
    >
      <h2>{t("Rule Details")}</h2>

      <div data-test-id="rules-details-created-by">
        <label id="title">{t("Created by")}</label>
        {integration?.created_by?.first_name ? (
          <Contact
            contact={{
              avatar: integration?.created_by?.avatar,
              firstName: integration?.created_by?.first_name,
              lastName: integration?.created_by?.las_name,
              position: integration?.created_by?.position,
              email: integration?.created_by?.email,
              secondaryEmails: integration?.created_by?.secondary_emails || [],
              phones: integration?.created_by?.phones || [],
              messaging: integration?.created_by?.messaging,
            }}
            contactInline
          />
        ) : (
          "--"
        )}
      </div>

      <div data-test-id="rules-details-created-at">
        <label id="title">{t("Created at")}</label>
        <label id="value">{getUserLocaleDate(integration?.created_at)}</label>
      </div>

      <div data-test-id="rules-details-last-modified-by">
        <label id="title">{t("Last Modified by")}</label>
        {integration?.modified_by?.first_name ? (
          <Contact
            contact={{
              avatar: integration?.modified_by?.avatar,
              firstName: integration?.modified_by?.first_name,
              lastName: integration?.modified_by?.las_name,
              position: integration?.modified_by?.position,
              email: integration?.modified_by?.email,
              secondaryEmails: integration?.modified_by?.secondary_emails || [],
              phones: integration?.modified_by?.phones || [],
              messaging: integration?.modified_by?.messaging,
            }}
            contactInline
          />
        ) : (
          "--"
        )}
      </div>

      <div data-test-id="rules-details-last-modified-at">
        <label id="title">{t("Last Modified at")}</label>
        <label id="value">{getUserLocaleDate(integration?.modified_at)}</label>
      </div>

      <div data-test-id="rules-details-fourkites-reference">
        <label id="title">{t("Config (FourKites reference)")}</label>
        <label id="value">
          {integration?.platform_config_reference || "--"}
        </label>
      </div>

      <div data-test-id="rules-details-notes">
        <label id="title">{t("Notes")}</label>
        <label id="value">{integration?.notes?.join(", ") || "--"}</label>
      </div>
    </div>
  );
};

export default EmailIntegrationRuleDetails;
