@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container {
  display: flex;
  flex: 1;
  flex-direction: column;
  border-left: 1px solid $color-neutral-300;
  border-top: 1px solid $color-neutral-300;
  width: 100%;
  overflow-y: auto;
  height: calc(100vh + -250px);
  padding-bottom: 2px;
}

.wrapper {
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 27vw;
  overflow-y: auto;
  padding: 24px;

  > div[id="delete-button-container"] {
    width: 100%;

    > button {
      display: flex;
      align-content: center;
      align-items: center;
      justify-content: center;
      width: 100%;
    }
  }
}

.loader {
  display: flex;
  align-items: left;
  align-content: center;
  justify-content: center;
  margin-top: 32px;
  margin-bottom: 32px;
}

.headerContainer {
  > div[id="top-header"] {
    display: flex;
    width: 100%;
    justify-content: space-between;

    > h1 {
      font-size: 20px;
      font-weight: bold;
      letter-spacing: 0;
      line-height: 30px;
      margin: 0;
    }
  }

  > label {
    color: $color-neutral-700;
    font-size: 16px;
    letter-spacing: 0;
    line-height: 24px;
  }

  > div {
    margin-top: 16px;
  }
}

.modeIcon {
  margin-left: 8px;
  margin-right: 8px;
}
