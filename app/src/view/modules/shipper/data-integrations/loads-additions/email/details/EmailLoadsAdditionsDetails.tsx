import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";

import { Button, TrashFullIcon, Edit1Icon } from "@fourkites/elemental-atoms";
import { Spinner } from "@fourkites/elemental-loading-indicator";
import { Switch } from "@fourkites/elemental-switch";

import { useAppDispatch, useAppSelector } from "state/hooks";
import { EmailLoadsAdditionsIntegrationsState } from "state/modules/shipper/EmailLoadsAdditionsIntegrations";

import EmailIntegrationForm from "./EmailIntegrationForm";
import EmailIntegrationRuleDetails from "./EmailIntegrationRuleDetails";

import styles from "./EmailLoadsAdditionsDetails.module.scss";
import EmailLoadsAdditionsDetailsProps from "./EmailLoadsAdditionsDetails.types";

const EmailLoadsAdditionsDetails = ({
  shipperId,
  integrationId,
  onEditIntegration,
  onDeleteIntegration,
}: EmailLoadsAdditionsDetailsProps) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * REDUX
   ****************************************************************************/
  const dispatch = useAppDispatch();

  const integration = useAppSelector(
    EmailLoadsAdditionsIntegrationsState.selectors.emailIntegrationsDetails()
  );
  const isLoading = useAppSelector(
    EmailLoadsAdditionsIntegrationsState.selectors.isRetrieving()
  );

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  useEffect(() => {
    // Otherwise, get carrier details
    dispatch(
      EmailLoadsAdditionsIntegrationsState.actions.retrieveIntegrationDetails({
        shipperId,
        integrationId,
      })
    );
    // eslint-disable-next-line
  }, [shipperId, integrationId]);

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  if (isLoading) {
    return (
      <div key={shipperId} className={styles.container}>
        <div className={styles.loader}>
          <Spinner isLoading size="medium" />
        </div>
      </div>
    );
  }

  return (
    <div key={shipperId} className={styles.container}>
      <div className={styles.wrapper}>
        <EmailIntegrationHeader
          name={integration?.name}
          status={integration?.enabled}
          // TODO: add status
          onChangeStatus={(status: boolean) => alert("status changed")}
          onEditIntegration={() => onEditIntegration(integration)}
        />

        <EmailIntegrationForm integration={integration} />

        <EmailIntegrationRuleDetails integration={integration} />

        <div
          id="delete-button-container"
          data-test-id="emails-loads-additions-button-delete-integration"
        >
          <Button
            size={"large"}
            onClick={() => onDeleteIntegration(integration)}
            theme="danger"
          >
            <TrashFullIcon fill="#fff" iconClass={"button-icon-left"} />
            {t("Delete Email Integration")}
          </Button>
        </div>
      </div>
    </div>
  );
};

const EmailIntegrationHeader = ({
  name,
  status,
  onChangeStatus,
  onEditIntegration,
}: any) => {
  const { t } = useTranslation();

  return (
    <div
      className={styles.headerContainer}
      data-test-id="emails-loads-additions-details-header"
    >
      <div id="top-header">
        <h1>{name}</h1>

        <Button size="small" theme="tertiary" onClick={onEditIntegration}>
          <Edit1Icon fill="#0e65e5" iconClass={"button-icon-left"} />
          {t("Edit")}
        </Button>
      </div>

      <label>
        {t("Last Processed at: ")}
        {t("--")}
      </label>

      <div>
        <Switch
          //TODO
          disabled
          size="large"
          defaultLabel={status ? t("Active") : t("Inactive")}
          checked={status}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            onChangeStatus(!status);
          }}
        />
      </div>
    </div>
  );
};

export default EmailLoadsAdditionsDetails;
