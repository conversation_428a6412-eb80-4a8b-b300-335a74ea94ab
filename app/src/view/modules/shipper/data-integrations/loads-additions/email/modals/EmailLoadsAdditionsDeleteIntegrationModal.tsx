import React from "react";
import { useTranslation } from "react-i18next";

import { Modal } from "@fourkites/elemental-modal";

import { useAppDispatch } from "state/hooks";
import { EmailLoadsAdditionsIntegrationsState } from "state/modules/shipper/EmailLoadsAdditionsIntegrations";

import { showToast } from "view/components/base/toast/Toast";

import styles from "./EmailLoadsAdditionsDeleteIntegrationModal.module.scss";

const EmailLoadsAdditionsDeleteIntegrationModal = ({
  shipperId,
  integrationId,
  integrationName,
  show,
  onClose,
  onDelete,
}: any) => {
  const { t } = useTranslation();

  const dispatch = useAppDispatch();

  const onDeleteIntegration = async () => {
    const response = await dispatch(
      EmailLoadsAdditionsIntegrationsState.actions.deleteIntegration({
        shipperId,
        integrationId: integrationId,
      })
    );

    if ("error" in response) {
      showToast(
        t("Error"),
        t("There was an error while deleting the integration."),
        "error"
      );
      return;
    }

    showToast(t("Success"), t("Integration was deleted successfully."), "ok");

    onDelete();
  };

  const title = t("Delete email integration?");

  return (
    <Modal
      size="small"
      title={title}
      show={show}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: onClose,
      }}
      saveButtonProps={{
        label: t("Delete"),
        onClick: onDeleteIntegration,
      }}
    >
      <div
        className={styles.container}
        data-test-id="email-loads-additions-delete-modal-content"
      >
        <label>
          <b>{integrationName}</b>
        </label>
        <label>
          {t(
            "Are you sure you want to delete this email integration? " +
              "This action is irreversible."
          )}
        </label>
      </div>
    </Modal>
  );
};

export default EmailLoadsAdditionsDeleteIntegrationModal;
