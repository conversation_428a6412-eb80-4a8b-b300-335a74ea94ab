import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

import { Modal } from "@fourkites/elemental-modal";

import { useAppDispatch, useAppSelector } from "state/hooks";
import { EmailLoadsAdditionsIntegrationsState } from "state/modules/shipper/EmailLoadsAdditionsIntegrations";
import { UsersState } from "state/modules/Users";

import { showToast } from "view/components/base/toast/Toast";
import { isEmailInvalid, isFieldInvalid } from "view/components/base/FormUtils";

import EmailLoadsAdditionsModalForm from "./EmailLoadsAdditionsModalForm";

import EmailLoadsAdditionsModalProps from "./EmailLoadsAdditionsModal.types";

const EmailLoadsAdditionsModal = ({
  shipperId,
  isAddingIntegration,
  integrationId,
  integration,
  show,
  onClose,
  onSave,
}: EmailLoadsAdditionsModalProps) => {
  const { t } = useTranslation();

  const dispatch = useAppDispatch();
  const currentUser = useAppSelector(UsersState.selectors.getCurrentUser);

  const [form, setForm] = useState<any>({});
  const [confirmed, setConfirmed] = useState<boolean>(false);

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  useEffect(() => {
    const shouldUpdateForm =
      show &&
      !isAddingIntegration &&
      integration != null &&
      Object.keys(form).length === 0;

    if (shouldUpdateForm) {
      const parsedIntegration = {
        name: integration?.name,
        mode: integration?.mode,
        enabled: integration?.enabled,
        sender_email: integration?.sender_email,
        file_format: integration?.file_format,
        "template.preset": integration?.template?.preset,
        "template.custom.mapping_configuration_name":
          integration?.template?.custom?.mapping_configuration_name,
        "template.custom.edi_schema_source":
          integration?.template?.custom?.edi_schema_source,
      };

      setForm(parsedIntegration);
    }
  }, [isAddingIntegration, integration, form, show]);

  /*****************************************************************************
   * INTERNAL METHOS
   ****************************************************************************/

  /*
   * Helper to control form
   */
  const onChangeFormField = async (fieldName: string, fieldValue: string) => {
    setForm({ ...form, [fieldName]: fieldValue });
  };

  const getParsedIntegration = () => {
    return {
      name: form?.name,
      enabled: true,
      sender_email: form?.sender_email,
      file_format: form?.file_format,
      template: {
        preset: form["template.preset"] || null,
        custom: {
          mapping_configuration_name:
            form["template.custom.mapping_configuration_name"] || null,
          edi_schema_source: form["template.custom.edi_schema_source"] || null,
        },
      },
    };
  };

  const createIntegration = async () => {
    const response = await dispatch(
      EmailLoadsAdditionsIntegrationsState.actions.createIntegration({
        shipperId,
        mode: form?.mode,
        userId: currentUser?.userId,
        integration: getParsedIntegration(),
      })
    );

    return response;
  };

  const updateIntegration = async () => {
    const response = await dispatch(
      EmailLoadsAdditionsIntegrationsState.actions.updateIntegration({
        shipperId,
        integrationId: integrationId || "",
        mode: form?.mode,
        userId: currentUser?.userId,
        integration: getParsedIntegration(),
      })
    );

    return response;
  };

  const resetState = () => {
    setConfirmed(false);
    setForm({});
  };

  const onCloseModal = () => {
    resetState();
    onClose();
  };

  const onSaveModal = async () => {
    // Validation
    setConfirmed(true);
    if (!areFieldsOk) {
      return;
    }

    let response;
    if (isAddingIntegration) {
      response = await createIntegration();
    } else {
      response = await updateIntegration();
    }

    if ("error" in response) {
      showToast(
        t("Error"),
        t(
          `There was an error while ${
            isAddingIntegration ? "creating" : "updating"
          } the integration. Check if the email is already in use.`
        ),
        "error"
      );
      return;
    }

    showToast(
      t("Success"),
      t(
        `Integration was ${
          isAddingIntegration ? "created" : "updated"
        } successfully.`
      ),
      "ok"
    );

    onSave();
    resetState();
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const isTemplateLogicOk = // Preset template
    !isFieldInvalid(form["template.preset"] || null) ||
    // Custom non-edi
    (isFieldInvalid(form["template.preset"] || null) &&
      !isFieldInvalid(
        form["template.custom.mapping_configuration_name"] || null
      ) &&
      (form?.file_format === "edi"
        ? !isFieldInvalid(form["template.custom.edi_schema_source"] || null)
        : true));

  const areFieldsOk =
    !isFieldInvalid(form?.name) &&
    !isEmailInvalid(form?.sender_email) &&
    !isFieldInvalid(form?.file_format) &&
    isTemplateLogicOk;

  return (
    <Modal
      size="full"
      title={t("Create a new email integration")}
      subtitle={t(
        "Provide your credentials to automatically " +
          "retrieve shipment information and create loads in FourKites"
      )}
      show={show}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: onCloseModal,
      }}
      saveButtonProps={{
        label: t("Save & Continue"),
        onClick: onSaveModal,
        disabled: confirmed && !areFieldsOk,
      }}
    >
      <EmailLoadsAdditionsModalForm
        shipperId={shipperId}
        confirmed={confirmed}
        isAddingIntegration={isAddingIntegration}
        form={form}
        onChangeFormField={onChangeFormField}
      />
    </Modal>
  );
};

export default EmailLoadsAdditionsModal;
