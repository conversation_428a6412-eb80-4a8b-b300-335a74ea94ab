@import "@fourkites/elemental-atoms/build/scss/colors/index";

@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.form {
  display: flex;
  flex-direction: column;

  > label {
    font-weight: bold;
    margin-top: 10px;
    margin-bottom: 10px;
  }

  > div[id="input-row"] {
    display: flex;
    flex-wrap: wrap;
    width: fit-content;

    > label {
      margin-top: 28px;
      font-size: 16px;
      letter-spacing: 0;
      line-height: 24px;
      color: $color-neutral-700;
    }

    > label[id="template"] {
      margin-top: 2px;
      font-size: 16px;
      letter-spacing: 0;
      line-height: 24px;
      color: $color-neutral-700;
    }
  }
}

.inputWrapper {
  flex: 1;
  margin-right: 16px;
  margin-bottom: 6px;
  width: 420px;

  > div {
    width: 420px;

    > div {
      display: flex;

      > input {
        width: 100%;
      }
    }
  }
}

.radioButtonWrapper {
  display: flex;
  flex: 1;
  flex-direction: row;
  margin-top: 8px;
  margin-bottom: 8px;

  > div {
    margin-right: 16px;
  }
}

.shareFileInfo {
  border: 1px solid $color-accent-mint-600;
  padding: 10px;
  border-radius: 5px;
  background: $color-accent-mint-300;
}

.templateSection {
  margin-left: 25px;
}

.intendedInputWrapper {
  flex: 1;
  margin-right: 16px;
  margin-bottom: 6px;
  width: 395px;

  > div {
    width: 395px;

    > div {
      display: flex;

      > input {
        width: 100%;
      }
    }
  }
}
