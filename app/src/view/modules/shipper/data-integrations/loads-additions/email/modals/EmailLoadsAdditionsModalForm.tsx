import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

import { LinkButton, DownloadIcon } from "@fourkites/elemental-atoms";

import { Input } from "@fourkites/elemental-input";
import { Select } from "@fourkites/elemental-select";
import { RadioButton } from "@fourkites/elemental-radio-button";

import { isEmailInvalid, isFieldInvalid } from "view/components/base/FormUtils";

import FileFormatSelector, {
  getFileFormatValue,
  getFileFormatKeyFromValue,
} from "view/components/base/field-selectors/file-format-selector/FileFormatSelector";
import ModeSelector, {
  getModeValue,
  getModeKeyFromValue,
} from "view/components/base/field-selectors/mode-selector/ModeSelector";

import { useLoadsAdditionsTemplates } from "../../useLoadsAdditionsTemplates";

import styles from "./EmailLoadsAdditionsModalForm.module.scss";

const EmailLoadsAdditionsModalForm = ({
  shipperId,
  confirmed,
  form,
  onChangeFormField,
}: any) => {
  const { t } = useTranslation();

  const [templateUsage, setTemplateUsage] = useState<"preset" | "custom">(
    "preset"
  );

  const {
    nonEmptyModes,
    nonEmptyFileFormatsForMode,
    templateNames,
    templateUrl,
  } = useLoadsAdditionsTemplates(
    shipperId,
    form?.mode,
    form?.file_format,
    form["template.preset"]
  );

  /*****************************************************************************
   * INTENRAL EFFECTS
   ****************************************************************************/

  /*
   * When the possible template options change, we need to reset the value
   */
  useEffect(() => {
    // Reset value of preset if it is not valid
    const shouldResetPresetTemplate =
      // Preset template field is valid
      form["template.preset"] != null &&
      form?.file_format != null &&
      Object.keys(form).length > 0 &&
      // Preset template value is not valid
      templateNames?.length > 0 &&
      !templateNames.includes(form["template.preset"]);

    if (shouldResetPresetTemplate) {
      onChangeFormField("template.preset", null);
    }

    // Sets template usage to custom if it is custom template
    const shouldSetCustomTemplateUsage =
      form["template.custom.mapping_configuration_name"] != null;
    if (shouldSetCustomTemplateUsage) {
      setTemplateUsage("custom");
    } else {
      setTemplateUsage("preset");
    }
  }, [templateNames]);

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const isCustomTemplate = templateUsage === "custom";
  const requiresMappingConfiguration = isCustomTemplate;
  const requiresEdiSchema = isCustomTemplate && form?.file_format === "edi";

  const shouldDisableDownloadTemplateButton =
    !form["template.preset"] || !templateUrl;

  const fileFormat = getFileFormatValue(form?.file_format);

  return (
    <div
      className={styles.container}
      data-test-id="email-loads-additions-modal-form-container"
    >
      <p className={styles.shareFileInfo}>
        {t("Send loads files to ")}
        <b><EMAIL></b>{" "}
        {t(" from the sender address mentioned in the below format")}
      </p>

      <div className={styles.form}>
        <div id="input-row">
          <div className={styles.inputWrapper} data-test-id="input-rule-name">
            <Input
              label={`${t("Configuration Name")}`}
              errorLabel="Field is required"
              value={form?.name}
              invalid={confirmed && isFieldInvalid(form?.name)}
              onChange={(e: any) => onChangeFormField("name", e.target.value)}
              required
            />
          </div>
        </div>

        <div id="input-row">
          <div className={styles.inputWrapper} data-test-id="input-mode">
            <ModeSelector
              enabledModes={nonEmptyModes}
              value={[getModeValue(form?.mode)]}
              onChange={(selectedOptions: any) => {
                // Change Mode
                onChangeFormField(
                  "mode",
                  getModeKeyFromValue(selectedOptions[0])
                );
              }}
              required
              select={"multiple"}
            />
          </div>

          <label>{t("Mode applicable to this integration")}</label>
        </div>

        <label>{t("Integration Details")}</label>

        <div id="input-row">
          <div
            className={styles.inputWrapper}
            data-test-id="input-sender-email"
          >
            <Input
              label={`${t("Sender Email")}`}
              errorLabel="Please provide a valid email"
              value={form?.sender_email}
              invalid={confirmed && isEmailInvalid(form?.sender_email)}
              onChange={(e: any) =>
                onChangeFormField("sender_email", e.target.value)
              }
              required
            />
          </div>

          <label>
            {t("The email ID from where load creation data is sent from")}
          </label>
        </div>

        <div id="input-row">
          <div className={styles.inputWrapper} data-test-id="input-file-format">
            <FileFormatSelector
              enabledFormats={nonEmptyFileFormatsForMode}
              mode={form?.mode}
              errorLabel="Field is required"
              value={[fileFormat]}
              showError={confirmed && isFieldInvalid(form?.file_format)}
              onChange={(selectedOptions: any) => {
                // Change form filed
                onChangeFormField(
                  "file_format",
                  getFileFormatKeyFromValue(selectedOptions[0])
                );
              }}
              required
            />
          </div>

          <label>{t("Type of shipment data file sent to FourKites")}</label>
        </div>

        <div id="input-row" className={styles.templateSection}>
          {t("Templates")}
        </div>

        <div id="input-row" className={styles.templateSection}>
          <div
            className={styles.radioButtonWrapper}
            data-test-id="input-template-type"
          >
            <RadioButton
              label={t("Use Preset Template")}
              checked={templateUsage === "preset"}
              onClick={(e: any) => {
                setTemplateUsage("preset");
                // Reset custom template to null when user chooses custom
                onChangeFormField(
                  "template.custom.mapping_configuration_name",
                  null
                );
              }}
              size="large"
            />
            <RadioButton
              label={t("Use Custom Template")}
              checked={templateUsage === "custom"}
              onClick={(e: any) => {
                setTemplateUsage("custom");
                // Reset preset template to null when user chooses custom
                onChangeFormField("template.preset", null);
              }}
              size="large"
            />
          </div>
        </div>

        <div id="input-row" className={styles.templateSection}>
          <div
            className={styles.intendedInputWrapper}
            data-test-id="input-preset-template"
          >
            <Select
              label={" "}
              options={templateNames}
              value={[form["template.preset"]]}
              showError={confirmed && isFieldInvalid(form["template.preset"])}
              onChange={(selectedOptions: any) =>
                onChangeFormField("template.preset", selectedOptions[0])
              }
              disabled={isCustomTemplate || !fileFormat}
            />
          </div>

          <label id="template" data-test-id="button-download-template">
            <LinkButton
              variant="flat"
              target="_blank"
              href={templateUrl}
              disabled={shouldDisableDownloadTemplateButton}
              style={
                shouldDisableDownloadTemplateButton
                  ? { pointerEvents: "none" }
                  : {}
              }
            >
              <DownloadIcon className="button-icon-left" fill="#0e65e5" />
              {t("Download template")}
            </LinkButton>

            {t("Use existing pre-built templates to create loads")}
          </label>
        </div>

        <div id="input-row">
          <div
            className={styles.inputWrapper}
            data-test-id="input-mapping-configuration"
          >
            <Input
              label={`${t("Mapping Configuration")}`}
              errorLabel="Field is required"
              value={form["template.custom.mapping_configuration_name"]}
              invalid={
                confirmed &&
                requiresMappingConfiguration &&
                isFieldInvalid(
                  form["template.custom.mapping_configuration_name"]
                )
              }
              onChange={(e: any) =>
                onChangeFormField(
                  "template.custom.mapping_configuration_name",
                  e.target.value
                )
              }
              required={requiresMappingConfiguration}
              disabled={!requiresMappingConfiguration}
            />
          </div>

          <label>
            {t("Enter your custom map name or reach out to FourKites for help")}
          </label>
        </div>

        <div id="input-row">
          <div
            className={styles.inputWrapper}
            data-test-id="input-source-edi-schema"
          >
            <Input
              label={`${t("Source EDI Schema")}`}
              errorLabel="Field is required"
              value={form["template.custom.edi_schema_source"]}
              invalid={
                confirmed &&
                requiresEdiSchema &&
                isFieldInvalid(form["template.custom.edi_schema_source"])
              }
              onChange={(e: any) =>
                onChangeFormField(
                  "template.custom.edi_schema_source",
                  e.target.value
                )
              }
              required={requiresEdiSchema}
              disabled={!requiresEdiSchema}
            />
          </div>

          <label>
            {t("Enter your custom map name or reach out to FourKites for help")}
          </label>
        </div>
      </div>
    </div>
  );
};

export default EmailLoadsAdditionsModalForm;
