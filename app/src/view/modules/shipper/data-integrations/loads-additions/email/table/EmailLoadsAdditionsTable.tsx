import React, { useMemo, useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

import { Table } from "@fourkites/elemental-table";

import { getColumns } from "./EmailLoadsAdditionsTableColumns";

import styles from "./EmailLoadsAdditionsTable.module.scss";

const EmailLoadsAdditionsTable = ({
  emailIntegrations,
  setIntegrationId,
  onChangeIntegrationStatus,
}: any) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * STATUS
   ****************************************************************************/

  const [clickedRowId, setClickedRowId] = useState<any>(null);
  const [clickedRowIndex, setClickedRowIndex] = useState<any>(null);

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  // Updates clicked company data when we click on row
  useEffect(() => {
    setIntegrationId(
      clickedRowIndex !== null ? emailIntegrations[clickedRowIndex]?.id : null
    );
  }, [clickedRowIndex]);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const onRowClicked = (rowInfo: any) => {
    const selectedRowId = rowInfo.row.id;
    const selectedRowIndex = rowInfo.row.index;

    if (selectedRowId !== clickedRowId) {
      setClickedRowId(selectedRowId);
      setClickedRowIndex(selectedRowIndex);
    } else {
      setClickedRowId(null);
      setClickedRowIndex(null);
    }
  };

  const doChangeIntegrationStatus = (index: number, enabled: boolean) =>
    onChangeIntegrationStatus({
      ...emailIntegrations[index],
      enabled: enabled,
    });

  /*****************************************************************************
   * DATA
   ****************************************************************************/

  const columns = useMemo(() => {
    return getColumns(t, doChangeIntegrationStatus);
  }, [doChangeIntegrationStatus]);

  const data = useMemo(() => {
    return emailIntegrations?.map((emailIntegration: any) => {
      const userDefinedTemplate =
        emailIntegration?.template?.preset ||
        emailIntegration?.template?.custom?.mapping_configuration;

      const fileFormat =
        emailIntegration?.file_format === "xml20"
          ? "XML 2.0"
          : emailIntegration?.file_format?.toUpperCase();

      return {
        id: emailIntegration?.id,
        mode: emailIntegration?.mode,
        name: emailIntegration?.name,
        file_format: fileFormat,
        template: userDefinedTemplate || "--",
        enabled: emailIntegration?.enabled,
      };
    });
  }, [emailIntegrations]);

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const preselectedRowIds =
    clickedRowId && data.length > clickedRowId ? [clickedRowId] : [];
  const paginationParams = {
    defaultPageSize: 25,
    paginated: true,
  };

  return (
    <>
      {data ? (
        <div
          className={styles.tableContainer}
          data-test-id="email-loads-additions-table-container"
        >
          <Table
            rowHeight="small"
            variant="flat-bordered"
            striped
            data={data}
            columns={columns}
            onRowClicked={onRowClicked}
            preselectedRowIds={preselectedRowIds}
            pagination={paginationParams}
          />
        </div>
      ) : (
        <label>{t("There are no email integrations.")}</label>
      )}
    </>
  );
};

export default EmailLoadsAdditionsTable;
