import React from "react";

import { Switch } from "@fourkites/elemental-switch";
import { Tooltip } from "@fourkites/elemental-tooltip";

import { startWithUpperCase } from "view/components/base/StringUtils";
import { getIconForMode } from "view/components/base/ModeUtils";

export const getColumns = (
  t: Function,
  onChangeIntegrationStatus: Function
) => {
  return [
    {
      Header: "Configuration Name",
      accessor: "name",
    },
    {
      Header: "Shipment Mode",
      accessor: "mode",
      Cell: (cellProps: any) => {
        const mode = cellProps?.value;
        const Icon = getIconForMode(mode);

        return (
          <Tooltip
            placement="bottom"
            text={startWithUpperCase(mode) || t("Generic")}
            theme="dark"
          >
            <span>
              <Icon size={"20px"} />
            </span>
          </Tooltip>
        );
      },
    },
    {
      Header: "File Format",
      accessor: "file_format",
    },
    {
      Header: "Template",
      accessor: "template",
    },
    {
      Header: "Status",
      accessor: "enabled",
      Cell: (cellProps: any) => {
        const enabled = cellProps?.value;

        return (
          <Switch
            // TODO: needs to add disable feature
            disabled
            size="large"
            defaultLabel={enabled ? t("Active") : t("Inactive")}
            checked={enabled}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
              onChangeIntegrationStatus(
                cellProps?.row?.index,
                !cellProps?.value
              );
            }}
          />
        );
      },
    },
  ];
};
