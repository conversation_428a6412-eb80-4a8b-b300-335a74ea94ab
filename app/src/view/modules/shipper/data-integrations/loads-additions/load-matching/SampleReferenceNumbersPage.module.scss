@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container {
  > div > div {
    height: calc(100vh + -113px);
    padding: 0;
    background-color: white;
  }
}

.headerWrapper {
  padding: 32px;
  padding-top: 27px;

  .headerTitle {
    margin: 0;
    font-size: 20px;
    font-weight: 700;
    letter-spacing: 0;
    line-height: 30px;
    color: $color-neutral-900;
  }

  .infoWrapper {
    margin-top: 10px;
  }

  .infoIcon {
    padding-right: 10px;
    vertical-align: sub;
  }

  .infoText {
    font-size: 16px;
    line-height: 27px;
    display: inline-block;
    font-weight: 400;
    color: $color-neutral-900;
    margin-right: 50px;
  }

  .highlightText {
    font-weight: 600;
  }

  .aboutText {
    margin-top: 25px;
  }

  .loadMatchingWrapper {
    margin-top: 25px;
  }
}

.infoIcon {
  padding-right: 10px;
  vertical-align: middle;
}

.toolTipInfoMessageContainer {
  display: flex;
  flex-direction: column;
}

.toolTipHeadingContainer {
  display: flex;
}

.tootTipContainer {
  padding-right: 10px;
}

.tootlTipMessage {
  background-color: $color-neutral-900;
  box-shadow: 0 5px 10px 5px rgba(0, 0, 0, 0.509);
}
