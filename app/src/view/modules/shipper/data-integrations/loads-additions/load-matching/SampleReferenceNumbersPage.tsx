import { useEffect } from "react";
import { useTranslation } from "react-i18next";

import { useAppSelector, useAppDispatch } from "state/hooks";
import { UsersState } from "state/modules/Users";
import { ShipperSettingsState } from "state/modules/shipper/ShipperSettings";

import SubPagePanel from "view/components/base/containers/SubPagePanel";
import MultiInput from "view/components/base/multi-input/MultiInput";

import styles from "./SampleReferenceNumbersPage.module.scss";

const SampleReferenceNumbersPage = () => {
  const { t } = useTranslation();

  // Can be dynamic with other modes later.
  const mode = "ftl";

  /*****************************************************************************
   * REDUX
   ****************************************************************************/
  const dispatch = useAppDispatch();

  const loadFormatsData: string[] = useAppSelector(
    ShipperSettingsState.selectors.loadNumberFormats()
  );

  const shipperId: string = useAppSelector(UsersState.selectors.getCompanyId);

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  /*
   * Fetch load number formats for a shipper
   */
  useEffect(() => {
    if (shipperId) {
      fetchLoadNumberFormats();
    }
  }, [shipperId]);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const fetchLoadNumberFormats = () => {
    dispatch(
      ShipperSettingsState.actions.retrieveLoadNumberFormats({
        shipperId,
      })
    );
  };

  const onAddLoadNumberFormat = (loadNumberFormat: string) => {
    dispatch(
      ShipperSettingsState.actions.addLoadNumberFormat({
        shipperId,
        mode,
        value: loadNumberFormat.toUpperCase(),
      })
    );
  };

  const onRemoveLoadNumberFormat = (loadNumberFormat: string) => {
    dispatch(
      ShipperSettingsState.actions.deleteLoadNumberFormat({
        shipperId,
        mode,
        loadNumberFormat: loadNumberFormat.toUpperCase(),
      })
    );
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const infoMessage =
    "These sample reference numbers are also shared with your carriers " +
    "so they know which data to send to FourKites®. " +
    "Providing samples will help FourKites® and carriers troubleshoot any potential issues. ";

  const multiInputValidation = {
    maxNumberOfValues: 4,
    maxValueLength: 258,
  };

  return (
    <div
      className={styles.container}
      data-testid="carrier-load-matching-page-container"
    >
      <SubPagePanel>
        <div className={styles.headerWrapper}>
          <h5 className={styles.headerTitle}>
            {t("Sample Reference Numbers")}
          </h5>
          <div className={styles.infoWrapper}>
            <div className={styles.toolTipInfoMessageContainer}>
              <div className={styles.toolTipHeadingContainer}>
                <div className={styles.tootTipContainer}>
                  <span
                    className={styles.infoText}
                    data-testid="carrier-load-matching-info-text"
                  >
                    {t(
                      "Please input 2-3 sample load reference numbers in the box below. " +
                        "These should be numbers which you share with both your carriers and "
                    )}
                    <span className={styles.highlightText}>
                      {t("FourKites®.")}
                    </span>
                  </span>
                </div>
              </div>
            </div>

            <span
              className={styles.infoText}
              data-testid="carrier-load-matching-sub-text"
            >
              {t(
                "(Commonly, these are the load numbers you use when communicating with your carriers about specific loads.)"
              )}
            </span>
          </div>
          <div
            className={styles.aboutText}
            data-testid="carrier-load-matching-about-text"
          >
            <span className={styles.infoText}>{t(infoMessage)}</span>
          </div>
          <div className={styles.loadMatchingWrapper}>
            <MultiInput
              label={t("Sample Load Reference Number(s):")}
              placeholder={t("eg: 7N######000")}
              size={"medium"}
              values={loadFormatsData || []}
              defaultValues={[]}
              onAddValue={onAddLoadNumberFormat}
              onRemoveValue={onRemoveLoadNumberFormat}
              disabled={false}
              validation={multiInputValidation}
            />
          </div>
        </div>
      </SubPagePanel>
    </div>
  );
};

export default SampleReferenceNumbersPage;
