import { getShippersMockAppState } from "tests/ShippersMockAppState";
import { inviteCode } from "tests/TestConstants";
export const mockAppState = getShippersMockAppState();

export const mockAppStateWithLoadFormats = {
  ...mockAppState,
  shipperSettings: {
    loadNumberFormats: ["FGHHT"],
    retrieving: false,
  },
};

export const mockAppStateWithAddingLoadFormats = {
  ...mockAppStateWithLoadFormats,
  shipperSettings: {
    loadNumberFormats: ["FGHHT", "ASF", "GDFG", "GHJU"],
  },
};

export const mockAppStateWithDeleteLoadFormats = {
  ...mockAppState,
  shipperSettings: {
    loadNumberFormats: ["FGHHT", "ASF", "GDFG"],
  },
};
