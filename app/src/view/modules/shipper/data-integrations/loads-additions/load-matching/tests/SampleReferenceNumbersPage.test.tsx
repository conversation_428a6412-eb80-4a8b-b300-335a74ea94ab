import { cleanup, fireEvent } from "@testing-library/react";

import store from "state/store";

import SampleReferenceNumbersPage from "../SampleReferenceNumbersPage";

import {
  mockedUseSelector,
  renderWithRedux,
  changeState,
  toBeValidAssertion,
  toHaveTextContentAssertion,
  mockUseLocation,
} from "tests/TestUtils";

import {
  carrierLoadMatchingInfoText,
  carrierLoadMatchingSubText,
  carrierLoadMatchingAboutText,
} from "tests/TestConstants";

import {
  mockAppState,
  mockAppStateWithLoadFormats,
  mockAppStateWithAddingLoadFormats,
  mockAppStateWithDeleteLoadFormats,
} from "./MockAppState";

import { toKebabCase } from "view/components/base/StringUtils";

jest.mock("react-i18next", () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}));

// TODO: create mockUseSelector()
jest.mock("react-redux", () => ({
  ...jest.requireActual("react-redux"),
  useSelector: jest.fn(),
}));

describe("SampleReferenceNumbersPage", () => {
  let ftlModeProps: any;
  const clickHandler = jest.fn();

  const renderComponent = () =>
    renderWithRedux(<SampleReferenceNumbersPage />, store);

  beforeEach(() => {
    ftlModeProps = {
      managerCompanyName: "Ashish shipper",
      shipperId: "ashish-shipper",
      managedCompanyType: "carrier",
      onShowManualCarrierAdditions: clickHandler,
      onDownloadBulkTemplate: clickHandler,
      onShowBulkCarrierAdditions: clickHandler,
    };

    mockedUseSelector.mockImplementation((callback) => {
      return callback(mockAppState);
    });
  });

  afterEach(() => {
    cleanup();
    mockedUseSelector.mockClear();
  });

  it("should render the SampleReferenceNumbersPage component", () => {
    const { getByTestId } = renderComponent();
    const testComponent = getByTestId(`carrier-load-matching-page-container`);
    toBeValidAssertion(testComponent);
  });

  it("should have the valid label SampleReferenceNumbersPage component", () => {
    const { getByTestId } = renderComponent();
    const labelComponent = getByTestId(`elemental-multi-input-label`);
    toBeValidAssertion(labelComponent);
    toHaveTextContentAssertion(
      labelComponent,
      "Sample Load Reference Number(s):"
    );
  });

  it("should have the info text in SampleReferenceNumbersPage component", () => {
    const { getByTestId } = renderComponent();
    const infoText = getByTestId(`carrier-load-matching-info-text`);
    toHaveTextContentAssertion(infoText, carrierLoadMatchingInfoText);
  });

  it("should have the sub text in SampleReferenceNumbersPage component", () => {
    const { getByTestId } = renderComponent();
    const subText = getByTestId(`carrier-load-matching-sub-text`);
    toHaveTextContentAssertion(subText, carrierLoadMatchingSubText);
  });

  it("should have the about text in SampleReferenceNumbersPage component", () => {
    const { getByTestId } = renderComponent();
    const aboutText = getByTestId(`carrier-load-matching-about-text`);
    toHaveTextContentAssertion(aboutText, carrierLoadMatchingAboutText);
  });

  it("the load reference numbers should be empty in initial render", () => {
    const { getByTestId } = renderComponent();
    const multiInputListComponent = getByTestId(`multi-input-list`);
    expect(multiInputListComponent.firstChild?.childNodes.length).toEqual(0);
  });

  it("should update the load reference numbers after its retrieved", () => {
    changeState(mockAppStateWithLoadFormats);
    const { getByTestId } = renderComponent();
    const multiInputListComponent = getByTestId(`multi-input-list`);
    expect(multiInputListComponent.firstChild?.childNodes.length).toEqual(1);
  });

  it("should add the load reference number on add button click", () => {
    changeState(mockAppStateWithAddingLoadFormats);
    const { getByTestId } = renderComponent();
    const loadReferenceInput = getByTestId(`input-component-input`);
    const multiInputListComponent = getByTestId(`multi-input-list`);
    const addLoadReferenceButton = getByTestId(`button-component`);

    fireEvent.change(loadReferenceInput, {
      target: { value: "ghju" },
    });
    fireEvent.click(addLoadReferenceButton);

    expect(multiInputListComponent.firstChild?.childNodes.length).toEqual(4);
  });

  it("add button should be disabled if max load reference number is 4", () => {
    changeState(mockAppStateWithAddingLoadFormats);
    const { getByTestId } = renderComponent();
    const loadReferenceInput = getByTestId(`input-component-input`);
    const multiInputListComponent = getByTestId(`multi-input-list`);
    const addLoadReferenceButton = getByTestId(`button-component`);

    fireEvent.change(loadReferenceInput, {
      target: { value: "dsa" },
    });

    expect(addLoadReferenceButton.hasAttribute("disabled")).toBeTruthy();
    expect(multiInputListComponent.firstChild?.childNodes.length).toEqual(4);
  });

  //   it("clicking on close icon should delete the load reference number", () => {
  //     changeState(mockAppStateWithAddingLoadFormats);
  //     changeState(mockAppStateWithDeleteLoadFormats);
  //     const { getByTestId, getByText } = renderComponent();
  //     const loadReferenceInput = getByTestId(`input-component-input`);
  //     const multiInputListComponent = getByTestId(`multi-input-list`);
  //     const addLoadReferenceButton = getByTestId(`button-component`);

  //     fireEvent.change(loadReferenceInput, {
  //       target: { value: "dsa" },
  //     });

  //     // expect(multiInputListComponent.firstChild?.childNodes.length).toEqual(4);

  //     const removeButton = getByText(/GHJU/i).firstElementChild;
  //     fireEvent.click(removeButton!);

  //     renderComponent();

  //     // const addLoadReferenceButton = getByTestId(`button-component`);
  //     console.log(multiInputListComponent.firstChild?.childNodes.length);
  //     // fireEvent.change(loadReferenceInput, {
  //     //   target: { value: "dsa" },
  //     // });

  //     // expect(addLoadReferenceButton.hasAttribute("disabled")).toBeTruthy();
  //     // expect(multiInputListComponent.firstChild?.childNodes.length).toEqual(3);
  //   });
});
