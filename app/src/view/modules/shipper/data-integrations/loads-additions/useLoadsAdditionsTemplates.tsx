import { useEffect, useMemo, useState, useCallback } from "react";

import { useAppDispatch, useAppSelector } from "state/hooks";
import { LoadsTrackingMode } from "state/BaseTypes";
import { LoadsAdditionsTemplatesAndValidationsState } from "state/modules/shipper/LoadsAdditionsTemplatesAndValidations";

export const useLoadsAdditionsTemplates = (
  shipperId: string,
  mode: LoadsTrackingMode,
  fileFormat: "xml" | "xml20" | "csv" | "edi" | "xlsx",

  selectedTemplateName: string
) => {
  /*****************************************************************************
   * REDUX
   ****************************************************************************/

  const dispatch = useAppDispatch();

  const loadsAdditionsTemplates = useAppSelector(
    LoadsAdditionsTemplatesAndValidationsState.selectors.loadsAdditionsTemplates()
  );

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [parsedTemplateOptions, setParsedTemplateOptions] = useState(null);

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  /*
   * Fetchs credentials for each new shipper ID
   */
  useEffect(() => {
    // Only fetch if not fetched before
    if (loadsAdditionsTemplates?.length > 0) {
      return;
    }

    const templateActions = LoadsAdditionsTemplatesAndValidationsState.actions;
    dispatch(templateActions.retrieveLoadsAdditionsTemplates({ shipperId }));
  }, [shipperId, loadsAdditionsTemplates]);

  /*
   * Parses templates options
   */
  useEffect(() => {
    if (!parsedTemplateOptions && loadsAdditionsTemplates.length > 0) {
      setParsedTemplateOptions(parseTemplateOptions(loadsAdditionsTemplates));
    }
  }, [loadsAdditionsTemplates, parsedTemplateOptions]);

  /*****************************************************************************
   * DERIVED VARIABLES
   ****************************************************************************/

  /*
   * Calculates all templates for the selected mode
   */
  const modeTemplates = parsedTemplateOptions
    ? parsedTemplateOptions?.[mode || "generic"]
    : TEMPLATE_MODES;

  /*
   * Calculates all modes which have valid templates
   */
  const isModeEmpty = (modeTemplates: any) =>
    modeTemplates?.csv?.length > 0 ||
    modeTemplates?.xml?.length > 0 ||
    modeTemplates?.xml20?.length > 0 ||
    modeTemplates?.xlsx?.length > 0 ||
    modeTemplates?.edi?.length > 0;
  let nonEmptyModes = [
    "generic",
    "parcel",
    "ltl",
    "ocean",
    "air",
    "courier",
    "ftl",
    "rail",
  ]?.filter((mode: string) =>
    isModeEmpty(
      parsedTemplateOptions ? parsedTemplateOptions[mode || "generic"] : {}
    )
  );

  /*
   * Calculates all file formats for mode which has valid templates
   */
  const nonEmptyFileFormatsForMode = [
    "csv",
    "xml",
    "xml20",
    "edi",
    "xlsx",
  ]?.filter(
    //@ts-ignore
    (format: string) => modeTemplates[format]?.length > 0
  );

  /*
   * Calculates the template otpions
   */
  const templateNames = useMemo(() => {
    if (!parsedTemplateOptions || !modeTemplates) {
      return [];
    }

    // Gets mode templates by file format
    let templateNames: string[] =
      modeTemplates[fileFormat]?.map((template: any) => template?.name) || [];

    return templateNames;
  }, [mode, fileFormat, parsedTemplateOptions, modeTemplates]);

  /*
   * Calculates template URL
   */
  const templateUrl = modeTemplates[fileFormat]?.find(
    (template: any) => template.name === selectedTemplateName
    //@ts-ignore
  )?.url;

  return {
    nonEmptyModes,
    nonEmptyFileFormatsForMode,
    templateNames,
    templateUrl,
  };
};

/*
 * Parses the template options into the format being used by the selectors
 */
const parseTemplateOptions = (loadsAdditionsTemplates: any) => {
  // We need to deep clone it
  var templateOptions: any = JSON.parse(JSON.stringify(TEMPLATE_OPTIONS_EMPTY));

  const templatesWithUrls = loadsAdditionsTemplates?.filter(
    (template: any) =>
      template?.template_url != null && template?.template_url !== ""
  );

  // TODO: we now need to just return option which have templates.

  // Parse through all templates to get the right template options
  for (let template of templatesWithUrls) {
    const templateMode = template?.mode;

    const templateFileFormat = template.file_format;

    if (!templateFileFormat) {
      continue;
    }

    templateOptions[templateMode][templateFileFormat].push({
      name: template?.template_name,
      url: template?.template_url,
    });
  }

  return templateOptions;
};

// Constants for the format of templates
const TEMPLATE_MODES = {
  xml: [],
  xml20: [],
  csv: [],
  edi: [],
  xlsx: [],
};
const TEMPLATE_OPTIONS_EMPTY = {
  generic: { ...TEMPLATE_MODES },
  courier: { ...TEMPLATE_MODES },
  parcel: { ...TEMPLATE_MODES },
  ltl: { ...TEMPLATE_MODES },
  ftl: { ...TEMPLATE_MODES },
  ocean: { ...TEMPLATE_MODES },
  rail: { ...TEMPLATE_MODES },
  air: { ...TEMPLATE_MODES },
};
