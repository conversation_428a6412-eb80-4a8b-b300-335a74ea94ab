@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container {
}

.validationOk {
  display: flex;
  flex-direction: column;
  padding: 32px;

  > span {
    display: flex;
    align-items: center;

    > h1 {
      margin-left: 16px;
      margin-right: 16px;
      font-size: 24px;
    }
  }

  > button {
    margin-top: 16px;
    width: 200px;
  }
}

.validationErrors {
  padding-left: 32px;
  margin-bottom: 12px;
  > span {
    display: flex;
    align-items: center;

    > h1 {
      margin-left: 16px;
      margin-right: 16px;
      font-size: 24px;
    }
  }
}

.tableContainer {
  width: 100%;
  height: calc(100vh + -340px);

  > div > div {
    overflow-x: auto;
    overflow-y: auto;

    > table {
      > tbody > tr > td {
        padding-top: 8px;
        padding-bottom: 8px;
      }
    }
  }
}

.errorDescription {
  display: flex;
  flex-direction: column;
}
