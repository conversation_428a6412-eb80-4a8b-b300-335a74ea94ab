import { useMemo } from "react";
import { useTranslation } from "react-i18next";

import {
  Button,
  CheckCircleIcon,
  XCircleIcon,
} from "@fourkites/elemental-atoms";

import { Table } from "@fourkites/elemental-table";

import styles from "./FileValidationsResults.module.scss";

const FileValidationsResults = ({ fileValidation, onValidateNewFile }: any) => {
  const isValidationOk = fileValidation?.is_valid;

  return (
    <div className={styles.container} data-test-id="file-validations-container">
      {isValidationOk ? (
        <ValidationOk onValidateNewFile={onValidateNewFile} />
      ) : (
        <ErrorsTable
          fileValidation={fileValidation}
          onValidateNewFile={onValidateNewFile}
        />
      )}
    </div>
  );
};

const ValidationOk = ({ onValidateNewFile }: any) => {
  const { t } = useTranslation();
  return (
    <div
      className={styles.validationOk}
      data-test-id="file-validations-result-ok-container"
    >
      <span>
        <CheckCircleIcon size="24px" fill="#24a148" />
        <h1>{t("Your file looks good!")}</h1>

        <Button onClick={onValidateNewFile}>
          {t("Validate another file")}
        </Button>
      </span>
      {t("You can use it to track your loads with FourKites now.")}
    </div>
  );
};

const ErrorsTable = ({ fileValidation, onValidateNewFile }: any) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * DATA
   ****************************************************************************/

  const columns = useMemo(() => {
    return [
      {
        Header: "Row Number",
        accessor: "record_index",
      },
      {
        Header: "Error Description",
        accessor: "details",
        Cell: (cellProps: any) => {
          const details = cellProps?.value;

          return (
            <span className={styles.errorDescription}>
              {details?.map((detail: any) => (
                <label>
                  {t("Column: ")}
                  <b>{detail?.name}</b>
                  {` - `}
                  {detail?.issue}
                </label>
              ))}
            </span>
          );
        },
      },
    ];
  }, []);

  const data = useMemo(() => {
    return fileValidation?.errors?.map((validation: any) => {
      return {
        record_index: validation?.record_index,
        details: validation?.details,
      };
    });
  }, [fileValidation]);

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const paginationParams = {
    defaultPageSize: 25,
    paginated: true,
  };

  return (
    <div data-test-id="file-validations-result-errors-container">
      <div className={styles.validationErrors}>
        <span>
          <XCircleIcon size="24px" fill="#da1e28" />
          <h1>{t("Your file contains errors!")}</h1>

          <Button onClick={onValidateNewFile}>
            {t("Validate another file")}
          </Button>
        </span>

        <label>{fileValidation?.general_validation}</label>
      </div>

      <div className={styles.tableContainer}>
        <Table
          data={data}
          columns={columns}
          rowHeight="small"
          variant="flat-bordered"
          striped
          pagination={paginationParams}
        />
      </div>
    </div>
  );
};

export default FileValidationsResults;
