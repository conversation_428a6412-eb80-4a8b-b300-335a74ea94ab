import { useTranslation } from "react-i18next";

import FileUpload from "view/components/base/file-upload/FileUpload";

import styles from "./FileValidationsUpload.module.scss";

const FileValidationsUpload = ({ onConfirmFile }: any) => {
  const { t } = useTranslation();

  return (
    <div
      className={styles.container}
      data-test-id="file-validations-file-upload-container"
    >
      <label>{t("Please upload a loads creation file to validate.")}</label>

      {/* AllowedFileFormats includes windows excel formats */}
      <FileUpload
        allowedFileFormats={[
          "csv",
          "text/csv",
          "text/xml",
          "application/vnd.ms-excel",
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        ]}
        onConfirmFile={onConfirmFile}
      />
    </div>
  );
};

export default FileValidationsUpload;
