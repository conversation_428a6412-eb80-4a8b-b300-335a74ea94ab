import { useState } from "react";
import { useTranslation } from "react-i18next";

import { Spinner } from "@fourkites/elemental-loading-indicator";

import { useAppDispatch, useAppSelector } from "state/hooks";
import { LoadsAdditionsTemplatesAndValidationsState } from "state/modules/shipper/LoadsAdditionsTemplatesAndValidations";
import { UsersState } from "state/modules/Users";

import BreadcrumbsHeader from "view/components/base/breadcrumbs-header/BreadcrumbsHeader";
import SubPagePanel from "view/components/base/containers/SubPagePanel";
import FileFormatSelector, {
  getFileFormatKeyFromValue,
  getFileFormatValue,
} from "view/components/base/field-selectors/file-format-selector/FileFormatSelector";
import { showToast } from "view/components/base/toast/Toast";

import FileValidationsResults from "./FileValidationsResults";
import FileValidationsUpload from "./FileValidationsUpload";

import styles from "./LoadsAdditionsFileValidationsPage.module.scss";

const LoadsAdditionsFileValidationsPage = () => {
  const { t } = useTranslation();

  /*****************************************************************************
   * REDUX
   ****************************************************************************/

  const dispatch = useAppDispatch();

  const shipperId: string = useAppSelector(UsersState.selectors.getCompanyId);

  const validation = useAppSelector(
    LoadsAdditionsTemplatesAndValidationsState.selectors.loadsAdditionsValidation()
  );
  const isValidating = useAppSelector(
    LoadsAdditionsTemplatesAndValidationsState.selectors.isValidating()
  );

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [fileFormat, setFileFormat] = useState<string>("csv");

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const onConfirmFile = async (files: File[], data: any) => {
    const file = files[0];

    const response = await dispatch(
      LoadsAdditionsTemplatesAndValidationsState.actions.createLoadsAdditionsValidation(
        {
          shipperId,
          file,
          fileFormat,
        }
      )
    );

    if ("error" in response) {
      showToast(
        t("Error"),
        t("There was an error while validating the file."),
        "error"
      );
      return;
    }
  };

  const onValidateNewFile = () => {
    dispatch(
      LoadsAdditionsTemplatesAndValidationsState.actions.clearValidation()
    );
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const breadcrumbTitles = [
    t("Data Integrations"),
    t("Shipments"),
    t("File Validation"),
  ];

  if (isValidating) {
    return (
      <div className={styles.container}>
        <SubPagePanel>
          <div className={styles.headerWrapper}>
            <BreadcrumbsHeader titles={breadcrumbTitles} />
          </div>

          <div className={styles.loader}>
            <Spinner isLoading size="medium" />
          </div>
        </SubPagePanel>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <SubPagePanel>
        <div className={styles.headerWrapper}>
          <BreadcrumbsHeader titles={breadcrumbTitles} />
        </div>

        <div className={styles.fileFormatWrapper}>
          <FileFormatSelector
            enabledFormats={["csv", "xml"]}
            errorLabel="Field is required"
            value={[getFileFormatValue(fileFormat)]}
            onChange={(selectedOptions: any) =>
              setFileFormat(getFileFormatKeyFromValue(selectedOptions[0]))
            }
          />
        </div>

        {validation ? (
          <FileValidationsResults
            fileValidation={validation}
            onValidateNewFile={onValidateNewFile}
          />
        ) : (
          <FileValidationsUpload onConfirmFile={onConfirmFile} />
        )}
      </SubPagePanel>
    </div>
  );
};

export default LoadsAdditionsFileValidationsPage;
