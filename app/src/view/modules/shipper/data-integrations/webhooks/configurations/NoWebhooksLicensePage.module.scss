@import "@fourkites/elemental-atoms/build/scss/colors/index";

.noLicenseContainer {
  display: flex;
  height: calc(100vh - 113px);
}

.rightContainer {
  h3 {
    font-weight: 300;
    font-style: normal;
    font-size: 40px;
    margin-bottom: 0;
    color: $color-neutral-900;

    sup {
      font-size: 25px;
    }
  }

  p {
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    text-align: justify;
    line-height: 35px;
    width: 84%;
    color: $color-neutral-900;
  }

  a {
    cursor: pointer;
    text-decoration: underline;
  }
}

.buttonContainer {
  display: inline-flex;
  margin-right: 20px;

  button {
    width: 200px;
  }
}

.buttonIcon {
  margin-right: 8px;
  position: relative;
  top: 2px;
}
