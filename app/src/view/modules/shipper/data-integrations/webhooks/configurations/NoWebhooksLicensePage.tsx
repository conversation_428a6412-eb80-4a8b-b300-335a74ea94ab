import React from "react";
import { useTranslation } from "react-i18next";

import { But<PERSON>, HeadphoneIcon, HelpIcon } from "@fourkites/elemental-atoms";

import WebhooksNoLicenseImg from "assets/img/webhooksLaptop.png";

import {
  onContactSupportClick,
  onStandardCallbacksKnowledgeBaseClick,
} from "router/navigationUtils";

import styles from "./NoWebhooksLicensePage.module.scss";

const NoWebhooksLicensePage = ({}) => {
  const { t } = useTranslation();

  return (
    <div className={styles.noLicenseContainer}>
      <div className={styles.leftContainer}>
        <img alt={t("No license for webhooks")} src={WebhooksNoLicenseImg} />
      </div>
      <div className={styles.rightContainer}>
        <h3>
          {t("FourKites")}
          <sup>{t("® ")}</sup>
          {t("Webhooks")}
        </h3>
        <p>
          {t(
            'FourKites Webhooks are "user-defined HTTP callbacks". They are ' +
              "triggered by an event, such as a "
          )}
          <i>{t("load creation or an ETA update, ")}</i>
          {t(
            "and are posted with data to a customer’s API, " +
              "which is always listening for those events. This is the most common way that " +
              "customers access FourKites data outside of the application. " +
              "A full list of events that a customer can subscribe to via Webhooks can be found "
          )}

          <a onClick={onStandardCallbacksKnowledgeBaseClick}>{t("here.")}</a>
        </p>
        <p>
          {t(
            "If you are interested in enabling FourKites Webhooks, please reach out " +
              "to your Customer Success Manager."
          )}
        </p>
        <div className={styles.buttonContainer}>
          <Button
            size={"large"}
            variant={"outline"}
            theme={"primary"}
            onClick={onStandardCallbacksKnowledgeBaseClick}
          >
            <HelpIcon fill="#0e65e5" iconClass={"buttonIcon"} />
            {t("Learn More")}
          </Button>
        </div>
        <div className={styles.buttonContainer} onClick={onContactSupportClick}>
          <Button size={"large"} theme={"primary"}>
            <HeadphoneIcon fill="#fff" iconClass={"buttonIcon"} />
            {t("Talk to Support")}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default NoWebhooksLicensePage;
