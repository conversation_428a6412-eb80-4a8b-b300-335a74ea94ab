@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container {
  > div > div {
    height: calc(100vh + -113px);
    padding: 0;
    background-color: white;
  }
}

.subHeading {
  font-size: 16px;
  color: $color-neutral-700;
}

.loader {
  width: 100%;
  display: flex;
  align-content: center;
  justify-content: center;
  margin-top: 32px;
  margin-bottom: 32px;
}

.headerWrapper {
  padding: 32px;
  padding-top: 27px;
  background-color: $color-neutral-50;
}

.headerButton {
  display: flex;
  > button {
    display: flex;
    align-items: center;
    align-content: center;
    min-width: fit-content;
    margin-left: 16px;
  }

  > a {
    margin-left: 16px;
  }
}

.headerLoader {
  composes: loader;
  width: fit-content;
  margin: 0;
  margin-right: 16px;

  > label {
    margin-right: 16px;
  }
}

.noLicenseContainer {
  display: flex;
  height: calc(100vh - 113px);
}

.rightContainer {
  h3 {
    font-weight: 300;
    font-style: normal;
    font-size: 40px;
    margin-bottom: 0;
    color: $color-neutral-900;

    sup {
      font-size: 25px;
    }
  }

  p {
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    text-align: justify;
    line-height: 35px;
    width: 84%;
    color: $color-neutral-900;
  }
}

.buttonContainer {
  display: inline-flex;
  margin-right: 20px;

  button {
    width: 200px;
  }
}

.buttonIcon {
  margin-right: 8px;
  position: relative;
  top: 2px;
}
