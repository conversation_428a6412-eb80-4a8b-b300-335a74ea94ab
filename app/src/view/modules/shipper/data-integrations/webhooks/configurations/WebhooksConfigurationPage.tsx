import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import {
  PlusIcon,
  Button,
  ExternalLinkIcon,
  MailIcon,
} from "@fourkites/elemental-atoms";
import { Spinner } from "@fourkites/elemental-loading-indicator";

import {
  onWebhooksKnowledgeBaseClick,
  onContactSupportClick,
} from "router/navigationUtils";

import { useAppSelector, useAppDispatch } from "state/hooks";
import { UsersState } from "state/modules/Users";
import { WebhookConfigurationState } from "state/modules/shipper/WebhookConfigurations";

import SubPagePanel from "view/components/base/containers/SubPagePanel";
import BreadcrumbsHeader from "view/components/base/breadcrumbs-header/BreadcrumbsHeader";
import { showToast } from "view/components/base/toast/Toast";

import WebhooksListTable from "./table/WebhooksListTable";
import WebhooksCreationModal from "./modals/WebhooksCreationModal";
import WebhooksDeletionModal from "./modals/WebhooksDeletionModal";
import NoWebhooksLicensePage from "./NoWebhooksLicensePage";

import styles from "./WebhooksConfigurationPage.module.scss";

const WebhooksConfigurationPage = () => {
  const { t } = useTranslation();

  const dispatch = useAppDispatch();

  /*****************************************************************************
   * REDUX
   ****************************************************************************/
  const shipperId: string = useAppSelector(UsersState.selectors.getCompanyId);

  const webhooks = useAppSelector(
    WebhookConfigurationState.selectors.webhookConfigurations()
  );

  const isLoading = useAppSelector(
    WebhookConfigurationState.selectors.isRetrieving()
  );

  const isUpdating = useAppSelector(
    WebhookConfigurationState.selectors.isUpdating()
  );

  const companyLicenses: string = useAppSelector(
    UsersState.selectors.getCompanyLicenses
  );
  const hasWebhooksLicense = companyLicenses.indexOf("callbacks") !== -1;

  /*****************************************************************************
   * STATE
   ****************************************************************************/
  const [webhookData, setWebhookData] = useState<any>(null);
  const [webhookId, setWebhookId] = useState<any>(null);
  const [configurationName, setConfigurationName] = useState<any>("");
  const [showWebookCreationModal, setShowWebookCreationModal] =
    useState<boolean>(false);
  const [isAddingWebhooks, setIsAddingWebhooks] = useState<boolean>(false);
  const [showWebhookDeleteModal, setShowWebhookDeleteModal] =
    useState<boolean>(false);

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  useEffect(() => {
    if (isUpdating) {
      setWebhookData(null);
      setWebhookId(null);
    }
  }, [isUpdating]);

  useEffect(() => {
    fetchWebhookConfigurations();
  }, [shipperId]);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  /*
   * Fetch webhook configurations
   */
  const fetchWebhookConfigurations = async () => {
    await dispatch(
      WebhookConfigurationState.actions.retrieveWebhooks({
        shipperId,
        mode: ["ftl"],
      })
    );
  };

  /*
   * Create webhook configurations
   */
  const onCreateWebhookConfiguration = () => {
    setIsAddingWebhooks(true);
    setShowWebookCreationModal(true);
    setWebhookId(null);
    setWebhookData(null);
  };

  /*
   * Edit webhook configurations
   */
  const onEditWebhookConfiguration = async (webhookId: string | number) => {
    const webhookResponse = await dispatch(
      WebhookConfigurationState.actions.retrieveWebhookDetails({
        shipperId,
        webhookId,
      })
    );

    if ("error" in webhookResponse) {
      showToast(
        t("Error"),
        t("There was an error in getting configuration details"),
        "error"
      );
      return;
    }

    showToast(
      t("Success"),
      t("Configuration details loaded successfully"),
      "ok"
    );

    const webhookDetails = webhookResponse?.payload;
    setIsAddingWebhooks(false);
    setShowWebookCreationModal(true);
    setWebhookId(webhookDetails?.id);
    setWebhookData(webhookDetails);
  };

  /*
   * Delete webhook configurations
   */
  const onDeleteWebhookConfiguration = (
    webhookId: string | number,
    configName: string
  ) => {
    setWebhookId(webhookId);
    setConfigurationName(configName);
    setShowWebhookDeleteModal(true);
  };

  /*
   * Confirmation delete webhook configurations
   */
  const onConfirmConfigurationDelete = async () => {
    setShowWebhookDeleteModal(false);
    setConfigurationName("");
    fetchWebhookConfigurations();
  };

  /*
   * Complete webhook configurations
   */
  const onCompleteWebhookCreation = () => {
    onCloseWebhookCreationModal();
  };

  /*
   * Close webhook modal
   */
  const onCloseWebhookCreationModal = async () => {
    setShowWebookCreationModal(false);
    setWebhookData(null);
    setIsAddingWebhooks(false);
    await fetchWebhookConfigurations();
  };

  /*
   * Enable/Disable webhook configuration
   */
  const onEnableDisableWebhookConfiguration = async (
    status: boolean,
    currentRowInfo: any
  ) => {
    const response = await dispatch(
      WebhookConfigurationState.actions.updateWebhook({
        shipperId,
        webhookId: currentRowInfo?.id || "",
        modes: currentRowInfo?.modes,
        webhook: {
          is_enabled: status,
        },
      })
    );

    if ("error" in response) {
      showToast(
        t("Error"),
        t(
          `There was an error while ${
            status ? "enabling" : "disabling"
          } the webhook.`
        ),
        "error"
      );
      return;
    }

    fetchWebhookConfigurations();

    showToast(
      t("Success"),
      t(`Webhook was ${status ? "enabled" : "disabled"} successfully.`),
      "ok"
    );
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/
  const showHeaderLoader = isLoading && webhooks.length > 0;
  const showTableLoader = isLoading && webhooks?.length === 0;

  const breadcrumbTitles = [t("Webhook Configuration")];

  const headerItems = [
    showHeaderLoader ? (
      <div className={styles.headerLoader}>
        <label>{t("Loading Callbacks Configuration")}</label>
        <Spinner isLoading size="small" />
      </div>
    ) : null,
    <HeaderButtons
      onCreateWebhookConfiguration={onCreateWebhookConfiguration}
    />,
  ];
  const webhooksContent = (
    <div
      className={styles.container}
      data-test-id="webhooks-configuration-page-container"
    >
      <SubPagePanel>
        <div className={styles.headerWrapper}>
          <BreadcrumbsHeader titles={breadcrumbTitles} children={headerItems} />
          <label className={styles.subHeading}>
            {t("View your existing configurations & create new ones")}
          </label>
        </div>
        {showTableLoader ? (
          <div className={styles.loader}>
            <Spinner isLoading size="medium" />
          </div>
        ) : (
          <WebhooksListTable
            onEditWebhookConfiguration={onEditWebhookConfiguration}
            onDeleteWebhookConfiguration={onDeleteWebhookConfiguration}
            onCreateWebhookConfiguration={onCreateWebhookConfiguration}
            onEnableDisableWebhookConfiguration={
              onEnableDisableWebhookConfiguration
            }
            webhooks={webhooks}
          />
        )}
      </SubPagePanel>

      <WebhooksCreationModal
        show={showWebookCreationModal}
        shipperId={shipperId}
        isAddingWebhooks={isAddingWebhooks}
        webhookId={webhookId}
        webhookData={webhookData}
        onClose={onCloseWebhookCreationModal}
        onSave={onCompleteWebhookCreation}
      />

      <WebhooksDeletionModal
        shipperId={shipperId}
        webhookId={webhookId}
        configurationName={configurationName}
        show={showWebhookDeleteModal}
        onClose={() => setShowWebhookDeleteModal(false)}
        onDelete={onConfirmConfigurationDelete}
      />
    </div>
  );

  return hasWebhooksLicense ? webhooksContent : <NoWebhooksLicensePage />;
};

const HeaderButtons = ({ onCreateWebhookConfiguration }: any) => {
  const { t } = useTranslation();
  return (
    <>
      <div className={styles.headerButton}>
        <Button
          size={"large"}
          onClick={onContactSupportClick}
          data-testid="btn-contact-support"
          variant="outline"
          theme="tertiary"
        >
          <MailIcon fill="#0e65e5" iconClass={"button-icon-left"} />
          {t("Contact Support")}
        </Button>

        <Button
          size={"large"}
          onClick={onWebhooksKnowledgeBaseClick}
          variant="outline"
          theme="tertiary"
          data-testid="btn-callback-kb"
        >
          <ExternalLinkIcon fill="#0e65e5" iconClass={"button-icon-left"} />
          {t("Go to Documentation")}
        </Button>

        <Button
          size={"large"}
          onClick={onCreateWebhookConfiguration}
          data-testid="btn-create-configuration"
        >
          <PlusIcon fill="#fff" iconClass={"button-icon-left"} />
          {t("Create Configuration")}
        </Button>
      </div>
    </>
  );
};

export default WebhooksConfigurationPage;
