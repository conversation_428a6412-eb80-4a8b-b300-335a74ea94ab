@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 85vh;
}

@mixin inputRow {
  display: flex;
  flex-wrap: wrap;
  width: fit-content;
  margin-bottom: 15px;

  > label {
    margin-top: 28px;
    font-size: 16px;
    letter-spacing: 0;
    line-height: 24px;
    color: $color-neutral-700;
  }

  > label[id="template"] {
    margin-top: 2px;
    font-size: 16px;
    letter-spacing: 0;
    line-height: 24px;
    color: $color-neutral-700;
  }
}

.form {
  display: flex;
  flex-direction: column;

  > label {
    font-weight: 700;
    margin-top: 10px;
    margin-bottom: 10px;
  }

  > div[id="input-row"] {
    @include inputRow;
  }

  > div[id="advanced-settings"] {
    div[class="elemental-Accordion-container"] {
      width: 95%;
    }

    div[class="elemental-Accordion-content-description"] {
      padding: 18px 0 18px 0;

      > div[id="input-row"] {
        @include inputRow;
      }
    }
  }
}

@mixin labelHeading {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: $color-neutral-900;
  white-space: break-spaces;
}

.inputWrapper {
  flex: 1;
  margin-right: 16px;
  margin-bottom: 6px;
  width: 570px;
  display: flex;
  flex-direction: column;

  > div {
    width: 570px;
    display: flex;
    flex-wrap: wrap;

    > label:first-child {
      width: 32%;
      @include labelHeading;
    }

    > div {
      width: 64.5%;
      display: flex;

      > input {
        width: 100%;
      }
    }

    > [class~="elemental-Input-errorLabel"]:last-of-type {
      margin-left: 32%;
    }
  }
}

.selectWrapper {
  flex: 1;
  margin-right: 16px;
  margin-bottom: 6px;
  width: 610px;
  display: flex;

  > div {
    width: 570px;
    display: flex;
    flex-wrap: wrap;

    > label:first-child {
      width: 32%;
      @include labelHeading;
    }

    > button {
      width: 68%;
    }

    > [class~="elemental-Select-errorLabel"]:last-of-type {
      margin-left: 32%;
    }
  }
}

.radioButtonWrapper {
  width: 660px;
  display: flex;
  flex-direction: row;

  > label {
    width: 29%;
    @include labelHeading;
  }

  > div {
    margin-right: 16px;

    > label {
      @include labelHeading;
    }
  }
}

.toggleSwitchWrapper {
  flex: 1 1;
  margin-right: 16px;
  margin-bottom: 6px;
  width: 570px;
  display: flex;
  flex-direction: row;

  > [class~="finalToggleSwitch"] {
    padding-bottom: 40px;
  }

  > label {
    width: 48%;
    @include labelHeading;
  }

  > div {
    width: 100%;
    display: flex;

    > label {
      @include labelHeading;
    }
  }
}

.checkboxWrapper {
  width: 660px;
  display: flex;
  flex-direction: column;

  > label {
    width: 28%;
    @include labelHeading;
  }

  > div {
    display: flex;
    margin-left: 28%;

    > label {
      @include labelHeading;
    }
  }

  > a {
    margin-left: 28%;
  }
}

.modalContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.disabledInfo {
  display: inline-flex;
  align-items: center;
  margin-left: 10px;
}
