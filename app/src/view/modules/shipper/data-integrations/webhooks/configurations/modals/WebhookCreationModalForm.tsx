import React from "react";

import WebhookInformation from "./components/webhook-information/WebhookInformation";
import LoadInformation from "./components/load-information/LoadInformation";
import AdvancedSettings from "./components/advanced-settings/AdvancedSettings";

import WebhookCreationModalFormProps from "./WebhookCreationModalForm.types";

import styles from "./WebhookCreationModalForm.module.scss";

const WebhookCreationModalForm = ({
  confirmed,
  webhookForm,
  onChangeFormField,
}: WebhookCreationModalFormProps) => {
  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div
      className={styles.container}
      data-test-id="webhook-creation-modal-form-container"
    >
      <div className={styles.form}>
        <WebhookInformation
          webhookForm={webhookForm}
          confirmed={confirmed}
          onChangeFormField={onChangeFormField}
        />

        <LoadInformation
          webhookForm={webhookForm}
          confirmed={confirmed}
          onChangeFormField={onChangeFormField}
        />

        <AdvancedSettings
          webhookForm={webhookForm}
          confirmed={confirmed}
          onChangeFormField={onChangeFormField}
        />
      </div>
    </div>
  );
};

export default WebhookCreationModalForm;
