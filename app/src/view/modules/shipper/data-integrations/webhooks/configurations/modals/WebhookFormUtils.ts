export const setWebhookFormDetails = (
  webhook: any,
  isAddingWebhooks: boolean
) => {
  const form = {
    name: webhook?.name,
    modes: webhook?.modes || ["ftl"],
    is_enabled: webhook?.is_enabled,
    is_standard: webhook?.is_standard,
    type: webhook?.type,
    subscribed_events: webhook?.subscribed_events || [],
    additional_subscribed_events: webhook?.additional_subscribed_events,
    setup_url: webhook?.setup_url,
    "authentication.scheme": webhook?.authentication?.scheme,
    "authentication.custom_header": webhook?.authentication?.custom_header,
    "authentication.custom_payload": webhook?.authentication?.custom_payload,
    "authentication.http_basic_credentials.username":
      webhook?.authentication?.http_basic_credentials?.username,
    "authentication.http_basic_credentials.password":
      webhook?.authentication?.http_basic_credentials?.password,
    "authentication.static_bearer_token_credentials.token_name":
      webhook?.authentication?.static_bearer_token_credentials?.token_name,
    "authentication.static_bearer_token_credentials.token_value":
      webhook?.authentication?.static_bearer_token_credentials?.token_value,
    "authentication.session_bearer_token_credentials.grant_type":
      webhook?.authentication?.session_bearer_token_credentials?.grant_type,
    "authentication.session_bearer_token_credentials.username":
      webhook?.authentication?.session_bearer_token_credentials?.username,
    "authentication.session_bearer_token_credentials.password":
      webhook?.authentication?.session_bearer_token_credentials?.password,
    "authentication.session_bearer_token_credentials.client_id":
      webhook?.authentication?.session_bearer_token_credentials?.client_id,
    "authentication.session_bearer_token_credentials.client_secret":
      webhook?.authentication?.session_bearer_token_credentials?.client_secret,
    "authentication.session_bearer_token_credentials.scope":
      webhook?.authentication?.session_bearer_token_credentials?.scope,
    "authentication.session_bearer_token_credentials.session_url":
      webhook?.authentication?.session_bearer_token_credentials?.session_url,
    "authentication.session_bearer_token_credentials.additional_properties":
      webhook?.authentication?.session_bearer_token_credentials
        ?.additional_properties,
    "event_notifications_criteria.value":
      webhook?.event_notifications_criteria?.value,
    "event_notifications_criteria.loads_identifier.type":
      webhook?.event_notifications_criteria?.loads_identifier?.type,
    "event_notifications_criteria.loads_identifier.regex.rule":
      webhook?.event_notifications_criteria?.loads_identifier?.regex?.rule,
    "event_notifications_criteria.loads_identifier.regex.match":
      webhook?.event_notifications_criteria?.loads_identifier?.regex?.match,
    "update_settings.frequency_min":
      webhook?.update_settings?.frequency_min || DEFAULT_MINUTES_FREQUENCY,
    "update_settings.etd.frequency_min":
      webhook?.update_settings?.etd?.frequency_min || DEFAULT_ETD_FREQUENCY,
    "update_settings.etd.type": webhook?.update_settings?.etd?.type,
    "update_settings.eta.frequency_min":
      webhook?.update_settings?.eta?.frequency_min || DEFAULT_UPDATE_FREQUENCY,
    "update_settings.eta.type": webhook?.update_settings?.eta?.type,
    "update_settings.timestamp_header.header_key":
      webhook?.update_settings?.timestamp_header?.header_key,
    "update_settings.timestamp_header.is_included":
      webhook?.update_settings?.timestamp_header?.is_included,
    "update_settings.should_send_stationary_location":
      webhook?.update_settings?.should_send_stationary_location,
  };

  return form;
};

export const getWebhookFormDetails = (webhookForm: any) => {
  const payload = {
    name: webhookForm?.name,
    modes: webhookForm?.modes || ["ftl"],
    is_enabled: webhookForm?.is_enabled,
    is_standard: webhookForm?.is_standard || true,
    type: webhookForm?.type || "others",
    subscribed_events: webhookForm?.subscribed_events,
    additional_subscribed_events: webhookForm?.additional_subscribed_events,
    setup_url: webhookForm?.setup_url,
    event_notifications_criteria: {
      value: webhookForm["event_notifications_criteria.value"] || "all_loads",
      loads_identifier: {
        type:
          webhookForm["event_notifications_criteria.loads_identifier.type"] ||
          "LoadNumber",
        regex: {
          rule:
            webhookForm[
              "event_notifications_criteria.loads_identifier.regex.rule"
            ] || "Contains",
          match:
            webhookForm[
              "event_notifications_criteria.loads_identifier.regex.match"
            ] || null,
        },
      },
    },
    authentication: {
      scheme: webhookForm["authentication.scheme"] || "no_auth",
      custom_header: webhookForm["authentication.custom_header"] || null,
      custom_payload: webhookForm["authentication.custom_payload"] || null,
      http_basic_credentials: {
        username:
          webhookForm["authentication.http_basic_credentials.username"] || null,
        password:
          webhookForm["authentication.http_basic_credentials.password"] || null,
      },
      static_bearer_token_credentials: {
        token_name:
          webhookForm[
            "authentication.static_bearer_token_credentials.token_name"
          ] || null,
        token_value:
          webhookForm[
            "authentication.static_bearer_token_credentials.token_value"
          ] || null,
      },
      session_bearer_token_credentials: {
        grant_type:
          webhookForm[
            "authentication.session_bearer_token_credentials.grant_type"
          ] || "username_and_password",
        username:
          webhookForm[
            "authentication.session_bearer_token_credentials.username"
          ] || null,
        password:
          webhookForm[
            "authentication.session_bearer_token_credentials.password"
          ] || null,
        client_id:
          webhookForm[
            "authentication.session_bearer_token_credentials.client_id"
          ] || null,
        client_secret:
          webhookForm[
            "authentication.session_bearer_token_credentials.client_secret"
          ] || null,
        scope:
          webhookForm[
            "authentication.session_bearer_token_credentials.scope"
          ] || null,
        session_url:
          webhookForm[
            "authentication.session_bearer_token_credentials.session_url"
          ] || null,
        additional_properties:
          webhookForm[
            "authentication.session_bearer_token_credentials.additional_properties"
          ] || null,
      },
    },
    update_settings: {
      frequency_min:
        webhookForm["update_settings.frequency_min"] ||
        DEFAULT_MINUTES_FREQUENCY,
      eta: {
        frequency_min:
          webhookForm["update_settings.eta.frequency_min"] ||
          DEFAULT_UPDATE_FREQUENCY,
        type: webhookForm["update_settings.eta.type"] || "fourkites_eta",
      },
      etd: {
        frequency_min:
          webhookForm["update_settings.etd.frequency_min"] ||
          DEFAULT_ETD_FREQUENCY,
        type: webhookForm["update_settings.etd.type"] || "fourkites_etd",
      },
      timestamp_header: {
        is_included:
          webhookForm["update_settings.timestamp_header.is_included"] || false,
        header_key:
          webhookForm["update_settings.timestamp_header.header_key"] || null,
      },
      should_send_stationary_location:
        webhookForm["update_settings.should_send_stationary_location"] || null,
    },
  };

  return payload;
};

export const getFieldValue = (key: string, options: any) => {
  const value = options[key] ? options[key] : Object.values(options)[0];
  return [value];
};

export const getFieldKey = (value: string, options: any) => {
  return Object.keys(options).find(
    // @ts-ignore
    (key: any) => options[key] === value
  );
};

export const getKeyByValue = (object: any, value: string) =>
  Object.keys(object).find((key) => object[key] === value);

export const getFieldKeysForMultiSelect = (
  selectedOptions: any,
  availableOptions: any
) => {
  const selectedKeys: any = [];
  selectedOptions.map((option: string) => {
    selectedKeys.push(getKeyByValue(availableOptions, option));
  });
  return selectedKeys;
};

export const getFieldValuesForMultiSelect = (
  selectedKeys: any,
  availableOptions: any
) => {
  const selectedValues: any = [];
  selectedKeys?.length &&
    selectedKeys.map((key: string) => {
      if (availableOptions[key]) {
        selectedValues.push(availableOptions[key]);
      }
    });
  return selectedValues;
};

export const isFrequencyInvalid = (value: any, min: number, max: number) => {
  let hasInvalidFrequency = false;
  if (value !== "") {
    if (!isNaN(value)) {
      const frequency = Number(value);
      hasInvalidFrequency = frequency < min || frequency > max;
    }
  } else {
    hasInvalidFrequency = true;
  }
  return hasInvalidFrequency;
};

//TODO: Add Translations
export const CONFIG_TYPE_NAMES: any = {
  others: "FourKites Standard Callback",
  aljex: "Aljex",
  mercurygate: "MercuryGate",
  otm: "OTM",
  sap: "SAP",
  jda: "JDA",
  file: "File",
  "inspirage-otm": "Inspirage OTM",
  alt_payload: "Alternate Payload Callbacks",
  mercurygate_v2: "MercuryGate V2",
  "one-network": "One Network",
  "one-network-v2": "One Network V2",
  dat: "FourKites DAT Integration",
  custom: "Custom",
};

export const MIN_UPDATE_FREQUENCY = 0;
export const MIN_MINUTES_FREQUENCY = 0;
export const MAX_UPDATE_FREQUENCY = 720;
export const MIN_ETD_FREQUENCY = 5;
export const DEFAULT_ETD_FREQUENCY = 15;
export const DEFAULT_UPDATE_FREQUENCY = 15;
export const DEFAULT_MINUTES_FREQUENCY = 0;
