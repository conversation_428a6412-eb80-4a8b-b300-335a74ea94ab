import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { BookIcon, Button, HeadphoneIcon } from "@fourkites/elemental-atoms";
import { Modal } from "@fourkites/elemental-modal";

import {
  onContactSupportClick,
  onWebhooksKnowledgeBaseClick,
} from "router/navigationUtils";

import { useAppDispatch } from "state/hooks";
import { WebhookConfigurationState } from "state/modules/shipper/WebhookConfigurations";

import { isFieldInvalid, isUrlInvalid } from "view/components/base/FormUtils";
import { showToast } from "view/components/base/toast/Toast";

import CustomWizardFooter from "view/components/self-service/wizard/footer/CustomWizardFooter";

import WebhookCreationModalForm from "./WebhookCreationModalForm";

import {
  getWebhookFormDetails,
  isFrequencyInvalid,
  MAX_UPDATE_FREQUENCY,
  MIN_ETD_FREQUENCY,
  MIN_UPDATE_FREQUENCY,
  MIN_MINUTES_FREQUENCY,
  setWebhookFormDetails,
} from "./WebhookFormUtils";

import WebhookCreationModalProps from "./WebhookCreationModal.types";

import styles from "./WebhooksCreationModal.module.scss";

const WebhooksCreationModal = ({
  shipperId,
  isAddingWebhooks,
  webhookId,
  webhookData,
  show,
  onClose,
  onSave,
}: WebhookCreationModalProps) => {
  const { t } = useTranslation();

  const dispatch = useAppDispatch();

  /*****************************************************************************
   * STATE
   ****************************************************************************/
  const [webhookForm, setWebhookForm] = useState<any>({});
  const [confirmed, setConfirmed] = useState<boolean>(false);

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  useEffect(() => {
    const shouldUpdateWebhookForm =
      show &&
      !isAddingWebhooks &&
      webhookData != null &&
      Object.keys(webhookForm).length === 0;

    if (shouldUpdateWebhookForm) {
      const parsedWebhook = setWebhookFormDetails(
        webhookData,
        isAddingWebhooks
      );

      setWebhookForm(parsedWebhook);
    }
  }, [isAddingWebhooks, webhookData, webhookForm, show]);

  /*****************************************************************************
   * INTERNAL METHOS
   ****************************************************************************/

  //TODO: useMemo
  const getParsedWebhook = () => getWebhookFormDetails(webhookForm);

  /*
   * Handler to reset form
   */
  const resetState = () => {
    setConfirmed(false);
    setWebhookForm({});
  };

  /*
   * Handler to close modal
   */
  const onCloseModal = () => {
    resetState();
    onClose();
  };

  /*
   * Handler to validate specific loads case
   */
  const hasValidSpecificLoadsCriteria = () => {
    const loadCriteria = webhookForm["event_notifications_criteria.value"];
    if (loadCriteria === "specific_loads") {
      return !isFieldInvalid(
        webhookForm["event_notifications_criteria.loads_identifier.regex.match"]
      );
    }
    return true;
  };

  /*
   * Handler to validate  webhook form elements
   */
  const isWebhookFormValid = () => {
    const updateFrequencyMinutes = webhookForm["update_settings.frequency_min"];
    const etaChangeThreshold = webhookForm["update_settings.eta.frequency_min"];
    const etdFrequencyMinutes =
      webhookForm["update_settings.etd.frequency_min"];

    return (
      hasValidSpecificLoadsCriteria() &&
      !isFrequencyInvalid(
        updateFrequencyMinutes,
        MIN_MINUTES_FREQUENCY,
        MAX_UPDATE_FREQUENCY
      ) &&
      !isFrequencyInvalid(
        etaChangeThreshold,
        MIN_UPDATE_FREQUENCY,
        MAX_UPDATE_FREQUENCY
      ) &&
      !isFrequencyInvalid(
        etdFrequencyMinutes,
        MIN_ETD_FREQUENCY,
        MAX_UPDATE_FREQUENCY
      )
    );
  };

  /*
   * Handler to save modal
   */
  const onSaveModal = async () => {
    setConfirmed(true);
    if (!isMandatoryFieldsFilled) {
      return;
    }

    if (!isWebhookFormValid()) {
      return;
    }
    let response;
    if (isAddingWebhooks) {
      response = await createWebhook();
    } else {
      response = await updateWebhook();
    }

    if ("error" in response) {
      showToast(
        t("Error"),
        t(
          `There was an error while ${
            isAddingWebhooks ? "creating" : "updating"
          } the webhook.`
        ),
        "error"
      );
      return;
    }

    showToast(
      t("Success"),
      t(
        `Webhook was ${isAddingWebhooks ? "created" : "updated"} successfully.`
      ),
      "ok"
    );

    resetState();
    onSave();
  };

  /*
   * Helper to control form
   */
  const onChangeFormField = async (fieldName: string, fieldValue: string) => {
    setWebhookForm({ ...webhookForm, [fieldName]: fieldValue });
  };

  /*
   * Handler to create the webhook
   */
  const createWebhook = async () => {
    const response = await dispatch(
      WebhookConfigurationState.actions.createWebhook({
        shipperId,
        modes: webhookForm?.mode,
        webhook: getParsedWebhook(),
      })
    );
    return response;
  };

  /*
   * Handler to update the webhook
   */
  const updateWebhook = async () => {
    const response = await dispatch(
      WebhookConfigurationState.actions.updateWebhook({
        shipperId,
        webhookId: webhookId || "",
        modes: webhookForm?.mode,
        webhook: getParsedWebhook(),
      })
    );
    return response;
  };

  const checkTimeStampHeaderKey = () => {
    const isTimeStampHeaderKeyIncluded =
      webhookForm["update_settings.timestamp_header.is_included"];
    const timeStampHeaderKey =
      webhookForm["update_settings.timestamp_header.header_key"];

    const isValidTimeStampHeaderKey = isTimeStampHeaderKeyIncluded
      ? !isFieldInvalid(timeStampHeaderKey)
      : true;
    return isValidTimeStampHeaderKey;
  };

  const isMandatoryFieldsFilled =
    !isFieldInvalid(webhookForm?.name) &&
    !isFieldInvalid(webhookForm?.setup_url) &&
    !isUrlInvalid(webhookForm?.setup_url) &&
    webhookForm?.subscribed_events?.length >= 1 &&
    checkTimeStampHeaderKey();

  /*****************************************************************************
   * RENDER
   ****************************************************************************/
  const modalTitle = isAddingWebhooks
    ? t("Create a new webhook configuration")
    : t("Edit a webhook configuration");

  const modalSubtitle = isAddingWebhooks
    ? t("Fill up the required fields to create a new webhook configuration")
    : t("Fill up the required fields to edit a webhook configuration");

  const footerLinkOptions = [
    {
      label: t("Help Documentation"),
      onClick: onWebhooksKnowledgeBaseClick,
      icon: <BookIcon fill="#0e65e5" iconClass={"button-icon-left"} />,
    },
    {
      label: t("Contact FourKites Support"),
      onClick: onContactSupportClick,
      icon: <HeadphoneIcon fill="#0e65e5" iconClass={"button-icon-left"} />,
    },
  ];

  return (
    <Modal
      size="full"
      title={modalTitle}
      subtitle={modalSubtitle}
      show={show}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: onCloseModal,
      }}
      saveButtonProps={{
        label: t("Save Configuration"),
        onClick: onSaveModal,
        disabled: confirmed && !isMandatoryFieldsFilled,
      }}
      customFooter={
        <CustomWizardFooter footerLinkOptions={footerLinkOptions}>
          <Button
            className={styles.modalFooterButton}
            onClick={onCloseModal}
            size="medium"
            theme="secondary"
            variant="solid"
          >
            {t("Cancel")}
          </Button>
          <Button
            className={styles.modalFooterButton}
            onClick={onSaveModal}
            size="medium"
            theme="primary"
            variant="solid"
          >
            {t("Save Configuration")}
          </Button>
        </CustomWizardFooter>
      }
    >
      <WebhookCreationModalForm
        confirmed={confirmed}
        webhookForm={webhookForm}
        onChangeFormField={onChangeFormField}
      />
    </Modal>
  );
};

export default WebhooksCreationModal;
