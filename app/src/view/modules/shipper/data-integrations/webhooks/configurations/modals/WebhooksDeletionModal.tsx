import React from "react";
import { useTranslation } from "react-i18next";

import { Modal } from "@fourkites/elemental-modal";

import { useAppDispatch } from "state/hooks";
import { WebhookConfigurationState } from "state/modules/shipper/WebhookConfigurations";

import { showToast } from "view/components/base/toast/Toast";

import styles from "./WebhooksDeletionModal.module.scss";
import { AlertTriangleColoredIcon } from "@fourkites/elemental-atoms";

const WebhooksDeletionModal = ({
  shipperId,
  webhookId,
  configurationName,
  show,
  onClose,
  onDelete,
}: any) => {
  const { t } = useTranslation();

  const dispatch = useAppDispatch();

  const onDeleteWebhookConfiguration = async () => {
    const response = await dispatch(
      WebhookConfigurationState.actions.deleteWebhook({
        shipperId,
        webhookId: webhookId,
      })
    );

    if ("error" in response) {
      showToast(
        t("Error"),
        t("There was an error while deleting the configuration."),
        "error"
      );
      return;
    }

    showToast(t("Success"), t("Configuration was deleted successfully."), "ok");

    onDelete();
  };

  const title = configurationName
    ? t(`Delete '${configurationName}'`)
    : t("Delete webhook configuration?");

  return (
    <Modal
      size="small"
      title={title}
      show={show}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: onClose,
      }}
      saveButtonProps={{
        label: t("Confirm Delete"),
        onClick: onDeleteWebhookConfiguration,
      }}
    >
      <div
        className={styles.container}
        data-test-id="webhooks-configuration-delete-modal-content"
      >
        <label className={styles.primaryMessage}>
          <AlertTriangleColoredIcon
            size={"1.8em"}
            className={styles.alertIcon}
          />
          {t("Are you sure you want to delete this webhook configuration?")}
        </label>
        <label>
          {t(
            "All messages currently subscribed will stop sending immediately once you delete a configuration!"
          )}
        </label>
      </div>
    </Modal>
  );
};

export default WebhooksDeletionModal;
