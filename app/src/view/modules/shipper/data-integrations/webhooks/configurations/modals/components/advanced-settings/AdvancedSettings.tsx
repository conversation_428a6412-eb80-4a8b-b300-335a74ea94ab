import React from "react";
import { useTranslation } from "react-i18next";

import { Accordion } from "@fourkites/elemental-accordion";

import UpdateSettings from "./UpdateSettings";
import Authentication from "./authentication/Authentication";

const AdvancedSettings = ({
  webhookForm,
  confirmed,
  onChangeFormField,
}: any) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div id="advanced-settings">
      <Accordion title={"Advanced Settings"}>
        <Authentication
          webhookForm={webhookForm}
          onChangeFormField={onChangeFormField}
        />
        <UpdateSettings
          webhookForm={webhookForm}
          confirmed={confirmed}
          onChangeFormField={onChangeFormField}
        />
      </Accordion>
    </div>
  );
};

export default AdvancedSettings;
