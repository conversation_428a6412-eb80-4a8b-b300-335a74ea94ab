import React, { ChangeEvent, useState } from "react";
import { useTranslation } from "react-i18next";

import { Input } from "@fourkites/elemental-input";
import { Switch } from "@fourkites/elemental-switch";
import { Select } from "@fourkites/elemental-select";

import { isFieldInvalid } from "view/components/base/FormUtils";
import {
  getFieldKey,
  getFieldValue,
  isFrequencyInvalid,
  MAX_UPDATE_FREQUENCY,
  MIN_ETD_FREQUENCY,
  MIN_UPDATE_FREQUENCY,
  MIN_MINUTES_FREQUENCY,
  DEFAULT_UPDATE_FREQUENCY,
  DEFAULT_MINUTES_FREQUENCY,
  DEFAULT_ETD_FREQUENCY,
} from "view/modules/shipper/data-integrations/webhooks/configurations/modals/WebhookFormUtils";

import styles from "view/modules/shipper/data-integrations/webhooks/configurations/modals/WebhookCreationModalForm.module.scss";

const UpdateSettings = ({ webhookForm, confirmed, onChangeFormField }: any) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <>
      <UpdateFrequency
        webhookForm={webhookForm}
        confirmed={confirmed}
        onChangeFormField={onChangeFormField}
      />
      <EtaThreshold
        webhookForm={webhookForm}
        confirmed={confirmed}
        onChangeFormField={onChangeFormField}
      />
      <EtdFrequency
        webhookForm={webhookForm}
        confirmed={confirmed}
        onChangeFormField={onChangeFormField}
      />
      <TimestampHeader
        webhookForm={webhookForm}
        confirmed={confirmed}
        onChangeFormField={onChangeFormField}
      />
      <StationaryLocation
        webhookForm={webhookForm}
        onChangeFormField={onChangeFormField}
      />
    </>
  );
};

const UpdateFrequency = ({
  webhookForm,
  confirmed,
  onChangeFormField,
}: any) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * STATE
   ****************************************************************************/
  const [updateFrequencyMinutes, setUpdateFrequencyMinutes] = useState(
    webhookForm["update_settings.frequency_min"] || DEFAULT_MINUTES_FREQUENCY
  );

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div id="input-row">
      <div className={styles.inputWrapper} data-test-id="input-rule-name">
        <Input
          label={`${t("Update Frequency (Minutes)")}`}
          errorLabel={`${t("Value should be between 0 - 720 minutes")}`}
          value={updateFrequencyMinutes}
          invalid={
            isFrequencyInvalid(
              updateFrequencyMinutes,
              MIN_MINUTES_FREQUENCY,
              MAX_UPDATE_FREQUENCY
            ) && confirmed
          }
          onChange={(e: any) => {
            setUpdateFrequencyMinutes(e.target.value);
            onChangeFormField("update_settings.frequency_min", e.target.value);
          }}
          placeholder={`${t("Value between 0 - 720 minutes")}`}
        />
      </div>
    </div>
  );
};

const EtaThreshold = ({ webhookForm, confirmed, onChangeFormField }: any) => {
  const { t } = useTranslation();
  const callbackEtaTypes =
    webhookForm["update_settings.eta.type"] || "fourkites_eta";

  /*****************************************************************************
   * STATE
   ****************************************************************************/
  const [etaChangeThreshold, setEtaChangeThreshold] = useState(
    webhookForm["update_settings.eta.frequency_min"] || DEFAULT_UPDATE_FREQUENCY
  );

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <>
      <div id="input-row">
        <div className={styles.inputWrapper} data-test-id="input-rule-name">
          <Input
            label={`${t("ETA Change Callback Threshold")}`}
            value={etaChangeThreshold}
            errorLabel={`${t("Value should be between 0 - 720 minutes")}`}
            invalid={
              isFrequencyInvalid(
                etaChangeThreshold,
                MIN_UPDATE_FREQUENCY,
                MAX_UPDATE_FREQUENCY
              ) && confirmed
            }
            onChange={(e: any) => {
              setEtaChangeThreshold(e.target.value);
              onChangeFormField(
                "update_settings.eta.frequency_min",
                e.target.value
              );
            }}
            placeholder={`${t("Value between 0 - 720 minutes")}`}
          />
        </div>
      </div>
      <div id="input-row">
        <div className={styles.selectWrapper} data-test-id="eta-types">
          <Select
            label={t("Callback ETA Type")}
            options={CALLBACK_ETA_TYPES_OPTIONS}
            value={getFieldValue(callbackEtaTypes, CALLBACK_ETA_TYPES)}
            onChange={(selectedOptions: any) => {
              onChangeFormField(
                "update_settings.eta.type",
                getFieldKey(selectedOptions[0], CALLBACK_ETA_TYPES)
              );
            }}
            select={"single"}
            showError={confirmed && isFieldInvalid(callbackEtaTypes)}
          />
        </div>
      </div>
    </>
  );
};

const EtdFrequency = ({ webhookForm, confirmed, onChangeFormField }: any) => {
  const { t } = useTranslation();
  const callbackEtdTypes =
    webhookForm["update_settings.etd.type"] || "fourkites_etd";

  /*****************************************************************************
   * STATE
   ****************************************************************************/
  const [etdFrequencyMinutes, setEtdFrequencyMinutes] = useState(
    webhookForm["update_settings.etd.frequency_min"] || DEFAULT_ETD_FREQUENCY
  );

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <>
      <div id="input-row">
        <div className={styles.inputWrapper} data-test-id="input-rule-name">
          <Input
            label={`${t("ETD Change Frequency (Minutes)")}`}
            value={etdFrequencyMinutes}
            errorLabel={`${t("Value should be between 5 - 720 minutes")}`}
            invalid={
              isFrequencyInvalid(
                etdFrequencyMinutes,
                MIN_ETD_FREQUENCY,
                MAX_UPDATE_FREQUENCY
              ) && confirmed
            }
            onChange={(e: any) => {
              setEtdFrequencyMinutes(e.target.value);
              onChangeFormField(
                "update_settings.etd.frequency_min",
                e.target.value
              );
            }}
            placeholder={`${t("Value between 5 - 720 minutes")}`}
          />
        </div>
      </div>
      <div id="input-row">
        <div className={styles.selectWrapper} data-test-id="etd-types">
          <Select
            label={t("Callback ETD Type")}
            options={CALLBACK_ETD_TYPES_OPTIONS}
            value={getFieldValue(callbackEtdTypes, CALLBACK_ETD_TYPES)}
            onChange={(selectedOptions: any) => {
              onChangeFormField(
                "update_settings.etd.type",
                getFieldKey(selectedOptions[0], CALLBACK_ETD_TYPES)
              );
            }}
            select={"single"}
            showError={confirmed && isFieldInvalid(callbackEtdTypes)}
          />
        </div>
      </div>
    </>
  );
};

const TimestampHeader = ({
  webhookForm,
  confirmed,
  onChangeFormField,
}: any) => {
  const { t } = useTranslation();
  const isTimeStampHeaderKeyIncluded =
    webhookForm["update_settings.timestamp_header.is_included"];
  const timeStampHeaderKey =
    webhookForm["update_settings.timestamp_header.header_key"];

  return (
    <>
      <div id="input-row">
        <div
          className={styles.toggleSwitchWrapper}
          data-test-id="input-rule-name"
        >
          <label>{t("Timestamp Header Key")}</label>
          <Switch
            size="large"
            defaultLabel={t("Include Live Timestamp")}
            checked={isTimeStampHeaderKeyIncluded}
            onChange={(e: ChangeEvent<HTMLInputElement>) => {
              onChangeFormField(
                "update_settings.timestamp_header.is_included",
                !isTimeStampHeaderKeyIncluded
              );
            }}
          />
        </div>
      </div>

      <div id="input-row">
        <div className={styles.inputWrapper} data-test-id="input-rule-name">
          <Input
            label=""
            value={timeStampHeaderKey}
            onChange={(e: any) =>
              onChangeFormField(
                "update_settings.timestamp_header.header_key",
                e.target.value
              )
            }
            placeholder={`${t("Timestamp header key")}`}
            disabled={!isTimeStampHeaderKeyIncluded}
            errorLabel={`${t("Field is required")}`}
            invalid={
              confirmed &&
              isTimeStampHeaderKeyIncluded &&
              isFieldInvalid(timeStampHeaderKey)
            }
          />
        </div>
      </div>
    </>
  );
};

const StationaryLocation = ({ webhookForm, onChangeFormField }: any) => {
  const { t } = useTranslation();
  const shouldSendStationaryLocation =
    webhookForm["update_settings.should_send_stationary_location"];

  return (
    <div id="input-row">
      <div
        className={styles.toggleSwitchWrapper}
        data-test-id="input-rule-name"
      >
        <label>{t("Stationary location")}</label>
        <Switch
          size="large"
          defaultLabel={t(
            "Do not send location updates if truck location has not changed"
          )}
          checked={shouldSendStationaryLocation}
          className={"finalToggleSwitch"}
          onChange={(e: ChangeEvent<HTMLInputElement>) => {
            onChangeFormField(
              "update_settings.should_send_stationary_location",
              !shouldSendStationaryLocation
            );
          }}
        />
      </div>
    </div>
  );
};

export const CALLBACK_ETA_TYPES = {
  fourkites_eta: "FourKites ETA",
  fk_and_carrier_eta: "FourKites and Carrier ETA",
};

export const CALLBACK_ETD_TYPES = {
  fourkites_etd: "FourKites ETD",
  fk_and_carrier_etd: "FourKites and Carrier ETD",
};

const CALLBACK_ETA_TYPES_OPTIONS = Object.values(CALLBACK_ETA_TYPES);
const CALLBACK_ETD_TYPES_OPTIONS = Object.values(CALLBACK_ETD_TYPES);

export default UpdateSettings;
