import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

import AuthenticationHeaders from "./AuthenticationHeaders";
import HttpAuthentication from "./HttpAuthentication";
import BearerTokenAuthentication from "./BearerTokenAuthentication";
import SessionBasedAuthentication from "./SessionBasedAuthentication";

const Authentication = ({ webhookForm, onChangeFormField }: any) => {
  const { t } = useTranslation();
  const formGrantType =
    webhookForm["authentication.session_bearer_token_credentials.grant_type"];

  /*****************************************************************************
   * STATE
   ****************************************************************************/
  const [revealPassword, setRevealPassword] = useState<boolean>(false);
  const [grantType, setGrantType] = useState<
    "username_and_password" | "client_credentials"
  >("username_and_password");

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/
  /*
   * Sets the grant type as per form details
   */
  useEffect(() => {
    if (formGrantType) {
      setGrantType(formGrantType);
    } else {
      setGrantType("username_and_password");
    }
  }, [formGrantType]);

  /*****************************************************************************
   * RENDER
   ****************************************************************************/
  const isHttpAuthentication =
    webhookForm["authentication.scheme"] === "http_basic";
  const isBearerTokenAuthentication =
    webhookForm["authentication.scheme"] === "static_bearer_token";
  const isSessionBasedAuthentication =
    webhookForm["authentication.scheme"] === "session_bearer_token";

  return (
    <>
      <AuthenticationHeaders
        webhookForm={webhookForm}
        onChangeFormField={onChangeFormField}
      />

      {isHttpAuthentication && (
        <HttpAuthentication
          webhookForm={webhookForm}
          onChangeFormField={onChangeFormField}
          revealPassword={revealPassword}
          setRevealPassword={setRevealPassword}
        />
      )}

      {isBearerTokenAuthentication && (
        <BearerTokenAuthentication
          webhookForm={webhookForm}
          onChangeFormField={onChangeFormField}
        />
      )}

      {isSessionBasedAuthentication && (
        <SessionBasedAuthentication
          webhookForm={webhookForm}
          revealPassword={revealPassword}
          setRevealPassword={setRevealPassword}
          grantType={grantType}
          setGrantType={setGrantType}
          onChangeFormField={onChangeFormField}
        />
      )}
    </>
  );
};

export default Authentication;
