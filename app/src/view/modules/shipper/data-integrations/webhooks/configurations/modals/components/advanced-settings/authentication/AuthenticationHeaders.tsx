import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import { Input } from "@fourkites/elemental-input";
import { Select } from "@fourkites/elemental-select";
import { Modal } from "@fourkites/elemental-modal";
import { InfoIcon } from "@fourkites/elemental-atoms";
import { Tooltip } from "@fourkites/elemental-tooltip";

import {
  getFieldKey,
  getFieldValue,
} from "view/modules/shipper/data-integrations/webhooks/configurations/modals/WebhookFormUtils";

import styles from "view/modules/shipper/data-integrations/webhooks/configurations/modals/WebhookCreationModalForm.module.scss";

const AuthenticationHeaders = ({ webhookForm, onChangeFormField }: any) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * STATE
   ****************************************************************************/
  const [showOtherAuthSchemeAlertModal, setShowOtherAuthSchemeAlertModal] =
    useState<boolean>(false);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const selectAuthenticationScheme = (value: string) => {
    const key = getFieldKey(value, AUTHENTICATION_SCHEMES);
    onChangeFormField("authentication.scheme", key);

    if (key === "other") {
      setShowOtherAuthSchemeAlertModal(true);
    }
  };

  const selectNoAuthForOtherAuthScheme = () => {
    onChangeFormField(
      "authentication.scheme",
      AUTHENTICATION_SCHEMES_OPTIONS[0]
    );
    setShowOtherAuthSchemeAlertModal(false);
  };

  return (
    <>
      <div id="input-row">
        <div
          className={styles.inputWrapper}
          data-test-id="authentication-header"
        >
          <Input
            label={`${t("Custom Auth Header")}`}
            value={webhookForm["authentication.custom_header"]}
            onChange={(e: any) =>
              onChangeFormField("authentication.custom_header", e.target.value)
            }
            placeholder={`{"Key":"Value"}`}
          />
        </div>
      </div>

      <div id="input-row">
        <div
          className={styles.inputWrapper}
          data-test-id="authentication-payload"
        >
          <Input
            label={`${t("Custom Auth Payload")}`}
            value={webhookForm["authentication.custom_payload"]}
            onChange={(e: any) =>
              onChangeFormField("authentication.custom_payload", e.target.value)
            }
            placeholder={`{"Key":"Value"}`}
          />
        </div>
      </div>

      <div id="input-row">
        <div
          className={styles.selectWrapper}
          data-test-id="authentication-scheme"
        >
          <Select
            label={t("Authentication Scheme")}
            onChange={(selectedOptions: any) => {
              selectAuthenticationScheme(selectedOptions[0]);
            }}
            options={AUTHENTICATION_SCHEMES_OPTIONS}
            select={"single"}
            value={getFieldValue(
              webhookForm["authentication.scheme"],
              AUTHENTICATION_SCHEMES
            )}
            disabled={webhookForm["authentication.scheme"] === "other"}
          />
          {webhookForm["authentication.scheme"] === "other" && (
            <Tooltip
              text={t(
                "For modification of non-standard authentication methods, " +
                  "please contact customer support at: <EMAIL>"
              )}
            >
              <span className={styles.disabledInfo}>
                <InfoIcon fill="#0e65e5" iconClass={"button-icon-left"} />
              </span>
            </Tooltip>
          )}
        </div>
      </div>

      <OtherAuthSchemeAlertModal
        showOtherAuthSchemeAlertModal={showOtherAuthSchemeAlertModal}
        selectNoAuthForOtherAuthScheme={selectNoAuthForOtherAuthScheme}
      />
    </>
  );
};

const OtherAuthSchemeAlertModal = ({
  showOtherAuthSchemeAlertModal,
  selectNoAuthForOtherAuthScheme,
}: any) => {
  const { t } = useTranslation();

  return (
    <Modal
      size="small"
      title={t("Selecting Other Authentication Schemes?")}
      show={showOtherAuthSchemeAlertModal}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: selectNoAuthForOtherAuthScheme,
      }}
      saveButtonProps={{
        label: t("Ok"),
        onClick: selectNoAuthForOtherAuthScheme,
      }}
    >
      <div
        className={styles.modalContainer}
        data-test-id="webhooks-configuration-auth-scheme-alert"
      >
        <label>
          {t(
            "Contact FourKites <NAME_EMAIL> for more information"
          )}
        </label>
      </div>
    </Modal>
  );
};

export const AUTHENTICATION_SCHEMES = {
  no_auth: "No Authentication",
  http_basic: "HTTP Basic Authentication",
  static_bearer_token: "Bearer Token (Static Token)",
  session_bearer_token: "Session Based Bearer Token (From URL Encoded)",
  other: "Other",
};

const AUTHENTICATION_SCHEMES_OPTIONS = Object.values(AUTHENTICATION_SCHEMES);

export default AuthenticationHeaders;
