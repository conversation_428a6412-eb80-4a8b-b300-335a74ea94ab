import React from "react";
import { useTranslation } from "react-i18next";

import { Input } from "@fourkites/elemental-input";

import styles from "view/modules/shipper/data-integrations/webhooks/configurations/modals/WebhookCreationModalForm.module.scss";

const BearerTokenAuthentication = ({ webhookForm, onChangeFormField }: any) => {
  const { t } = useTranslation();
  const bearerTokenCredsTokenName =
    webhookForm["authentication.static_bearer_token_credentials.token_name"];
  const bearerTokenCredsTokenValue =
    webhookForm["authentication.static_bearer_token_credentials.token_value"];

  return (
    <>
      <div id="input-row">
        <div className={styles.inputWrapper} data-test-id="bearer-token-name">
          <Input
            label={`${t("Bearer Token Name")}`}
            errorLabel={`${t("Field is required")}`}
            value={bearerTokenCredsTokenName}
            onChange={(e: any) =>
              onChangeFormField(
                "authentication.static_bearer_token_credentials.token_name",
                e.target.value
              )
            }
            placeholder={`${t("Bearer token name")}`}
          />
        </div>
      </div>

      <div id="input-row">
        <div className={styles.inputWrapper} data-test-id="bearer-token-value">
          <Input
            label={`${t("Bearer Token")}`}
            errorLabel={`${t("Field is required")}`}
            value={bearerTokenCredsTokenValue}
            onChange={(e: any) =>
              onChangeFormField(
                "authentication.static_bearer_token_credentials.token_value",
                e.target.value
              )
            }
            placeholder={`${t("Bearer token value")}`}
          />
        </div>
      </div>
    </>
  );
};

export default BearerTokenAuthentication;
