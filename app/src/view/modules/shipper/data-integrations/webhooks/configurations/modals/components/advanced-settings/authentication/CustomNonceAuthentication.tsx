import React from "react";
import { useTranslation } from "react-i18next";

import { Input } from "@fourkites/elemental-input";
import { EyeIcon } from "@fourkites/elemental-atoms";
import { Select } from "@fourkites/elemental-select";

import {
  getFieldKey,
  getFieldValue,
} from "view/modules/shipper/data-integrations/webhooks/configurations/modals/WebhookFormUtils";

import styles from "view/modules/shipper/data-integrations/webhooks/configurations/modals/WebhookCreationModalForm.module.scss";

const CustomNonceAuthentication = ({
  webhookForm,
  onChangeFormField,
  revealPassword,
  setRevealPassword,
}: any) => {
  const { t } = useTranslation();
  const customNonceAuthUserName =
    webhookForm["authentication.custom_nonce_credentials.username"];
  const customNonceAuthPassword =
    webhookForm["authentication.custom_nonce_credentials.password"];
  const customNonceAuthHashFunction =
    webhookForm["authentication.custom_nonce_credentials.hash_function"];
  const customNonceAuthSignature =
    webhookForm["authentication.custom_nonce_credentials.signature"];

  return (
    <>
      <div id="input-row">
        <div
          className={styles.inputWrapper}
          data-test-id="custom-nonce-username"
        >
          <Input
            label={`${t("Username or Client ID")}`}
            errorLabel={`${t("Field is required")}`}
            value={customNonceAuthUserName}
            onChange={(e: any) =>
              onChangeFormField(
                "authentication.custom_nonce_credentials.username",
                e.target.value
              )
            }
            placeholder={`${t("Username or client ID")}`}
          />
        </div>
      </div>

      <div id="input-row">
        <div
          className={styles.inputWrapper}
          data-test-id="custom-nonce-password"
        >
          <Input
            label={`${t("Password")}`}
            errorLabel={`${t("Field is required")}`}
            type={revealPassword ? "text" : "password"}
            value={customNonceAuthPassword}
            onChange={(e: any) =>
              onChangeFormField(
                "authentication.custom_nonce_credentials.password",
                e.target.value
              )
            }
            placeholder={`${t("Password or client secret")}`}
            icon={<EyeIcon />}
            onIconClick={() => setRevealPassword(!revealPassword)}
          />
        </div>
      </div>
      <div id="input-row">
        <div
          className={styles.selectWrapper}
          data-test-id="custom-nonce-hash-function"
        >
          <Select
            label={`${t("Hash Function")}`}
            onChange={(selectedOptions: any) => {
              onChangeFormField(
                "authentication.custom_nonce_credentials.hash_function",
                getFieldKey(selectedOptions[0], HASH_FUNCTION_TYPES)
              );
            }}
            options={HASH_FUNCTION_TYPES_OPTIONS}
            select={"single"}
            value={getFieldValue(
              customNonceAuthHashFunction,
              HASH_FUNCTION_TYPES
            )}
          />
        </div>
      </div>

      <div id="input-row">
        <div
          className={styles.inputWrapper}
          data-test-id="custom-nonce-signature"
        >
          <Input
            label={`${t("Signature")}`}
            errorLabel={`${t("Field is required")}`}
            value={customNonceAuthSignature}
            onChange={(e: any) =>
              onChangeFormField(
                "authentication.custom_nonce_credentials.signature",
                e.target.value
              )
            }
            placeholder={`${t("{username}:{signature_epoch}")}`}
          />
        </div>
      </div>
    </>
  );
};

export default CustomNonceAuthentication;

export const HASH_FUNCTION_TYPES = {
  SHA1: "SHA1",
  SHA256: "SHA256",
  SHA384: "SHA384",
  SHA512: "SHA512",
  SHA224: "SHA224",
  MD5: "MD5",
};

const HASH_FUNCTION_TYPES_OPTIONS = Object.values(HASH_FUNCTION_TYPES);
