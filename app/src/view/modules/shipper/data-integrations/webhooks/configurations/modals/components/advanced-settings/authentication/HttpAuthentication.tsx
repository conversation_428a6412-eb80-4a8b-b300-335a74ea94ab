import React from "react";
import { useTranslation } from "react-i18next";

import { Input } from "@fourkites/elemental-input";
import { EyeIcon } from "@fourkites/elemental-atoms";

import styles from "view/modules/shipper/data-integrations/webhooks/configurations/modals/WebhookCreationModalForm.module.scss";

const HttpAuthentication = ({
  webhookForm,
  onChangeFormField,
  revealPassword,
  setRevealPassword,
}: any) => {
  const { t } = useTranslation();
  const httpBasicCredsUserName =
    webhookForm["authentication.http_basic_credentials.username"];
  const httpBasicCredsPassword =
    webhookForm["authentication.http_basic_credentials.password"];

  return (
    <>
      <div id="input-row">
        <div className={styles.inputWrapper} data-test-id="http-auth-username">
          <Input
            label={`${t("Username or Client ID")}`}
            errorLabel={`${t("Field is required")}`}
            value={httpBasicCredsUserName}
            onChange={(e: any) =>
              onChangeFormField(
                "authentication.http_basic_credentials.username",
                e.target.value
              )
            }
            placeholder={`${t("Username or client ID")}`}
          />
        </div>
      </div>

      <div id="input-row">
        <div
          className={styles.inputWrapper}
          data-test-id="inhttp-auth-password"
        >
          <Input
            label={`${t("Password")}`}
            errorLabel={`${t("Field is required")}`}
            type={revealPassword ? "text" : "password"}
            value={httpBasicCredsPassword}
            onChange={(e: any) =>
              onChangeFormField(
                "authentication.http_basic_credentials.password",
                e.target.value
              )
            }
            placeholder={`${t("Password or client secret")}`}
            icon={<EyeIcon />}
            onIconClick={() => setRevealPassword(!revealPassword)}
          />
        </div>
      </div>
    </>
  );
};

export default HttpAuthentication;
