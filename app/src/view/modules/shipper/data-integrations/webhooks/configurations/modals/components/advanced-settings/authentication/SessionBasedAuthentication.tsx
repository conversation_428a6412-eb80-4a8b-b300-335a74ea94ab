import React from "react";
import { useTranslation } from "react-i18next";

import { Input } from "@fourkites/elemental-input";
import { RadioButton } from "@fourkites/elemental-radio-button";
import { EyeIcon } from "@fourkites/elemental-atoms";

import styles from "view/modules/shipper/data-integrations/webhooks/configurations/modals/WebhookCreationModalForm.module.scss";

const SessionBasedAuthentication = ({
  webhookForm,
  revealPassword,
  setRevealPassword,
  grantType,
  setGrantType,
  onChangeFormField,
}: any) => {
  const { t } = useTranslation();
  const isUserNamePasswordGrantType = grantType === "username_and_password";
  const isClientCredentialsGrantType = grantType === "client_credentials";
  const sessionBasedAuthCredsScope =
    webhookForm["authentication.session_bearer_token_credentials.scope"];
  const sessionBasedAuthSessionUrl =
    webhookForm["authentication.session_bearer_token_credentials.session_url"];
  const sessionBasedAuthAdditionalProperties =
    webhookForm[
      "authentication.session_bearer_token_credentials.additional_properties"
    ];

  return (
    <>
      <div id="input-row">
        <div className={styles.radioButtonWrapper} data-test-id="grant-type">
          <label>{t("Grant Type")}</label>
          <RadioButton
            label={t("Username & Password")}
            checked={grantType === "username_and_password"}
            onClick={(e: any) => {
              setGrantType("username_and_password");
              onChangeFormField(
                "authentication.session_bearer_token_credentials.grant_type",
                "username_and_password"
              );
            }}
          />
          <RadioButton
            label={t("Client Credentials")}
            checked={grantType === "client_credentials"}
            onClick={(e: any) => {
              setGrantType("client_credentials");
              onChangeFormField(
                "authentication.session_bearer_token_credentials.grant_type",
                "client_credentials"
              );
            }}
          />
        </div>
      </div>
      {isUserNamePasswordGrantType && (
        <GrantTypeUserNamePassword
          webhookForm={webhookForm}
          revealPassword={revealPassword}
          setRevealPassword={setRevealPassword}
          onChangeFormField={onChangeFormField}
        />
      )}
      {isClientCredentialsGrantType && (
        <GrantTypeClientCredentials
          webhookForm={webhookForm}
          onChangeFormField={onChangeFormField}
        />
      )}
      <div id="input-row">
        <div className={styles.inputWrapper} data-test-id="session-auth-scope">
          <Input
            label={`${t("Scope")}`}
            errorLabel={`${t("Field is required")}`}
            value={sessionBasedAuthCredsScope}
            onChange={(e: any) =>
              onChangeFormField(
                "authentication.session_bearer_token_credentials.scope",
                e.target.value
              )
            }
            placeholder={`${t("Scope")}`}
          />
        </div>
      </div>

      <div id="input-row">
        <div className={styles.inputWrapper} data-test-id="session-auth-url">
          <Input
            label={`${t("Bearer Token Session URL")}`}
            errorLabel={`${t("Field is required")}`}
            value={sessionBasedAuthSessionUrl}
            onChange={(e: any) =>
              onChangeFormField(
                "authentication.session_bearer_token_credentials.session_url",
                e.target.value
              )
            }
            placeholder={`${t("URL")}`}
          />
        </div>
      </div>

      <div id="input-row">
        <div
          className={styles.inputWrapper}
          data-test-id="additional-properties"
        >
          <Input
            label={`${t("Additional Session Properties")}`}
            errorLabel={`${t("Field is required")}`}
            value={sessionBasedAuthAdditionalProperties}
            onChange={(e: any) =>
              onChangeFormField(
                "authentication.session_bearer_token_credentials.additional_properties",
                e.target.value
              )
            }
            placeholder={`${t("Session properties")}`}
          />
        </div>
      </div>
    </>
  );
};

const GrantTypeUserNamePassword = ({
  webhookForm,
  revealPassword,
  setRevealPassword,
  onChangeFormField,
}: any) => {
  const { t } = useTranslation();
  const sessionBasedAuthUserName =
    webhookForm["authentication.session_bearer_token_credentials.username"];
  const sessionBasedAuthPassword =
    webhookForm["authentication.session_bearer_token_credentials.password"];

  return (
    <>
      <div id="input-row">
        <div
          className={styles.inputWrapper}
          data-test-id="session-auth-username"
        >
          <Input
            label={`${t("Username")}`}
            errorLabel={`${t("Field is required")}`}
            value={sessionBasedAuthUserName}
            onChange={(e: any) =>
              onChangeFormField(
                "authentication.session_bearer_token_credentials.username",
                e.target.value
              )
            }
            placeholder={`${t("Username")}`}
          />
        </div>
      </div>

      <div id="input-row">
        <div
          className={styles.inputWrapper}
          data-test-id="session-auth-password"
        >
          <Input
            label={`${t("Password")}`}
            errorLabel={`${t("Field is required")}`}
            type={revealPassword ? "text" : "password"}
            value={sessionBasedAuthPassword}
            onChange={(e: any) =>
              onChangeFormField(
                "authentication.session_bearer_token_credentials.password",
                e.target.value
              )
            }
            placeholder={`${t("Password")}`}
            icon={<EyeIcon />}
            onIconClick={() => setRevealPassword(!revealPassword)}
          />
        </div>
      </div>
    </>
  );
};

const GrantTypeClientCredentials = ({
  webhookForm,
  onChangeFormField,
}: any) => {
  const { t } = useTranslation();
  const sessionBasedAuthClientId =
    webhookForm["authentication.session_bearer_token_credentials.client_id"];
  const sessionBasedAuthClientSecret =
    webhookForm[
      "authentication.session_bearer_token_credentials.client_secret"
    ];

  return (
    <>
      <div id="input-row">
        <div className={styles.inputWrapper} data-test-id="client-id">
          <Input
            label={`${t("Client ID")}`}
            errorLabel={`${t("Field is required")}`}
            value={sessionBasedAuthClientId}
            onChange={(e: any) =>
              onChangeFormField(
                "authentication.session_bearer_token_credentials.client_id",
                e.target.value
              )
            }
            placeholder={`${t("Client ID")}`}
          />
        </div>
      </div>

      <div id="input-row">
        <div className={styles.inputWrapper} data-test-id="client-secret">
          <Input
            label={`${t("Client Secret")}`}
            errorLabel={`${t("Field is required")}`}
            value={sessionBasedAuthClientSecret}
            onChange={(e: any) =>
              onChangeFormField(
                "authentication.session_bearer_token_credentials.client_secret",
                e.target.value
              )
            }
            placeholder={`${t("Client secret")}`}
          />
        </div>
      </div>
    </>
  );
};

export default SessionBasedAuthentication;
