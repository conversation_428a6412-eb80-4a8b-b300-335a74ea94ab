import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

import { Input } from "@fourkites/elemental-input";
import { Select } from "@fourkites/elemental-select";
import { RadioButton } from "@fourkites/elemental-radio-button";
import { Modal } from "@fourkites/elemental-modal";
import { Checkbox } from "@fourkites/elemental-checkbox";
import { ExternalLinkIcon, InfoIcon } from "@fourkites/elemental-atoms";
import { Tooltip } from "@fourkites/elemental-tooltip";

import { onSharedPartnersOverviewClick } from "router/navigationUtils";

import { isFieldInvalid } from "view/components/base/FormUtils";

import {
  getFieldKey,
  getFieldValue,
} from "view/modules/shipper/data-integrations/webhooks/configurations/modals/WebhookFormUtils";

import WebhookCreationModalFormProps from "view/modules/shipper/data-integrations/webhooks/configurations/modals/WebhookCreationModalForm.types";

import styles from "view/modules/shipper/data-integrations/webhooks/configurations/modals/WebhookCreationModalForm.module.scss";

const LoadInformation = ({
  webhookForm,
  confirmed,
  onChangeFormField,
}: WebhookCreationModalFormProps) => {
  const { t } = useTranslation();
  const loadIdentifierRegexRule =
    webhookForm["event_notifications_criteria.loads_identifier.regex.rule"];
  const hasSpecificLoads =
    webhookForm["event_notifications_criteria.value"] === "specific_loads";

  /*****************************************************************************
   * STATE
   ****************************************************************************/
  const [loadIdentifierRegex, setLoadIdentifierRegex] = useState<
    "Contains" | "DoNotContain"
  >("Contains");

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/
  /*
   * Sets the load identifier regex rule as per form details
   */
  useEffect(() => {
    if (loadIdentifierRegexRule) {
      setLoadIdentifierRegex(loadIdentifierRegexRule);
    } else {
      setLoadIdentifierRegex("Contains");
    }
  }, [loadIdentifierRegexRule]);

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <>
      <LoadTypes
        webhookForm={webhookForm}
        onChangeFormField={onChangeFormField}
      />

      {hasSpecificLoads && (
        <SpecificLoads
          webhookForm={webhookForm}
          confirmed={confirmed}
          onChangeFormField={onChangeFormField}
          loadIdentifierRegex={loadIdentifierRegex}
          setLoadIdentifierRegex={setLoadIdentifierRegex}
        />
      )}
    </>
  );
};

const LoadTypes = ({ webhookForm, onChangeFormField }: any) => {
  const { t } = useTranslation();
  let LOAD_TYPES: any = {
    all_loads: "All Loads",
    specific_loads: "Specific Loads",
    based_on_load_source: "Based on Load Source",
  };

  const LOAD_TYPES_OPTIONS: any = Object.values(LOAD_TYPES);

  /*****************************************************************************
   * STATE
   ****************************************************************************/
  const [showBasedOnLoadSourceAlertModal, setShowBasedOnLoadSourceAlertModal] =
    useState<boolean>(false);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const selectLoadInformation = (value: string) => {
    const key = getFieldKey(value, LOAD_TYPES);
    onChangeFormField("event_notifications_criteria.value", key);
    if (key === "based_on_load_source") {
      setShowBasedOnLoadSourceAlertModal(true);
    }
  };

  const selectAllLoadsForBasedOnLoadSource = () => {
    onChangeFormField(
      "event_notifications_criteria.value",
      LOAD_TYPES_OPTIONS[0]
    );
    setShowBasedOnLoadSourceAlertModal(false);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <>
      <label>{t("Load Information")}</label>
      <div id="input-row">
        <div className={styles.selectWrapper} data-test-id="load-types">
          <Select
            label={t("Criteria for Sending Callbacks ")}
            onChange={(selectedOptions: any) => {
              selectLoadInformation(selectedOptions[0]);
            }}
            options={LOAD_TYPES_OPTIONS}
            select={"single"}
            value={getFieldValue(
              webhookForm["event_notifications_criteria.value"],
              LOAD_TYPES
            )}
          />
        </div>
      </div>
      <BasedOnLoadSourceAlertModal
        showBasedOnLoadSourceAlertModal={showBasedOnLoadSourceAlertModal}
        selectAllLoadsForBasedOnLoadSource={selectAllLoadsForBasedOnLoadSource}
      />
    </>
  );
};

const SpecificLoads = ({
  webhookForm,
  confirmed,
  onChangeFormField,
  loadIdentifierRegex,
  setLoadIdentifierRegex,
}: any) => {
  const { t } = useTranslation();
  const loadIdentifierTypes =
    webhookForm["event_notifications_criteria.loads_identifier.type"] ||
    "LoadNumber";
  const isSharedLoadsIdentifier = loadIdentifierTypes === "SharedLoads";
  const loadIdentifierRegexMatch =
    webhookForm["event_notifications_criteria.loads_identifier.regex.match"];

  const setLoadIdentifier = (selectedOption: any) => {
    const isSharedLoadsIdentifier = selectedOption === "Shared Loads";
    // SELF-2116 (Temporary  fix until shared partners is ready)
    webhookForm["event_notifications_criteria.loads_identifier.regex.match"] =
      isSharedLoadsIdentifier ? "all" : "";
    onChangeFormField(
      "event_notifications_criteria.loads_identifier.type",
      getFieldKey(selectedOption, LOAD_IDENTIFIER_TYPES)
    );
  };

  return (
    <>
      <div id="input-row">
        <div className={styles.selectWrapper} data-test-id="load-identifier">
          <Select
            label={t("Load Identifier")}
            onChange={(selectedOptions: any) => {
              setLoadIdentifier(selectedOptions[0]);
            }}
            options={LOAD_IDENTIFIER_TYPES_OPTIONS}
            select={"single"}
            value={getFieldValue(loadIdentifierTypes, LOAD_IDENTIFIER_TYPES)}
            showError={confirmed && isFieldInvalid(loadIdentifierTypes)}
          />
          {isSharedLoadsIdentifier && (
            <Tooltip
              text={t(
                "Loads created by other shippers, who have chosen to " +
                  "share loads with you through Network Visibility"
              )}
            >
              <span className={styles.disabledInfo}>
                <InfoIcon fill="#0e65e5" iconClass={"button-icon-left"} />
              </span>
            </Tooltip>
          )}
        </div>
      </div>

      {isSharedLoadsIdentifier && (
        <>
          <div id="input-row">
            <div
              className={styles.checkboxWrapper}
              data-test-id="shared-loads-select-all"
            >
              <Checkbox
                label={t("Loads from all sharing partners")}
                size="large"
                disabled={true}
                value={"checked"}
              />
              <a
                href=""
                onClick={onSharedPartnersOverviewClick}
                target="_blank"
              >
                {t("Click here for more information on partners")}
                <ExternalLinkIcon
                  fill="#0e65e5"
                  iconClass={"button-icon-right"}
                />
              </a>
            </div>
          </div>
        </>
      )}

      {!isSharedLoadsIdentifier && (
        <>
          <div id="input-row">
            <div
              className={styles.radioButtonWrapper}
              data-test-id="load-identifier-regex"
            >
              <label>{t("Identifer Regex")}</label>
              <RadioButton
                label={t("Contains")}
                checked={loadIdentifierRegex === "Contains"}
                onClick={(e: any) => {
                  setLoadIdentifierRegex("Contains");
                  onChangeFormField(
                    "event_notifications_criteria.loads_identifier.regex.rule",
                    "Contains"
                  );
                }}
              />
              <RadioButton
                label={t("Does not contain")}
                checked={loadIdentifierRegex === "DoNotContain"}
                onClick={(e: any) => {
                  setLoadIdentifierRegex("DoNotContain");
                  onChangeFormField(
                    "event_notifications_criteria.loads_identifier.regex.rule",
                    "DoNotContain"
                  );
                }}
              />
            </div>
          </div>

          <div id="input-row">
            <div
              className={styles.inputWrapper}
              data-test-id="load-identifier-regex-match"
            >
              <Input
                label=""
                errorLabel={`${t("Field is required")}`}
                value={loadIdentifierRegexMatch}
                invalid={confirmed && isFieldInvalid(loadIdentifierRegexMatch)}
                onChange={(e: any) =>
                  onChangeFormField(
                    "event_notifications_criteria.loads_identifier.regex.match",
                    e.target.value
                  )
                }
                placeholder={`${t("Regex Value")}`}
              />
            </div>
          </div>
        </>
      )}
    </>
  );
};

const BasedOnLoadSourceAlertModal = ({
  showBasedOnLoadSourceAlertModal,
  selectAllLoadsForBasedOnLoadSource,
}: any) => {
  const { t } = useTranslation();

  return (
    <Modal
      size="small"
      title={t("Selecting Based on Load Source?")}
      show={showBasedOnLoadSourceAlertModal}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: selectAllLoadsForBasedOnLoadSource,
      }}
      saveButtonProps={{
        label: t("Ok"),
        onClick: selectAllLoadsForBasedOnLoadSource,
      }}
    >
      <div
        className={styles.modalContainer}
        data-test-id="webhooks-configuration-load-info-alert"
      >
        <label>
          {t(
            "Contact FourKites <NAME_EMAIL> for more information"
          )}
        </label>
      </div>
    </Modal>
  );
};

// Keys configured like this in comapny service and BE wants the same
// TODO: Add Translations
export const LOAD_IDENTIFIER_TYPES = {
  LoadNumber: "Load #",
  ReferenceNumbers: "Ref #",
  StopPONumbers: "PO #",
  ProNumber: "PRO #",
  Tags: "Tags",
  Shipper: "Shipper",
  Carrier: "Carrier",
  SharedLoads: "Shared Loads",
};

const LOAD_IDENTIFIER_TYPES_OPTIONS = Object.values(LOAD_IDENTIFIER_TYPES);

export default LoadInformation;
