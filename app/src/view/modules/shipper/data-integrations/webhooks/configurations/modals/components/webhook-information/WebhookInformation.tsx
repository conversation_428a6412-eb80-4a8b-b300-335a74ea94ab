import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

import { Input } from "@fourkites/elemental-input";
import { Select } from "@fourkites/elemental-select";
import { RadioButton } from "@fourkites/elemental-radio-button";
import { InfoIcon } from "@fourkites/elemental-atoms";
import { Tooltip } from "@fourkites/elemental-tooltip";

import { isFieldInvalid, isUrlInvalid } from "view/components/base/FormUtils";

import {
  CONFIG_TYPE_NAMES,
  getFieldKey,
  getFieldKeysForMultiSelect,
  getFieldValue,
  getFieldValuesForMultiSelect,
} from "view/modules/shipper/data-integrations/webhooks/configurations/modals/WebhookFormUtils";

import WebhookCreationModalFormProps from "view/modules/shipper/data-integrations/webhooks/configurations/modals/WebhookCreationModalForm.types";

import styles from "view/modules/shipper/data-integrations/webhooks/configurations/modals/WebhookCreationModalForm.module.scss";

const WebhookInformation = ({
  webhookForm,
  confirmed,
  onChangeFormField,
}: WebhookCreationModalFormProps) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/
  const isSetupUrlInvalid = (setupUrl: string) =>
    confirmed && isUrlInvalid(setupUrl);

  const hasUnsupportedEventsSelected = () => {
    const selectedFieldValues = getFieldValuesForMultiSelect(
      webhookForm?.subscribed_events,
      EVENT_TYPES
    );
    return (
      webhookForm?.subscribed_events &&
      webhookForm?.subscribed_events?.length !== selectedFieldValues?.length
    );
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <>
      <label>{t("Webhook Information")}</label>

      <div id="input-row">
        <div className={styles.inputWrapper} data-test-id="webhook-name">
          <Input
            label={`${t("Webhook Name")}`}
            errorLabel={`${t("Field is required")}`}
            value={webhookForm?.name}
            invalid={confirmed && isFieldInvalid(webhookForm?.name)}
            onChange={(e: any) => onChangeFormField("name", e.target.value)}
            placeholder={`${t("Eg: Standard Callbacks")}`}
            required
          />
        </div>
      </div>

      <div id="input-row">
        <div className={styles.selectWrapper} data-test-id="webhook-type">
          <Select
            label={`${t("Webhook Type")}`}
            options={["FourKites Standarad Callback"]}
            value={
              getFieldValue(webhookForm?.type, CONFIG_TYPE_NAMES) || [
                "FourKites Standarad Callback",
              ]
            }
            onChange={(selectedOptions: any) =>
              onChangeFormField(
                "type",
                getFieldKey(selectedOptions[0], CONFIG_TYPE_NAMES)
              )
            }
            disabled={true}
          />
        </div>
      </div>

      <div id="input-row">
        <div className={styles.selectWrapper} data-test-id="webhook-events">
          <Select
            label={`${t("Events")}`}
            options={EVENT_TYPES_OPTIONS}
            showError={confirmed && !webhookForm?.subscribed_events?.length}
            errorLabel={`${t("Field is required")}`}
            onChange={(selectedOptions: any) => {
              onChangeFormField(
                "subscribed_events",
                getFieldKeysForMultiSelect(selectedOptions, EVENT_TYPES)
              );
            }}
            value={getFieldValuesForMultiSelect(
              webhookForm?.subscribed_events,
              EVENT_TYPES
            )}
            select={"multiple"}
            disabled={hasUnsupportedEventsSelected()}
          />

          {hasUnsupportedEventsSelected() && (
            <Tooltip
              text={t(
                "For modification of custom events not present in the list, " +
                  "please contact customer support at: <EMAIL>"
              )}
            >
              <span className={styles.disabledInfo}>
                <InfoIcon fill="#0e65e5" iconClass={"button-icon-left"} />
              </span>
            </Tooltip>
          )}
        </div>
      </div>

      <div id="input-row">
        <div className={styles.inputWrapper} data-test-id="setup-url">
          <Input
            label={`${t("Additional Subscribed   Events")}`}
            value={webhookForm?.additional_subscribed_events}
            onChange={(e: any) =>
              onChangeFormField("additional_subscribed_events", e.target.value.split(','))
            }
            placeholder={"Eg: LOAD_CREATION"}
          />
        </div>
      </div>

      <div id="input-row">
        <div className={styles.inputWrapper} data-test-id="setup-url">
          <Input
            label={`${t("Setup URL")}`}
            errorLabel={`${t("Please enter a valid URL")}`}
            value={webhookForm?.setup_url}
            invalid={isSetupUrlInvalid(webhookForm?.setup_url)}
            onChange={(e: any) =>
              onChangeFormField("setup_url", e.target.value)
            }
            placeholder={"https://www.example.com"}
            required
          />
        </div>
      </div>
    </>
  );
};

export const EVENT_TYPES = {
  new_location_updated: "New Location Update",
  new_tracking_updated: "New Tracking Update",
  new_parcel_updated: "New LTL/Parcel Update",
  new_user_note: "New User Note",
  carrier_eta_updated: "Carrier ETA Update",
  tracking_info_assigned: "Tracking Information Assignment",
  new_ocean_updated: "Ocean Update",
  stop_late_reason_code: "Late Reason Code",
  carrier_reason_code: "Carrier Reason Code",
  stop_arrival: "Stop Arrival",
  stop_departure: "Stop Departure",
  stop_eta_updated: "Stop ETA Update",
  stop_etd_updated: "Stop ETD Update",
  stop_amend: "Stop Amend",
  stop_file_uploaded: "Stop File Upload",
  load_creation: "Load Creation",
  load_updation: "Load Updation",
  load_deleted: "Load Deletion",
  appointment_confirmed: "Appointment Confirmed",
  appointment_rescheduled: "Appointment Rescheduled",
  appointment_cancelled: "Appointment Cancelled",
  temperature_reading_update: "Temperature Reading Update",
  temperature_violation: "Temperature Violation",
  stop_geofence_update: "Stop Geofence Update",
  stop_pod: "Stop Level UOM",
  stop_return: "Return orders UOM",
  load_tracking: "Load Tracking Started",
  driver_app_status: "Driver App Status",
  appless_subscription_status: "Appless Subscription Status",
  carrier_relationship_setup: "Carrier Relationship Setup",
  ready_to_track: "Ready To Track",
  tracking_now: "Tracking Now",
  load_expired: "Load Expired",
  load_not_reachable: "Load Not Reachable",
  appointment_autoscheduled: "Appointment Autoscheduled",
  am_load_created: "AM Load Created",
  appointment_checkin: "Appointment Checkin",
  appointment_checkout: "Appointment Checkout",
  scan_update: "Scan Update",
};

const EVENT_TYPES_OPTIONS = Object.values(EVENT_TYPES);

export default WebhookInformation;
