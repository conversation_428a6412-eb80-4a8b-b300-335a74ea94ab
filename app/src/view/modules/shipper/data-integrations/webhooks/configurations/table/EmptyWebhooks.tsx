import React from "react";
import { useTranslation } from "react-i18next";

import { PlusIcon, Button } from "@fourkites/elemental-atoms";

import emptyStateLogo from "assets/img/emptyBox.png";

import styles from "./EmptyWebhooks.module.scss";

const EmptyWebhooks = ({ onCreateWebhookConfiguration }: any) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div className={styles.container}>
      <img className={styles.emptyStateLogo} src={emptyStateLogo} />
      <h2>{t("No webhooks configured")}</h2>
      <h6>
        {t(
          "You don’t have any webhooks configured. Click on create configuration to start."
        )}
      </h6>
      <Button size={"large"} onClick={onCreateWebhookConfiguration}>
        <PlusIcon fill="#fff" iconClass={"button-icon-left"} />
        {t("Create Configuration")}
      </Button>
    </div>
  );
};

export default EmptyWebhooks;
