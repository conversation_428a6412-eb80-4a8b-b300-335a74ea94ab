import React from "react";

import { Tooltip } from "@fourkites/elemental-tooltip";

import { getUserLocaleDate } from "view/components/base/DateUtils";

const LastModifiedDetail = ({ lastModifiedAt, lastModifiedBy }: any) => {
  const createdAt = lastModifiedAt ? getUserLocaleDate(lastModifiedAt) : "--";
  const lastModifiedTooltipText = `Last modified by: ${
    lastModifiedBy ? lastModifiedBy : "--"
  }`;

  const lastModifiedDetails = lastModifiedBy ? (
    <Tooltip text={lastModifiedTooltipText}>
      <span>{createdAt}</span>
    </Tooltip>
  ) : (
    <span>{createdAt}</span>
  );
  return lastModifiedDetails;
};

export default LastModifiedDetail;
