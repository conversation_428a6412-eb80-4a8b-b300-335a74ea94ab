import React from "react";
import { useTranslation } from "react-i18next";

import { Tooltip } from "@fourkites/elemental-tooltip";
import {
  Button,
  TrashFullIcon,
  Edit3Icon,
  InfoIcon,
} from "@fourkites/elemental-atoms";

import { useAppSelector } from "state/hooks";
import { WebhookConfigurationState } from "state/modules/shipper/WebhookConfigurations";

import styles from "./WebhooksListTable.module.scss";

const WebhookEditAndDeleteAction = ({
  rowId,
  isStandardCallback,
  onEditWebhookConfiguration,
  onDeleteWebhookConfiguration,
}: any) => {
  const { t } = useTranslation();
  const webhooks = useAppSelector(
    WebhookConfigurationState.selectors.webhookConfigurations()
  );

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <>
      {isStandardCallback ? (
        <>
          <Button
            size="small"
            className={styles.actionsItem}
            onClick={async () => {
              const webhookId = rowId ? webhooks[rowId]?.id : null;
              onEditWebhookConfiguration(webhookId);
            }}
          >
            <Edit3Icon fill="#0e65e5" iconClass={"button-icon-left"} />
          </Button>

          <Button
            size={"small"}
            className={styles.actionsItem}
            onClick={async () => {
              const webhookId = rowId ? webhooks[rowId]?.id : null;
              const configurationName = rowId ? webhooks[rowId]?.name : null;
              onDeleteWebhookConfiguration(webhookId, configurationName);
            }}
          >
            <TrashFullIcon fill="#da1e28" iconClass={"button-icon-left"} />
          </Button>
        </>
      ) : (
        <Tooltip
          contentComponent={
            <div className={styles.customCallbackTooltipInfo}>
              <p>
                <span className={styles.tooltipInfoIcon}>
                  <InfoIcon fill={"#fff"} />
                </span>
                {t(
                  "For non standard callbacks, any updates or changes to your configuration or " +
                    "advanced filtering of messages can be done with the help of FourKites. This is 3rd Party enabled Callbacks."
                )}
              </p>
              <p>
                {t(
                  "Contact FourKites <NAME_EMAIL> for more information."
                )}
              </p>
            </div>
          }
        >
          <span className={styles.actionsConatiner}>
            <InfoIcon fill="#0e65e5" iconClass={"button-icon-left"} />
          </span>
        </Tooltip>
      )}
    </>
  );
};

export default WebhookEditAndDeleteAction;
