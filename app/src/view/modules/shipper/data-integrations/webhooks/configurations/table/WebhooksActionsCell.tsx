import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import { Switch } from "@fourkites/elemental-switch";
import { Modal } from "@fourkites/elemental-modal";

import { useAppSelector } from "state/hooks";
import { WebhookConfigurationState } from "state/modules/shipper/WebhookConfigurations";

import WebhookEditAndDeleteAction from "./WebhookEditAndDeleteAction";

import styles from "./WebhooksListTable.module.scss";

const WebhooksActionsCell = ({
  enabled,
  rowId,
  isStandardCallback,
  onEditWebhookConfiguration,
  onDeleteWebhookConfiguration,
  onEnableDisableWebhookConfiguration,
}: any) => {
  const { t } = useTranslation();
  const webhooks = useAppSelector(
    WebhookConfigurationState.selectors.webhookConfigurations()
  );

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [showConfirmDisableWebhookModal, setShowConfirmDisableWebhookModal] =
    useState<boolean>(false);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  /*
   * Enable/Disable webhook configuration
   */
  const enableOrDisableWebhook = (status: boolean) => {
    if (status) {
      onEnableDisableWebhookConfiguration(status, webhooks[rowId]);
    } else {
      setShowConfirmDisableWebhookModal(true);
    }
  };

  /*
   * On click ok on disable webhook confirmation modal
   */
  const onSave = () => {
    onEnableDisableWebhookConfiguration(false, webhooks[rowId]);
  };

  /*
   * On close modal
   */
  const onCancel = () => {
    setShowConfirmDisableWebhookModal(false);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <>
      <div className={styles.actionsConatiner}>
        <Switch
          size="large"
          defaultLabel={enabled ? t("Enabled") : t("Disabled")}
          checked={enabled}
          disabled={!isStandardCallback}
          className={styles.webhookEnabledSwitch}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            enableOrDisableWebhook(!enabled);
          }}
        />
        <WebhookEditAndDeleteAction
          rowId={rowId}
          isStandardCallback={isStandardCallback}
          onEditWebhookConfiguration={onEditWebhookConfiguration}
          onDeleteWebhookConfiguration={onDeleteWebhookConfiguration}
        />
      </div>
      <ConfirmDisableWebhookModal
        showConfirmDisableWebhookModal={showConfirmDisableWebhookModal}
        onSave={onSave}
        onCancel={onCancel}
      />
    </>
  );
};

const ConfirmDisableWebhookModal = ({
  showConfirmDisableWebhookModal,
  onSave,
  onCancel,
}: any) => {
  const { t } = useTranslation();

  return (
    <Modal
      size="small"
      title={t("Are you sure you want to disable this webhook configuration?")}
      show={showConfirmDisableWebhookModal}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: onCancel,
      }}
      saveButtonProps={{
        label: t("Ok"),
        onClick: onSave,
      }}
    >
      <div
        className={styles.modalContainer}
        data-test-id="webhooks-configuration-disbale-info-alert"
      >
        <label>
          {t(
            "All messages currently subscribed will stop sending immediately once you disable a configuration!"
          )}
        </label>
        <p>{t("You can enable configuration anytime to resume messages")}</p>
      </div>
    </Modal>
  );
};

export default WebhooksActionsCell;
