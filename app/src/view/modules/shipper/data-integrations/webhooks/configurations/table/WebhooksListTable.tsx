import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { Table } from "@fourkites/elemental-table";

import { getColumns } from "./WebhooksListTableColumns";
import EmptyWebhooks from "./EmptyWebhooks";

import styles from "./WebhooksListTable.module.scss";

const WebhooksListTable = ({
  webhooks,
  onEditWebhookConfiguration,
  onDeleteWebhookConfiguration,
  onCreateWebhookConfiguration,
  onEnableDisableWebhookConfiguration,
}: any) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * DATA
   ****************************************************************************/

  const isUserNamePresent = (userObj: any) => {
    return userObj?.first_name && userObj?.last_name;
  };

  const getUserFullName = (userObj: any) => {
    return `${userObj?.first_name}  ${userObj?.last_name}`;
  };

  const getLastModifiedUserName = (webhook: any) => {
    let lastModifiedUserName = null;
    // If last modified by not present, use created by details - PROD Requirement
    const lastModifiedByDetails = webhook?.updated_by;
    const createdByDetails = webhook?.created_by;

    if (isUserNamePresent(lastModifiedByDetails)) {
      lastModifiedUserName = getUserFullName(lastModifiedByDetails);
    } else if (isUserNamePresent(createdByDetails)) {
      lastModifiedUserName = getUserFullName(createdByDetails);
    }

    return lastModifiedUserName;
  };

  const columns = useMemo(() => {
    return getColumns(
      t,
      onEditWebhookConfiguration,
      onDeleteWebhookConfiguration,
      onEnableDisableWebhookConfiguration
    );
  }, [
    t,
    onEditWebhookConfiguration,
    onDeleteWebhookConfiguration,
    onEnableDisableWebhookConfiguration,
  ]);

  const data = useMemo(() => {
    return webhooks?.map((webhook: any, index: number) => {
      return {
        id: webhook?.id,
        name: webhook?.name,
        type: webhook?.type,
        subscribedEvents: webhook?.subscribed_events,
        // If last modified at not present, use created at time - PROD Requirement
        lastModifiedAt: webhook?.updated_at || webhook?.created_at,
        lastModifiedBy: getLastModifiedUserName(webhook),
        isActive: webhook?.is_active,
        isEnabled: webhook?.is_enabled,
        isStandard: webhook?.is_standard,
        index: index,
      };
    });
  }, [webhooks]);

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const paginationParams = {
    paginated: true,
  };

  return (
    <>
      {data.length ? (
        <div
          className={styles.tableContainer}
          data-test-id="webhooks-list-table-container"
        >
          <Table
            rowHeight="small"
            variant="flat-bordered"
            striped
            data={data}
            columns={columns}
            pagination={paginationParams}
          />
        </div>
      ) : (
        <EmptyWebhooks
          onCreateWebhookConfiguration={onCreateWebhookConfiguration}
        />
      )}
    </>
  );
};

export default WebhooksListTable;
