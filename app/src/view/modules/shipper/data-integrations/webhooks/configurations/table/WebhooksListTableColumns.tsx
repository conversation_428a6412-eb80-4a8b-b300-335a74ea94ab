import React from "react";

import LastModifiedDetail from "./LastModifiedDetail";
import WebhooksActionsCell from "./WebhooksActionsCell";
import { CONFIG_TYPE_NAMES } from "view/modules/shipper/data-integrations/webhooks/configurations/modals/WebhookFormUtils";

export const getColumns = (
  t: Function,
  onEditWebhookConfiguration: Function,
  onDeleteWebhookConfiguration: Function,
  onEnableDisableWebhookConfiguration: Function
) => {
  return [
    {
      Header: t("Config Name"),
      accessor: "name",
      id: "name",
      disableSortBy: true,
      Cell: (cellProps: any) => {
        let configName = cellProps?.cell?.value;
        configName = configName ? configName : t("NA");
        return <span>{configName}</span>;
      },
    },
    {
      Header: t("Type"),
      accessor: "type",
      id: "type",
      disableSortBy: true,
      Cell: (cellProps: any) => {
        const configType = cellProps?.cell?.value;
        const configName = CONFIG_TYPE_NAMES[configType] || "--";
        return <span>{configName}</span>;
      },
    },
    {
      Header: t("Subscribed Events"),
      accessor: "subscribedEvents",
      id: "subscribedEvents",
      disableSortBy: true,
      Cell: (cellProps: any) => {
        const eventsSubscribed = cellProps?.cell?.value;
        const subscribedEventsCount = eventsSubscribed?.length;
        const totalEvents =
          subscribedEventsCount > 1
            ? `${subscribedEventsCount} events`
            : `${subscribedEventsCount} event`;
        return <span>{totalEvents}</span>;
      },
    },
    {
      Header: t("Last Modified"),
      accessor: (d: any) => ({
        lastModifiedAt: d.lastModifiedAt,
        lastModifiedBy: d.lastModifiedBy,
      }),
      disableSortBy: true,
      Cell: (cellProps: any) => {
        const lastModifiedAt = cellProps?.cell?.value?.lastModifiedAt;
        const lastModifiedBy = cellProps?.cell?.value?.lastModifiedBy;
        return (
          <LastModifiedDetail
            lastModifiedAt={lastModifiedAt}
            lastModifiedBy={lastModifiedBy}
          />
        );
      },
    },
    {
      Header: t("Actions"),
      accessor: (d: any) => ({
        isEnabled: d.isEnabled,
        isStandard: d.isStandard,
      }),
      Cell: (cellProps: any) => {
        const enabled = cellProps?.cell?.value?.isEnabled;
        const rowId = cellProps?.cell?.row?.id;
        const isStandardCallback = cellProps?.cell?.value?.isStandard;
        return (
          <WebhooksActionsCell
            enabled={enabled}
            rowId={rowId}
            isStandardCallback={isStandardCallback}
            onEditWebhookConfiguration={onEditWebhookConfiguration}
            onDeleteWebhookConfiguration={onDeleteWebhookConfiguration}
            onEnableDisableWebhookConfiguration={
              onEnableDisableWebhookConfiguration
            }
          />
        );
      },
    },
  ];
};
