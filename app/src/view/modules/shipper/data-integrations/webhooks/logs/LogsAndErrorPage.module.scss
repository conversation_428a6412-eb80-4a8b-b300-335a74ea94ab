@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container {
  > div > div {
    height: calc(100vh + -113px);
    padding: 0;
    background-color: white;
  }
}

.subHeading {
  font-size: 16px;
  color: $color-neutral-700;
}

.loader {
  width: 100%;
  display: flex;
  align-content: center;
  justify-content: center;
  margin-top: 32px;
  margin-bottom: 32px;
}

.headerWrapper {
  padding: 27px 32px 32px 32px;
  background-color: $color-neutral-50;
}

.headerButton {
  display: flex;
  > button {
    display: flex;
    align-items: center;
    align-content: center;
    min-width: fit-content;
    margin-left: 16px;
  }

  > a {
    margin-left: 16px;
  }
}

.headerLoader {
  composes: loader;
  width: fit-content;
  margin: 0;
  margin-right: 16px;

  > label {
    margin-right: 16px;
  }
}

.filtersWrapper {
  padding: 0 32px 32px 32px;
  background-color: $color-neutral-50;
}

.tableContainer {
  width: 100%;
  height: calc(100vh + -300px);

  > div > div {
    overflow-x: auto;
    overflow-y: auto;

    > table {
      > tbody > tr > td {
        padding-top: 9px;
        padding-bottom: 8px;
      }
    }
  }
}

.loading {
  display: flex;
  width: 100%;
  margin-top: 32px;
  justify-content: center;
}

.emptyDataContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 10%;
  height: calc(100vh - 195px);

  img {
    object-fit: contain;
    height: 150px;
    width: 150px;
    margin-right: 16px;
  }

  h2 {
    display: flex;
    align-items: center;
    font-size: 28px;
    font-weight: 300;
    letter-spacing: 0;
    text-align: center;
    margin-bottom: 5px;
  }

  h6 {
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    text-align: center;
    color: #868e96;
    margin-top: 0;
    margin-bottom: 30px;
  }

  .createConfigurationIcon {
    position: relative;
    top: 2px;
    margin-right: 16px;
  }
}
