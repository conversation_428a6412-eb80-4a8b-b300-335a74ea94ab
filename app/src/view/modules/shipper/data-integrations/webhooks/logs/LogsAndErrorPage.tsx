import React, { useState, useEffect, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { useHistory } from "react-router";
import { DateTime } from "luxon";

import { Spinner } from "@fourkites/elemental-loading-indicator";
import { Button, ExternalLinkIcon, PlusIcon } from "@fourkites/elemental-atoms";

import emptyStateLogo from "assets/img/emptyBox.png";

import { dataIntegrationRoutes } from "router/shipper/ShipperDataIntegrationsRouter";
import { onWebhooksLogsKnowledgeBaseClick } from "router/navigationUtils";

import { PaginationType } from "state/BaseTypes";
import { useAppSelector, useAppDispatch } from "state/hooks";
import { UsersState } from "state/modules/Users";
import { WebhooksDataState } from "state/modules/shipper/WebhooksData";

import { convertDateToUtcDate } from "view/components/base/DateUtils";
import { showToast } from "view/components/base/toast/Toast";
import SubPagePanel from "view/components/base/containers/SubPagePanel";
import BreadcrumbsHeader from "view/components/base/breadcrumbs-header/BreadcrumbsHeader";

import LogsFilterComponent from "./filters/LogsFilterComponent";
import LogsErrorTable from "./table/LogsAndErrorTable";
import NoWebhooksLicensePage from "view/modules/shipper/data-integrations/webhooks/configurations/NoWebhooksLicensePage";
import SearchFilter from "./filters/search/SearchFilterComponent";

import styles from "./LogsAndErrorPage.module.scss";

const DEFAULT_PAGINATION_PARAMETERS = { pageSize: 10, pageIndex: 0 };

const LogsErrorPage = () => {
  const { t } = useTranslation();
  const DEFAULT_HOUR_LIMIT = 2;
  const DATE_TIME_FORMAT = "yyyy-LL-dd HH:mm";
  let startDate: any = new Date();
  startDate.setHours(startDate.getHours() - DEFAULT_HOUR_LIMIT);
  const startDateInUtc = convertDateToUtcDate(startDate);
  const endDate = new Date();
  const endDateInUtc = convertDateToUtcDate(endDate);

  /*****************************************************************************
   * REDUX
   ****************************************************************************/
  const dispatch = useAppDispatch();
  const shipperId: string = useAppSelector(UsersState.selectors.getCompanyId);
  const webhooksData = useAppSelector(
    WebhooksDataState.selectors.webhooksData()
  );
  const eventsData = useAppSelector(WebhooksDataState.selectors.eventsData());

  const isRetrieving = useAppSelector(
    WebhooksDataState.selectors.isRetrievingWebhooksData()
  );

  const hasErrorInRetrievingWebhooks = useAppSelector(
    WebhooksDataState.selectors.hasError()
  );

  const companyLicenses: string = useAppSelector(
    UsersState.selectors.getCompanyLicenses
  );
  const hasWebhooksLicense = companyLicenses.indexOf("callbacks") !== -1;

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [searchValue, setSearchValue] = useState("");
  const [eventOptions, setEventOptions] = useState<any>({});
  const [events, setEvents] = useState<any>([]);
  const [statusType, setStatusType] = useState<any>(["success", "error"]);
  const [processedTimeframe, setProcessedTimeframe] = useState<any>({
    start: { type: "calendar", dateTime: startDateInUtc },
    end: { type: "calendar", dateTime: endDateInUtc },
  });

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  /*
   * Fetch events
   */
  const fetchEvents = async () => {
    if (!shipperId) {
      return;
    }

    const response = await dispatch(
      WebhooksDataState.actions.retrieveCallbackEvents({
        shipperId,
      })
    );
    return response;
  };

  /*
   * Fetch the initial webhooks data on launch
   */
  const fetchInitialData = async () => {
    const response: any = await fetchEvents();
    const eventTypes = response?.payload?.event_types;

    setEvents(eventTypes);
    setMessageTypes(eventTypes);

    fetchInitialWebhooksData(DEFAULT_PAGINATION_PARAMETERS, eventTypes);
  };

  const setMessageTypes = (eventsData: any) => {
    const eventTypeOptions = eventsData.reduce(
      (eventTypesArray: any, value: any) => ({
        ...eventTypesArray,
        [value]: value,
      }),
      {}
    );
    setEventOptions(eventTypeOptions);
  };

  /*
   * Fetch webhooks
   */
  const fetchWebhooks = async (
    pageIndex: number,
    pageSize: number,
    eventTypes: any
  ) => {
    const processedFrom = DateTime.fromJSDate(
      processedTimeframe?.start?.dateTime
    ).toFormat(DATE_TIME_FORMAT);

    const processedTo = DateTime.fromJSDate(
      processedTimeframe?.end?.dateTime
    ).toFormat(DATE_TIME_FORMAT);

    const response = await dispatch(
      WebhooksDataState.actions.retrieveWebhooksData({
        eventTypes,
        statusType,
        shipperId,
        searchValue,
        currentPage: pageIndex + 1,
        pageSize: pageSize,
        processedFrom: processedFrom,
        processedTo: processedTo,
      })
    );
    if ("error" in response) {
      showToast(
        t("An error occurred"),
        t("There was an error retrieving the webhooks."),
        "error"
      );
      return;
    }
  };

  /*
   * Function fetchInitialWebhooksData would be used for the very first time
   * Since the setEvents is working only on next render
   * Kept 2 functions one with events as args and other without events (fetchWebhooksData)
   */
  const fetchInitialWebhooksData = useCallback(
    async ({ pageSize, pageIndex }: PaginationType, eventTypes: any) => {
      if (!shipperId || eventTypes?.length === 0) {
        return;
      }

      await fetchWebhooks(pageIndex, pageSize, eventTypes);
    },
    [shipperId, events, statusType, processedTimeframe, searchValue]
  );

  /*
   * Fetch webhooks data
   */
  const fetchWebhooksData = useCallback(
    async ({ pageSize, pageIndex }: PaginationType) => {
      if (!shipperId || events?.length === 0) {
        return;
      }

      await fetchWebhooks(pageIndex, pageSize, events);
    },
    [shipperId, events, statusType, processedTimeframe, searchValue]
  );

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  // Fetch events
  useEffect(() => {
    const fetchData = async () => {
      await fetchInitialData();
    };
    fetchData();
  }, [shipperId]);

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  if (!hasWebhooksLicense) {
    return <NoWebhooksLicensePage />;
  }

  const noEventsData = eventsData?.length === 0;

  if (noEventsData) {
    return <EmptyPage />;
  }

  const HeaderButtons = ({}: any) => {
    const { t } = useTranslation();
    return (
      <>
        <div className={styles.headerButton}>
          <Button
            size={"large"}
            onClick={onWebhooksLogsKnowledgeBaseClick}
            variant="outline"
            theme="tertiary"
            data-testid="btn-callback-kb"
          >
            <ExternalLinkIcon fill="#0e65e5" iconClass={"button-icon-left"} />
            {t("Go to Documentation")}
          </Button>
        </div>
      </>
    );
  };

  const breadcrumbTitles = [t("Webhook Logs & Errors")];

  const headerItems = [
    isRetrieving ? (
      <div className={styles.headerLoader}>
        <Spinner isLoading size="small" />
      </div>
    ) : null,
    <SearchFilter onChange={(value: string) => setSearchValue(value)}/>,
    <HeaderButtons />,
  ];

  const logsAndErrorsContent = (
    <>
      <div className={styles.filtersWrapper}>
        <LogsFilterComponent
          events={events}
          setEvents={setEvents}
          eventOptions={eventOptions}
          statusType={statusType}
          setStatusType={setStatusType}
          processedTimeframe={processedTimeframe}
          setProcessedTimeframe={setProcessedTimeframe}
          fetchWebhooks={fetchWebhooksData}
        />
      </div>

      {isRetrieving && (
        <div className={styles.loading}>
          <Spinner isLoading size="medium" />
        </div>
      )}

      {hasErrorInRetrievingWebhooks ? (
        <ErrorPage />
      ) : (
        <LogsErrorTable
          webhooksData={webhooksData}
          fetchWebhooksData={fetchWebhooksData}
        />
      )}
    </>
  );

  return (
    <div
      className={styles.container}
      data-test-id="webhooks-logs-errors-page-container"
    >
      <SubPagePanel>
        <div className={styles.headerWrapper}>
          <BreadcrumbsHeader titles={breadcrumbTitles} children={headerItems} />
          <label className={styles.subHeading}>
            {t(
              "View all your logs for existing webhooks and retrigger failed ones"
            )}
          </label>
        </div>
        {logsAndErrorsContent}
      </SubPagePanel>
    </div>
  );
};

const EmptyPage = () => {
  const { t } = useTranslation();
  const history = useHistory();

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  /**
   * Redirect to customers page from Overview
   */
  const redirectToWebhookConfigurationsPage = () => {
    history.push(`${dataIntegrationRoutes.webhooks}`);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div className={styles.emptyDataContainer}>
      <img className={styles.emptyStateLogo} src={emptyStateLogo} />
      <h2>{t("No data!")}</h2>
      <h6>{t("Click on create configuration to start.")}</h6>
      <Button size={"large"} onClick={redirectToWebhookConfigurationsPage}>
        <PlusIcon fill="#fff" iconClass={styles.createConfigurationIcon} />
        {t("Create Configuration")}
      </Button>
    </div>
  );
};

const ErrorPage = () => {
  const { t } = useTranslation();
  const history = useHistory();

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div className={styles.emptyDataContainer}>
      <img className={styles.emptyStateLogo} src={emptyStateLogo} />
      <h2>{t("Something went wrong.!")}</h2>
      <h6>{t("Error in fetching webhooks.")}</h6>
    </div>
  );
};
export default LogsErrorPage;
