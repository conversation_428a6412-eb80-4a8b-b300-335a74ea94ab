.container {
  width: 100%;
  display: flex;

  .content {
    display: flex;
    flex-direction: row;
    width: 100%;

    .filter {
      display: flex;
      margin-right: 2%;
      flex-direction: column;
      width: 15%;
    }

    .filterButton {
      display: flex;
      margin-right: 0;
      flex-direction: column-reverse;
    }

    .eventsFilter {
      width: 29%;
      display: flex;
      margin-right: 2%;
      flex-direction: column;
    }
  }
}

.dateRangeFilter {
  display: flex;
  position: relative;
  top: 1px;
  width: 370px;

  > div > div > div > div {
    height: 37px;
  }

  span {
    font-size: 13px;
  }

  .timeframeInfo {
    position: relative;
    left: -255px;
    top: 3px;
    font-size: 14px;
  }
}

.logsFilter {
  label {
    font-size: 14px;
  }
}

.buttonIcon {
  margin-right: 8px;
  position: relative;
  top: 2px;
}

.infoMessage {
  display: inline-flex;
  align-items: center;
  position: relative;
  top: 2px;
  cursor: pointer;
}
