import { useState } from "react";
import { useTranslation } from "react-i18next";

import { Select } from "@fourkites/elemental-select";
import { Button, InfoIcon, RotateCwIcon } from "@fourkites/elemental-atoms";
import { DateRangePicker } from "@fourkites/elemental-datepicker";
import { Modal } from "@fourkites/elemental-modal";
import { Tooltip } from "@fourkites/elemental-tooltip";

import { useAppSelector } from "state/hooks";
import { WebhooksDataState } from "state/modules/shipper/WebhooksData";

import {
  getFieldKeysForMultiSelect,
  getFieldValuesForMultiSelect,
} from "view/modules/shipper/data-integrations/webhooks/configurations/modals/WebhookFormUtils";
import { convertDateToUtcDate } from "view/components/base/DateUtils";

import LogsFilterComponentProps from "./LogsFilterComponent.types";

import styles from "./LogsFilterComponent.module.scss";

const LogsFilterComponent = ({
  events,
  setEvents,
  eventOptions,
  statusType,
  setStatusType,
  processedTimeframe,
  setProcessedTimeframe,
  fetchWebhooks,
}: LogsFilterComponentProps) => {
  const { t } = useTranslation();

  const MAX_RANGE_ALLOWED = 30;

  /*****************************************************************************
   * REDUX
   ****************************************************************************/

  // Pagination
  const pagination = useAppSelector(
    WebhooksDataState.selectors.paginationDetails()
  );
  const DEFAULT_PAGINATION_PARAMETERS = {
    pageSize: pagination?.pageSize,
    pageIndex: 0,
  };

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  /*
   * Get min and max default dates
   */
  const getMinMaxDefaultDates = () => {
    let minStartDate = new Date();
    minStartDate.setDate(minStartDate.getDate() - MAX_RANGE_ALLOWED);
    minStartDate.setHours(0o0, 0o0, 0o0, 0o0);
    let maxEndDate = new Date();
    maxEndDate.setHours(23, 59, 59, 999);
    return { minStartDate, maxEndDate };
  };

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <EventsFilter
          eventOptions={eventOptions}
          events={events}
          setEvents={setEvents}
        />

        <StatusFilter statusType={statusType} setStatusType={setStatusType} />

        <TimeframeFilter
          processedTimeframe={processedTimeframe}
          setProcessedTimeframe={setProcessedTimeframe}
          getMinMaxDefaultDates={getMinMaxDefaultDates}
        />

        <div className={styles.filterButton}>
          <Button
            size={"large"}
            variant="outline"
            onClick={() => fetchWebhooks(DEFAULT_PAGINATION_PARAMETERS)}
          >
            <RotateCwIcon fill="#0E65E5" iconClass={"buttonIcon"} />
            {t("Fetch Data")}
          </Button>
        </div>
      </div>
    </div>
  );
};

// EventsFilter
const EventsFilter = ({ eventOptions, events, setEvents }: any) => {
  const { t } = useTranslation();
  const MESSAGE_TYPES_VALUES: any = Object.values(eventOptions);

  return (
    <div className={styles.eventsFilter}>
      <Select
        label={t("Events")}
        onChange={(selectedOptions: any) => {
          const data = getFieldKeysForMultiSelect(
            selectedOptions,
            eventOptions
          );

          setEvents(data);
        }}
        options={MESSAGE_TYPES_VALUES}
        placeHolder={t("Select a configuration type")}
        select={"multiple"}
        value={getFieldValuesForMultiSelect(events, eventOptions)}
        className={styles.logsFilter}
        size={"large"}
      />
    </div>
  );
};

// StatusFilter
const StatusFilter = ({ statusType, setStatusType }: any) => {
  const { t } = useTranslation();

  return (
    <div className={styles.filter}>
      <Select
        label={t("Status")}
        onChange={(selectedOptions: any) => {
          const data = getFieldKeysForMultiSelect(
            selectedOptions,
            STATUS_TYPES
          );

          setStatusType(data);
        }}
        options={STATUS_TYPES_OPTIONS}
        placeHolder={t("Select a Status")}
        select={"multiple"}
        value={getFieldValuesForMultiSelect(statusType, STATUS_TYPES)}
        className={styles.logsFilter}
        size={"large"}
      />
    </div>
  );
};

// TimeframeFilter
const TimeframeFilter = ({
  processedTimeframe,
  setProcessedTimeframe,
  getMinMaxDefaultDates,
}: any) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * STATE
   ****************************************************************************/
  const [showInvalidTimeframeAlertModal, setShowInvalidTimeframeAlertModal] =
    useState<boolean>(false);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  /*
   * On date range changed
   */
  const onTimeframeChanged = (range: any) => {
    if (range?.start?.type == "offset") {
      const startDate = range?.start?.dateTime;
      let selectedStartDateString: any = new Date(startDate).toDateString();
      selectedStartDateString = new Date(selectedStartDateString);
      selectedStartDateString.setDate(selectedStartDateString.getDate() + range?.start?.offset);
      selectedStartDateString.setHours(0, 0, 0, 0);
      range.start.dateTime = selectedStartDateString
    } else if (range?.start?.type == "time_offset") {
      range.start.dateTime = convertDateToUtcDate(range?.start?.dateTime)
      range.end.dateTime = convertDateToUtcDate(range?.end?.dateTime)
    }
    const isInvalidRangeSelected = checkIsInvalidTimeframe(range);

    if (isInvalidRangeSelected) {
      setShowInvalidTimeframeAlertModal(true);
      return;
    }

    setProcessedTimeframe(range);
  };

  /*
   * Validate date range selected
   */
  const checkIsInvalidTimeframe = (range: any) => {
    const startDate = range?.start?.dateTime;
    const endDate = range?.end?.dateTime;
    const MAX_DAYS_ALLOWED = 7;
    let isTimeframeInvalid = false;

    if (!startDate || !endDate) {
      isTimeframeInvalid = true;
      return isTimeframeInvalid;
    }

    let selectedStartDateString: any = new Date(startDate).toDateString();
    selectedStartDateString = new Date(selectedStartDateString);
    let selectedEndDateString: any = new Date(endDate).toDateString();
    let maxRange = new Date(endDate)
    maxRange.setDate(maxRange.getDate() - MAX_DAYS_ALLOWED);
    maxRange.setHours(0, 0, 0, 0);
    selectedEndDateString = new Date(selectedEndDateString);

    if (
      selectedStartDateString < MIN_START_DATE ||
      selectedStartDateString > MAX_END_DATE ||
      selectedEndDateString < MIN_START_DATE ||
      selectedEndDateString > MAX_END_DATE ||
      selectedStartDateString < maxRange
    ) {
      isTimeframeInvalid = true;
      return isTimeframeInvalid;
    }

    return isTimeframeInvalid;
  };

  const relativeRangeButtons=[
          {
            startOffset: -30,
            endOffset: 0,
            id: "last_30_minutes",
            label: t("Last 30 minutes"),
            type: "time"
          },
          {
            startOffset: -60,
            endOffset: 0,
            id: "last_60_minutes",
            label: t("Last 60 minutes"),
            type: "time"
          },
          {
            startOffset: -360,
            endOffset: 0,
            id: "last_6_hours",
            label: t("Last 6 hours"),
            type: "time"
          },
          {
            startOffset: -3,
            endOffset: 0,
            id: "last_3_days",
            label: t("Last 3 days"),
            type: "date"
          },
          {
            startOffset: -7,
            endOffset: 0,
            id: "last_7_days",
            label: t("Last 7 days"),
            type: "date"
          },
        ]

  /*
   * Close timeframe alert
   */
  const closeTimeframeAlert = () => {
    setShowInvalidTimeframeAlertModal(false);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const { minStartDate, maxEndDate } = getMinMaxDefaultDates();
  const MIN_START_DATE = minStartDate,
    MAX_END_DATE = maxEndDate;

  return (
    <>
      <div className={styles.dateRangeFilter}>
        <DateRangePicker
          label={"Timeframe"}
          relativeRangeButtons={relativeRangeButtons}
          cancelButtonLabel={"Cancel"}
          saveButtonLabel={"Apply"}
          currentRange={processedTimeframe}
          onRangeChange={(range: any) => onTimeframeChanged(range)}
          timeEnabled={true}
          singleDayRange={false}
          dateFormat={"yyyy-LL-dd"}
          timeFormat={"HH:mm"}
          calendarPosition={{ top: "250px", left: "420px" }}
          calendarZIndex={1000}
          className={"timeframe"}
          disableFutureDates={true}
        />
        <Tooltip
          contentComponent={
            <div>
              {t(
                "Logs / errors support  data for any 7 days in last 30 days only, " +
                  "please contact customer support for more historic data  beyond 30 days. "
              )}
              <p>
                {t("All Timestamps shown below are converted to UTC timezone.")}
              </p>
            </div>
          }
        >
          <span className={styles.timeframeInfo}>
            <InfoIcon fill="#0e65e5" iconClass={"button-icon-left"} />
          </span>
        </Tooltip>
      </div>
      <InvalidTimeframeAlertModal
        showInvalidTimeframeAlertModal={showInvalidTimeframeAlertModal}
        closeTimeframeAlert={closeTimeframeAlert}
      />
    </>
  );
};

// InvalidTimeframeAlertModal
const InvalidTimeframeAlertModal = ({
  showInvalidTimeframeAlertModal,
  closeTimeframeAlert,
}: any) => {
  const { t } = useTranslation();

  return (
    <Modal
      size="small"
      title={t("Timeframe not supported")}
      show={showInvalidTimeframeAlertModal}
      closeButtonProps={{
        label: t("Cancel"),
        onClick: closeTimeframeAlert,
      }}
      saveButtonProps={{
        label: t("Ok"),
        onClick: closeTimeframeAlert,
      }}
    >
      <div
        className={styles.modalContainer}
        data-test-id="webhooks-timeframe-alert"
      >
        <label>
          <Tooltip
            text={t(
              "This console  supports logs / errors only for last 7 days"
            )}
          >
            <span className={styles.infoMessage}>
              <InfoIcon fill="#0e65e5" iconClass={"button-icon-left"} />
            </span>
          </Tooltip>
          {t(
            "Logs / errors support  data for any 7 days in last 30 days only, " +
              "please contact customer support for more historic data  beyond 30 days"
          )}
        </label>
      </div>
    </Modal>
  );
};

export const STATUS_TYPES = {
  success: "Success",
  error: "Error",
};

const STATUS_TYPES_OPTIONS = Object.values(STATUS_TYPES);

export default LogsFilterComponent;
