import React, { useState } from 'react';
import { useTranslation } from "react-i18next";

import { Input } from '@fourkites/elemental-input';

import SearchFilterProps from './SearchFilterComponent.types';

const SearchFilter = (props: SearchFilterProps) => {
    const { t } = useTranslation();
    const [searchValue, setSearchValue] = useState('');
    const onChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      setSearchValue(event.target.value);
      props.onChange(event.target.value);
    };
  
    return (
      <div>
        <Input
          label={""}
          hideLabel={true}
          placeholder={t("Search By Load #")}
          value={searchValue}
          onChange={onChange}
          size="large"
        />      
      </div>
    );
};

export default SearchFilter;