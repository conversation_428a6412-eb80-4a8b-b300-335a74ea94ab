@import "@fourkites/elemental-atoms/build/scss/colors/index";

.tableContainer {
  width: 100%;
  height: calc(100vh + -380px);

  > div > div {
    overflow-x: auto;
    overflow-y: auto;

    > table {
      > tbody > tr > td {
        padding-top: 9px;
        padding-bottom: 8px;
      }
    }
  }
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 10%;

  img {
    object-fit: contain;
    height: 150px;
    width: 150px;
    margin-right: 16px;
  }

  h2 {
    display: flex;
    align-items: center;
    font-size: 28px;
    font-weight: 300;
    letter-spacing: 0;
    text-align: center;
    margin-bottom: 5px;
  }

  h6 {
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    text-align: center;
    color: $color-neutral-600;
    margin-top: 0;
    margin-bottom: 30px;
  }
}
