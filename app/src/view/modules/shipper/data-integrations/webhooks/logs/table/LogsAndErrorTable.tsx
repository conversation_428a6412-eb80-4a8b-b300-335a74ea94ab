import React, { useState, useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";

import { Table } from "@fourkites/elemental-table";
import { AlertTriangleIcon, CheckIcon } from "@fourkites/elemental-atoms";

import passwordError from "assets/img/passwordError.png";

import { useAppSelector } from "state/hooks";
import { WebhooksDataState } from "state/modules/shipper/WebhooksData";

import TableWithSidePanel from "view/components/base/containers/TableWithSidePanel";

import getWebhookListColumns from "./WebhookTableColumns";
import WebhookDetails from "./WebhookDetails";

import styles from "./LogsAndErrorTable.module.scss";

const LogsErrorTable = ({ webhooksData, fetchWebhooksData }: any) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * REDUX
   ****************************************************************************/

  // Pagination
  const pagination = useAppSelector(
    WebhooksDataState.selectors.paginationDetails()
  );
  const { totalItems, totalPages } = pagination;

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  /*
   * Function to get the clicked row id and index
   */
  const onRowClicked = (rowInfo: any) => {
    const selectedRowId = rowInfo?.row?.id;
    const selectedRowIndex = rowInfo?.row?.index;

    if (selectedRowId !== clickedRowId) {
      setClickedRowId(selectedRowId);
      setClickedRowIndex(selectedRowIndex);
    } else {
      setClickedRowId(null);
      setClickedRowIndex(null);
    }
  };

  /*
   * On close side panel
   */
  const onClosePanel = () => {
    setClickedRowId(null);
    setClickedRowIndex(null);
  };

  /*
   * Fetch webhook status details
   */
  const fetchWebhookStatusDetails = (statusCode: any) => {
    const successStatusCodes = statusCode >= 200 && statusCode <= 299;
    const validationErrorStatus = statusCode === 0;
    const progressStatus = statusCode === 1;
    const variant = successStatusCodes
      ? "ok"
      : validationErrorStatus || progressStatus
      ? "alert"
      : "error";
    const icon = successStatusCodes ? <CheckIcon /> : <AlertTriangleIcon />;
    const webhookStatusTooltip = successStatusCodes ? t("Success") : t("Error");
    const statusText = getStatusText(
      statusCode,
      successStatusCodes,
      validationErrorStatus
    );

    return { variant, statusCode, icon, webhookStatusTooltip, statusText };
  };

  /*
   * Fetch webhook status text
   */
  const getStatusText = (
    statusCode: any,
    successStatusCodes: boolean,
    validationErrorStatus: boolean
  ) => {
    if (successStatusCodes) {
      return t("Success");
    }

    if (validationErrorStatus) {
      return t("Validation Error");
    } else {
      return statusCodesMapping[statusCode] || t("Bad Request");
    }
  };

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [clickedRowId, setClickedRowId] = useState<any>(null);
  const [clickedRowIndex, setClickedRowIndex] = useState<any>(null);
  const [clickedWebhook, setClickedWebhook] = useState<any>(null);

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  // To show the side panel
  useEffect(() => {
    setClickedWebhook(
      clickedRowIndex !== null ? webhooksData[clickedRowIndex] : null
    );
  }, [clickedRowIndex]);

  // To hide the side panel on data change
  useEffect(() => {
    setClickedRowIndex(null);
  }, [webhooksData]);

  /*****************************************************************************
   * RENDER
   ****************************************************************************/
  const webhookDataDetailsComponent = (
    <WebhookDetails
      webhookId={clickedWebhook?.id}
      webhookData={clickedWebhook}
      onClosePanel={onClosePanel}
      fetchWebhookStatusDetails={fetchWebhookStatusDetails}
    />
  );

  const sidePanel = clickedWebhook != null ? webhookDataDetailsComponent : null;
  const columns = useMemo(() => {
    return getWebhookListColumns(
      t,
      clickedWebhook != null,
      fetchWebhookStatusDetails
    );
  }, [clickedWebhook]);

  const preselectedRowIds =
    clickedRowIndex != null &&
    clickedRowId != null &&
    clickedRowIndex < webhooksData?.length
      ? [clickedRowId]
      : [];

  const paginationParams = {
    defaultPageSize: 10,
    pageCount: totalPages,
    fetchDataForPage: fetchWebhooksData,
    totalEntries: totalItems,
    enableManualSort: false,
    striped: true,
    paginated: true,
  };

  return (
    <div className={styles.webhookContent}>
      {webhooksData.length ? (
        <TableWithSidePanel
          table={
            <div className={styles.tableContainer}>
              <Table
                variant="flat-bordered"
                data={webhooksData}
                columns={columns}
                onRowClicked={onRowClicked}
                preselectedRowIds={preselectedRowIds}
                pagination={paginationParams}
              />
            </div>
          }
          sidePanel={sidePanel}
        />
      ) : (
        <EmptyWebhooks />
      )}
    </div>
  );
};

// EmptyWebhooks
const EmptyWebhooks = () => {
  const { t } = useTranslation();

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div className={styles.container}>
      <img className={styles.emptyStateLogo} src={passwordError} />
      <h2>{t("No data found!")}</h2>
      <h6>{t("Update your filters or timeframe to view results")}</h6>
    </div>
  );
};

// Status code mapping
const statusCodesMapping: any = {
  1:   "In Progress",
  404: "Resource not found",
  401: "Unauthorised",
  500: "Internal Server error",
  400: "Bad Request",
  410: "Resource not found",
  429: "Too many requests",
  503: "Service Unavailable",
};

export default LogsErrorTable;
