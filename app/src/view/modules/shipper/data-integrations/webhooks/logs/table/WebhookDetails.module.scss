@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container {
  display: flex;
  flex: 1;
  flex-direction: column;
  border-left: 1px solid $color-neutral-300;
  border-top: 1px solid $color-neutral-300;
  width: 100%;
  overflow-y: auto;
  height: calc(100vh + -325px);
  padding-bottom: 2px;
}

.wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
  width: 33vw;
  background-color: white;
  overflow-y: auto;
}

.loader {
  display: flex;
  align-items: left;
  align-content: center;
  justify-content: center;
  margin-top: 32px;
  margin-bottom: 32px;
}

.headerContainer,
.webhookDestinationUrl {
  display: flex;
  padding: 16px;
  flex-direction: column;
  border-bottom: 1px solid $color-neutral-400;

  &:last-child {
    border-bottom: none;
  }
}

.loadDetails {
  display: flex;
  flex-direction: column;
  width: 50%;
  margin-bottom: 15px;
  margin-right: 15px;

  span {
    color: $color-neutral-700;
    font-size: 14px;
    font-weight: 600;
  }

  h5 {
    margin: 0;
    color: $color-neutral-900;
    font-size: 20px;
    font-weight: 700;
    word-break: break-all;
  }
}

.webhookDetails {
  display: flex;
  flex-direction: row;
  width: 100%;
}

.detail {
  display: flex;
  flex-direction: column;
}

.detailTitle {
  color: $color-neutral-700;
  font-size: 14px;
  font-weight: 600;
}

.detailReasonTitle {
  color: $color-neutral-700;
  font-size: 14px;
  font-weight: 600;
  margin-top: 24px;
}

.detailContent {
  color: $color-neutral-900;
  font-size: 14px;
  font-weight: 400;
}

.headerRight {
  display: flex;
  align-content: center;
  align-items: baseline;
  margin-left: auto;
}

.closeButtonWrapper {
  background-color: transparent;
  border: none;
  cursor: pointer;
  border-radius: 4px;
  padding: 2px 4px 0;

  &:hover {
    background-color: $color-neutral-200;
  }
}

.closeIcon {
  height: 24px;
  width: 24px;
}

.payload {
  display: flex;
  flex-direction: column;
  margin-left: auto;
}

.jsonDataWrapper {
  display: block;
  width: 350px;
}

.jsonData {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 21px;
  color: $color-neutral-900;
}

.copyPayload {
  vertical-align: middle;
  cursor: pointer;
}

.payloadDetail {
  display: flex;
  flex-direction: column;
  max-width: 20%;
}

.createdOnContent {
  color: $color-neutral-900;
  font-size: 14px;
  font-weight: 400;
  line-height: 30px;
}

.webhookErrorReason {
  display: flex;
  padding: 16px;
  flex-direction: column;
  border-bottom: 1px solid $color-neutral-400;
  background: #f2b5b8;

  .detailTitle {
    color: $color-neutral-700;
    font-size: 14px;
    font-weight: 700;
    text-transform: uppercase;
  }

  .detailContent {
    color: $color-neutral-900;
    font-size: 14px;
    font-weight: 600;
  }
}

.errorReasonIcon {
  position: relative;
  top: 5px;
  margin-right: 5px;
}
