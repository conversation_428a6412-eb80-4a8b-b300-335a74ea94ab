import React from "react";
import { useTranslation } from "react-i18next";
import copyToClipboard from "copy-to-clipboard";
import <PERSON><PERSON><PERSON><PERSON>ty from "react-json-pretty";

import {
  CopyIcon,
  XCircleInvertedColoredIcon,
  XIcon,
  DownloadIcon,
  LinkButton,
} from "@fourkites/elemental-atoms";

import { convertDatetoUtcString } from "view/components/base/DateUtils";
import { showToast } from "view/components/base/toast/Toast";
import NewStatusTag from "view/components/base/new-status-indicators/NewStatusTag";

import styles from "./WebhookDetails.module.scss";

const WebhookDetails = ({
  webhookId,
  webhookData,
  onClosePanel,
  fetchWebhookStatusDetails,
}: any) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const webhookStatusDetail = fetchWebhookStatusDetails(
    JSON.parse(webhookData?.statusCode)
  );

  const statusCode = JSON.parse(webhookData?.statusCode);

  const iSuccessStatusCode =
    webhookData?.statusCode >= 200 && webhookData?.statusCode <= 299;

  const isProgressStatusCode = statusCode === 1;

  return (
    <div key={webhookId} className={styles.container}>
      <div
        data-test-id="webhook-details-wrapper"
        key={webhookId}
        className={styles.wrapper}
      >
        <WebhookDetailsHeader
          webhookData={webhookData}
          onClosePanel={onClosePanel}
        />

        <WebhookDestinationUrl webhookData={webhookData} />

        {!iSuccessStatusCode && !isProgressStatusCode && (
          <WebhookErrorReason webhookData={webhookData} />
        )}

        <WebhookJsonPayload
          webhookData={webhookData}
          webhookStatusDetail={webhookStatusDetail}
        />
      </div>
    </div>
  );
};

// WebhookDetailsHeader
const WebhookDetailsHeader = ({ webhookData, onClosePanel }: any) => {
  const { t } = useTranslation();

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/
  /*
   * Get webhook response
   */
  const getWebhookResponse = (statusCode: any) => {
    const iSuccessStatusCodes = statusCode >= 200 && statusCode <= 299;
    const validationErrorStatus = statusCode === 0;
    const progressStatus = statusCode === 1;
    const webhookResponseMessage = iSuccessStatusCodes
      ? t("Success")
      : progressStatus 
      ? t("InProgress")
      :validationErrorStatus
      ? t("Validation Error")
      : t("Error");
    return webhookResponseMessage;
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div className={styles.headerContainer}>
      <div className={styles.webhookDetails}>
        <div className={styles.loadDetails}>
          <span>{t("Load Number")}</span>
          <h5>{webhookData?.loadNumber}</h5>
        </div>
        <div className={styles.detail}>
          <span className={styles.detailTitle}>{t("Event Timestamp")}</span>
          <span className={styles.createdOnContent}>
            {convertDatetoUtcString(webhookData?.processedAt)}
          </span>
        </div>
        <div className={styles.headerRight}>
          <button className={styles.closeButtonWrapper} onClick={onClosePanel}>
            <XIcon iconClass={styles.closeIcon} />
          </button>
        </div>
      </div>
      <div className={styles.webhookDetails}>
        <div className={styles.detail}>
          <span className={styles.detailTitle}>{t("Response Message")}</span>
          <span className={styles.detailContent}>
            {getWebhookResponse(JSON.parse(webhookData?.statusCode))}
          </span>
        </div>
      </div>
    </div>
  );
};

// WebhookDestinationUrl
const WebhookDestinationUrl = ({ webhookData }: any) => {
  const { t } = useTranslation();
  return (
    <div className={styles.webhookDestinationUrl}>
      <div className={styles.webhookDetails}>
        <div className={styles.detail}>
          <span className={styles.detailTitle}>{t("Destination URL")}</span>
          <span className={styles.detailContent}>{webhookData?.updateUrl}</span>
        </div>
      </div>
    </div>
  );
};

// WebhookErrorReason
const WebhookErrorReason = ({ webhookData }: any) => {
  const { t } = useTranslation();

  return (
    <div className={styles.webhookErrorReason}>
      <div className={styles.webhookDetails}>
        <div className={styles.detail}>
          <span className={styles.detailTitle}>
            <span>
              <XCircleInvertedColoredIcon
                iconClass={styles.errorReasonIcon}
                size="22px"
              />
            </span>
            {t("Error!")}
          </span>
          <span className={styles.detailContent}>{webhookData?.reason}</span>
          {webhookData?.errorMessage && (
            <WebhookErrorReasonDownload webhookData={webhookData} />
          )}
        </div>
      </div>
    </div>
  );
};

const WebhookErrorReasonDownload = ({ webhookData }: any) => {
  const { t } = useTranslation();

  return (
    <div className={styles.detailReasonTitle}>
        <LinkButton
            variant="flat"
            href={webhookData?.errorMessage}
            target="_blank"
            theme="primary"
        >
        <DownloadIcon className="button-icon-left" fill="#0e65e5" />
          {t("Download Error")}
        </LinkButton>
    </div>
  );
};

// WebhookJsonPayload
const WebhookJsonPayload = ({ webhookData, webhookStatusDetail }: any) => {
  const { t } = useTranslation();
  const payload = webhookData?.payload;
  const { variant, statusCode, icon, statusText } = webhookStatusDetail;

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  /*
   * On copy payload
   */
  const onCopyPayload = async () => {
    await copyToClipboard(JSON.stringify(payload, null, 2));
    showToast(t("Payload copied to clipboard."), t(""), "ok");
  };

  /*
   * Get payload details
   */
  const getPayloadDetails = (webhookData: any) => {
    const computedPayload = payload ? (
      <div className={styles.jsonDataWrapper}>
        <JSONPretty className={styles.jsonData} data={payload}></JSONPretty>
      </div>
    ) : (
      "--"
    );
    return computedPayload;
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div className={styles.webhookDestinationUrl}>
      <div className={styles.webhookDetails}>
        <div className={styles.payloadDetail}>
          <span className={styles.detailTitle}>{t("Payload")}</span>
          <span className={styles.detailContent}>
            {getPayloadDetails(webhookData)}
          </span>
        </div>
        <div className={styles.payload}>
          <span className={styles.detailTitle}>
            <NewStatusTag
              variant={variant}
              statusCode={statusCode}
              statusIcon={icon}
              statusText={statusText}
            />
            {payload && (
              <span
                className={styles.copyPayload}
                data-testid="copy-payload"
                onClick={onCopyPayload}
              >
                <CopyIcon iconClass={"button-icon-right"} size="18px" />
              </span>
            )}
          </span>
        </div>
      </div>
    </div>
  );
};

export default WebhookDetails;
