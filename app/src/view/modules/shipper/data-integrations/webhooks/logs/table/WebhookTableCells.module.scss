@import "@fourkites/elemental-atoms/build/scss/colors/index";

.textCell {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: $color-neutral-900;
}

.webhookStatusWrapper {
  display: flex;
  align-items: center;
}

.webhookStatus {
  display: flex;
  align-items: center;
}

.container {
  > button {
    display: flex;
    align-items: center;
    align-content: center;
    background-color: transparent !important;
    padding-left: 2px !important;
    padding-right: 2px !important;
  }

  > a {
    width: max-content;
    display: flex;
    align-items: center;
    align-content: center;
    background-color: transparent !important;
    padding-left: 2px !important;
    padding-right: 2px !important;
    font-size: 14px;
    border-bottom: 1px solid $color-primary-500;
  }
}

.createdOnHeader {
  display: flex;
  align-items: center;
}

.createdOnHeaderTooltip {
  margin-left: 10px;
  position: relative;
  top: 1px;
}
