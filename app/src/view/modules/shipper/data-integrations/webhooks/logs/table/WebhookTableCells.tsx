import React from "react";
import { useTranslation } from "react-i18next";

import { InfoIcon } from "@fourkites/elemental-atoms";
import { Tooltip } from "@fourkites/elemental-tooltip";

import NewStatusTag from "view/components/base/new-status-indicators/NewStatusTag";

import styles from "./WebhookTableCells.module.scss";
import classNames from "classnames";

// Webhook status column
export const StatusCell = ({ code, fetchWebhookStatusDetails }: any) => {
  const { t } = useTranslation();

  const webhookStatusDetail = fetchWebhookStatusDetails(code);
  const { variant, statusCode, icon, webhookStatusTooltip, statusText } =
    webhookStatusDetail;

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div className={styles.webhookStatusWrapper}>
      <div className={styles.webhookStatus}>
        <NewStatusTag
          variant={variant}
          statusCode={statusCode}
          statusIcon={icon}
          tooltipText={webhookStatusTooltip}
          statusText={statusText}
        />
      </div>
    </div>
  );
};

// Webhook payload view column
export const ActionLinkWithTooltip = ({ title }: any) => {
  return (
    <div className={styles.container}>
      <a>{title}</a>
    </div>
  );
};

// Webhook created on header cell
export const WebhookCreatedOnHeaderCell = ({ t }: any) => {
  return (
    <>
      <span className={styles.createdOnHeader}>{t("Event Timestamp")}</span>
      <Tooltip
        text={t("All Timestamps shown below are converted to UTC timezone.")}
      >
        <span className={styles.timeframeInfo}>
          <InfoIcon fill="#0e65e5" iconClass={styles.createdOnHeaderTooltip} />
        </span>
      </Tooltip>
    </>
  );
};
