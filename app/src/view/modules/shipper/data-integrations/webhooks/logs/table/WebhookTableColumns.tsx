import React from "react";

import { convertDatetoUtcString } from "view/components/base/DateUtils";

import {
  ActionLinkWithTooltip,
  StatusCell,
  WebhookCreatedOnHeaderCell,
} from "./WebhookTableCells";

const getWebhookListColumns = (
  t: Function,
  isSidePanelVisible: boolean,
  fetchWebhookStatusDetails: Function
) => {
  const webhookColumns = [
    {
      Header: t("UUID"),
      accessor: "uuid",
      disableSortBy: true,
    },
    {
      Header: t("Load Number"),
      accessor: "loadNumber",
      disableSortBy: true,
    },
    {
      Header: t("Events"),
      accessor: "eventType",
      disableSortBy: true,
    },
    ...(!isSidePanelVisible ? [getWebhookCreatedOnColumn(t)] : []),
    ...(!isSidePanelVisible ? [getWebhookStatusColumn(t)] : []),
    ...(!isSidePanelVisible
      ? [getWebhookPayloadColumn(t, fetchWebhookStatusDetails)]
      : []),
  ];

  return webhookColumns;
};

// Created on column
const getWebhookCreatedOnColumn = (t: Function) => ({
  Header: <WebhookCreatedOnHeaderCell t={t} />,
  accessor: "processedAt",
  disableSortBy: true,
  Cell: (cellProps: any) => {
    return <span>{convertDatetoUtcString(cellProps?.cell?.value)}</span>;
  },
});

// Payload column
const getWebhookStatusColumn = (t: Function) => ({
  Header: t("Payload"),
  accessor: "payload",
  disableSortBy: true,
  Cell: (cellProps: any) => {
    return <ActionLinkWithTooltip title={t("View")} />;
  },
});

// Status column
const getWebhookPayloadColumn = (
  t: Function,
  fetchWebhookStatusDetails: Function
) => ({
  id: "status",
  Header: t("Status"),
  accessor: "statusCode",
  disableSortBy: true,
  Cell: (cellProps: any) => {
    const statusCode = JSON.parse(cellProps?.cell?.value);
    return (
      <StatusCell
        code={statusCode}
        fetchWebhookStatusDetails={fetchWebhookStatusDetails}
      />
    );
  },
});

export default getWebhookListColumns;
