.notesContainer {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  height: 250px;
  overflow-y: auto;
  overflow-x: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.notesContent {
  padding: 20px;
}

.notesTitle {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
  position: sticky;
  top: 0;
  background: white;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
  z-index: 5;
}

.notesList {
  list-style: none;
  padding: 0;
  margin: 0;
  
  li {
    padding: 12px 0;
    font-size: 14px;
    color: #6b7280;
    position: relative;
    padding-left: 20px;
    line-height: 1.5;
    border-bottom: 1px solid #f3f4f6;
    min-height: 40px;
    
    &:last-child {
      border-bottom: none;
    }
    
    &:before {
      content: "•";
      color: #3b82f6;
      font-weight: bold;
      position: absolute;
      left: 0;
      top: 12px;
    }
    
    &:hover {
      background-color: #f9fafb;
      margin: 0 -10px;
      padding-left: 30px;
      padding-right: 10px;
      border-radius: 4px;
    }
  }
}