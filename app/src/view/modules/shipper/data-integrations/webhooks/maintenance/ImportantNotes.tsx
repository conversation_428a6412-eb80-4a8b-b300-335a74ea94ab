import React from "react";
import { useTranslation } from "react-i18next";

import styles from "./ImportantNotes.module.scss";

const ImportantNotes: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className={styles.notesContainer}>
      <div className={styles.notesContent}>
        <h4 className={styles.notesTitle}>{t("Important Notes:")}</h4>
        <ul className={styles.notesList}>
          <li>{t("Start and end times must always be in the future")}</li>
          <li>{t("Maintenance windows cannot overlap with existing schedules")}</li>
          <li>{t("Minimum maintenance duration is 1 minute")}</li>
          <li>{t("All times are converted to UTC when stored")}</li>
          <li>{t("Select your preferred timezone for easier scheduling")}</li>
          <li>{t("Deleted maintenance windows cannot be recovered")}</li>
          <li>{t("Maintenance windows will automatically end at the specified time")}</li>
        </ul>
      </div>
    </div>
  );
};

export default ImportantNotes;