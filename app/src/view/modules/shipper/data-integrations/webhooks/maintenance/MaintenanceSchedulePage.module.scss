.container {
  background-color: #f5f5f5;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.pageHeader {
  padding: 20px 30px;
  background: #fafafa;
  border-bottom: 1px solid #e5e7eb;
  flex-shrink: 0;
  
  .title {
    font-size: 24px;
    font-weight: 600;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin: 0;
  }
}

.pageContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.formSection {
  padding: 30px;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  flex-shrink: 0;
  
  .formRow {
    display: flex;
    gap: 20px;
    align-items: flex-end;
    flex-wrap: wrap;
  }
  
  .formGroup {
    flex: 1;
    min-width: 200px;
    
    .label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: #555;
      font-size: 14px;
    }
    
    .input, .select {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid #d1d5db;
      border-radius: 4px;
      font-size: 14px;
      font-family: inherit;
      
      &:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
      }
      
      &:disabled {
        background-color: #f9fafb;
        cursor: not-allowed;
      }
    }
    
    .select {
      cursor: pointer;
    }
    
    .errorMessage {
      color: #dc2626;
      font-size: 12px;
      margin-top: 5px;
      font-weight: 500;
    }
  }
  
  .scheduleButton {
    background-color: #5bc0de;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    min-width: 100px;
    height: 38px;
    
    &:hover:not(:disabled) {
      background-color: #46b8da;
    }
    
    &:disabled {
      background-color: #9ca3af;
      cursor: not-allowed;
    }
  }
}

.tableSection {
  flex: 2;
  margin: 20px;
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.notesSection {
  flex: 1;
  margin: 0 20px 20px 20px;
  display: flex;
  flex-direction: column;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
  background: white;
  flex-shrink: 0;
}

.emptyDataContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 24px;
  text-align: center;
  background: white;
  flex: 1;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  
  .emptyStateLogo {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
    opacity: 0.6;
  }
  
  h2 {
    font-size: 18px;
    font-weight: 600;
    color: #666;
    margin-bottom: 8px;
  }
  
  h6 {
    font-size: 14px;
    color: #999;
    margin-bottom: 0;
  }
}