import React, { useState, useEffect, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { useHistory } from "react-router";
import { DateTime } from "luxon";

import { Spinner } from "@fourkites/elemental-loading-indicator";
import { Button, ExternalLinkIcon, PlusIcon } from "@fourkites/elemental-atoms";

import emptyStateLogo from "assets/img/emptyBox.png";

import { dataIntegrationRoutes } from "router/shipper/ShipperDataIntegrationsRouter";

import { PaginationType } from "state/BaseTypes";
import { useAppSelector, useAppDispatch } from "state/hooks";
import { UsersState } from "state/modules/Users";

import { convertDateToUtcDate } from "view/components/base/DateUtils";
import { showToast } from "view/components/base/toast/Toast";
import SubPagePanel from "view/components/base/containers/SubPagePanel";
import BreadcrumbsHeader from "view/components/base/breadcrumbs-header/BreadcrumbsHeader";

import MaintenanceWindowsApi from "api/shipper/MaintenanceWindowsApi";
import MaintenanceScheduleTable from "./table/MaintenanceScheduleTable";
import ImportantNotes from "./ImportantNotes";
import NoWebhooksLicensePage from "view/modules/shipper/data-integrations/webhooks/configurations/NoWebhooksLicensePage";
import { 
  sortMaintenanceWindows, 
  paginateMaintenanceWindows,
  MaintenanceWindow,
  PaginationData 
} from "./utils/MaintenanceUtils";

import styles from "./MaintenanceSchedulePage.module.scss";

const DEFAULT_PAGINATION_PARAMETERS = { pageSize: 10, pageIndex: 0 };

interface MaintenanceWindowsResponse {
  statusCode?: number;
  status?: number;
  schedules?: MaintenanceWindow[];
  data?: {
    schedules?: MaintenanceWindow[];
  } | MaintenanceWindow[];
}

const MaintenanceSchedulePage = () => {
  const { t } = useTranslation();
  const history = useHistory();

  /*****************************************************************************
   * REDUX
   ****************************************************************************/
  const dispatch = useAppDispatch();
  const shipperId: string = useAppSelector(UsersState.selectors.getCompanyId);
  
  const companyLicenses: string = useAppSelector(
    UsersState.selectors.getCompanyLicenses
  );
  const hasCallbacksLicense = companyLicenses.indexOf("callbacks") !== -1;

  /*****************************************************************************
   * STATE
   ****************************************************************************/
  const [allMaintenanceWindows, setAllMaintenanceWindows] = useState<MaintenanceWindow[]>([]);
  const [paginatedWindows, setPaginatedWindows] = useState<MaintenanceWindow[]>([]);
  const [pagination, setPagination] = useState<PaginationData>({
    currentPage: 1,
    totalPages: 0,
    totalItems: 0,
    itemsPerPage: 5,
    hasNextPage: false,
    hasPreviousPage: false,
  });
  const [isRetrieving, setIsRetrieving] = useState<boolean>(false);
  const [hasError, setHasError] = useState<boolean>(false);
  
  const [startTime, setStartTime] = useState<string>("");
  const [endTime, setEndTime] = useState<string>("");
  const [selectedTimezone, setSelectedTimezone] = useState<string>("UTC");
  const [validationError, setValidationError] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  const getTimezones = () => {
    return [
      { value: "UTC", label: "UTC (Coordinated Universal Time)" },
      { value: "America/New_York", label: "EST/EDT (Eastern Time)" },
      { value: "America/Chicago", label: "CST/CDT (Central Time)" },
      { value: "America/Denver", label: "MST/MDT (Mountain Time)" },
      { value: "America/Los_Angeles", label: "PST/PDT (Pacific Time)" },
      { value: "Europe/London", label: "GMT/BST (London)" },
      { value: "Europe/Paris", label: "CET/CEST (Paris)" },
      { value: "Asia/Tokyo", label: "JST (Tokyo)" },
      { value: "Asia/Shanghai", label: "CST (Shanghai)" },
      { value: "Asia/Kolkata", label: "IST (India)" },
      { value: "Australia/Sydney", label: "AEDT/AEST (Sydney)" }
    ];
  };

  const convertToUTC = (dateTimeLocal: string, timezone: string): string => {
    if (!dateTimeLocal) return "";
    
    try {
      const dt = DateTime.fromFormat(dateTimeLocal, "yyyy-MM-dd'T'HH:mm", { zone: timezone });
      return dt.toUTC().toFormat("yyyy-MM-dd HH:mm");
    } catch (error) {
      console.error("Error converting timezone:", error);
      return dateTimeLocal.replace("T", " ");
    }
  };

  const checkForOverlap = (newStart: string, newEnd: string): boolean => {
    const newStartTime = new Date(newStart);
    const newEndTime = new Date(newEnd);
    
    return allMaintenanceWindows.some(window => {
      const existingStart = new Date(window.openTime);
      const existingEnd = new Date(window.closeTime);
      return (newStartTime < existingEnd && newEndTime > existingStart);
    });
  };

  const validateMaintenanceForm = (): boolean => {
    setValidationError("");

    if (!startTime || !endTime) {
      setValidationError(t("Both start and end times are required"));
      return false;
    }

    try {
      const start = DateTime.fromFormat(startTime, "yyyy-MM-dd'T'HH:mm", { zone: selectedTimezone });
      const end = DateTime.fromFormat(endTime, "yyyy-MM-dd'T'HH:mm", { zone: selectedTimezone });
      const now = DateTime.now().setZone(selectedTimezone);
      
      if (!start.isValid || !end.isValid) {
        setValidationError(t("Invalid date format"));
        return false;
      }
      
      if (start <= now) {
        setValidationError(t("Start time must be in the future"));
        return false;
      }
      
      if (end <= now) {
        setValidationError(t("End time must be in the future"));
        return false;
      }
      
      if (end <= start) {
        setValidationError(t("End time must be after start time"));
        return false;
      }
      
      const diffMs = end.diff(start).milliseconds;
      if (diffMs < 60000) {
        setValidationError(t("Maintenance window must be at least 1 minute long"));
        return false;
      }
      
      const startUTC = convertToUTC(startTime, selectedTimezone);
      const endUTC = convertToUTC(endTime, selectedTimezone);
      
      if (checkForOverlap(startUTC, endUTC)) {
        setValidationError(t("Maintenance window overlaps with existing maintenance"));
        return false;
      }
      
      return true;
    } catch (error) {
      setValidationError(t("Invalid date or timezone"));
      return false;
    }
  };

  const formatTimeForAPI = (dateTimeLocal: string): string => {
    return convertToUTC(dateTimeLocal, selectedTimezone);
  };

  const updatePaginationData = useCallback((windows: MaintenanceWindow[], currentPage: number = 1) => {
    const sortedWindows = sortMaintenanceWindows(windows);
    const { data, pagination: paginationData } = paginateMaintenanceWindows(sortedWindows, currentPage, 5);
    
    console.log("Pagination Debug:", {
      totalWindows: sortedWindows.length,
      currentPage,
      totalPages: paginationData.totalPages,
      itemsPerPage: paginationData.itemsPerPage,
      paginatedData: data.length
    });
    
    setAllMaintenanceWindows(sortedWindows);
    setPaginatedWindows(data);
    setPagination(paginationData);
  }, []);

  const handleTimezoneChange = useCallback((newTimezone: string) => {
    setSelectedTimezone(newTimezone);
    if (allMaintenanceWindows.length > 0) {
      updatePaginationData(allMaintenanceWindows, pagination.currentPage);
    }
  }, [allMaintenanceWindows, pagination.currentPage, updatePaginationData]);

  const handlePageChange = useCallback((newPage: number) => {
    if (newPage >= 1 && newPage <= pagination.totalPages) {
      updatePaginationData(allMaintenanceWindows, newPage);
    }
  }, [allMaintenanceWindows, pagination.totalPages, updatePaginationData]);

  const fetchMaintenanceWindows = async (resetToFirstPage: boolean = false) => {
    if (!shipperId) return;

    try {
      setIsRetrieving(true);
      setHasError(false);
      
      const response: MaintenanceWindowsResponse = await MaintenanceWindowsApi.retrieveMaintenanceWindows(shipperId);
      console.log("API Response:", response);
      
      let schedules: MaintenanceWindow[] = [];
      
      if (response.schedules) {
        schedules = response.schedules;
      } else if (response.data) {
        if (typeof response.data === 'object' && !Array.isArray(response.data) && 'schedules' in response.data) {
          schedules = response.data.schedules || [];
        } else if (Array.isArray(response.data)) {
          schedules = response.data;
        }
      } else if (Array.isArray(response)) {
        schedules = response;
      }
      
      const currentPage = resetToFirstPage ? 1 : pagination.currentPage;
      updatePaginationData(schedules, currentPage);
      
    } catch (err) {
      console.error("Error fetching maintenance windows:", err);
      setHasError(true);
      showToast(t("An error occurred"), t("There was an error retrieving the maintenance windows."), "error");
    } finally {
      setIsRetrieving(false);
    }
  };

  const fetchInitialData = async () => {
    await fetchMaintenanceWindows();
  };

  const handleScheduleSubmit = async () => {
    if (!validateMaintenanceForm()) return;

    try {
      setIsSubmitting(true);
      
      const formattedStartTime = formatTimeForAPI(startTime);
      const formattedEndTime = formatTimeForAPI(endTime);
      
      const response = await MaintenanceWindowsApi.createMaintenanceWindow(
        shipperId,
        { openTime: formattedStartTime, closeTime: formattedEndTime }
      );
      
      const isSuccess = response?.status === 200 || response?.statusCode === 200 || (!response?.status && !response?.statusCode);
      
      if (isSuccess) {
        showToast(t("Success"), t("Maintenance window scheduled successfully."), "ok");
        setStartTime("");
        setEndTime("");
        setValidationError("");
        await fetchMaintenanceWindows(true);
      } else {
        throw new Error('Failed to create maintenance window');
      }
    } catch (err) {
      console.error("Error creating maintenance window:", err);
      showToast(t("An error occurred"), t("There was an error scheduling the maintenance window."), "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteMaintenance = async (maintenanceId: number) => {
    try {
      setIsRetrieving(true);
      await MaintenanceWindowsApi.deleteMaintenanceWindow(shipperId, maintenanceId);
      showToast(t("Success"), t("Maintenance window deleted successfully."), "ok");
      await fetchMaintenanceWindows(false);
    } catch (err) {
      console.error("Error deleting maintenance window:", err);
      showToast(t("An error occurred"), t("There was an error deleting the maintenance window."), "error");
    } finally {
      setIsRetrieving(false);
    }
  };

  const fetchMaintenanceData = useCallback(async ({ pageSize, pageIndex }: PaginationType) => {
    await fetchMaintenanceWindows();
  }, [shipperId]);

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  useEffect(() => {
    const fetchData = async () => {
      await fetchInitialData();
    };
    fetchData();
  }, [shipperId]);

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  if (!hasCallbacksLicense) {
    return <NoWebhooksLicensePage />;
  }

  const noMaintenanceData = allMaintenanceWindows?.length === 0 && !isRetrieving && !hasError;

  const maintenanceContent = (
    <div className={styles.pageContent}>
      <div className={styles.formSection}>
        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label className={styles.label}>
              {t("Maintenance Start Time")} ({selectedTimezone})
            </label>
            <input
              type="datetime-local"
              value={startTime}
              onChange={(e) => setStartTime(e.target.value)}
              className={styles.input}
              disabled={isSubmitting}
            />
          </div>
          
          <div className={styles.formGroup}>
            <label className={styles.label}>
              {t("Maintenance End Time")} ({selectedTimezone})
            </label>
            <input
              type="datetime-local"
              value={endTime}
              onChange={(e) => setEndTime(e.target.value)}
              className={styles.input}
              disabled={isSubmitting}
            />
            {validationError && (
              <div className={styles.errorMessage}>
                *{validationError}
              </div>
            )}
          </div>

          <div className={styles.formGroup}>
            <label className={styles.label}>{t("Timezone")}</label>
            <select
              value={selectedTimezone}
              onChange={(e) => handleTimezoneChange(e.target.value)}
              className={styles.select}
              disabled={isSubmitting}
            >
              {getTimezones().map(tz => (
                <option key={tz.value} value={tz.value}>
                  {tz.label}
                </option>
              ))}
            </select>
          </div>
          
          <button
            className={styles.scheduleButton}
            onClick={handleScheduleSubmit}
            disabled={isSubmitting || !startTime || !endTime}
          >
            {isSubmitting ? t("Scheduling...") : t("Schedule").toUpperCase()}
          </button>
        </div>
      </div>

      {isRetrieving && (
        <div className={styles.loading}>
          <Spinner isLoading size="medium" />
        </div>
      )}

      <div className={styles.tableSection}>
        {hasError ? (
          <ErrorPage />
        ) : noMaintenanceData ? (
          <EmptyPage />
        ) : (
          <MaintenanceScheduleTable
            maintenanceWindows={paginatedWindows}
            loading={isRetrieving}
            onDelete={handleDeleteMaintenance}
            fetchMaintenanceData={fetchMaintenanceData}
            pagination={pagination}
            onPageChange={handlePageChange}
            selectedTimezone={selectedTimezone}
          />
        )}
      </div>

      <div className={styles.notesSection}>
        <ImportantNotes />
      </div>
    </div>
  );

  return (
    <div className={styles.container} data-test-id="maintenance-schedule-page-container">
      <div className={styles.pageHeader}>
        <h1 className={styles.title}>
          {t("Callback Maintenance")}
        </h1>
      </div>
      {maintenanceContent}
    </div>
  );
};

const EmptyPage = () => {
  const { t } = useTranslation();
  return (
    <div className={styles.emptyDataContainer}>
      <img className={styles.emptyStateLogo} src={emptyStateLogo} />
      <h2>{t("No maintenance windows scheduled!")}</h2>
      <h6>{t("Use the form above to schedule maintenance windows.")}</h6>
    </div>
  );
};

const ErrorPage = () => {
  const { t } = useTranslation();
  return (
    <div className={styles.emptyDataContainer}>
      <img className={styles.emptyStateLogo} src={emptyStateLogo} />
      <h2>{t("Something went wrong!")}</h2>
      <h6>{t("Error in fetching maintenance windows.")}</h6>
    </div>
  );
};

export default MaintenanceSchedulePage;