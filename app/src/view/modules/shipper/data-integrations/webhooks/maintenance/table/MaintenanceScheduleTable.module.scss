.tableWrapper {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.tableContainer {
  background: white;
  overflow-y: auto;
  overflow-x: hidden;
  flex: 1;
  max-height: 400px;
}

.maintenanceTable {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
  
  th {
    background-color: #f8f9fa;
    padding: 12px 15px;
    text-align: left;
    font-weight: 600;
    color: #666;
    text-transform: uppercase;
    font-size: 11px;
    letter-spacing: 0.5px;
    border-bottom: 1px solid #e5e7eb;
    position: sticky;
    top: 0;
    z-index: 5;
    
    &:nth-child(1) { width: 15%; }
    &:nth-child(2) { width: 20%; }
    &:nth-child(3) { width: 20%; }
    &:nth-child(4) { width: 15%; }
    &:nth-child(5) { width: 15%; }
    &:nth-child(6) { width: 15%; }
  }
  
  td {
    padding: 12px 15px;
    border-bottom: 1px solid #f3f4f6;
    vertical-align: middle;
    font-size: 12px;
    color: #333;
    overflow: hidden;
    text-overflow: ellipsis;
    
    &:nth-child(1) { width: 15%; }
    &:nth-child(2) { width: 20%; }
    &:nth-child(3) { width: 20%; }
    &:nth-child(4) { width: 15%; }
    &:nth-child(5) { width: 15%; }
    &:nth-child(6) { width: 15%; }
  }
  
  tr:hover {
    background-color: #fafafa;
  }
  
  tr:last-child td {
    border-bottom: none;
  }
}

.tableRow {
  // Additional row styling if needed
}

.idCell {
  font-weight: 500;
}

.timeCell {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
}

.durationCell {
  color: #666;
}

.statusCell {
  .statusBadge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    
    &.completed {
      color: #28a745;
      background-color: #d4edda;
    }
    
    &.active {
      color: #007bff;
      background-color: #cce5ff;
    }
    
    &.scheduled {
      color: #856404;
      background-color: #fff3cd;
    }
    
    &.in-progress {
      color: #004085;
      background-color: #cce5ff;
    }
  }
}

.actionCell {
  text-align: center;
}

.deleteButton {
  background: none;
  border: none;
  color: #dc2626;
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  font-size: 14px;
  
  &:hover {
    background-color: #fef2f2;
  }
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  
  .loadingSpinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f4f6;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  span {
    margin-left: 12px;
    color: #666;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 24px;
  text-align: center;
  color: #666;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.paginationContainer {
  padding: 15px 20px;
  background: white;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.paginationInfo {
  font-size: 14px;
  color: #666;
}

.paginationControls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.paginationButton {
  background: #f8f9fa;
  border: 1px solid #d1d5db;
  color: #374151;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  
  &:hover:not(:disabled) {
    background: #e5e7eb;
  }
  
  &:disabled {
    background: #f3f4f6;
    color: #9ca3af;
    cursor: not-allowed;
  }
  
  &.active {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
  }
}

.pageNumbers {
  display: flex;
  gap: 5px;
}