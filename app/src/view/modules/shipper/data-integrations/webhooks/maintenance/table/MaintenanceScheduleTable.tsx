import React from "react";
import { useTranslation } from "react-i18next";
import { 
  formatDateTime, 
  calculateDuration, 
  getMaintenanceStatus,
  generatePageNumbers,
  MaintenanceWindow,
  PaginationData
} from "../utils/MaintenanceUtils";

import styles from "./MaintenanceScheduleTable.module.scss";

interface MaintenanceScheduleTableProps {
  maintenanceWindows: MaintenanceWindow[];
  loading: boolean;
  onDelete: (maintenanceId: number) => void;
  fetchMaintenanceData: (pagination: { pageSize: number; pageIndex: number }) => Promise<void>;
  pagination: PaginationData;
  onPageChange: (page: number) => void;
  selectedTimezone: string;
}

const MaintenanceScheduleTable: React.FC<MaintenanceScheduleTableProps> = ({
  maintenanceWindows,
  loading,
  onDelete,
  fetchMaintenanceData,
  pagination,
  onPageChange,
  selectedTimezone,
}) => {
  const { t } = useTranslation();

  const handleDeleteClick = (maintenanceId: number) => {
    if (window.confirm(t("Are you sure you want to delete this maintenance window?"))) {
      onDelete(maintenanceId);
    }
  };

  const renderPagination = () => {
    console.log("Pagination render check:", {
      totalPages: pagination.totalPages,
      totalItems: pagination.totalItems,
      currentPage: pagination.currentPage,
      itemsPerPage: pagination.itemsPerPage
    });

    // Show pagination if we have any data
    if (pagination.totalItems === 0) return null;

    const pageNumbers = generatePageNumbers(pagination.currentPage, pagination.totalPages);

    return (
      <div className={styles.paginationContainer}>
        <div className={styles.paginationInfo}>
          Showing {(pagination.currentPage - 1) * pagination.itemsPerPage + 1} to{' '}
          {Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems)} of{' '}
          {pagination.totalItems} entries
        </div>
        
        <div className={styles.paginationControls}>
          <button
            className={styles.paginationButton}
            onClick={() => onPageChange(pagination.currentPage - 1)}
            disabled={!pagination.hasPreviousPage}
          >
            Previous
          </button>
          
          <div className={styles.pageNumbers}>
            {pageNumbers.map((pageNum, index) => (
              pageNum === -1 ? (
                <span key={`ellipsis-${index}`} className={styles.paginationButton}>
                  ...
                </span>
              ) : (
                <button
                  key={pageNum}
                  className={`${styles.paginationButton} ${
                    pageNum === pagination.currentPage ? styles.active : ''
                  }`}
                  onClick={() => onPageChange(pageNum)}
                >
                  {pageNum}
                </button>
              )
            ))}
          </div>
          
          <button
            className={styles.paginationButton}
            onClick={() => onPageChange(pagination.currentPage + 1)}
            disabled={!pagination.hasNextPage}
          >
            Next
          </button>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingSpinner}></div>
        <span>{t("Loading maintenance windows...")}</span>
      </div>
    );
  }

  if (maintenanceWindows.length === 0) {
    return (
      <div className={styles.emptyState}>
        <p>{t("No maintenance windows scheduled")}</p>
      </div>
    );
  }

  return (
    <div className={styles.tableWrapper}>
      <div className={styles.tableContainer}>
        <table className={styles.maintenanceTable}>
          <thead>
            <tr>
              <th>{t("ID")}</th>
              <th>{t("Open Time")}</th>
              <th>{t("Close Time")}</th>
              <th>{t("Duration")}</th>
              <th>{t("Status")}</th>
              <th>{t("Delete")}</th>
            </tr>
          </thead>
          <tbody>
            {maintenanceWindows.map((maintenance) => {
              const status = getMaintenanceStatus(maintenance.openTime, maintenance.closeTime);
              const duration = calculateDuration(maintenance.openTime, maintenance.closeTime);
              
              return (
                <tr key={maintenance.id} className={styles.tableRow}>
                  <td className={styles.idCell}>{maintenance.id}</td>
                  <td className={styles.timeCell}>
                    {formatDateTime(maintenance.openTime, selectedTimezone)}
                  </td>
                  <td className={styles.timeCell}>
                    {formatDateTime(maintenance.closeTime, selectedTimezone)}
                  </td>
                  <td className={styles.durationCell}>{duration}</td>
                  <td className={styles.statusCell}>
                    <span className={`${styles.statusBadge} ${styles[status.toLowerCase()]}`}>
                      {t(status)}
                    </span>
                  </td>
                  <td className={styles.actionCell}>
                    <button
                      className={styles.deleteButton}
                      onClick={() => handleDeleteClick(maintenance.id)}
                      title={t("Delete maintenance window")}
                      data-test-id={`delete-maintenance-${maintenance.id}`}
                    >
                      🗑️
                    </button>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
      
      {renderPagination()}
    </div>
  );
};

export default MaintenanceScheduleTable;