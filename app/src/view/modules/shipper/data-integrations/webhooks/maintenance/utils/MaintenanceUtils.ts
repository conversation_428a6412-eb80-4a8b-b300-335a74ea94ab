import { DateTime } from "luxon";

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

export interface PaginationData {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface MaintenanceWindow {
  id: number;
  companyId: string;
  openTime: string;
  closeTime: string;
}

export const formatDateTime = (dateTimeString: string, timezone: string = "UTC"): string => {
  try {
    let dt;
    
    if (dateTimeString.includes('T')) {
      dt = DateTime.fromISO(dateTimeString, { zone: "UTC" });
    } else {
      dt = DateTime.fromFormat(dateTimeString, "yyyy-MM-dd HH:mm", { zone: "UTC" });
    }
    
    if (!dt.isValid) {
      dt = DateTime.fromJSDate(new Date(dateTimeString), { zone: "UTC" });
    }
    
    if (dt.isValid) {
      return dt.setZone(timezone).toFormat('MM/dd/yyyy, HH:mm') + ` ${timezone}`;
    }
    
    return dateTimeString;
  } catch (error) {
    console.error("Error formatting date:", error, dateTimeString);
    return dateTimeString;
  }
};

export const calculateDuration = (openTime: string, closeTime: string): string => {
  try {
    const start = new Date(openTime);
    const end = new Date(closeTime);
    const diffMs = end.getTime() - start.getTime();
    
    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 0) {
      return `${hours} Hour(s) ${minutes} Minute(s)`;
    } else {
      return `${minutes} Minute(s)`;
    }
  } catch (error) {
    return "Unknown";
  }
};

export const getMaintenanceStatus = (openTime: string, closeTime: string): string => {
  try {
    const now = new Date();
    const start = new Date(openTime);
    const end = new Date(closeTime);
    
    if (now < start) {
      return "Scheduled";
    } else if (now >= start && now <= end) {
      return "In Progress";
    } else {
      return "Completed";
    }
  } catch (error) {
    return "Unknown";
  }
};

export const validateMaintenanceWindow = (
  startTime: string, 
  endTime: string
): ValidationResult => {
  if (!startTime || !endTime) {
    return {
      isValid: false,
      error: "Both start and end times are required"
    };
  }

  try {
    const start = new Date(startTime);
    const end = new Date(endTime);
    
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return {
        isValid: false,
        error: "Invalid date format"
      };
    }
    
    if (end <= start) {
      return {
        isValid: false,
        error: "End time must be after start time"
      };
    }
    
    const diffMs = end.getTime() - start.getTime();
    if (diffMs < 60000) {
      return {
        isValid: false,
        error: "Maintenance window must be at least 1 minute long"
      };
    }
    
    if (diffMs > 86400000) {
      return {
        isValid: false,
        error: "Maintenance window cannot exceed 24 hours"
      };
    }
    
    return { isValid: true };
  } catch (error) {
    return {
      isValid: false,
      error: "Invalid date format"
    };
  }
};

export const formatTimeForAPI = (dateTimeLocal: string): string => {
  return dateTimeLocal.replace("T", " ");
};

export const formatTimeFromAPI = (apiDateTime: string): string => {
  try {
    const date = new Date(apiDateTime);
    return date.toISOString().slice(0, 16);
  } catch (error) {
    return apiDateTime;
  }
};

export const sortMaintenanceWindows = (windows: MaintenanceWindow[]): MaintenanceWindow[] => {
  return [...windows].sort((a, b) => {
    const dateA = new Date(a.openTime);
    const dateB = new Date(b.openTime);
    return dateB.getTime() - dateA.getTime();
  });
};

export const paginateMaintenanceWindows = (
  windows: MaintenanceWindow[],
  currentPage: number,
  itemsPerPage: number = 5
): { data: MaintenanceWindow[]; pagination: PaginationData } => {
  const totalItems = windows.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  
  const paginatedData = windows.slice(startIndex, endIndex);
  
  const pagination: PaginationData = {
    currentPage,
    totalPages,
    totalItems,
    itemsPerPage,
    hasNextPage: currentPage < totalPages,
    hasPreviousPage: currentPage > 1,
  };
  
  return {
    data: paginatedData,
    pagination,
  };
};

export const generatePageNumbers = (currentPage: number, totalPages: number): number[] => {
  const delta = 2;
  const range: number[] = [];
  
  for (
    let i = Math.max(2, currentPage - delta);
    i <= Math.min(totalPages - 1, currentPage + delta);
    i++
  ) {
    range.push(i);
  }
  
  if (currentPage - delta > 2) {
    range.unshift(-1);
  }
  if (currentPage + delta < totalPages - 1) {
    range.push(-1);
  }
  
  range.unshift(1);
  if (totalPages !== 1) {
    range.push(totalPages);
  }
  
  return range.filter((v, i, a) => a.indexOf(v) === i);
};