@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container {
  display: flex;
  flex-direction: column;
  background-color: $color-neutral-50;
  width: 84%;
  padding-left: 8%;
  padding-right: 8%;
  height: calc(100vh + -113px);
  overflow-y: scroll;
}

.titleContainer {
  display: block;
  padding-top: 36px;
  padding-bottom: 36px;
}

.title {
  color: $color-neutral-700;
  font-size: 20px;
  font-weight: 600;
  letter-spacing: 0;
  line-height: 24px;
}

.content {
  display: flex;
  align-content: center;
  justify-content: center;

  > div {
    display: flex;
    margin-right: 24px;
    width: 100%;

    &:last-child {
      margin-right: 0;
    }
  }
}

.contentColumn {
  display: flex;
  flex: 1;
  height: fit-content;
  padding-top: 24px;

  > div {
    display: flex;
    width: 100%;
    max-width: 490px;

    &:last-child {
      margin-right: 0;
    }
  }
}

.cardContent {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.divider {
  display: block;
  height: 1px;
  width: 100%;
  background-color: $color-neutral-300;
}

.carrierIndicator {
  display: flex;
  align-content: center;
  align-items: center;

  > label {
    margin-left: 8px;
  }
}

.connectionErrors {
  display: flex;
  flex-direction: column;
  padding: 24px;
}
