import React from "react";

//import BreadcrumbsHeader from "view/components/base/breadcrumbs-header/BreadcrumbsHeader";
//import InviteUsers from "view/components/self-service/overview/invite-users/InviteUsers";

import styles from "./ShipperOverviewPage.module.scss";

import ShipperOverview from "view/components/self-service/overview/shipper/ShipperOverview";

const ShipperOverviewPage: React.FC = () => {
  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div className={styles.container}>
      <ShipperOverview />

      {/*
      <div className={styles.titleContainer}>
        <BreadcrumbsHeader titles={[t("Organization")]} />
      </div>
      <div className={styles.content}>
        <InviteUsers />

        <div />
      </div>
      */}
    </div>
  );
};

export default ShipperOverviewPage;
