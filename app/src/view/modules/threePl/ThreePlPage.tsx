import React, { useState, useEffect } from "react";
import { useHistory } from "react-router-dom";
import { useTranslation } from "react-i18next";

import {
  GlobalHeader as Header,
  ProductId,
} from "@fourkites/elemental-global-header";

import { useAppSelector, useAppDispatch } from "state/hooks";
import UsersState, { getCookies } from "state/modules/Users";

import { appRoutes } from "router/AppRouter";
import ThreePlRouter, {
  threePlRoutes,
  getThreePlRouteId,
} from "router/threePl/ThreePlRouter";
import {
  onNewWindow,
  onNav,
  onCommunitySelect,
  onCarrierHelpClick,
} from "router/navigationUtils";
import TermsAndConditionsModal from "view/components/self-service/terms-and-conditions/TermsAndConditions";

import { COMPANY_API_DOMAIN, fourkitesUrls } from "api/http/apiUtils";
import { startWithUpperCase } from "view/components/base/StringUtils";
import CompanySwitcher from "view/components/self-service/component/CompanySwitcher"


import styles from "./ThreePlPage.module.scss";

const ThreePlPage: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const history = useHistory();

  const currentUser = useAppSelector(UsersState.selectors.getCurrentUser);
  const superAdmin = useAppSelector(UsersState.selectors.getIsSuperAdmin);
  const isCompanyAdmin = useAppSelector(UsersState.selectors.getIsCompanyAdmin);
  const termsAgreed = useAppSelector(UsersState.selectors.getTermsAgreed);
  const { authToken, userId, deviceId } = getCookies();


  const currentUserObject = {
    authToken,
    userId,
    deviceId,
    superAdmin: superAdmin ? "true" : "false",
    name: {
      firstName: startWithUpperCase(currentUser?.firstName),
      lastName: startWithUpperCase(currentUser?.lastName),
    },
    isCompanyAdmin: isCompanyAdmin ? "true" : "false",
    role: currentUser?.role,
    company: {
      defaultTypes: currentUser?.defaultCompanyType,
      defaultId: currentUser?.defaultCompanyID,
      context: currentUser?.companyContext,
    },
    displayCompanySwitcher: false,
  };

  const domains = {
    company: COMPANY_API_DOMAIN,
    user: "",
  }

  const currentUserData = sessionStorage.getItem("fkcUserDetails") || localStorage.getItem("tfcurrent-user");
  const userObject = currentUserData ? JSON.parse(currentUserData) : null;

  /*****************************************************************************
   * STATE
   ****************************************************************************/

  const [showTermsAndConditionsModal, setShowTermsAndConditionsModal] =
    useState<boolean>(false);

  /*****************************************************************************
   * EFFECTS
   ****************************************************************************/

  useEffect(() => {
    setShowTermsAndConditionsModal(!termsAgreed);
  }, [termsAgreed]);

  /*****************************************************************************
   * INTERNAL METHODS
   ****************************************************************************/

  // Used to determine what happens when we click the fourkites Logo
  const onLogoClick = () => {
    currentUser?.hasWorkspaceEnabled ?
    onNav(fourkitesUrls.myWorkspace) : onNav(fourkitesUrls.dashboard)
 }


 /*onWorkspaceSelect, onDeveloperPortalSelect, onAppointmentsManagerSelect, onDynamicYardSelec, onDynamicYardPlusSelect, onPrivateFleetTrackingSelect
  onRailFleetSelect - All these methods are used to decide where to navigate when a product is selected from product switcher*/
 const onWorkspaceSelect = currentUser?.hasWorkspaceEnabled
  ? () => onNav(fourkitesUrls.myWorkspace)
  : undefined; 

 const onDeveloperPortalSelect = async () => {
    const archbeeJwt = await UsersState.helpers.getArchbeeJwt();
    const jwtAddedUrl = `${fourkitesUrls.developerPortalDoc}?jwt=${archbeeJwt}`;

    if (userObject?.superAdmin || userObject?.companyAdmin) {
      onNav(fourkitesUrls.developerPortal);
    } else {
      onNav(jwtAddedUrl);
    }
  };


  const onAppointmentsManagerSelect = userObject?.modules?.includes(
    "appointment-manager"
  )
    ? () => onNav(fourkitesUrls.appointmentsManager)
    : undefined;

  const onDynamicYardSelect = userObject?.modules?.includes("dynamic-yard")
    ? () => onNav(fourkitesUrls.dynamicYard)
    : undefined; 

 const onDynamicYardPlusSelect = userObject?.modules?.includes(
   "dynamic-yard-plus"
 )
   ? () => onNav(fourkitesUrls.dynamicYardPlus)
   : undefined;

 const onPrivateFleetTrackingSelect = userObject?.modules?.includes(
   "nic-private-fleet"
 )
   ? () => onNav(fourkitesUrls.privateFleetTracking)
   : undefined;

 const onRailFleetSelect = userObject?.modules?.includes("nic-rail-fleet")
   ? () => onNav(fourkitesUrls.railFleet)
   : undefined;
  const onDigitalWorkforceSelect = () => onNav(fourkitesUrls.digitalWorkforce);
  //Used to decide where to navigate when user logs out
  const onUserLogoutClick = () => {
    dispatch(
      UsersState.actionCreators.userLogout(() => {
        const isRunninLocally = window.location.href.includes("localhost");

        if (isRunninLocally) {
          history.push(appRoutes.login);
        } else {
          onNav(fourkitesUrls.login);
        }
      })
    );
  };

  //This method gets used when we use company switcher to switch company context, gets passed as a prop to header
  const onSelectCompany = (selectedCompany: any) => {
    if (!selectedCompany || !selectedCompany?.id) {
      return;
    }

    // TODO: needs to clear state
    dispatch(UsersState.actionCreators.setSelectedCompany(selectedCompany?.id));
  };

  //used to decide what happens when user agrees the terms and conditions
  const onAgreedConditions = async () => {
    try {
      await dispatch(UsersState.actionCreators.setTermsAgreed());
      // Close modal
      setShowTermsAndConditionsModal(false);
    } catch (error) {
      onDeniedConditions();
    }
  };

  //used to decide what happens when user denies the terms and conditions
  const onDeniedConditions = () => {
    setShowTermsAndConditionsModal(false);
    onNav(fourkitesUrls.visibility);
  };

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  const subHeaderItems = [
    {
      label: t("Overview"),
      id: threePlRoutes.overview,
      pagePath: threePlRoutes.overview,
      headerType: "app_route",
    },
    ...(superAdmin || isCompanyAdmin
      ? [
          {
            label: t("Company"),
            id: threePlRoutes.company,
            pagePath: threePlRoutes.company,
            headerType: "app_route",
          },
        ]
      : []),
  ];

  const onConnectivitySelect = () => onNav(fourkitesUrls.connectivity);

  const initialSelectedProduct = ProductId.FourKitesConnect;

  const shouldShowTcModal = isCompanyAdmin && !termsAgreed;

  return (
    <div className={styles.pageContainer}>
      <CompanySwitcher/>
      <Header
        currentUser={currentUserObject}
        subHeaderItems={subHeaderItems}
        initialSelectedItemId={getThreePlRouteId(history)}
        initialSelectedProductId={initialSelectedProduct}
        onWorkspaceSelect={onWorkspaceSelect}
        onLogoClick={onLogoClick}
        // Product switcher
        onVisibilitySelect={() => onNav(fourkitesUrls.visibility)}
        onAppointmentsManagerSelect={onAppointmentsManagerSelect}
        onDynamicYardSelect={onDynamicYardSelect}
        onCommunitySelect={onCommunitySelect}
        onDeveloperPortalSelect={onDeveloperPortalSelect}
        onFourKitesConnectSelect={onConnectivitySelect}
        onDynamicYardPlusSelect={onDynamicYardPlusSelect}
        onPrivateFleetTrackingSelect={onPrivateFleetTrackingSelect}
        onRailFleetSelect={onRailFleetSelect}
        onDigitalWorkforceSelect={onDigitalWorkforceSelect}
        // Right menu
        onNotificationsClick={() => onNewWindow(fourkitesUrls.notifications)}
        onHelpClick={onCarrierHelpClick}
        onSettingsClick={() => onNav(fourkitesUrls.companySettings)}
        // Avatar menu
        onUserLogoutClick={onUserLogoutClick}
        domains={domains}
        setSelectedCompany={onSelectCompany}
      />

      <ThreePlRouter />

      {shouldShowTcModal && (
        <TermsAndConditionsModal
          show={showTermsAndConditionsModal}
          onClose={onDeniedConditions}
          onAgreed={onAgreedConditions}
        />
      )}
    </div>
  );
};

export default ThreePlPage;
