@import "@fourkites/elemental-atoms/build/scss/colors/index";

.container {
  margin-bottom: 24px;
}

.titleContainer {
  margin-bottom: 36px;
}

.businessDetails{
  display: flex;

  > div[id="inputs-form"] {
    width: 100%;
    flex: 7;
    margin-right: 88px;
  }

  > div[id="logo-container"] {
    flex: 1
  }
}

.inputsRow {
  display: flex;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }

  > div  {
    width: 100%;
    margin-right: 88px;

    &:last-child {
      margin-right: 0;
    }

    > span {
      width: 100%;
    }

    > input{
      width: 100%;
    }
  }
}

.logo {
  object-fit: contain;
  height: 154px;
  width: 154px;
  border: 1px solid $color-neutral-300;
  border-radius: 2px;
  background-color: white;
  padding: 8px;
  margin-right: 24px;
}

.loader {
  display: flex;
  align-items: left;
  align-content: center;
  justify-content: center;
  margin-top: 32px;
  margin-bottom: 32px;
}

.headerLoader {
  composes: loader;
  margin: 0;
  margin-right: 16px;

  > label {
    margin-right: 16px;
  }
}
