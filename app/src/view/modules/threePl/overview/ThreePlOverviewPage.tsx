import React, { useState } from "react";

import { useAppSelector } from "state/hooks";
import UsersState from "state/modules/Users";

import ThreePlOverview from "view/components/self-service/overview/threePl/ThreePlOverview";

import styles from "./ThreePlOverviewPage.module.scss";

const ThreePlOverviewPage: React.FC = () => {
  /*****************************************************************************
   * REDUX
   ****************************************************************************/

  const companyType = useAppSelector(UsersState.selectors.getCompanyType);

  /*****************************************************************************
   * RENDER
   ****************************************************************************/

  return (
    <div className={styles.container}>

      <ThreePlOverview />

    </div>
  );
};

export default ThreePlOverviewPage;
