server {
	listen 80;
	listen [::]:80;

	server_name _;

	root /var/www/html;
	index index.html;
  location /self-service {
       try_files $uri $uri/ /self-service/index.html;
	     expires 0;
       location ~* \.(?:jpg|jpeg|gif|png|ico|cur|gz|svg|svgz|mp4|ogg|ogv|webm|htc)$ {
		      expires 1d;
		      access_log off;
		      add_header Cache-Control "public";
	     }
	    location ~* \.(?:js)$ {
                expires 15d;
                access_log off;
                add_header Cache-Control "public";
            }
        }

}
