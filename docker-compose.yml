version: "3.8"

services:
  self-service-ui:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    environment:
      CHOKIDAR_INTERVAL: 3000
      CHOKIDAR_USEPOLLING: "true"
      PORT: "3000"
      APP_ENVIRONMENT: "dev"
    ports:
      - 3000:3000
    volumes:
      - ./app/src:/app/src
      - ./app/public:/app/public
  nginx:
    image: nginx:latest
    ports:
      - 80:80
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
