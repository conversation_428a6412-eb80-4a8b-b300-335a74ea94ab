@Library('jenkins_shared_library') _

stage('Checkout shared library')
{
    EKSDeploy(
        appname: "self-service-frontend",
        branch: "$BRANCH",
        env: "dev",
        hostingType: "frontend",
        ecrRepoistory: "self-service-app",
        buildArg: ["DEFAULT_LOGENTRIES_TOKEN=1bb5523c-903f-40a7-a4d0-6cc060732786", "DEFAULT_ENV=dev"],
        deploymentFile: "scripts/kubernetes/deployments-dev.yml"
    )
}