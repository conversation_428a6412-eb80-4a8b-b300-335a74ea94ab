@Library('jenkins_shared_library') _

stage('Checkout shared library')
{
    EKSDeploy(
        appname: "self-service-frontend",
        branch: "$BRANCH",
        env: "prod",
        hostingType: "frontend",
        ecrRepoistory: "self-service-app-prod",
        buildArg: ["DEFAULT_LOGENTRIES_TOKEN=36c4bc92-fb41-4f5b-81c4-79454c2becbc", "DEFAULT_ENV=prod"],
        deploymentFile: "scripts/kubernetes/deployments-prod.yml"
    )
}