@Library('jenkins_shared_library') _

stage('Checkout shared library')
{
    EKSDeploy(
        appname: "self-service-frontend",
        branch: "$BRANCH",
        env: "qat",
        hostingType: "frontend",
        ecrRepoistory: "self-service-app-qat",
        buildArg: ["DEFAULT_LOGENTRIES_TOKEN=ecc692c7-4bd8-4cd7-ab80-1ff1ecd70d54", "DEFAULT_ENV=qat"],
        deploymentFile: "scripts/kubernetes/deployments-qat.yml"
    )
}