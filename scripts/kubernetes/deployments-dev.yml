apiVersion: v1
kind: Service
metadata:
  name: self-service-app-dev-clusterip
  namespace: self-service-app-dev
spec:
  type: ClusterIP
  selector:
    app: self-service-app-dev
  ports:
    - name: app-port
      port: 3000
      targetPort: 3000
      protocol: TCP
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: self-service-app-dev-cm
  namespace: self-service-app-dev
data:
  CHOKIDAR_INTERVAL: "3000"
  CHOKIDAR_USEPOLLING: "true"
  PORT: "3000"
  SELF_SERVICE_API_URL: "http://localhost:8010/api"
  SELF_SERVICE_UI_URL: "http://localhost:3000"
  APP_ENVIRONMENT: "dev"
  USERS_API_URL: "user-api-dev.fourkites.com"
  COMPANIES_API_URL: "company-api-dev.fourkites.com"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: self-service-app-dev
  namespace: self-service-app-dev
spec:
  selector:
    matchLabels:
      app: self-service-app-dev
  replicas: 1
  template:
    metadata:
      labels:
        app: self-service-app-dev
    spec:
      containers:
        - name: con1
          image: 723008196684.dkr.ecr.us-east-1.amazonaws.com/self-service-app:tag
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          resources:
            requests:
              memory: "1Gi"
              cpu: "500m"
            limits:
              memory: "1Gi"
              cpu: "500m"
          env:
            - name: CHOKIDAR_INTERVAL
              valueFrom:
                configMapKeyRef:
                  name: self-service-app-dev-cm
                  key: CHOKIDAR_INTERVAL
            - name: CHOKIDAR_USEPOLLING
              valueFrom:
                configMapKeyRef:
                  name: self-service-app-dev-cm
                  key: CHOKIDAR_USEPOLLING
            - name: PORT
              valueFrom:
                configMapKeyRef:
                  name: self-service-app-dev-cm
                  key: PORT
            - name: SELF_SERVICE_API_URL
              valueFrom:
                configMapKeyRef:
                  name: self-service-app-dev-cm
                  key: SELF_SERVICE_API_URL
            - name: SELF_SERVICE_UI_URL
              valueFrom:
                configMapKeyRef:
                  name: self-service-app-dev-cm
                  key: SELF_SERVICE_UI_URL
            - name: APP_ENVIRONMENT
              valueFrom:
                configMapKeyRef:
                  name: self-service-app-dev-cm
                  key: APP_ENVIRONMENT
            - name: USERS_API_URL
              valueFrom:
                configMapKeyRef:
                  name: self-service-app-dev-cm
                  key: USERS_API_URL
            - name: COMPANIES_API_URL
              valueFrom:
                configMapKeyRef:
                  name: self-service-app-dev-cm
                  key: COMPANIES_API_URL
---
apiVersion: v1
kind: Service
metadata:
  name: self-service-app-dev-lb
  namespace: self-service-app-dev
  annotations:
     service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "http"
     service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: 'true'
     service.beta.kubernetes.io/aws-load-balancer-ssl-cert: "arn:aws:acm:us-east-1:723008196684:certificate/8095007a-8e72-4a2a-95ed-491b79a70f26"
     service.beta.kubernetes.io/aws-load-balancer-ssl-ports: "https"
spec:
  selector:
    app: self-service-app-dev
  ports:
    - name: http
      protocol: TCP
      port: 80 # oustide cluster port
      targetPort: 80 # container port
    - name: https
      protocol: TCP
      port: 443 # oustide cluster port
      targetPort: 80 # container port
  type: LoadBalancer
