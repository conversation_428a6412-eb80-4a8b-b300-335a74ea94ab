apiVersion: v1
kind: Service
metadata:
  name: self-service-app-prod-clusterip
  namespace: self-service-app-prod
spec:
  type: ClusterIP
  selector:
    app: self-service-app-prod
  ports:
    - name: app-port
      port: 3000
      targetPort: 3000
      protocol: TCP
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: self-service-app-prod-cm
  namespace: self-service-app-prod
data:
  CHOKIDAR_INTERVAL: "3000"
  CHOKIDAR_USEPOLLING: "true"
  PORT: "3000"
  SELF_SERVICE_API_URL: "http://localhost:8010/api"
  SELF_SERVICE_UI_URL: "http://localhost:3000"
  APP_ENVIRONMENT: "prod"
  USERS_API_URL: "user-api.fourkites.com"
  COMPANIES_API_URL: "company-api.fourkites.com"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: self-service-app-prod
  namespace: self-service-app-prod
spec:
  selector:
    matchLabels:
      app: self-service-app-prod
  replicas: 2
  template:
    metadata:
      labels:
        app: self-service-app-prod
    spec:
      containers:
        - name: con1
          image: 723008196684.dkr.ecr.us-east-1.amazonaws.com/self-service-app-prod:tag
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          resources:
            requests:
              memory: "512Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "250m"
          env:
            - name: CHOKIDAR_INTERVAL
              valueFrom:
                configMapKeyRef:
                  name: self-service-app-prod-cm
                  key: CHOKIDAR_INTERVAL
            - name: CHOKIDAR_USEPOLLING
              valueFrom:
                configMapKeyRef:
                  name: self-service-app-prod-cm
                  key: CHOKIDAR_USEPOLLING
            - name: PORT
              valueFrom:
                configMapKeyRef:
                  name: self-service-app-prod-cm
                  key: PORT
            - name: SELF_SERVICE_API_URL
              valueFrom:
                configMapKeyRef:
                  name: self-service-app-prod-cm
                  key: SELF_SERVICE_API_URL
            - name: SELF_SERVICE_UI_URL
              valueFrom:
                configMapKeyRef:
                  name: self-service-app-prod-cm
                  key: SELF_SERVICE_UI_URL
            - name: APP_ENVIRONMENT
              valueFrom:
                configMapKeyRef:
                  name: self-service-app-prod-cm
                  key: APP_ENVIRONMENT
            - name: USERS_API_URL
              valueFrom:
                configMapKeyRef:
                  name: self-service-app-prod-cm
                  key: USERS_API_URL
            - name: COMPANIES_API_URL
              valueFrom:
                configMapKeyRef:
                  name: self-service-app-prod-cm
                  key: COMPANIES_API_URL
---
apiVersion: v1
kind: Service
metadata:
  name: self-service-app-prod-lb
  namespace: self-service-app-prod
  annotations:
     service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "http"
     service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: 'true'
     service.beta.kubernetes.io/aws-load-balancer-ssl-cert: "arn:aws:acm:us-east-1:723008196684:certificate/cfecff59-25cf-4bc8-851a-71fc0044fe19"
     service.beta.kubernetes.io/aws-load-balancer-ssl-ports: "https"
spec:
  selector:
    app: self-service-app-prod
  ports:
    - name: http
      protocol: TCP
      port: 80 # oustide cluster port
      targetPort: 80 # container port
    - name: https
      protocol: TCP
      port: 443 # oustide cluster port
      targetPort: 80 # container port
  type: LoadBalancer
